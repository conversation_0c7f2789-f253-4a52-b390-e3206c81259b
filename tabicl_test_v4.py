#!/usr/bin/env python3
"""
TabICL Test V4 - JAX Implementation
In-Context Learning with Tabular Transformer using JAX/Flax
"""

import numpy as np
import pandas as pd
import jax
import jax.numpy as jnp
from jax import random, grad, jit, vmap
import flax.linen as nn
from flax.training import train_state
import optax
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
import logging
from typing import Dict, List, Tuple, Any
import warnings
from tqdm import tqdm

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TabularTransformer(nn.Module):
    """JAX/Flax Tabular Transformer for In-Context Learning"""
    
    feature_dim: int
    d_model: int = 256
    nhead: int = 8
    num_layers: int = 4
    max_context_len: int = 32
    dropout_rate: float = 0.1
    
    def setup(self):
        # Feature projection
        self.feature_proj = nn.Dense(self.d_model)
        self.label_embedding = nn.Embed(3, self.d_model)  # 0, 1, 2 (2 for query)
        
        # Positional encoding (learnable)
        self.pos_embedding = self.param('pos_embedding', 
                                       nn.initializers.normal(stddev=0.02),
                                       (self.max_context_len + 1, self.d_model))
        
        # Transformer layers
        self.transformer_layers = [
            nn.MultiHeadDotProductAttention(
                num_heads=self.nhead,
                dropout_rate=self.dropout_rate,
                deterministic=False
            ) for _ in range(self.num_layers)
        ]
        
        self.layer_norms1 = [nn.LayerNorm() for _ in range(self.num_layers)]
        self.layer_norms2 = [nn.LayerNorm() for _ in range(self.num_layers)]
        
        self.ffn_layers = [
            nn.Sequential([
                nn.Dense(self.d_model * 4),
                nn.relu,
                nn.Dropout(self.dropout_rate, deterministic=False),
                nn.Dense(self.d_model),
                nn.Dropout(self.dropout_rate, deterministic=False)
            ]) for _ in range(self.num_layers)
        ]
        
        # Output head
        self.output_head = nn.Sequential([
            nn.Dense(self.d_model // 2),
            nn.relu,
            nn.Dropout(self.dropout_rate, deterministic=False),
            nn.Dense(1),
            nn.sigmoid
        ])
    
    def __call__(self, features, labels, query_features, training=True):
        """
        Args:
            features: [batch_size, context_len, feature_dim]
            labels: [batch_size, context_len] 
            query_features: [batch_size, feature_dim]
        """
        batch_size, context_len = features.shape[:2]
        
        # Project features
        context_embeds = self.feature_proj(features)  # [batch, context_len, d_model]
        query_embed = jnp.expand_dims(self.feature_proj(query_features), 1)  # [batch, 1, d_model]
        
        # Add label embeddings to context
        label_embeds = self.label_embedding(labels)  # [batch, context_len, d_model]
        context_embeds = context_embeds + label_embeds
        
        # Add query with special token (label=2)
        query_label = jnp.full((batch_size, 1), 2)
        query_label_embed = self.label_embedding(query_label)
        query_embed = query_embed + query_label_embed
        
        # Concatenate context and query
        sequence = jnp.concatenate([context_embeds, query_embed], axis=1)  # [batch, context_len+1, d_model]
        
        # Add positional encoding
        seq_len = sequence.shape[1]
        sequence = sequence + self.pos_embedding[:seq_len]
        
        # Apply transformer layers
        x = sequence
        for i in range(self.num_layers):
            # Self-attention
            attn_out = self.transformer_layers[i](x, deterministic=not training)
            x = self.layer_norms1[i](x + attn_out)
            
            # Feed-forward
            ffn_out = self.ffn_layers[i](x, deterministic=not training)
            x = self.layer_norms2[i](x + ffn_out)
        
        # Extract query prediction (last token)
        query_output = x[:, -1, :]  # [batch, d_model]
        
        # Predict
        prediction = self.output_head(query_output, deterministic=not training)  # [batch, 1]
        
        return jnp.squeeze(prediction, -1)  # [batch]

class TabICLPredictor:
    """JAX-based TabICL predictor"""
    
    def __init__(self, feature_dim, context_size=16, d_model=256, nhead=8, num_layers=4, learning_rate=1e-4):
        self.feature_dim = feature_dim
        self.context_size = context_size
        self.learning_rate = learning_rate
        
        # Initialize model
        self.model = TabularTransformer(
            feature_dim=feature_dim,
            d_model=d_model,
            nhead=nhead,
            num_layers=num_layers,
            max_context_len=context_size
        )
        
        # Initialize parameters
        key = random.PRNGKey(42)
        dummy_features = jnp.ones((1, context_size, feature_dim))
        dummy_labels = jnp.ones((1, context_size), dtype=jnp.int32)
        dummy_query = jnp.ones((1, feature_dim))
        
        self.params = self.model.init(key, dummy_features, dummy_labels, dummy_query, training=True)
        
        # Initialize optimizer
        self.optimizer = optax.adam(learning_rate)
        self.opt_state = self.optimizer.init(self.params)
        
        # JIT compile functions
        self.train_step = jit(self._train_step)
        self.predict_step = jit(self._predict_step)
    
    def select_context(self, X_train, y_train, strategy='balanced'):
        """Select context examples for in-context learning"""
        if strategy == 'balanced':
            pos_indices = np.where(y_train == 1)[0]
            neg_indices = np.where(y_train == 0)[0]
            
            n_pos = min(self.context_size // 2, len(pos_indices))
            n_neg = min(self.context_size - n_pos, len(neg_indices))
            
            selected_pos = np.random.choice(pos_indices, n_pos, replace=False) if len(pos_indices) > 0 else []
            selected_neg = np.random.choice(neg_indices, n_neg, replace=False) if len(neg_indices) > 0 else []
            
            context_indices = np.concatenate([selected_pos, selected_neg])
            np.random.shuffle(context_indices)
            
        elif strategy == 'random':
            context_indices = np.random.choice(len(X_train), 
                                             min(self.context_size, len(X_train)), 
                                             replace=False)
        
        return context_indices
    
    def create_batch(self, X_train, y_train, X_queries, y_queries=None):
        """Create batch for training/inference"""
        batch_size = len(X_queries)
        
        batch_features = []
        batch_labels = []
        batch_queries = []
        
        for i in range(batch_size):
            # Select context for this query
            context_indices = self.select_context(X_train, y_train)
            
            # Get context features and labels
            context_features = X_train[context_indices]
            context_labels = y_train[context_indices]
            
            # Pad if necessary
            if len(context_features) < self.context_size:
                pad_size = self.context_size - len(context_features)
                context_features = np.vstack([
                    context_features,
                    np.zeros((pad_size, context_features.shape[1]))
                ])
                context_labels = np.concatenate([context_labels, np.zeros(pad_size)])
            
            batch_features.append(context_features)
            batch_labels.append(context_labels)
            batch_queries.append(X_queries[i])
        
        return (
            jnp.array(batch_features),
            jnp.array(batch_labels, dtype=jnp.int32),
            jnp.array(batch_queries)
        )
    
    def _loss_fn(self, params, features, labels, queries, targets):
        """Loss function"""
        predictions = self.model.apply(params, features, labels, queries, training=True)
        
        # Clamp predictions to avoid numerical issues
        predictions = jnp.clip(predictions, 1e-7, 1-1e-7)
        
        # Binary cross-entropy loss
        loss = -jnp.mean(targets * jnp.log(predictions) + (1 - targets) * jnp.log(1 - predictions))
        
        return loss
    
    def _train_step(self, params, opt_state, features, labels, queries, targets):
        """Single training step"""
        loss, grads = jax.value_and_grad(self._loss_fn)(params, features, labels, queries, targets)
        
        # Clip gradients
        grads = optax.clip_by_global_norm(1.0).update(grads, opt_state, params)[1]
        
        updates, opt_state = self.optimizer.update(grads, opt_state, params)
        params = optax.apply_updates(params, updates)
        
        return params, opt_state, loss
    
    def _predict_step(self, params, features, labels, queries):
        """Single prediction step"""
        predictions = self.model.apply(params, features, labels, queries, training=False)
        return predictions
    
    def train_epoch(self, X_train, y_train, X_val, y_val, batch_size=32):
        """Train for one epoch"""
        total_loss = 0
        n_batches = 0
        
        # Create training batches
        indices = np.arange(len(X_val))
        np.random.shuffle(indices)
        
        for i in range(0, len(indices), batch_size):
            batch_indices = indices[i:i+batch_size]
            X_batch = X_val[batch_indices]
            y_batch = y_val[batch_indices]
            
            # Create ICL batch
            features, labels, queries = self.create_batch(X_train, y_train, X_batch, y_batch)
            targets = jnp.array(y_batch, dtype=jnp.float32)
            
            # Training step
            self.params, self.opt_state, loss = self.train_step(
                self.params, self.opt_state, features, labels, queries, targets
            )
            
            # Skip batch if loss is invalid
            if jnp.isnan(loss) or jnp.isinf(loss):
                continue
            
            total_loss += float(loss)
            n_batches += 1
        
        return total_loss / n_batches if n_batches > 0 else 0
    
    def predict(self, X_train, y_train, X_test, batch_size=32):
        """Make predictions using in-context learning"""
        predictions = []
        
        for i in range(0, len(X_test), batch_size):
            X_batch = X_test[i:i+batch_size]
            
            # Create ICL batch
            features, labels, queries = self.create_batch(X_train, y_train, X_batch)
            
            # Predict
            batch_preds = self.predict_step(self.params, features, labels, queries)
            predictions.extend(np.array(batch_preds))
        
        return np.array(predictions)

def load_data():
    """Load prepared features and labels"""
    logger.info("📊 Loading prepared features and labels...")
    
    try:
        # Try loading from resources first
        try:
            features_path = "resources/processed_data/prepared_features.parquet"
            X = pd.read_parquet(features_path)
        except:
            # Fallback to current directory
            X = pd.read_parquet('X_train.parquet')
        
        y_data = pd.read_parquet('y_train.parquet')
        y = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
        y = y.astype(int)
        
        logger.info(f"   ✅ Data loaded: X={X.shape}, y={len(y)}")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X.values, y
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def evaluate_tabicl_cv(X, y, n_folds=3, context_size=16, n_epochs=15):
    """Cross-validation evaluation of JAX TabICL"""
    logger.info(f"📊 JAX TabICL {n_folds}-fold CV evaluation (context_size={context_size})...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # Create TabICL predictor
        predictor = TabICLPredictor(
            feature_dim=X.shape[1],
            context_size=context_size,
            d_model=128,  # Smaller for speed
            nhead=4,
            num_layers=3,
            learning_rate=1e-4
        )
        
        # Train
        for epoch in range(n_epochs):
            loss = predictor.train_epoch(X_train_scaled, y_train, X_val_scaled, y_val, batch_size=16)
            if epoch % 5 == 0:
                logger.info(f"     Epoch {epoch}: Loss = {loss:.4f}")
        
        # Predict
        y_pred_proba = predictor.predict(X_train_scaled, y_train, X_val_scaled, batch_size=32)
        y_pred_binary = (y_pred_proba >= 0.5).astype(int)
        
        # Calculate metrics
        try:
            auc = roc_auc_score(y_val, y_pred_proba)
            f1 = f1_score(y_val, y_pred_binary)
            precision = precision_score(y_val, y_pred_binary, zero_division=0)
            recall = recall_score(y_val, y_pred_binary, zero_division=0)
            accuracy = accuracy_score(y_val, y_pred_binary)
            
            # Confusion matrix
            tn, fp, fn, tp = confusion_matrix(y_val, y_pred_binary).ravel()
            
            # Store metrics
            all_metrics['auc'].append(auc)
            all_metrics['f1'].append(f1)
            all_metrics['precision'].append(precision)
            all_metrics['recall'].append(recall)
            all_metrics['accuracy'].append(accuracy)
            all_metrics['tp'].append(tp)
            all_metrics['tn'].append(tn)
            all_metrics['fp'].append(fp)
            all_metrics['fn'].append(fn)
            
            # Store for overall analysis
            all_y_true.extend(y_val)
            all_y_pred_proba.extend(y_pred_proba)
            
            logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
            
        except Exception as e:
            logger.warning(f"     Metrics calculation failed: {e}")
            # Add default values
            for metric in ['auc', 'f1', 'precision', 'recall', 'accuracy']:
                all_metrics[metric].append(0.5 if metric == 'auc' else 0.0)
            for metric in ['tp', 'tn', 'fp', 'fn']:
                all_metrics[metric].append(0)
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    if len(all_y_pred_proba) > 0:
        all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
        overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    else:
        overall_cm = np.array([[0, 0], [0, 0]])
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba),
        'fold_metrics': all_metrics
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting JAX TabICL Structural Break Detection...")
    
    # Check JAX setup
    logger.info(f"JAX devices: {jax.devices()}")
    logger.info(f"JAX backend: {jax.default_backend()}")
    
    # Load data
    X, y = load_data()
    
    # Limit data size for faster testing
    if len(X) > 3000:
        indices = np.random.choice(len(X), 3000, replace=False)
        X, y = X[indices], y[indices]
        logger.info(f"   📊 Limited to {len(X)} samples for faster testing")
    
    # Run cross-validation evaluation
    results = evaluate_tabicl_cv(X, y, n_folds=3, context_size=16, n_epochs=10)
    
    # Print final results
    logger.info("\n🎉 JAX TABICL EVALUATION COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Save results
    import joblib
    joblib.dump(results, 'tabicl_jax_results.joblib')
    logger.info("💾 Results saved to 'tabicl_jax_results.joblib'")
    
    return results

if __name__ == "__main__":
    results = main()