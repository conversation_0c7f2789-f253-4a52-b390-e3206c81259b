#!/usr/bin/env python3
"""
Plot examples of different time series patterns from the dataset
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, <PERSON><PERSON>

def extract_series_boundary_label(X_train: pd.DataFrame, y_train: pd.Series):
    """Converts X_train with multiindex ['id', 'time'] and y_train Series into:
    - all_series: list of 1D np.ndarrays (float)
    - all_tstars: list of ints (boundary index where period == 1 starts)
    - all_labels: list of 0/1 ints (from y_train)
    """
    all_series = []
    all_tstars = []
    all_labels = []
    
    grouped = X_train.groupby(level='id')
    for id_, group in grouped:
        # Sort by time in case it's not sorted
        group = group.sort_index(level='time')
        values = group['value'].values  # full time series
        periods = group['period'].values  # same length
        
        # Boundary index is first point where period switches to 1
        try:
            tstar = np.where(periods == 1)[0][0]  # first index where period==1
        except IndexError:
            # No break in this series
            tstar = len(values)  # one past end — we won't use it for training
            
        label = int(y_train.loc[id_])  # 0 or 1
        
        all_series.append(values)
        all_tstars.append(tstar)
        all_labels.append(label)
    
    return all_series, all_tstars, all_labels

def plot_pattern_examples():
    """Plot examples of different volatility patterns"""
    
    # Load data
    print("Loading data...")
    X_train = pd.read_parquet('X_train.parquet')
    y_train = pd.read_parquet('y_train.parquet')
    volatility_df = pd.read_csv('volatility_pattern_analysis.csv')
    
    # Extract series data
    all_series, all_tstars, all_labels = extract_series_boundary_label(X_train, y_train.iloc[:, 0])
    
    # Get examples by pattern
    patterns = ['clipped', 'spiky', 'calm', 'moderate', 'volatile']
    examples_per_pattern = 3
    
    fig, axes = plt.subplots(len(patterns), examples_per_pattern, figsize=(15, 12))
    fig.suptitle('Time Series Examples by Volatility Pattern', fontsize=16)
    
    for i, pattern in enumerate(patterns):
        pattern_indices = volatility_df[volatility_df['pattern_type'] == pattern].head(examples_per_pattern)['series_id'].values
        
        for j, idx in enumerate(pattern_indices):
            ax = axes[i, j]
            
            # Get the time series
            series = all_series[idx]
            tstar = all_tstars[idx]
            label = all_labels[idx]
            
            # Plot the series
            time_points = np.arange(len(series))
            ax.plot(time_points, series, 'b-', alpha=0.7, linewidth=1)
            
            # Mark the structural break point if it exists
            if tstar < len(series):
                ax.axvline(x=tstar, color='red', linestyle='--', alpha=0.8, label=f'Break at {tstar}')
            
            # Styling
            ax.set_title(f'{pattern.capitalize()} #{j+1} (ID:{idx}, Label:{label})', fontsize=10)
            ax.grid(True, alpha=0.3)
            ax.set_xlabel('Time')
            ax.set_ylabel('Value')
            
            # Add statistics
            stats_text = f'Len:{len(series)}, Std:{np.std(series):.4f}'
            ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, 
                   verticalalignment='top', fontsize=8, 
                   bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('time_series_pattern_examples.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Print detailed analysis
    print("\n" + "="*80)
    print("DETAILED PATTERN ANALYSIS")
    print("="*80)
    
    for pattern in patterns:
        pattern_indices = volatility_df[volatility_df['pattern_type'] == pattern].head(examples_per_pattern)
        
        print(f"\n{pattern.upper()} PATTERN:")
        print("-" * 40)
        
        for j, (_, row) in enumerate(pattern_indices.iterrows()):
            idx = row['series_id']
            series = all_series[idx]
            tstar = all_tstars[idx]
            label = all_labels[idx]
            
            # Analyze clipping/truncation patterns
            unique_vals = len(np.unique(series))
            total_vals = len(series)
            uniqueness_ratio = unique_vals / total_vals
            
            # Check for repeated boundary values
            if len(series) > 10:
                first_10_unique = len(np.unique(series[:10]))
                last_10_unique = len(np.unique(series[-10:]))
            else:
                first_10_unique = len(np.unique(series[:len(series)//2]))
                last_10_unique = len(np.unique(series[len(series)//2:]))
            
            # Value range analysis
            val_min, val_max = np.min(series), np.max(series)
            val_range = val_max - val_min
            val_std = np.std(series)
            
            print(f"  Example {j+1} (ID {idx}, Label {label}):")
            print(f"    Length: {len(series)}, Break at: {tstar}")
            print(f"    Range: [{val_min:.6f}, {val_max:.6f}] (span: {val_range:.6f})")
            print(f"    Std: {val_std:.6f}")
            print(f"    Uniqueness: {unique_vals}/{total_vals} = {uniqueness_ratio:.3f}")
            print(f"    First 10 unique: {first_10_unique}, Last 10 unique: {last_10_unique}")
            
            # Show first and last few values to detect clipping
            if len(series) >= 20:
                print(f"    First 10 values: {series[:10]}")
                print(f"    Last 10 values: {series[-10:]}")
            else:
                print(f"    All values: {series}")
            
            # Detect potential clipping patterns
            clipping_indicators = []
            if uniqueness_ratio < 0.1:
                clipping_indicators.append("Very low uniqueness")
            if val_range < 0.001:
                clipping_indicators.append("Very small range")
            if np.sum(series == val_min) > len(series) * 0.1:
                clipping_indicators.append(f"Many min values ({np.sum(series == val_min)}/{len(series)})")
            if np.sum(series == val_max) > len(series) * 0.1:
                clipping_indicators.append(f"Many max values ({np.sum(series == val_max)}/{len(series)})")
            
            if clipping_indicators:
                print(f"    🔍 Clipping indicators: {', '.join(clipping_indicators)}")
            else:
                print(f"    ✅ No obvious clipping detected")
            print()

if __name__ == "__main__":
    plot_pattern_examples()