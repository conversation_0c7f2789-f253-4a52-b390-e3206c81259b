"""
Visual truncation analysis focusing on patterns that are visible to naked eye
but might be missed by statistical tests
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def detailed_visual_analysis_sample_878():
    """Detailed visual analysis focusing on truncation patterns"""
    
    print("👁️  VISUAL TRUNCATION ANALYSIS - SAMPLE 878")
    print("=" * 60)
    
    # Load data and extract sample 878
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    if 'structural_breakpoint' in y_data.columns:
        labels = y_data['structural_breakpoint'].astype(int).values
    else:
        labels = y_data.iloc[:, 0].astype(int).values
    
    window_size = len(X_data) // len(labels)
    sample_idx = 878
    
    start_idx = sample_idx * window_size
    end_idx = min((sample_idx + 1) * window_size, len(X_data))
    
    window_data = X_data.iloc[start_idx:end_idx]
    normal_mask = window_data['period'] == 0
    normal_values = window_data.loc[normal_mask, 'value'].values
    
    print(f"📊 Sample 878 Normal Part: {len(normal_values)} values")
    print(f"   Range: {np.min(normal_values):.8f} to {np.max(normal_values):.8f}")
    
    # Create high-resolution visualization to spot visual patterns
    fig, axes = plt.subplots(3, 2, figsize=(16, 12))
    fig.suptitle('Sample 878 - Visual Truncation Analysis', fontsize=16)
    
    # 1. Full time series with extreme value highlighting
    axes[0, 0].plot(normal_values, linewidth=0.5, alpha=0.8, color='blue')
    
    # Highlight potential truncation zones
    min_val, max_val = np.min(normal_values), np.max(normal_values)
    range_val = max_val - min_val
    
    # Mark values very close to extremes
    truncation_threshold = 0.02  # 2% from extremes
    lower_bound = min_val + truncation_threshold * range_val
    upper_bound = max_val - truncation_threshold * range_val
    
    # Highlight extreme regions
    axes[0, 0].axhline(lower_bound, color='red', linestyle=':', alpha=0.7, label=f'2% from min')
    axes[0, 0].axhline(upper_bound, color='red', linestyle=':', alpha=0.7, label=f'2% from max')
    axes[0, 0].axhline(min_val, color='red', linestyle='--', alpha=0.9, label=f'Min: {min_val:.6f}')
    axes[0, 0].axhline(max_val, color='red', linestyle='--', alpha=0.9, label=f'Max: {max_val:.6f}')
    
    # Highlight points in extreme zones
    extreme_low = normal_values <= lower_bound
    extreme_high = normal_values >= upper_bound
    
    if np.sum(extreme_low) > 0:
        axes[0, 0].scatter(np.where(extreme_low)[0], normal_values[extreme_low], 
                          color='red', s=20, alpha=0.8, label=f'Low extremes ({np.sum(extreme_low)})')
    
    if np.sum(extreme_high) > 0:
        axes[0, 0].scatter(np.where(extreme_high)[0], normal_values[extreme_high], 
                          color='orange', s=20, alpha=0.8, label=f'High extremes ({np.sum(extreme_high)})')
    
    axes[0, 0].set_title('Time Series with Extreme Value Zones')
    axes[0, 0].set_xlabel('Time Step')
    axes[0, 0].set_ylabel('Value')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Zoomed view of extreme regions
    if np.sum(extreme_low) > 0 or np.sum(extreme_high) > 0:
        extreme_indices = np.concatenate([np.where(extreme_low)[0], np.where(extreme_high)[0]])
        extreme_values = np.concatenate([normal_values[extreme_low], normal_values[extreme_high]])
        
        # Create context around extreme points
        context_window = 20
        for idx in extreme_indices[:10]:  # Show first 10 extreme points
            start_ctx = max(0, idx - context_window)
            end_ctx = min(len(normal_values), idx + context_window)
            
            ctx_indices = np.arange(start_ctx, end_ctx)
            ctx_values = normal_values[start_ctx:end_ctx]
            
            axes[0, 1].plot(ctx_indices, ctx_values, alpha=0.6, linewidth=1)
            axes[0, 1].scatter([idx], [normal_values[idx]], color='red', s=30, alpha=0.8)
        
        axes[0, 1].axhline(min_val, color='red', linestyle='--', alpha=0.5)
        axes[0, 1].axhline(max_val, color='red', linestyle='--', alpha=0.5)
        axes[0, 1].set_title('Context Around Extreme Points')
        axes[0, 1].set_xlabel('Time Step')
        axes[0, 1].set_ylabel('Value')
        axes[0, 1].grid(True, alpha=0.3)
    
    # 3. High-resolution histogram focusing on tails
    axes[1, 0].hist(normal_values, bins=100, alpha=0.7, density=True)
    axes[1, 0].axvline(min_val, color='red', linestyle='--', alpha=0.8)
    axes[1, 0].axvline(max_val, color='red', linestyle='--', alpha=0.8)
    axes[1, 0].set_title('High-Resolution Distribution')
    axes[1, 0].set_xlabel('Value')
    axes[1, 0].set_ylabel('Density')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Tail analysis - zoom into extreme regions
    # Left tail
    left_tail_mask = normal_values <= np.percentile(normal_values, 5)
    right_tail_mask = normal_values >= np.percentile(normal_values, 95)
    
    if np.sum(left_tail_mask) > 0:
        axes[1, 1].hist(normal_values[left_tail_mask], bins=20, alpha=0.7, 
                       label=f'Left tail (n={np.sum(left_tail_mask)})', color='blue')
    
    if np.sum(right_tail_mask) > 0:
        axes[1, 1].hist(normal_values[right_tail_mask], bins=20, alpha=0.7, 
                       label=f'Right tail (n={np.sum(right_tail_mask)})', color='orange')
    
    axes[1, 1].axvline(min_val, color='red', linestyle='--', alpha=0.8, label='Absolute min/max')
    axes[1, 1].axvline(max_val, color='red', linestyle='--', alpha=0.8)
    axes[1, 1].set_title('Tail Distributions (5% extremes)')
    axes[1, 1].set_xlabel('Value')
    axes[1, 1].set_ylabel('Count')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 5. First differences to detect sudden changes (truncation artifacts)
    if len(normal_values) > 1:
        diffs = np.diff(normal_values)
        
        axes[2, 0].plot(diffs, linewidth=0.5, alpha=0.8)
        
        # Highlight large jumps that might indicate truncation
        large_jumps = np.abs(diffs) > 3 * np.std(diffs)
        if np.sum(large_jumps) > 0:
            jump_indices = np.where(large_jumps)[0]
            axes[2, 0].scatter(jump_indices, diffs[large_jumps], 
                             color='red', s=30, alpha=0.8, 
                             label=f'Large jumps ({np.sum(large_jumps)})')
        
        axes[2, 0].axhline(0, color='black', linestyle='-', alpha=0.3)
        axes[2, 0].set_title('First Differences (Truncation Artifacts)')
        axes[2, 0].set_xlabel('Time Step')
        axes[2, 0].set_ylabel('Difference')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)
        
        # 6. Distribution of differences
        axes[2, 1].hist(diffs, bins=50, alpha=0.7, density=True)
        axes[2, 1].axvline(0, color='black', linestyle='-', alpha=0.5)
        axes[2, 1].axvline(np.mean(diffs), color='red', linestyle='--', alpha=0.8, 
                          label=f'Mean: {np.mean(diffs):.6f}')
        axes[2, 1].set_title('Distribution of First Differences')
        axes[2, 1].set_xlabel('Difference')
        axes[2, 1].set_ylabel('Density')
        axes[2, 1].legend()
        axes[2, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('sample_878_visual_truncation_analysis.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visual analysis saved to 'sample_878_visual_truncation_analysis.png'")
    
    # Detailed numerical analysis of potential truncation patterns
    print(f"\n🔍 DETAILED TRUNCATION PATTERN ANALYSIS:")
    
    # Check for clustering near extremes with finer resolution
    for threshold in [0.001, 0.005, 0.01, 0.02, 0.05]:
        lower_bound = min_val + threshold * range_val
        upper_bound = max_val - threshold * range_val
        
        near_min = np.sum(normal_values <= lower_bound)
        near_max = np.sum(normal_values >= upper_bound)
        
        print(f"   Within {threshold*100:.1f}% of extremes: {near_min} low, {near_max} high")
    
    # Check for flat segments (potential clipping)
    print(f"\n🔍 FLAT SEGMENT ANALYSIS:")
    tolerance = 1e-8
    
    # Find segments where values don't change
    flat_segments = []
    current_segment_start = 0
    current_value = normal_values[0]
    
    for i in range(1, len(normal_values)):
        if abs(normal_values[i] - current_value) > tolerance:
            if i - current_segment_start > 1:  # Segment of length > 1
                flat_segments.append({
                    'start': current_segment_start,
                    'end': i-1,
                    'length': i - current_segment_start,
                    'value': current_value
                })
            current_segment_start = i
            current_value = normal_values[i]
    
    # Check last segment
    if len(normal_values) - current_segment_start > 1:
        flat_segments.append({
            'start': current_segment_start,
            'end': len(normal_values)-1,
            'length': len(normal_values) - current_segment_start,
            'value': current_value
        })
    
    if flat_segments:
        print(f"   Found {len(flat_segments)} flat segments:")
        for seg in flat_segments[:10]:  # Show first 10
            is_extreme = (abs(seg['value'] - min_val) < tolerance or 
                         abs(seg['value'] - max_val) < tolerance)
            extreme_marker = " ⚠️ EXTREME" if is_extreme else ""
            print(f"      Length {seg['length']}: value={seg['value']:.8f}{extreme_marker}")
    else:
        print(f"   No flat segments found")
    
    return normal_values

def main():
    """Main visual analysis"""
    normal_values = detailed_visual_analysis_sample_878()
    
    print(f"\n👁️  VISUAL INSPECTION GUIDE:")
    print(f"   Look for the following truncation patterns in the generated plot:")
    print(f"   1. Flat horizontal lines at min/max values")
    print(f"   2. Sudden jumps or discontinuities near extremes")
    print(f"   3. Clustering of points at specific values")
    print(f"   4. Unnatural distribution shapes in the tails")
    print(f"   5. Symmetric patterns around zero")
    print(f"   6. Values that seem 'cut off' rather than naturally distributed")

if __name__ == "__main__":
    main()