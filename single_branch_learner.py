"""
Single Branch Incremental Learner
Stores all normal/candidate parts incrementally and uses advanced similarity measures
"""

import numpy as np
import torch
import torch.nn as nn
import torch.optim as optim
from collections import deque
from typing import Dict, List, Tuple, Optional
from scipy.spatial.distance import pdist, squareform
from scipy.stats import entropy
import time

class MutualInformationNeuralEstimator(nn.Module):
    """Neural Mutual Information Estimator (MINE)"""
    
    def __init__(self, input_dim: int = 100, hidden_dim: int = 64):
        super().__init__()
        self.net = nn.Sequential(
            nn.Linear(input_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        )
        
    def forward(self, x, y):
        """Estimate MI between x and y"""
        # Joint samples
        joint = torch.cat([x, y], dim=-1)
        joint_scores = self.net(joint)
        
        # Marginal samples (shuffle y)
        y_shuffled = y[torch.randperm(len(y))]
        marginal = torch.cat([x, y_shuffled], dim=-1)
        marginal_scores = self.net(marginal)
        
        # MINE objective
        mi_estimate = torch.mean(joint_scores) - torch.log(torch.mean(torch.exp(marginal_scores)))
        return mi_estimate

class KernelSimilarity:
    """Kernel-based similarity computation"""
    
    def __init__(self, kernel_type: str = 'rbf', gamma: float = 1.0):
        self.kernel_type = kernel_type
        self.gamma = gamma
    
    def rbf_kernel(self, X: np.ndarray, Y: np.ndarray) -> np.ndarray:
        """RBF (Gaussian) kernel"""
        # Compute pairwise squared distances
        X_norm = np.sum(X**2, axis=1, keepdims=True)
        Y_norm = np.sum(Y**2, axis=1, keepdims=True)
        distances = X_norm + Y_norm.T - 2 * np.dot(X, Y.T)
        
        # Apply RBF kernel
        return np.exp(-self.gamma * distances)
    
    def polynomial_kernel(self, X: np.ndarray, Y: np.ndarray, degree: int = 3) -> np.ndarray:
        """Polynomial kernel"""
        return (np.dot(X, Y.T) + 1) ** degree
    
    def linear_kernel(self, X: np.ndarray, Y: np.ndarray) -> np.ndarray:
        """Linear kernel"""
        return np.dot(X, Y.T)
    
    def compute_kernel_similarity(self, X: np.ndarray, Y: np.ndarray) -> float:
        """Compute kernel-based similarity between two datasets"""
        if self.kernel_type == 'rbf':
            K = self.rbf_kernel(X, Y)
        elif self.kernel_type == 'polynomial':
            K = self.polynomial_kernel(X, Y)
        else:
            K = self.linear_kernel(X, Y)
        
        # Return mean kernel value as similarity
        return np.mean(K)

class SingleBranchLearner:
    """
    Single branch learner with incremental storage and advanced similarity measures
    """
    
    def __init__(self, 
                 segment_length: int = 50,
                 max_history: int = 1000,
                 learning_rate: float = 0.001,
                 device: str = 'cpu'):
        
        self.segment_length = segment_length
        self.max_history = max_history
        self.device = torch.device(device)
        
        # Incremental storage
        self.normal_parts = deque(maxlen=max_history)
        self.candidate_parts = deque(maxlen=max_history)
        self.labels = deque(maxlen=max_history)
        
        # Neural MI estimator
        self.mi_estimator = MutualInformationNeuralEstimator(segment_length).to(self.device)
        self.mi_optimizer = optim.Adam(self.mi_estimator.parameters(), lr=learning_rate)
        
        # Kernel similarity
        self.kernel_sim = KernelSimilarity(kernel_type='rbf', gamma=0.1)
        
        # Statistics
        self.sample_count = 0
        self.processing_times = deque(maxlen=100)
        
    def add_sample(self, normal_part: np.ndarray, candidate_part: np.ndarray, label: int) -> Dict:
        """Add new sample and compute similarities with stored history"""
        
        start_time = time.time()
        
        # Store new parts
        self.normal_parts.append(normal_part.copy())
        self.candidate_parts.append(candidate_part.copy())
        self.labels.append(label)
        self.sample_count += 1
        
        if len(self.normal_parts) < 2:
            # First sample - no comparison possible
            result = {
                'sample_id': self.sample_count,
                'label_probability': 0.5,
                'similarities': {},
                'stored_samples': len(self.normal_parts),
                'processing_time': time.time() - start_time
            }
            self.processing_times.append(result['processing_time'])
            return result
        
        # Compute similarities with stored history
        similarities = self._compute_all_similarities(normal_part, candidate_part)
        
        # Learn probability of label
        label_prob = self._learn_label_probability(similarities, label)
        
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        result = {
            'sample_id': self.sample_count,
            'label_probability': label_prob,
            'similarities': similarities,
            'stored_samples': len(self.normal_parts),
            'processing_time': processing_time
        }
        
        return result
    
    def _compute_all_similarities(self, normal_part: np.ndarray, candidate_part: np.ndarray) -> Dict:
        """Compute all similarity measures"""
        
        similarities = {}
        
        # Get concatenated history
        normal_history = np.array(list(self.normal_parts)[:-1])  # Exclude current
        candidate_history = np.array(list(self.candidate_parts)[:-1])
        
        if len(normal_history) == 0:
            return similarities
        
        # 1. Kernel Similarity
        similarities['kernel_normal'] = self._compute_kernel_similarity(normal_part, normal_history)
        similarities['kernel_candidate'] = self._compute_kernel_similarity(candidate_part, candidate_history)
        similarities['kernel_combined'] = (similarities['kernel_normal'] + similarities['kernel_candidate']) / 2
        
        # 2. Mutual Information (Neural)
        similarities['mi_normal'] = self._compute_neural_mi(normal_part, normal_history)
        similarities['mi_candidate'] = self._compute_neural_mi(candidate_part, candidate_history)
        
        # 3. KL Divergence
        similarities['kl_normal'] = self._compute_kl_divergence(normal_part, normal_history)
        similarities['kl_candidate'] = self._compute_kl_divergence(candidate_part, candidate_history)
        
        # 4. Cross-part similarities (normal vs candidate history)
        similarities['cross_normal_to_candidate'] = self._compute_kernel_similarity(normal_part, candidate_history)
        similarities['cross_candidate_to_normal'] = self._compute_kernel_similarity(candidate_part, normal_history)
        
        return similarities
    
    def _compute_kernel_similarity(self, current_part: np.ndarray, history: np.ndarray) -> float:
        """Compute kernel similarity between current part and history"""
        
        if len(history) == 0:
            return 0.0
        
        # Reshape for kernel computation
        current_reshaped = current_part.reshape(1, -1)
        
        # Compute kernel similarity
        similarity = self.kernel_sim.compute_kernel_similarity(current_reshaped, history)
        
        return float(similarity)
    
    def _compute_neural_mi(self, current_part: np.ndarray, history: np.ndarray) -> float:
        """Compute neural mutual information estimate"""
        
        if len(history) < 5:  # Need minimum samples for MI
            return 0.0
        
        try:
            # Convert to tensors
            current_tensor = torch.FloatTensor(current_part).unsqueeze(0).to(self.device)
            history_tensor = torch.FloatTensor(history).to(self.device)
            
            # Sample from history for MI estimation
            n_samples = min(len(history), 50)  # Limit for efficiency
            sample_indices = np.random.choice(len(history), n_samples, replace=False)
            history_sample = history_tensor[sample_indices]
            
            # Repeat current part to match history sample size
            current_repeated = current_tensor.repeat(n_samples, 1)
            
            # Estimate MI
            with torch.no_grad():
                mi_estimate = self.mi_estimator(current_repeated, history_sample)
            
            return float(mi_estimate.item())
            
        except Exception as e:
            return 0.0
    
    def _compute_kl_divergence(self, current_part: np.ndarray, history: np.ndarray) -> float:
        """Compute KL divergence between current part and history distributions"""
        
        if len(history) == 0:
            return 1.0  # Maximum divergence
        
        try:
            # Create histograms for distribution estimation
            n_bins = 20
            
            # Get data range
            all_data = np.concatenate([current_part, history.flatten()])
            data_range = (np.min(all_data), np.max(all_data))
            
            # Compute histograms
            current_hist, _ = np.histogram(current_part, bins=n_bins, range=data_range, density=True)
            history_hist, _ = np.histogram(history.flatten(), bins=n_bins, range=data_range, density=True)
            
            # Add small epsilon to avoid log(0)
            epsilon = 1e-10
            current_hist = current_hist + epsilon
            history_hist = history_hist + epsilon
            
            # Normalize to probabilities
            current_hist = current_hist / np.sum(current_hist)
            history_hist = history_hist / np.sum(history_hist)
            
            # Compute KL divergence
            kl_div = entropy(current_hist, history_hist)
            
            # Convert to similarity (0 = identical, 1 = maximum divergence)
            return min(1.0, kl_div / 5.0)  # Normalize
            
        except Exception as e:
            return 0.5  # Default moderate divergence
    
    def _learn_label_probability(self, similarities: Dict, true_label: int) -> float:
        """Learn probability of label based on similarities"""
        
        if not similarities:
            return 0.5  # Default probability
        
        # Combine similarities into features
        features = []
        
        # Kernel similarities
        features.append(similarities.get('kernel_normal', 0.0))
        features.append(similarities.get('kernel_candidate', 0.0))
        features.append(similarities.get('kernel_combined', 0.0))
        
        # MI similarities
        features.append(similarities.get('mi_normal', 0.0))
        features.append(similarities.get('mi_candidate', 0.0))
        
        # KL divergences (convert to similarities)
        features.append(1.0 - similarities.get('kl_normal', 0.5))
        features.append(1.0 - similarities.get('kl_candidate', 0.5))
        
        # Cross-part similarities
        features.append(similarities.get('cross_normal_to_candidate', 0.0))
        features.append(similarities.get('cross_candidate_to_normal', 0.0))
        
        # Simple weighted combination for probability estimation
        weights = np.array([0.2, 0.2, 0.15, 0.1, 0.1, 0.1, 0.1, 0.025, 0.025])
        
        # Ensure same length
        features = np.array(features[:len(weights)])
        if len(features) < len(weights):
            features = np.pad(features, (0, len(weights) - len(features)), 'constant')
        
        # Compute weighted similarity score
        similarity_score = np.dot(features, weights)
        
        # Convert to probability using sigmoid-like function
        # High similarity -> low probability of structural break (label 1)
        # Low similarity -> high probability of structural break (label 1)
        prob_label_1 = 1.0 / (1.0 + np.exp(5 * (similarity_score - 0.5)))
        
        return float(prob_label_1)
    
    def predict_label_probability(self, normal_part: np.ndarray, candidate_part: np.ndarray) -> float:
        """Predict label probability for new sample without storing it"""
        
        if len(self.normal_parts) == 0:
            return 0.5
        
        # Compute similarities with stored history
        similarities = self._compute_all_similarities(normal_part, candidate_part)
        
        # Get probability without updating
        prob = self._learn_label_probability(similarities, 0)  # Dummy label
        
        return prob
    
    def get_statistics(self) -> Dict:
        """Get learner statistics"""
        
        return {
            'total_samples': self.sample_count,
            'stored_normal_parts': len(self.normal_parts),
            'stored_candidate_parts': len(self.candidate_parts),
            'stored_labels': len(self.labels),
            'avg_processing_time': np.mean(list(self.processing_times)) if self.processing_times else 0,
            'label_distribution': dict(zip(*np.unique(list(self.labels), return_counts=True))) if self.labels else {},
            'memory_usage_mb': self._estimate_memory_usage()
        }
    
    def _estimate_memory_usage(self) -> float:
        """Estimate memory usage in MB"""
        
        # Rough estimation
        normal_size = len(self.normal_parts) * self.segment_length * 4  # float32
        candidate_size = len(self.candidate_parts) * self.segment_length * 4
        labels_size = len(self.labels) * 4  # int32
        
        total_bytes = normal_size + candidate_size + labels_size
        return total_bytes / (1024 * 1024)  # Convert to MB

def test_single_branch_learner():
    """Test the single branch learner"""
    
    print("🧠 Testing Single Branch Learner")
    print("=" * 50)
    
    # Initialize learner
    learner = SingleBranchLearner(
        segment_length=50,
        max_history=200,
        learning_rate=0.001
    )
    
    # Generate test data
    np.random.seed(42)
    n_samples = 100
    
    results = []
    
    for i in range(n_samples):
        # Generate normal and candidate parts
        if i < 30:
            # Similar patterns (label 0)
            normal_part = np.sin(np.linspace(0, 2*np.pi, 50)) + np.random.randn(50) * 0.1
            candidate_part = np.sin(np.linspace(0, 2*np.pi, 50)) + np.random.randn(50) * 0.1
            label = 0
        elif i < 60:
            # Different patterns (label 1)
            normal_part = np.sin(np.linspace(0, 2*np.pi, 50)) + np.random.randn(50) * 0.1
            candidate_part = np.cos(np.linspace(0, 4*np.pi, 50)) * 2 + np.random.randn(50) * 0.2
            label = 1
        else:
            # Mixed patterns
            normal_part = np.random.randn(50)
            candidate_part = np.random.randn(50) * 2
            label = np.random.randint(0, 2)
        
        # Add sample and get result
        result = learner.add_sample(normal_part, candidate_part, label)
        results.append(result)
        
        # Print progress
        if (i + 1) % 20 == 0:
            print(f"   Sample {i+1}: Prob={result['label_probability']:.3f}, "
                  f"Label={label}, Stored={result['stored_samples']}")
    
    # Final statistics
    stats = learner.get_statistics()
    print(f"\n📊 Final Statistics:")
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    # Test prediction on new sample
    test_normal = np.sin(np.linspace(0, 2*np.pi, 50)) + np.random.randn(50) * 0.1
    test_candidate = np.cos(np.linspace(0, 4*np.pi, 50)) * 2 + np.random.randn(50) * 0.2
    
    pred_prob = learner.predict_label_probability(test_normal, test_candidate)
    print(f"\n🎯 Test Prediction: {pred_prob:.3f} (should be high for structural break)")
    
    return learner, results

if __name__ == "__main__":
    learner, results = test_single_branch_learner()