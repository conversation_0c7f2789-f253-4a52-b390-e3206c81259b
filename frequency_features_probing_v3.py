import numpy as np
import pandas as pd
from scipy.signal import welch, butter, filtfilt, hilbert, medfilt
from scipy.stats import pearsonr, moment
import xgboost as xgb
from sklearn.model_selection import cross_val_score
import multiprocessing as mp
from tqdm import tqdm
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

def design_bandpass_filter(center_freq, bandwidth_ratio=0.2, fs=500, order=4):
    """Design bandpass filter around center frequency."""
    bandwidth = center_freq * bandwidth_ratio
    low_freq = max(0.01, center_freq - bandwidth/2)
    high_freq = min(fs/2 - 0.01, center_freq + bandwidth/2)
    
    if low_freq >= high_freq:
        return None, None
    
    try:
        b, a = butter(order, [low_freq, high_freq], btype='band', fs=fs)
        return b, a
    except Exception:
        return None, None

def extract_welch_psd_features(f, psd, prefix=''):
    """Extract Welch PSD-derived features."""
    features = {}
    
    if len(f) < 2 or len(psd) < 2:
        return {f'{prefix}_spectral_{k}': np.nan for k in ['centroid', 'spread', 'skew', 'kurt', 'slope', 'median']}
    
    try:
        # Spectral centroid (mean frequency)
        features[f'{prefix}_spectral_centroid'] = np.sum(f * psd) / np.sum(psd) if np.sum(psd) > 0 else np.nan
        
        # Spectral spread (standard deviation)
        if not np.isnan(features[f'{prefix}_spectral_centroid']):
            features[f'{prefix}_spectral_spread'] = np.sqrt(np.sum(((f - features[f'{prefix}_spectral_centroid']) ** 2) * psd) / np.sum(psd))
        else:
            features[f'{prefix}_spectral_spread'] = np.nan
        
        # Spectral skewness and kurtosis
        if len(psd) > 3:
            features[f'{prefix}_spectral_skew'] = moment(psd, moment=3)
            features[f'{prefix}_spectral_kurt'] = moment(psd, moment=4)
        else:
            features[f'{prefix}_spectral_skew'] = np.nan
            features[f'{prefix}_spectral_kurt'] = np.nan
        
        # Spectral slope (power law exponent)
        valid_idx = (f[1:] > 0) & (psd[1:] > 0)
        if np.sum(valid_idx) > 2:
            features[f'{prefix}_spectral_slope'] = np.polyfit(np.log(f[1:][valid_idx]), np.log(psd[1:][valid_idx]), 1)[0]
        else:
            features[f'{prefix}_spectral_slope'] = np.nan
        
        # Spectral median
        cumsum_psd = np.cumsum(psd)
        median_idx = np.where(cumsum_psd >= np.sum(psd)/2)[0]
        if len(median_idx) > 0:
            features[f'{prefix}_spectral_median'] = f[median_idx[0]]
        else:
            features[f'{prefix}_spectral_median'] = np.nan
            
    except Exception:
        for k in ['centroid', 'spread', 'skew', 'kurt', 'slope', 'median']:
            features[f'{prefix}_spectral_{k}'] = np.nan
    
    return features

def extract_v3_features(series, tstar, fs=500, bandwidth_ratio=0.2, window_size=5):
    """Extract V3 features with Welch PSD, dominant frequency analysis, and smoothing."""
    features = {}
    
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 20 or len(post_segment) < 20:
        return {f'v3_{k}': np.nan for k in range(35)}
    
    try:
        # 1. WELCH PSD FEATURES ON RAW SIGNALS
        f_pre_raw, psd_pre_raw = welch(pre_segment, fs=fs, nperseg=min(len(pre_segment)//2, 64))
        f_post_raw, psd_post_raw = welch(post_segment, fs=fs, nperseg=min(len(post_segment)//2, 64))
        
        # Extract Welch PSD features from raw signals
        pre_welch = extract_welch_psd_features(f_pre_raw, psd_pre_raw, 'pre')
        post_welch = extract_welch_psd_features(f_post_raw, psd_post_raw, 'post')
        features.update(pre_welch)
        features.update(post_welch)
        
        # PSD feature ratios
        for key in ['spectral_centroid', 'spectral_spread', 'spectral_skew', 'spectral_kurt', 'spectral_slope', 'spectral_median']:
            pre_val = pre_welch.get(f'pre_{key}', np.nan)
            post_val = post_welch.get(f'post_{key}', np.nan)
            features[f'{key}_ratio'] = post_val / pre_val if pre_val != 0 and not np.isnan(pre_val) and not np.isnan(post_val) else np.nan
        
        # 2. DETERMINE DOMINANT FREQUENCY FROM RAW PRE SIGNAL
        dominant_freq_raw = f_pre_raw[np.argmax(psd_pre_raw)]
        
        # 3. SMOOTH BOTH PARTS SEPARATELY
        pre_smoothed = medfilt(pre_segment, kernel_size=window_size)
        post_smoothed = medfilt(post_segment, kernel_size=window_size)
        
        # 4. DOMINANT FREQUENCY ANALYSIS
        dominant_freq_pre_raw = f_pre_raw[np.argmax(psd_pre_raw)]
        dominant_freq_post_raw = f_post_raw[np.argmax(psd_post_raw)]
        
        features['dominant_freq_pre_raw'] = dominant_freq_pre_raw
        features['dominant_freq_post_raw'] = dominant_freq_post_raw
        features['dominant_freq_ratio'] = dominant_freq_post_raw / dominant_freq_pre_raw if dominant_freq_pre_raw > 0 else np.nan
        features['dominant_freq_shift'] = abs(dominant_freq_post_raw - dominant_freq_pre_raw)
        
        # 5. FREQUENCY FILTERING WITH RAW DOMINANT FREQUENCY
        b, a = design_bandpass_filter(dominant_freq_raw, bandwidth_ratio, fs)
        if b is not None:
            post_filtered = filtfilt(b, a, post_smoothed)
            post_residual = post_smoothed - post_filtered
            
            power_original = np.var(post_smoothed)
            power_filtered = np.var(post_filtered)
            power_residual = np.var(post_residual)
            
            features['preservation_ratio'] = power_filtered / power_original if power_original > 0 else np.nan
            features['noise_ratio'] = power_residual / power_original if power_original > 0 else np.nan
            features['signal_loss'] = 1 - features['preservation_ratio'] if not np.isnan(features['preservation_ratio']) else np.nan
        else:
            features['preservation_ratio'] = np.nan
            features['noise_ratio'] = np.nan
            features['signal_loss'] = np.nan
        
        # 6. POWER ANALYSIS
        power_pre = np.sum(psd_pre_raw) * (f_pre_raw[1] - f_pre_raw[0]) if len(f_pre_raw) > 1 else np.sum(psd_pre_raw)
        power_post = np.sum(psd_post_raw) * (f_post_raw[1] - f_post_raw[0]) if len(f_post_raw) > 1 else np.sum(psd_post_raw)
        
        features['total_power_ratio'] = power_post / power_pre if power_pre > 0 else np.nan
        features['dominant_power_ratio'] = psd_post_raw[np.argmax(psd_post_raw)] / psd_pre_raw[np.argmax(psd_pre_raw)] if psd_pre_raw[np.argmax(psd_pre_raw)] > 0 else np.nan
        
        # 7. SMOOTHING EFFECTIVENESS
        pre_smooth_effect = np.var(pre_segment - pre_smoothed) / np.var(pre_segment) if np.var(pre_segment) > 0 else np.nan
        post_smooth_effect = np.var(post_segment - post_smoothed) / np.var(post_segment) if np.var(post_segment) > 0 else np.nan
        
        features['pre_smoothing_effect'] = pre_smooth_effect
        features['post_smoothing_effect'] = post_smooth_effect
        features['smoothing_effect_ratio'] = post_smooth_effect / pre_smooth_effect if pre_smooth_effect > 0 else np.nan
        
        # 8. ENVELOPE ANALYSIS
        envelope_pre = np.abs(hilbert(pre_smoothed))
        envelope_post = np.abs(hilbert(post_smoothed))
        
        features['envelope_mean_ratio'] = np.mean(envelope_post) / np.mean(envelope_pre) if np.mean(envelope_pre) > 0 else np.nan
        features['envelope_std_ratio'] = np.std(envelope_post) / np.std(envelope_pre) if np.std(envelope_pre) > 0 else np.nan
        
        # 9. AUTOCORRELATION
        def autocorr_at_lag(x, lag=1):
            if len(x) <= lag:
                return np.nan
            return np.corrcoef(x[:-lag], x[lag:])[0, 1] if len(x) > lag else np.nan
        
        ac_pre = autocorr_at_lag(pre_smoothed, lag=1)
        ac_post = autocorr_at_lag(post_smoothed, lag=1)
        features['autocorr_change'] = abs(ac_post - ac_pre) if not (np.isnan(ac_pre) or np.isnan(ac_post)) else np.nan
        
        # 10. COHERENCE BETWEEN PRE AND POST
        min_len = min(len(pre_smoothed), len(post_smoothed))
        if min_len > 10:
            features['pre_post_coherence'], _ = pearsonr(pre_smoothed[:min_len], post_smoothed[:min_len])
        else:
            features['pre_post_coherence'] = np.nan
            
    except Exception as e:
        return {f'v3_{k}': np.nan for k in range(35)}
    
    return features

def extract_v3_features_wrapper(args):
    """Wrapper for parallel processing."""
    series, tstar, fs, bandwidth_ratio, window_size = args
    return extract_v3_features(series, tstar, fs, bandwidth_ratio, window_size)

def evaluate_v3_approach(all_series, all_tstars, all_labels, 
                        window_sizes=[5, 7, 9], 
                        bandwidth_ratios=[0.2]):
    """Evaluate V3 approach with parallel processing and progress bars."""
    results = {}
    n_processes = max(1, mp.cpu_count() - 1)
    
    print(f"Evaluating V3 approach with {n_processes} processes...")
    
    for bandwidth_ratio in bandwidth_ratios:
        for window_size in window_sizes:
            config_name = f"v3_w{window_size}_b{bandwidth_ratio}"
            print(f"\nTesting: {config_name}")
            
            # Prepare arguments for parallel processing
            args_list = [(series, tstar, 500, bandwidth_ratio, window_size) 
                        for series, tstar in zip(all_series, all_tstars)]
            
            # Extract features in parallel with progress bar
            with mp.Pool(processes=n_processes) as pool:
                feature_list = list(tqdm(
                    pool.imap(extract_v3_features_wrapper, args_list),
                    total=len(args_list),
                    desc=f"Extracting {config_name}"
                ))
            
            feature_df = pd.DataFrame(feature_list)
            feature_df = feature_df.fillna(feature_df.median())
            
            if feature_df.shape[1] == 0:
                continue
            
            try:
                model = xgb.XGBClassifier(
                    n_estimators=100, random_state=42, 
                    tree_method='gpu_hist', gpu_id=0, eval_metric='auc'
                )
                cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
                auc_score = np.mean(cv_scores)
                auc_std = np.std(cv_scores)
                
                results[config_name] = {
                    'auc_mean': auc_score,
                    'auc_std': auc_std,
                    'n_features': feature_df.shape[1],
                    'window_size': window_size,
                    'bandwidth_ratio': bandwidth_ratio
                }
                
                print(f"AUC: {auc_score:.4f} ± {auc_std:.4f} ({feature_df.shape[1]} features)")
                
                # Feature importance for best result
                if auc_score == max([r.get('auc_mean', 0) for r in results.values()]):
                    model.fit(feature_df, all_labels)
                    feature_importance = pd.DataFrame({
                        'feature': feature_df.columns,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    
                    print("Top 5 features:")
                    for _, row in feature_importance.head().iterrows():
                        print(f"  {row['feature']}: {row['importance']:.4f}")
                
            except Exception as e:
                print(f"Error: {e}")
                results[config_name] = {'auc_mean': np.nan, 'auc_std': np.nan, 'n_features': 0}
    
    return results

def load_data(data_path):
    """Load time series data from parquet files."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series, all_tstars, all_labels = [], [], []
        
        for id_, group in X_train.groupby(level='id'):
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(int(y_train.loc[id_]))
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    parser = argparse.ArgumentParser(description='Frequency Features Probing v3')
    parser.add_argument('--data_path', type=str, default='.', help='Path to data directory')
    parser.add_argument('--window_sizes', nargs='+', type=int, default=[5, 7, 9],
                       help='Window sizes for median filtering')
    parser.add_argument('--bandwidth_ratios', nargs='+', type=float, default=[0.2],
                       help='Bandwidth ratios for frequency filtering')
    
    args = parser.parse_args()
    
    print("Frequency Features Probing v3")
    print("="*50)
    print("NEW FEATURES:")
    print("• Welch PSD-derived features (centroid, spread, skew, kurt, slope, median)")
    print("• Dominant frequency from raw pre-signal")
    print("• Separate smoothing of pre/post segments")
    print("• Parallel processing with progress bars")
    print("• Enhanced frequency filtering analysis")
    print(f"• Window sizes: {args.window_sizes}")
    print(f"• Bandwidth ratios: {args.bandwidth_ratios}")
    
    # Load data
    all_series, all_tstars, all_labels = load_data(args.data_path)
    if all_series is None:
        sys.exit(1)
    
    print(f"Loaded {len(all_series)} time series")
    print(f"Labels distribution: {np.bincount(all_labels)}")
    
    # Run analysis
    results = evaluate_v3_approach(
        all_series, all_tstars, all_labels,
        window_sizes=args.window_sizes,
        bandwidth_ratios=args.bandwidth_ratios
    )
    
    print("\n" + "="*50)
    print("SUMMARY RESULTS")
    print("="*50)
    
    # Sort results by AUC
    sorted_results = sorted(results.items(), key=lambda x: x[1].get('auc_mean', 0), reverse=True)
    
    for config_name, result in sorted_results:
        if not np.isnan(result['auc_mean']):
            print(f"{config_name}: AUC = {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
    
    if sorted_results:
        best_config, best_result = sorted_results[0]
        print(f"\n🏆 Best: {best_config} (AUC = {best_result['auc_mean']:.4f})")

if __name__ == "__main__":
    main()