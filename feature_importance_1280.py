"""
Robust Feature Importance Analysis for 1280 TSFRESHplusCatch22 Features
Fixed version with proper array handling and enhanced robustness
"""

import pandas as pd
import numpy as np
import joblib
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.tree import DecisionTreeClassifier
import lightgbm as lgb
import xgboost as xgb
from catboost import CatBoostClassifier
import shap
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def safe_shap_analysis(model, X_sample, model_name):
    """
    Robust SHAP analysis with proper error handling and type conversion
    """
    try:
        if model_name in ['lgb', 'xgb', 'rf', 'dt']:
            explainer = shap.TreeExplainer(model)
        else:
            explainer = shap.Explainer(model)
            
        shap_vals = explainer.shap_values(X_sample)
        
        # Handle different SHAP output formats
        if isinstance(shap_vals, list):
            # Multi-class case: take absolute mean across classes
            if len(shap_vals) > 1:
                shap_vals = shap_vals[1]  # or np.mean([np.abs(sv) for sv in shap_vals], axis=0)
            else:
                shap_vals = shap_vals[0]
        
        # Ensure 2D array structure for proper aggregation
        if shap_vals.ndim == 1:
            shap_vals = shap_vals.reshape(1, -1)
            
        # Compute mean absolute SHAP values across samples
        shap_importance = np.abs(shap_vals).mean(axis=0)
        
        return shap_importance
        
    except Exception as e:
        print(f"  SHAP analysis failed for {model_name}: {str(e)}")
        return None

def extract_top_features(importance_array, feature_names, top_k=50):
    """
    Safely extract top k features from importance array
    """
    try:
        # Ensure we're working with 1D array
        if importance_array.ndim > 1:
            importance_array = importance_array.flatten()
            
        # Get top indices and convert to list of integers
        top_indices = np.argsort(importance_array)[-top_k:][::-1]
        
        # Safe conversion to feature names
        top_features = []
        for idx in top_indices:
            idx_int = int(idx)  # Explicit conversion
            if 0 <= idx_int < len(feature_names):
                top_features.append(feature_names[idx_int])
                
        return top_features
        
    except Exception as e:
        print(f"  Error extracting top features: {str(e)}")
        return []

def compute_feature_consensus(all_top_features, feature_names):
    """
    Compute consensus ranking across multiple feature selection methods
    """
    feature_scores = {}
    
    # Weight different methods (can be adjusted based on domain expertise)
    method_weights = {
        'lgb_imp': 1.0, 'xgb_imp': 1.0, 'catboost_imp': 1.0,
        'rf_imp': 0.8, 'dt_imp': 0.6,  # Tree-based methods get lower weight
        'lgb_shap': 1.2, 'xgb_shap': 1.2, 'rf_shap': 1.0  # SHAP gets higher weight
    }
    
    for method_name, features_list in all_top_features.items():
        weight = method_weights.get(method_name, 1.0)
        
        for rank, feature in enumerate(features_list):
            # Higher rank = lower score (inverse ranking)
            rank_score = (len(features_list) - rank) / len(features_list)
            
            if feature not in feature_scores:
                feature_scores[feature] = {'total_score': 0, 'method_count': 0}
                
            feature_scores[feature]['total_score'] += rank_score * weight
            feature_scores[feature]['method_count'] += 1
    
    # Normalize by method count and sort
    consensus_ranking = []
    for feature, scores in feature_scores.items():
        avg_score = scores['total_score'] / scores['method_count']
        consensus_ranking.append((feature, avg_score, scores['method_count']))
    
    consensus_ranking.sort(key=lambda x: (x[2], x[1]), reverse=True)  # Sort by count, then score
    
    return consensus_ranking

def main():
    print("Loading data...")
    try:
        X = joblib.load('1280_tsfresh_catch22_cca')
        y_train = pd.read_parquet('y_train.parquet')
        y = y_train.iloc[:len(X)].values.ravel()
        
        # Robust data preprocessing
        print(f"Initial data shape: X={X.shape}, y={y.shape}")
        
        # Handle missing values with multiple strategies
        X_filled = X.copy()
        
        # For numerical columns: median imputation
        numeric_cols = X_filled.select_dtypes(include=[np.number]).columns
        X_filled[numeric_cols] = X_filled[numeric_cols].fillna(X_filled[numeric_cols].median())
        
        # Remove any remaining NaN/inf values
        X_filled = X_filled.replace([np.inf, -np.inf], np.nan)
        X_filled = X_filled.fillna(0)
        
        print(f"Preprocessed data shape: X={X_filled.shape}, y={y.shape}")
        
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    # Stratified split with proper error handling
    try:
        X_train, X_test, y_train_split, y_test = train_test_split(
            X_filled, y, test_size=0.2, random_state=42, stratify=y
        )
        print(f"Train/test split: {X_train.shape}/{X_test.shape}")
        
    except ValueError as e:
        print(f"Stratification failed, using random split: {e}")
        X_train, X_test, y_train_split, y_test = train_test_split(
            X_filled, y, test_size=0.2, random_state=42
        )
    
    # Enhanced model configuration
    print("Training models...")
    models = {
        'lgb': lgb.LGBMClassifier(
            n_estimators=100, max_depth=8, learning_rate=0.1,
            random_state=42, verbose=-1, force_col_wise=True
        ),
        'xgb': xgb.XGBClassifier(
            n_estimators=100, max_depth=8, learning_rate=0.1,
            random_state=42, eval_metric='logloss', tree_method='hist'
        ),
        'catboost': CatBoostClassifier(
            iterations=100, depth=8, learning_rate=0.1,
            random_state=42, verbose=False, allow_writing_files=False
        ),
        'rf': RandomForestClassifier(
            n_estimators=100, max_depth=15, random_state=42,
            n_jobs=-1, max_features='sqrt'
        ),
        'dt': DecisionTreeClassifier(
            random_state=42, max_depth=15, min_samples_split=10,
            min_samples_leaf=5
        )
    }
    
    # Train models and extract feature importance
    trained_models = {}
    feature_importance = {}
    
    for name, model in models.items():
        print(f"- Training {name.upper()}")
        try:
            model.fit(X_train, y_train_split)
            trained_models[name] = model
            
            # Extract feature importance
            if hasattr(model, 'feature_importances_'):
                feature_importance[name] = model.feature_importances_
            elif hasattr(model, 'coef_'):
                feature_importance[name] = np.abs(model.coef_).flatten()
            else:
                print(f"  No feature importance available for {name}")
                
        except Exception as e:
            print(f"  Training failed for {name}: {e}")
    
    # SHAP analysis with robust error handling
    print("SHAP analysis...")
    shap_values = {}
    
    # Use smaller sample for SHAP to avoid memory issues
    sample_size = min(100, len(X_test))
    X_sample = X_test.sample(n=sample_size, random_state=42)
    
    for name in ['lgb', 'xgb', 'rf']:
        if name in trained_models:
            print(f"- {name.upper()} SHAP")
            shap_importance = safe_shap_analysis(trained_models[name], X_sample, name)
            if shap_importance is not None:
                shap_values[name] = shap_importance
    
    # Feature selection and consensus analysis
    feature_names = X_filled.columns.tolist()
    top_k = 50
    all_top_features = {}
    
    # Extract top features from model importance
    print("Extracting top features...")
    for model_name, importance in feature_importance.items():
        top_features = extract_top_features(importance, feature_names, top_k)
        if top_features:
            all_top_features[f'{model_name}_imp'] = top_features
    
    # Extract top features from SHAP importance
    for model_name, shap_imp in shap_values.items():
        top_features = extract_top_features(shap_imp, feature_names, top_k)
        if top_features:
            all_top_features[f'{model_name}_shap'] = top_features
    
    # Compute consensus ranking
    consensus_features = compute_feature_consensus(all_top_features, feature_names)
    
    # Enhanced visualization
    plt.figure(figsize=(16, 10))
    
    # Subplot 1: Feature importance heatmap
    if feature_importance:
        plt.subplot(2, 2, 1)
        
        # Get top 25 features by consensus
        top_consensus_features = [f[0] for f in consensus_features[:25]]
        feature_idx_map = {fname: idx for idx, fname in enumerate(feature_names)}
        top_indices = [feature_idx_map[f] for f in top_consensus_features if f in feature_idx_map]
        
        if top_indices:
            importance_matrix = np.array([
                [imp[idx] for idx in top_indices] 
                for imp in feature_importance.values()
            ])
            
            im = plt.imshow(importance_matrix, aspect='auto', cmap='viridis', interpolation='nearest')
            plt.colorbar(im, label='Importance')
            plt.yticks(range(len(feature_importance)), list(feature_importance.keys()))
            plt.xticks(range(len(top_indices)), 
                      [top_consensus_features[i][:20] for i in range(len(top_indices))], 
                      rotation=45, ha='right')
            plt.title('Feature Importance Across Models')
    
    # Subplot 2: Consensus ranking
    plt.subplot(2, 2, 2)
    if consensus_features:
        top_20_consensus = consensus_features[:20]
        features, scores, counts = zip(*top_20_consensus)
        
        plt.barh(range(len(features)), counts, alpha=0.7)
        plt.yticks(range(len(features)), [f[:25] for f in features])
        plt.xlabel('Number of Methods')
        plt.title('Feature Selection Consensus')
        plt.gca().invert_yaxis()
    
    # Subplot 3: SHAP importance (if available)
    if shap_values:
        plt.subplot(2, 2, 3)
        shap_data = list(shap_values.items())
        if shap_data:
            model_name, shap_imp = shap_data[0]  # Use first available SHAP result
            top_shap_idx = np.argsort(shap_imp)[-15:][::-1]
            
            plt.barh(range(15), [shap_imp[i] for i in top_shap_idx])
            plt.yticks(range(15), [feature_names[i][:25] for i in top_shap_idx])
            plt.xlabel('Mean |SHAP Value|')
            plt.title(f'SHAP Importance ({model_name.upper()})')
            plt.gca().invert_yaxis()
    
    # Subplot 4: Method agreement analysis
    plt.subplot(2, 2, 4)
    if len(all_top_features) > 1:
        method_names = list(all_top_features.keys())
        agreement_matrix = np.zeros((len(method_names), len(method_names)))
        
        for i, method1 in enumerate(method_names):
            for j, method2 in enumerate(method_names):
                if i != j:
                    set1 = set(all_top_features[method1][:20])
                    set2 = set(all_top_features[method2][:20])
                    agreement = len(set1.intersection(set2)) / 20.0
                    agreement_matrix[i, j] = agreement
                else:
                    agreement_matrix[i, j] = 1.0
        
        im = plt.imshow(agreement_matrix, cmap='RdYlBu_r', vmin=0, vmax=1)
        plt.colorbar(im, label='Agreement Ratio')
        plt.xticks(range(len(method_names)), method_names, rotation=45, ha='right')
        plt.yticks(range(len(method_names)), method_names)
        plt.title('Method Agreement (Top 20 Features)')
    
    plt.tight_layout()
    plt.savefig('enhanced_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Comprehensive results structure
    results = {
        'feature_importance': feature_importance,
        'shap_values': shap_values,
        'consensus_features': consensus_features,
        'all_top_features': all_top_features,
        'feature_names': feature_names,
        'model_performance': {name: getattr(model, 'score', lambda x, y: None)(X_test, y_test) 
                             for name, model in trained_models.items()},
        'metadata': {
            'total_features': len(feature_names),
            'sample_size': len(X_filled),
            'models_trained': list(trained_models.keys()),
            'shap_models': list(shap_values.keys())
        }
    }
    
    # Save comprehensive results
    joblib.dump(results, 'comprehensive_feature_analysis_results.joblib')
    
    # Enhanced reporting
    print("\n" + "="*80)
    print("COMPREHENSIVE FEATURE IMPORTANCE ANALYSIS")
    print("="*80)
    
    print(f"\nDataset Statistics:")
    print(f"  Total samples: {len(X_filled):,}")
    print(f"  Total features: {len(feature_names):,}")
    print(f"  Models successfully trained: {len(trained_models)}")
    print(f"  SHAP analyses completed: {len(shap_values)}")
    
    if consensus_features:
        print(f"\nTop 15 Consensus Features (ranked by method agreement):")
        for i, (feature, score, count) in enumerate(consensus_features[:15]):
            print(f"{i+1:2d}. {feature[:65]:<65} (Score: {score:.3f}, Methods: {count})")
    
    # Model agreement analysis
    print(f"\nMethod Agreement Analysis (Top 20 features overlap):")
    for method1 in all_top_features:
        for method2 in all_top_features:
            if method1 < method2:  # Avoid duplicates
                set1 = set(all_top_features[method1][:20])
                set2 = set(all_top_features[method2][:20])
                overlap = len(set1.intersection(set2))
                print(f"  {method1} vs {method2}: {overlap}/20 ({overlap/20*100:.1f}%)")
    
    # Feature type analysis (based on naming patterns)
    print(f"\nFeature Category Analysis:")
    categories = {'tsfresh': 0, 'catch22': 0, 'other': 0}
    top_50_consensus = [f[0] for f in consensus_features[:50]]
    
    for feature in top_50_consensus:
        if 'tsfresh' in feature.lower() or any(ts_pattern in feature.lower() 
                                               for ts_pattern in ['mean', 'std', 'var', 'skew', 'kurt']):
            categories['tsfresh'] += 1
        elif 'catch22' in feature.lower() or 'co_' in feature.lower() or 'dn_' in feature.lower():
            categories['catch22'] += 1
        else:
            categories['other'] += 1
    
    for category, count in categories.items():
        print(f"  {category.upper()}: {count}/50 ({count/50*100:.1f}%)")
    
    print(f"\nResults saved to: comprehensive_feature_analysis_results.joblib")
    print(f"Visualization saved to: enhanced_feature_analysis.png")

if __name__ == "__main__":
    main()