import numpy as np
import pandas as pd
from scipy.signal import welch, butter, filtfilt, hilbert, order_filter, medfilt, wiener
from scipy.stats import pearsonr
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import cross_val_score
import xgboost as xgb
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

def design_bandpass_filter(center_freq, bandwidth_ratio=0.2, fs=500, order=4):
    """Design bandpass filter around center frequency."""
    bandwidth = center_freq * bandwidth_ratio
    low_freq = max(0.01, center_freq - bandwidth/2)
    high_freq = min(fs/2 - 0.01, center_freq + bandwidth/2)
    
    if low_freq >= high_freq:
        return None, None
    
    try:
        b, a = butter(order, [low_freq, high_freq], btype='band', fs=fs)
        return b, a
    except Exception:
        return None, None

def apply_order_filter_smoothing(signal, filter_type='median', window_size=5):
    """Apply order filter smoothing to signal."""
    try:
        if filter_type == 'median':
            return medfilt(signal, kernel_size=window_size)
        elif filter_type == 'min':
            domain = np.ones(window_size)
            return order_filter(signal, domain, 0)  # Min filter
        elif filter_type == 'max':
            domain = np.ones(window_size)
            return order_filter(signal, domain, window_size-1)  # Max filter
        elif filter_type == 'percentile_25':
            domain = np.ones(window_size)
            return order_filter(signal, domain, window_size//4)
        elif filter_type == 'percentile_75':
            domain = np.ones(window_size)
            return order_filter(signal, domain, 3*window_size//4)
        elif filter_type == 'wiener':
            return wiener(signal, mysize=window_size)
        else:
            return signal
    except Exception:
        return signal

def extract_order_filter_features(series, tstar, fs=500, bandwidth_ratio=0.2, 
                                 filter_type='median', window_size=5):
    """Extract features using order filter smoothing + frequency filtering."""
    features = {}
    
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 20 or len(post_segment) < 20:
        return {f'order_filter_{k}': np.nan for k in range(25)}
    
        # 1. Apply order filter smoothing to both segments
    pre_smoothed = apply_order_filter_smoothing(pre_segment, filter_type, window_size)
    post_smoothed = apply_order_filter_smoothing(post_segment, filter_type, window_size)
    
    # 2. Find dominant frequency in smoothed pre-break segment
    f_pre, psd_pre = welch(pre_smoothed, fs=fs, nperseg=64)
    dominant_freq_pre = f_pre[np.argmax(psd_pre)]
    
    # 3. Design bandpass filter around smoothed pre-break dominant frequency
    b, a = design_bandpass_filter(dominant_freq_pre, bandwidth_ratio, fs)
    if b is None:
        return {f'order_filter_{k}': np.nan for k in range(25)}
    
    # 4. Filter smoothed post-break segment
    post_filtered = filtfilt(b, a, post_smoothed)
    post_residual = post_smoothed - post_filtered
    
    # 5. SMOOTHING EFFECTIVENESS FEATURES
    # Compare original vs smoothed signals
    pre_smooth_diff = np.var(pre_segment - pre_smoothed)
    post_smooth_diff = np.var(post_segment - post_smoothed)
    
    features['pre_smoothing_effect'] = pre_smooth_diff / np.var(pre_segment) if np.var(pre_segment) > 0 else np.nan
    features['post_smoothing_effect'] = post_smooth_diff / np.var(post_segment) if np.var(post_segment) > 0 else np.nan
    features['smoothing_effect_ratio'] = features['post_smoothing_effect'] / features['pre_smoothing_effect'] if features['pre_smoothing_effect'] > 0 else np.nan
    
    # 6. SIGNAL PRESERVATION FEATURES (on smoothed signals)
    power_original = np.var(post_smoothed)
    power_filtered = np.var(post_filtered)
    power_residual = np.var(post_residual)
    
    features['preservation_ratio'] = power_filtered / power_original if power_original > 0 else np.nan
    features['noise_ratio'] = power_residual / power_original if power_original > 0 else np.nan
    features['signal_loss'] = 1 - features['preservation_ratio'] if not np.isnan(features['preservation_ratio']) else np.nan
    
    # 7. FREQUENCY SHIFT DETECTION (smoothed signals)
    f_post, psd_post = welch(post_smoothed, fs=fs, nperseg=64)
    dominant_freq_post = f_post[np.argmax(psd_post)]
    
    features['frequency_drift'] = abs(dominant_freq_post - dominant_freq_pre)
    features['frequency_drift_rel'] = abs(dominant_freq_post - dominant_freq_pre) / dominant_freq_pre if dominant_freq_pre > 0 else np.nan
    
    # 8. SMOOTHED vs ORIGINAL FREQUENCY COMPARISON
    # Compare dominant frequencies before and after smoothing
    f_pre_orig, psd_pre_orig = welch(pre_segment, fs=fs, nperseg=64)
    f_post_orig, psd_post_orig = welch(post_segment, fs=fs, nperseg=64)
    
    dominant_freq_pre_orig = f_pre_orig[np.argmax(psd_pre_orig)]
    dominant_freq_post_orig = f_post_orig[np.argmax(psd_post_orig)]
    
    features['pre_freq_smooth_shift'] = abs(dominant_freq_pre - dominant_freq_pre_orig)
    features['post_freq_smooth_shift'] = abs(dominant_freq_post - dominant_freq_post_orig)
    features['freq_smooth_shift_ratio'] = features['post_freq_smooth_shift'] / features['pre_freq_smooth_shift'] if features['pre_freq_smooth_shift'] > 0 else np.nan
    
    # 9. ENVELOPE FEATURES (smoothed signals)
    envelope_pre = np.abs(hilbert(pre_smoothed))
    envelope_post = np.abs(hilbert(post_smoothed))
    
    features['envelope_mean_ratio'] = np.mean(envelope_post) / np.mean(envelope_pre) if np.mean(envelope_pre) > 0 else np.nan
    features['envelope_std_ratio'] = np.std(envelope_post) / np.std(envelope_pre) if np.std(envelope_pre) > 0 else np.nan
    
    # 10. AUTOCORRELATION FEATURES (smoothed signals)
    def autocorr_at_lag(x, lag=1):
        if len(x) <= lag:
            return np.nan
        return np.corrcoef(x[:-lag], x[lag:])[0, 1] if len(x) > lag else np.nan
    
    ac_pre_smooth = autocorr_at_lag(pre_smoothed, lag=1)
    ac_post_smooth = autocorr_at_lag(post_smoothed, lag=1)
    ac_pre_orig = autocorr_at_lag(pre_segment, lag=1)
    ac_post_orig = autocorr_at_lag(post_segment, lag=1)
    
    features['autocorr_change_smooth'] = abs(ac_post_smooth - ac_pre_smooth) if not (np.isnan(ac_pre_smooth) or np.isnan(ac_post_smooth)) else np.nan
    features['autocorr_change_orig'] = abs(ac_post_orig - ac_pre_orig) if not (np.isnan(ac_pre_orig) or np.isnan(ac_post_orig)) else np.nan
    features['autocorr_improvement'] = features['autocorr_change_smooth'] / features['autocorr_change_orig'] if features['autocorr_change_orig'] > 0 else np.nan
    
    # 11. RESIDUAL ANALYSIS (what order filter removed)
    pre_noise = pre_segment - pre_smoothed
    post_noise = post_segment - post_smoothed
    
    features['noise_power_ratio'] = np.var(post_noise) / np.var(pre_noise) if np.var(pre_noise) > 0 else np.nan
    features['noise_mean_ratio'] = np.mean(np.abs(post_noise)) / np.mean(np.abs(pre_noise)) if np.mean(np.abs(pre_noise)) > 0 else np.nan
    
    # 12. SPECTRAL COHERENCE (smoothed vs original)
    min_len = min(len(pre_segment), len(pre_smoothed))
    if min_len > 10:
        features['pre_coherence'], _ = pearsonr(pre_segment[:min_len], pre_smoothed[:min_len])
        features['post_coherence'], _ = pearsonr(post_segment[:min_len], post_smoothed[:min_len])
        features['coherence_change'] = abs(features['post_coherence'] - features['pre_coherence'])
    else:
        features['pre_coherence'] = np.nan
        features['post_coherence'] = np.nan
        features['coherence_change'] = np.nan
    
    # 13. FILTER-SPECIFIC FEATURES
    features['filter_window_size'] = window_size
    features['dominant_freq_pre'] = dominant_freq_pre
    features['dominant_freq_post'] = dominant_freq_post
        
    
    return features

def evaluate_order_filter_approach(all_series, all_tstars, all_labels, 
                                  filter_types=['median', 'min', 'max', 'wiener'],
                                  window_sizes=[3, 5, 7, 9],
                                  bandwidth_ratios=[0.2]):
    """Evaluate order filter + frequency filtering approach."""
    results = {}
    
    print("Evaluating order filter + frequency filtering approach...")
    
    for filter_type in filter_types:
        for window_size in window_sizes:
            for bandwidth_ratio in bandwidth_ratios:
                config_name = f"{filter_type}_w{window_size}_b{bandwidth_ratio}"
                print(f"\nTesting: {config_name}")
                
                # Extract features
                feature_list = []
                for series, tstar in zip(all_series, all_tstars):
                    features = extract_order_filter_features(
                        series, tstar, bandwidth_ratio=bandwidth_ratio,
                        filter_type=filter_type, window_size=window_size
                    )
                    feature_list.append(features)
                
                feature_df = pd.DataFrame(feature_list)
                feature_df = feature_df.fillna(feature_df.median())
                
                if feature_df.shape[1] == 0:
                    continue
                
                try:
                    model = xgb.XGBClassifier(
                        n_estimators=100, random_state=42, 
                        tree_method='gpu_hist', gpu_id=0, eval_metric='auc'
                    )
                    cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
                    auc_score = np.mean(cv_scores)
                    auc_std = np.std(cv_scores)
                    
                    results[config_name] = {
                        'auc_mean': auc_score,
                        'auc_std': auc_std,
                        'n_features': feature_df.shape[1],
                        'filter_type': filter_type,
                        'window_size': window_size,
                        'bandwidth_ratio': bandwidth_ratio
                    }
                    
                    print(f"AUC: {auc_score:.4f} ± {auc_std:.4f} ({feature_df.shape[1]} features)")
                    
                    # Feature importance for best result so far
                    if auc_score == max([r.get('auc_mean', 0) for r in results.values()]):
                        model.fit(feature_df, all_labels)
                        feature_importance = pd.DataFrame({
                            'feature': feature_df.columns,
                            'importance': model.feature_importances_
                        }).sort_values('importance', ascending=False)
                        
                        print("Top 5 features:")
                        for _, row in feature_importance.head().iterrows():
                            print(f"  {row['feature']}: {row['importance']:.4f}")
                    
                except Exception as e:
                    print(f"Error: {e}")
                    results[config_name] = {'auc_mean': np.nan, 'auc_std': np.nan, 'n_features': 0}
    
    return results

def load_data(data_path):
    """Load time series data from parquet files."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series, all_tstars, all_labels = [], [], []
        
        for id_, group in X_train.groupby(level='id'):
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(int(y_train.loc[id_]))
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    parser = argparse.ArgumentParser(description='Order Filter + Frequency Filter Probing v2')
    parser.add_argument('--data_path', type=str, default='.', help='Path to data directory')
    parser.add_argument('--filter_types', nargs='+', default=['median', 'wiener'], 
                       help='Order filter types to test')
    parser.add_argument('--window_sizes', nargs='+', type=int, default=[3, 5, 7],
                       help='Window sizes to test')
    parser.add_argument('--bandwidth_ratios', nargs='+', type=float, default=[0.2],
                       help='Bandwidth ratios to test')
    
    args = parser.parse_args()
    
    print("Order Filter + Frequency Filter Probing v2")
    print("="*50)
    print(f"Filter types: {args.filter_types}")
    print(f"Window sizes: {args.window_sizes}")
    print(f"Bandwidth ratios: {args.bandwidth_ratios}")
    
    # Load data
    all_series, all_tstars, all_labels = load_data(args.data_path)
    if all_series is None:
        sys.exit(1)
    
    print(f"Loaded {len(all_series)} time series")
    
    # Run analysis
    results = evaluate_order_filter_approach(
        all_series, all_tstars, all_labels,
        filter_types=args.filter_types,
        window_sizes=args.window_sizes,
        bandwidth_ratios=args.bandwidth_ratios
    )
    
    print("\n" + "="*50)
    print("SUMMARY RESULTS")
    print("="*50)
    
    # Sort results by AUC
    sorted_results = sorted(results.items(), key=lambda x: x[1].get('auc_mean', 0), reverse=True)
    
    for config_name, result in sorted_results[:10]:  # Top 10
        if not np.isnan(result['auc_mean']):
            print(f"{config_name}: AUC = {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
    
    if sorted_results:
        best_config, best_result = sorted_results[0]
        print(f"\n🏆 Best: {best_config} (AUC = {best_result['auc_mean']:.4f})")

if __name__ == "__main__":
    main()