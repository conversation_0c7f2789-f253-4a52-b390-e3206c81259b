#!/usr/bin/env python3
"""
Granite-TSFM Foundation Models for Structural Break Detection
Clean implementation using IBM's Time Series Foundation Models
"""

import numpy as np
import pandas as pd
import sys
import os
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
from sklearn.linear_model import LogisticRegression
from sklearn.preprocessing import StandardScaler
from scipy.stats import skew, kurtosis
import logging
import warnings
import joblib

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add granite-tsfm to path
granite_path = os.path.join(os.getcwd(), 'granite-tsfm')
if os.path.exists(granite_path):
    sys.path.append(os.path.join(granite_path, 'tsfm_public'))

# Import Granite-TSFM components
try:
    from tsfm_public.models.tspulse import TSPulseForReconstruction, TSPulseForClassification
    import torch
    GRANITE_AVAILABLE = True
    logger.info("✅ Granite-TSFM modules imported successfully")
except ImportError as e:
    GRANITE_AVAILABLE = False
    logger.error(f"❌ Granite-TSFM not available: {e}")
    logger.info("💡 Install with: cd granite-tsfm && pip install '.[notebooks]'")
    sys.exit(1)

def load_raw_time_series():
    """Load raw time series data and labels"""
    logger.info("📊 Loading raw time series data...")
    
    try:
        # Load the original time series data (not engineered features)
        X = pd.read_parquet('X_train.parquet')
        y = pd.read_parquet('y_train.parquet')
        
        if isinstance(y.index, pd.MultiIndex):
            y = y.reset_index(drop=True).iloc[:, 0] if y.shape[1] == 1 else y.reset_index(drop=True)
        else:
            y = y.iloc[:, 0] if y.shape[1] == 1 else y.squeeze()
        
        min_len = min(len(X), len(y))
        X = X.iloc[:min_len]
        y = y.iloc[:min_len] if hasattr(y, 'iloc') else y[:min_len]
        
        logger.info(f"   ✅ Raw time series: {X.shape[0]} samples, {X.shape[1]} time points")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    except Exception as e:
        logger.error(f"❌ Error loading raw time series data: {e}")
        raise

class GraniteStructuralBreakDetector:
    """Granite-TSFM foundation model for structural break detection"""
    
    def __init__(self, model_type='tspulse', device='cpu'):
        self.model_type = model_type
        self.device = device
        self.anomaly_model = None
        self.classification_model = None
        self.scaler = StandardScaler()
        
    def initialize_models(self):
        """Initialize Granite-TSFM models"""
        logger.info("🔧 Initializing Granite-TSFM models...")
        
        try:
            # TSPulse for anomaly detection (structural breaks)
            logger.info("   📡 Loading TSPulse anomaly detection model...")
            self.anomaly_model = TSPulseForReconstruction.from_pretrained(
                "ibm-granite/granite-timeseries-tspulse-r1",
                revision="main"  # For anomaly detection
            )
            
            # TSPulse for classification
            logger.info("   🔄 Loading TSPulse classification model...")
            self.classification_model = TSPulseForClassification.from_pretrained(
                "ibm-granite/granite-timeseries-tspulse-r1",
                revision="tspulse-block-dualhead-512-p16-r1"  # For classification
            )
            
            logger.info("   ✅ Granite-TSFM models initialized successfully")
            
        except Exception as e:
            logger.warning(f"⚠️ Failed to initialize Granite models: {e}")
            logger.info("   📊 Will use basic statistical features instead")
            self.anomaly_model = None
            self.classification_model = None
    
    def extract_tspulse_features(self, X):
        """Extract anomaly-based features using TSPulse"""
        logger.info("🔍 Extracting TSPulse anomaly features...")
        
        if self.anomaly_model is None:
            logger.warning("   ⚠️ TSPulse model not available, using basic features")
            return self._extract_basic_features(X)
        
        features = []
        
        for i in range(min(len(X), 500)):  # Limit for speed
            try:
                # Convert row to time series format (512 context length minimum)
                ts_data = X.iloc[i].values
                
                # TSPulse expects exactly 512 sequence length
                if len(ts_data) > 512:
                    # Downsample to 512 points
                    indices = np.linspace(0, len(ts_data) - 1, 512, dtype=int)
                    ts_data = ts_data[indices]
                elif len(ts_data) < 512:
                    # Pad to 512 points
                    ts_data = np.pad(ts_data, (0, 512 - len(ts_data)), mode='edge')
                
                # Reshape for TSPulse input: (batch_size, sequence_length, num_features)
                ts_input = torch.tensor(ts_data.reshape(1, 512, 1), dtype=torch.float32)
                
                # Get TSPulse reconstruction for anomaly detection
                with torch.no_grad():
                    outputs = self.anomaly_model(ts_input)
                    
                    # Calculate reconstruction error (anomaly scores)
                    if hasattr(outputs, 'reconstruction'):
                        reconstruction = outputs.reconstruction.squeeze().numpy()
                        original = ts_input.squeeze().numpy()
                        anomaly_scores = np.abs(original - reconstruction)
                    elif hasattr(outputs, 'logits'):
                        anomaly_scores = outputs.logits.squeeze().numpy()
                    else:
                        anomaly_scores = outputs.squeeze().numpy() if hasattr(outputs, 'numpy') else np.array([0.5])
                
                # Extract statistical features from anomaly scores
                feature_vector = self._extract_anomaly_statistics(anomaly_scores)
                features.append(feature_vector)
                
            except Exception as e:
                logger.warning(f"   ⚠️ TSPulse failed for sample {i}: {e}")
                # Fallback features
                features.append(np.zeros(20))
        
        # Fill remaining samples with basic features if we limited processing
        if len(features) < len(X):
            logger.info(f"   📊 Processing remaining {len(X) - len(features)} samples with basic features...")
            remaining_features = self._extract_basic_features(X.iloc[len(features):])
            # Pad basic features to match TSPulse feature size
            padded_remaining = np.pad(remaining_features, ((0, 0), (0, 20 - remaining_features.shape[1])), mode='constant')
            features.extend(padded_remaining.tolist())
        
        features_array = np.array(features)
        logger.info(f"   ✅ Extracted {features_array.shape[1]} TSPulse features")
        return features_array
    
    def extract_classification_features(self, X):
        """Extract features using time series classification preprocessor"""
        logger.info("🔄 Extracting classification features...")
        
        try:
            # Transform data using classification preprocessor
            processed_data = self.classification_preprocessor.transform(X.values)
            
            # Extract statistical features from processed sequences
            features = []
            for sequence in processed_data:
                feature_vector = self._extract_sequence_statistics(sequence)
                features.append(feature_vector)
            
            features_array = np.array(features)
            logger.info(f"   ✅ Extracted {features_array.shape[1]} classification features")
            return features_array
            
        except Exception as e:
            logger.warning(f"   ⚠️ Classification feature extraction failed: {e}")
            # Fallback: basic statistical features
            return self._extract_basic_features(X)
    
    def _extract_anomaly_statistics(self, anomaly_scores):
        """Extract comprehensive statistics from anomaly scores"""
        if len(anomaly_scores) == 0:
            return np.zeros(20)
        
        scores = np.array(anomaly_scores)
        
        return [
            # Basic statistics
            np.mean(scores),
            np.std(scores),
            np.max(scores),
            np.min(scores),
            np.median(scores),
            
            # Percentiles
            np.percentile(scores, 75),
            np.percentile(scores, 25),
            np.percentile(scores, 90),
            np.percentile(scores, 10),
            
            # Distribution properties
            np.var(scores),
            skew(scores) if len(scores) > 2 else 0,
            kurtosis(scores) if len(scores) > 3 else 0,
            
            # Anomaly counts
            np.sum(scores > np.mean(scores)),
            np.sum(scores > np.mean(scores) + np.std(scores)),
            np.sum(scores > np.mean(scores) + 2 * np.std(scores)),
            
            # Trend and change points
            np.sum(np.diff(scores) > 0),  # Increasing trend count
            np.sum(np.diff(scores) < 0),  # Decreasing trend count
            np.sum(np.abs(np.diff(scores)) > np.std(np.diff(scores))),  # Change points
            
            # Energy and entropy
            np.sum(scores ** 2),  # Energy
            -np.sum(scores * np.log(scores + 1e-10))  # Entropy-like measure
        ]
    
    def _extract_sequence_statistics(self, sequence):
        """Extract statistics from processed sequences"""
        if sequence.ndim > 1:
            sequence = sequence.flatten()
        
        return [
            np.mean(sequence),
            np.std(sequence),
            np.max(sequence),
            np.min(sequence),
            np.median(sequence),
            np.percentile(sequence, 75),
            np.percentile(sequence, 25),
            np.var(sequence),
            np.sum(sequence > np.mean(sequence)),
            len(sequence)
        ]
    
    def _extract_prediction_statistics(self, predictions):
        """Extract statistics from TTM predictions"""
        if len(predictions) == 0:
            return np.zeros(20)
        
        preds = np.array(predictions)
        
        return [
            # Basic statistics
            np.mean(preds),
            np.std(preds),
            np.max(preds),
            np.min(preds),
            np.median(preds),
            
            # Percentiles
            np.percentile(preds, 75),
            np.percentile(preds, 25),
            np.percentile(preds, 90),
            np.percentile(preds, 10),
            
            # Distribution properties
            np.var(preds),
            skew(preds) if len(preds) > 2 else 0,
            kurtosis(preds) if len(preds) > 3 else 0,
            
            # Prediction patterns
            np.sum(preds > np.mean(preds)),
            np.sum(preds > np.mean(preds) + np.std(preds)),
            np.sum(preds > np.mean(preds) + 2 * np.std(preds)),
            
            # Trend analysis
            np.sum(np.diff(preds) > 0) if len(preds) > 1 else 0,
            np.sum(np.diff(preds) < 0) if len(preds) > 1 else 0,
            np.sum(np.abs(np.diff(preds)) > np.std(np.diff(preds))) if len(preds) > 1 else 0,
            
            # Energy measures
            np.sum(preds ** 2),
            np.sum(np.abs(preds))
        ]
    
    def _extract_basic_features(self, X):
        """Fallback: extract basic statistical features"""
        logger.info("   📊 Using basic statistical features as fallback...")
        
        features = []
        for i in range(len(X)):
            row = X.iloc[i].values
            feature_vector = [
                np.mean(row),
                np.std(row),
                np.max(row),
                np.min(row),
                np.median(row),
                np.percentile(row, 75),
                np.percentile(row, 25),
                np.var(row),
                np.sum(row > np.mean(row)),
                len(row)
            ]
            features.append(feature_vector)
        
        return np.array(features)
    
    def fit_and_predict_cv(self, X, y, n_folds=5):
        """Fit and predict using cross-validation"""
        logger.info("🎯 Training Granite-TSFM with 5-fold CV...")
        
        # Extract features using TSPulse
        tspulse_features = self.extract_tspulse_features(X)
        
        # Use TSPulse features only (classification model may not work)
        combined_features = tspulse_features
        logger.info(f"   📊 Combined features shape: {combined_features.shape}")
        
        # Scale features
        combined_features = self.scaler.fit_transform(combined_features)
        
        # Cross-validation
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        oof_predictions = np.zeros(len(X))
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(combined_features, y)):
            logger.info(f"   Fold {fold + 1}/{n_folds}...")
            
            X_train, X_val = combined_features[train_idx], combined_features[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # Train logistic regression on Granite features
            model = LogisticRegression(
                random_state=42,
                max_iter=1000,
                class_weight='balanced'
            )
            model.fit(X_train, y_train)
            
            # Predict
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            oof_predictions[val_idx] = y_pred_proba
            
            # Calculate AUC
            auc = roc_auc_score(y_val, y_pred_proba)
            cv_scores.append(auc)
            logger.info(f"     Fold {fold + 1} AUC: {auc:.4f}")
        
        # Overall performance
        overall_auc = roc_auc_score(y, oof_predictions)
        mean_cv_auc = np.mean(cv_scores)
        std_cv_auc = np.std(cv_scores)
        
        logger.info(f"\n📊 Cross-Validation Results:")
        logger.info(f"   Mean CV AUC: {mean_cv_auc:.4f} ± {std_cv_auc:.4f}")
        logger.info(f"   Overall AUC: {overall_auc:.4f}")
        
        return {
            'oof_predictions': oof_predictions,
            'cv_scores': cv_scores,
            'mean_cv_auc': mean_cv_auc,
            'overall_auc': overall_auc,
            'combined_features': combined_features
        }

def calculate_comprehensive_metrics(y_true, y_pred_proba, threshold=0.5):
    """Calculate comprehensive classification metrics"""
    y_pred_binary = (y_pred_proba >= threshold).astype(int)
    
    return {
        'auc': roc_auc_score(y_true, y_pred_proba),
        'f1': f1_score(y_true, y_pred_binary),
        'precision': precision_score(y_true, y_pred_binary, zero_division=0),
        'recall': recall_score(y_true, y_pred_binary),
        'accuracy': accuracy_score(y_true, y_pred_binary)
    }

# Main execution
if __name__ == "__main__":
    logger.info("🚀 Starting Granite-TSFM Structural Break Detection...")
    
    # Load data
    X, y = load_raw_time_series()
    
    # Initialize Granite detector
    detector = GraniteStructuralBreakDetector(device='cpu')
    detector.initialize_models()
    
    # Train and evaluate
    results = detector.fit_and_predict_cv(X, y, n_folds=5)
    
    # Calculate comprehensive metrics
    metrics = calculate_comprehensive_metrics(y, results['oof_predictions'])
    
    # Print results
    logger.info("\n🎉 GRANITE-TSFM STRUCTURAL BREAK DETECTION COMPLETED!")
    logger.info("=" * 60)
    logger.info("📊 FINAL RESULTS:")
    logger.info(f"   AUC:        {metrics['auc']:.4f}")
    logger.info(f"   F1 Score:   {metrics['f1']:.4f}")
    logger.info(f"   Precision:  {metrics['precision']:.4f}")
    logger.info(f"   Recall:     {metrics['recall']:.4f}")
    logger.info(f"   Accuracy:   {metrics['accuracy']:.4f}")
    
    # Save results
    final_results = {
        'cv_results': results,
        'metrics': metrics,
        'feature_shape': results['combined_features'].shape
    }
    
    joblib.dump(final_results, 'granite_tsfm_results.joblib')
    logger.info("💾 Results saved to 'granite_tsfm_results.joblib'")