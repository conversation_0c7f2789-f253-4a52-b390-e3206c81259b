"""
Rolling Fractal Ratio Analysis for Full Dataset
Measures rolling fractal ratios of label outcomes across multiple scales
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import json

def analyze_rolling_fractal_ratios():
    """Measure rolling fractal ratios of label outcomes for full dataset"""
    print("🔍 ANALYZING ROLLING FRACTAL RATIOS")
    print("="*50)
    
    # Load full dataset
    try:
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        if 'structural_breakpoint' in y_data.columns:
            labels = y_data['structural_breakpoint'].astype(int).values
        else:
            labels = y_data.iloc[:, 0].astype(int).values
        
        print(f"📊 FULL DATASET LOADED:")
        print(f"   - Total samples: {len(labels):,}")
        print(f"   - Overall ratio: {np.mean(labels):.4f}")
        print(f"   - Label distribution: {dict(Counter(labels))}")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None
    
    # Define rolling window sizes for fractal analysis
    window_sizes = [50, 100, 200, 500, 1000, 2000, 5000]
    step_sizes = [10, 25, 50, 100, 200, 500, 1000]  # Corresponding step sizes
    
    print(f"\n📈 ROLLING FRACTAL ANALYSIS:")
    print(f"   - Window sizes: {window_sizes}")
    print(f"   - Step sizes: {step_sizes}")
    
    rolling_results = {}
    
    for window_size, step_size in zip(window_sizes, step_sizes):
        print(f"\n🔍 Window size: {window_size}, Step: {step_size}")
        
        if window_size > len(labels):
            print(f"   ⚠️  Window too large, skipping")
            continue
        
        # Calculate rolling ratios
        positions = []
        ratios = []
        
        for start in range(0, len(labels) - window_size + 1, step_size):
            window = labels[start:start + window_size]
            ratio = np.mean(window)
            
            positions.append(start + window_size // 2)  # Center position
            ratios.append(ratio)
        
        ratios = np.array(ratios)
        positions = np.array(positions)
        
        # Calculate fractal statistics
        mean_ratio = np.mean(ratios)
        std_ratio = np.std(ratios)
        min_ratio = np.min(ratios)
        max_ratio = np.max(ratios)
        
        # Fractal stability metrics
        overall_ratio = np.mean(labels)
        deviations = np.abs(ratios - overall_ratio)
        stable_windows = np.sum(deviations < 0.05)  # Within 5%
        stability_score = stable_windows / len(ratios)
        
        # Trend analysis
        if len(ratios) > 1:
            trend_correlation = np.corrcoef(positions, ratios)[0, 1]
        else:
            trend_correlation = 0.0
        
        # Fractal dimension approximation (Hurst exponent)
        hurst_exponent = calculate_hurst_exponent(ratios)
        
        # Store results
        rolling_results[window_size] = {
            'window_size': window_size,
            'step_size': step_size,
            'n_windows': len(ratios),
            'positions': positions.tolist(),
            'ratios': ratios.tolist(),
            'statistics': {
                'mean_ratio': mean_ratio,
                'std_ratio': std_ratio,
                'min_ratio': min_ratio,
                'max_ratio': max_ratio,
                'coefficient_variation': std_ratio / mean_ratio if mean_ratio > 0 else 0,
                'stability_score': stability_score,
                'trend_correlation': trend_correlation,
                'hurst_exponent': hurst_exponent
            }
        }
        
        print(f"   📊 Results:")
        print(f"      - Windows: {len(ratios)}")
        print(f"      - Mean ratio: {mean_ratio:.4f} ± {std_ratio:.4f}")
        print(f"      - Range: [{min_ratio:.4f}, {max_ratio:.4f}]")
        print(f"      - Stability: {stability_score:.4f} ({stable_windows}/{len(ratios)} stable)")
        print(f"      - Trend correlation: {trend_correlation:.4f}")
        print(f"      - Hurst exponent: {hurst_exponent:.4f}")
    
    # Analyze fractal properties across scales
    print(f"\n🔬 FRACTAL PROPERTIES ANALYSIS:")
    
    # Scale invariance
    scales = list(rolling_results.keys())
    stds = [rolling_results[s]['statistics']['std_ratio'] for s in scales]
    stability_scores = [rolling_results[s]['statistics']['stability_score'] for s in scales]
    hurst_exponents = [rolling_results[s]['statistics']['hurst_exponent'] for s in scales]
    
    # Scale-std relationship (should be power law for fractals)
    scale_exponent = None
    if len(scales) > 2:
        log_scales = np.log(scales)
        log_stds = np.log(stds)
        scale_exponent = np.polyfit(log_scales, log_stds, 1)[0]
        
        print(f"   📏 Scale Invariance:")
        print(f"      - Scale exponent: {scale_exponent:.4f}")
        if -0.7 < scale_exponent < -0.3:
            print(f"      - ✅ FRACTAL behavior detected (expected: -0.5)")
        else:
            print(f"      - ❌ Non-fractal behavior")
    
    # Self-similarity
    mean_stability = np.mean(stability_scores)
    print(f"\n   🔄 Self-Similarity:")
    print(f"      - Average stability: {mean_stability:.4f}")
    if mean_stability > 0.8:
        print(f"      - ✅ HIGH self-similarity")
    elif mean_stability > 0.6:
        print(f"      - ⚠️  MODERATE self-similarity")
    else:
        print(f"      - ❌ LOW self-similarity")
    
    # Long-range dependence (Hurst exponent)
    mean_hurst = np.mean(hurst_exponents)
    print(f"\n   📈 Long-Range Dependence:")
    print(f"      - Average Hurst exponent: {mean_hurst:.4f}")
    if mean_hurst > 0.6:
        print(f"      - ✅ PERSISTENT long-range dependence")
    elif mean_hurst < 0.4:
        print(f"      - 🔄 ANTI-PERSISTENT behavior")
    else:
        print(f"      - ⚠️  RANDOM walk behavior")
    
    # Create comprehensive visualization
    plot_rolling_fractal_analysis(rolling_results, labels)
    
    # Calculate overall fractal score
    fractal_score = calculate_overall_fractal_score(rolling_results, mean_stability, mean_hurst)
    
    print(f"\n🎯 OVERALL FRACTAL ASSESSMENT:")
    print(f"   - Fractal Score: {fractal_score:.4f}")
    if fractal_score > 0.7:
        print(f"   - ✅ STRONG fractal properties")
    elif fractal_score > 0.5:
        print(f"   - ⚠️  MODERATE fractal properties")
    else:
        print(f"   - ❌ WEAK fractal properties")
    
    return {
        'rolling_results': rolling_results,
        'fractal_metrics': {
            'scale_exponent': scale_exponent,
            'mean_stability': mean_stability,
            'mean_hurst': mean_hurst,
            'fractal_score': fractal_score
        },
        'dataset_info': {
            'total_samples': len(labels),
            'overall_ratio': np.mean(labels),
            'label_distribution': dict(Counter(labels))
        }
    }

def calculate_hurst_exponent(time_series, max_lag=20):
    """Calculate Hurst exponent using R/S analysis"""
    try:
        if len(time_series) < max_lag * 2:
            return 0.5  # Default for insufficient data
        
        lags = range(2, min(max_lag, len(time_series) // 4))
        rs_values = []
        
        for lag in lags:
            # Divide series into non-overlapping blocks
            n_blocks = len(time_series) // lag
            if n_blocks < 2:
                continue
            
            rs_block = []
            for i in range(n_blocks):
                block = time_series[i*lag:(i+1)*lag]
                
                # Calculate mean
                mean_block = np.mean(block)
                
                # Calculate cumulative deviations
                deviations = np.cumsum(block - mean_block)
                
                # Calculate range
                R = np.max(deviations) - np.min(deviations)
                
                # Calculate standard deviation
                S = np.std(block)
                
                # R/S ratio
                if S > 0:
                    rs_block.append(R / S)
            
            if rs_block:
                rs_values.append(np.mean(rs_block))
        
        if len(rs_values) < 3:
            return 0.5
        
        # Fit log(R/S) vs log(lag)
        log_lags = np.log(lags[:len(rs_values)])
        log_rs = np.log(rs_values)
        
        # Hurst exponent is the slope
        hurst = np.polyfit(log_lags, log_rs, 1)[0]
        
        # Bound between 0 and 1
        return np.clip(hurst, 0.0, 1.0)
        
    except:
        return 0.5  # Default if calculation fails

def calculate_overall_fractal_score(rolling_results, mean_stability, mean_hurst):
    """Calculate overall fractal score"""
    
    # Component 1: Stability across scales (0-1)
    stability_component = mean_stability
    
    # Component 2: Hurst exponent deviation from 0.5 (0-1)
    hurst_component = 1.0 - 2 * abs(mean_hurst - 0.5)  # Closer to 0.5 = lower fractal
    hurst_component = max(0, hurst_component)
    
    # Component 3: Scale invariance (if available)
    if len(rolling_results) > 2:
        scales = list(rolling_results.keys())
        stds = [rolling_results[s]['statistics']['std_ratio'] for s in scales]
        
        # Check if std decreases with scale (fractal property)
        scale_correlation = np.corrcoef(scales, stds)[0, 1]
        scale_component = max(0, -scale_correlation)  # Negative correlation is good
    else:
        scale_component = 0.5
    
    # Weighted combination
    fractal_score = (0.4 * stability_component + 
                    0.3 * hurst_component + 
                    0.3 * scale_component)
    
    return fractal_score

def plot_rolling_fractal_analysis(rolling_results, labels):
    """Create comprehensive visualization of rolling fractal analysis"""
    
    print(f"\n📊 Creating Rolling Fractal Visualization...")
    
    fig, axes = plt.subplots(3, 2, figsize=(16, 12))
    fig.suptitle('Rolling Fractal Ratio Analysis - Full Dataset', fontsize=16)
    
    # 1. Rolling ratios for different scales
    for window_size, result in rolling_results.items():
        if len(result['positions']) > 1:  # Only plot if we have data
            positions = np.array(result['positions'])
            ratios = np.array(result['ratios'])
            
            # Subsample for visualization if too many points
            if len(positions) > 1000:
                indices = np.linspace(0, len(positions)-1, 1000, dtype=int)
                positions = positions[indices]
                ratios = ratios[indices]
            
            axes[0, 0].plot(positions, ratios, alpha=0.7, linewidth=1, 
                           label=f'Window {window_size}')
    
    overall_ratio = np.mean(labels)
    axes[0, 0].axhline(y=overall_ratio, color='red', linestyle='--', alpha=0.8, 
                      label=f'Overall: {overall_ratio:.3f}')
    axes[0, 0].set_xlabel('Position in Dataset')
    axes[0, 0].set_ylabel('Rolling Ratio')
    axes[0, 0].set_title('Rolling Ratios Across Scales')
    axes[0, 0].legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Stability scores by scale
    scales = list(rolling_results.keys())
    stability_scores = [rolling_results[s]['statistics']['stability_score'] for s in scales]
    
    axes[0, 1].semilogx(scales, stability_scores, 'o-', linewidth=2, markersize=8)
    axes[0, 1].set_xlabel('Window Size')
    axes[0, 1].set_ylabel('Stability Score')
    axes[0, 1].set_title('Stability Across Scales')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].axhline(y=0.8, color='green', linestyle='--', alpha=0.5, label='High Stability')
    axes[0, 1].legend()
    
    # 3. Standard deviation vs scale (fractal property)
    stds = [rolling_results[s]['statistics']['std_ratio'] for s in scales]
    
    axes[1, 0].loglog(scales, stds, 'o-', linewidth=2, markersize=8, color='purple')
    axes[1, 0].set_xlabel('Window Size (log)')
    axes[1, 0].set_ylabel('Std Deviation (log)')
    axes[1, 0].set_title('Scale vs Variability (Fractal Test)')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Add power law fit if enough points
    if len(scales) > 2:
        log_scales = np.log(scales)
        log_stds = np.log(stds)
        slope, intercept = np.polyfit(log_scales, log_stds, 1)
        
        fit_line = np.exp(intercept) * np.array(scales) ** slope
        axes[1, 0].plot(scales, fit_line, 'r--', alpha=0.7, 
                       label=f'Power law: slope={slope:.3f}')
        axes[1, 0].legend()
    
    # 4. Hurst exponents
    hurst_values = [rolling_results[s]['statistics']['hurst_exponent'] for s in scales]
    
    axes[1, 1].semilogx(scales, hurst_values, 'o-', linewidth=2, markersize=8, color='orange')
    axes[1, 1].axhline(y=0.5, color='gray', linestyle=':', alpha=0.5, label='Random (H=0.5)')
    axes[1, 1].axhline(y=0.6, color='green', linestyle='--', alpha=0.5, label='Persistent')
    axes[1, 1].axhline(y=0.4, color='red', linestyle='--', alpha=0.5, label='Anti-persistent')
    axes[1, 1].set_xlabel('Window Size')
    axes[1, 1].set_ylabel('Hurst Exponent')
    axes[1, 1].set_title('Long-Range Dependence')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    axes[1, 1].set_ylim(0, 1)
    
    # 5. Distribution of ratios for largest scale
    largest_scale = max(rolling_results.keys())
    largest_ratios = rolling_results[largest_scale]['ratios']
    
    axes[2, 0].hist(largest_ratios, bins=30, alpha=0.7, color='skyblue', density=True)
    axes[2, 0].axvline(x=overall_ratio, color='red', linestyle='--', alpha=0.8, 
                      label=f'Overall: {overall_ratio:.3f}')
    axes[2, 0].set_xlabel('Ratio Value')
    axes[2, 0].set_ylabel('Density')
    axes[2, 0].set_title(f'Ratio Distribution (Window={largest_scale})')
    axes[2, 0].legend()
    axes[2, 0].grid(True, alpha=0.3)
    
    # 6. Coefficient of variation vs scale
    cv_values = [rolling_results[s]['statistics']['coefficient_variation'] for s in scales]
    
    axes[2, 1].semilogx(scales, cv_values, 'o-', linewidth=2, markersize=8, color='brown')
    axes[2, 1].set_xlabel('Window Size')
    axes[2, 1].set_ylabel('Coefficient of Variation')
    axes[2, 1].set_title('Relative Variability Across Scales')
    axes[2, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('fragment_boundary_analysis.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visualization saved to 'fragment_boundary_analysis.png'")

def main():
    """Main analysis function"""
    
    print("🔬 Rolling Fractal Ratio Analysis")
    print("=" * 60)
    print("🎯 Measuring rolling fractal ratios across multiple scales")
    print("=" * 60)
    
    # Run rolling fractal analysis
    results = analyze_rolling_fractal_ratios()
    
    if results is not None:
        # Save results
        with open('fragment_boundary_analysis.json', 'w') as f:
            json.dump(results, f, indent=2)
        
        print(f"\n🎉 Rolling Fractal Analysis Complete!")
        print(f"   Fractal Score: {results['fractal_metrics']['fractal_score']:.4f}")
        print(f"   💾 Results saved to 'fragment_boundary_analysis.json'")
    
    return results

if __name__ == "__main__":
    results = main()