#!/usr/bin/env python3
"""
Example usage of Thermodynamics_truncated.py
"""

import numpy as np
from Thermodynamics_truncated import (
    AdaptiveTruncationDetector, 
    TruncationAwareThermodynamicAnalyzer,
    analyze_truncation_in_series_list
)

def example_1_basic_detection():
    """Example 1: Basic truncation detection"""
    print("🔍 Example 1: Basic Truncation Detection")
    print("-" * 50)
    
    # Create some test data
    np.random.seed(42)
    
    # Clean data
    clean_ts = np.random.normal(0, 1, 200)
    
    # Truncated data (clipped)
    truncated_ts = np.random.normal(0, 1, 200)
    truncated_ts = np.clip(truncated_ts, -0.5, 0.5)
    
    # Initialize detector
    detector = AdaptiveTruncationDetector()
    
    # Analyze both series
    clean_results = detector.compute_truncation_confidence(clean_ts)
    truncated_results = detector.compute_truncation_confidence(truncated_ts)
    
    print(f"Clean data confidence: {clean_results['truncation_confidence']:.3f}")
    print(f"Truncated data confidence: {truncated_results['truncation_confidence']:.3f}")
    print()

def example_2_advanced_features():
    """Example 2: Advanced features analysis"""
    print("🔬 Example 2: Advanced Features Analysis")
    print("-" * 50)
    
    np.random.seed(42)
    
    # Create quantized data
    raw_data = np.random.normal(0, 1, 200)
    quantized_ts = np.round(raw_data / 0.1) * 0.1  # Quantize to 0.1 steps
    
    detector = AdaptiveTruncationDetector()
    
    # Analyze with advanced features
    results = detector.compute_truncation_confidence(quantized_ts, include_advanced_features=True)
    
    print(f"Truncation Confidence: {results['truncation_confidence']:.3f}")
    print(f"Boundary Concentration: {results['boundary_concentration_ratio']:.3f}")
    print(f"Mean Precision: {results['mean_precision']:.3f}")
    print(f"Dominant Step Frequency: {results['dominant_step_frequency']:.3f}")
    print(f"Composite Truncation Score: {results['composite_truncation_score']:.3f}")
    print()

def example_3_thermodynamic_analysis():
    """Example 3: Full thermodynamic analysis"""
    print("🌡️  Example 3: Thermodynamic Analysis")
    print("-" * 50)
    
    np.random.seed(42)
    
    # Create time series with structural break
    pre_break = np.random.normal(0, 1, 100)
    post_break = np.random.normal(0.5, 1.5, 100)  # Different mean and variance
    full_series = np.concatenate([pre_break, post_break])
    
    # Initialize analyzer
    analyzer = TruncationAwareThermodynamicAnalyzer()
    
    # Analyze with structural break at position 100
    results = analyzer.analyze_time_series(full_series, tstar=100)
    
    print(f"Overall Truncation Confidence: {results['truncation_confidence']:.3f}")
    print(f"Quality-Weighted Total Energy: {results['quality_weighted_total_energy']:.3f}")
    print(f"Robust Potential Energy: {results['robust_potential_energy_harmonic']:.3f}")
    print(f"Robust Kinetic Energy: {results['robust_kinetic_energy_variance_based']:.3f}")
    print(f"Pre-break Confidence: {results['pre_truncation_confidence']:.3f}")
    print(f"Post-break Confidence: {results['post_truncation_confidence']:.3f}")
    print()

def example_4_batch_analysis():
    """Example 4: Batch analysis of multiple series"""
    print("📊 Example 4: Batch Analysis")
    print("-" * 50)
    
    np.random.seed(42)
    
    # Create mixed dataset
    series_list = []
    
    # Add 5 clean series
    for i in range(5):
        series_list.append(np.random.normal(0, 1, 150))
    
    # Add 3 truncated series
    for i in range(3):
        raw = np.random.normal(0, 1, 150)
        truncated = np.clip(raw, -0.3, 0.3)
        series_list.append(truncated)
    
    # Add 2 precision-limited series
    for i in range(2):
        raw = np.random.normal(0, 1, 150)
        precision_limited = np.round(raw, 1)
        series_list.append(precision_limited)
    
    # Analyze all series
    results = analyze_truncation_in_series_list(
        series_list=series_list,
        truncation_threshold=0.6,
        calibrate_detector=True,
        include_advanced_features=True,
        detailed_output=True
    )
    
    print(f"Found {len(results['truncated_indices'])} truncated series out of {len(series_list)}")
    print(f"Truncated indices: {results['truncated_indices']}")
    print()

def main():
    """Run all examples"""
    print("🚀 Thermodynamics_truncated.py Usage Examples")
    print("=" * 60)
    print()
    
    example_1_basic_detection()
    example_2_advanced_features()
    example_3_thermodynamic_analysis()
    example_4_batch_analysis()
    
    print("✅ All examples completed!")
    print("\nKey Features Available:")
    print("  • Adaptive truncation detection")
    print("  • Advanced feature analysis")
    print("  • Thermodynamic metrics calculation")
    print("  • Batch processing capabilities")
    print("  • Pre/post structural break analysis")

if __name__ == "__main__":
    main()