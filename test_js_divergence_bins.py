import numpy as np
import pandas as pd
from scipy.spatial.distance import jense<PERSON>hannon
from sklearn.model_selection import cross_val_score
import xgboost as xgb
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def jensen_shannon_divergence(pre_segment, post_segment, bins=10):
    """Calculate Jensen-Shannon divergence between pre and post segments."""
    try:
        # Create histograms
        combined_range = (min(np.min(pre_segment), np.min(post_segment)), 
                         max(np.max(pre_segment), np.max(post_segment)))
        
        hist_pre, _ = np.histogram(pre_segment, bins=bins, range=combined_range, density=True)
        hist_post, _ = np.histogram(post_segment, bins=bins, range=combined_range, density=True)
        
        # Normalize to probability distributions
        hist_pre = hist_pre / np.sum(hist_pre) if np.sum(hist_pre) > 0 else hist_pre
        hist_post = hist_post / np.sum(hist_post) if np.sum(hist_post) > 0 else hist_post
        
        # Add small epsilon to avoid log(0)
        eps = 1e-10
        hist_pre = hist_pre + eps
        hist_post = hist_post + eps
        
        # Renormalize
        hist_pre = hist_pre / np.sum(hist_pre)
        hist_post = hist_post / np.sum(hist_post)
        
        # Calculate JS divergence
        js_div = jensenshannon(hist_pre, hist_post)
        return js_div
        
    except Exception:
        return np.nan

def jensen_shannon_divergence_global_bins(pre_segment, post_segment, global_bins):
    """Calculate JS divergence using globally determined bin edges."""
    try:
        # Create histograms using global bins
        hist_pre, _ = np.histogram(pre_segment, bins=global_bins, density=True)
        hist_post, _ = np.histogram(post_segment, bins=global_bins, density=True)
        
        # Normalize to probability distributions
        hist_pre = hist_pre / np.sum(hist_pre) if np.sum(hist_pre) > 0 else hist_pre
        hist_post = hist_post / np.sum(hist_post) if np.sum(hist_post) > 0 else hist_post
        
        # Add small epsilon to avoid log(0)
        eps = 1e-10
        hist_pre = hist_pre + eps
        hist_post = hist_post + eps
        
        # Renormalize
        hist_pre = hist_pre / np.sum(hist_pre)
        hist_post = hist_post / np.sum(hist_post)
        
        # Calculate JS divergence
        js_div = jensenshannon(hist_pre, hist_post)
        return js_div
        
    except Exception:
        return np.nan

def compute_global_bins(all_series, n_bins=10):
    """Compute global bin edges from all series data."""
    all_values = np.concatenate([np.array(series) for series in all_series])
    all_values = all_values[~np.isnan(all_values)]
    
    # Use quantiles for more robust binning
    bin_edges = np.quantile(all_values, np.linspace(0, 1, n_bins + 1))
    
    # Ensure unique bin edges
    bin_edges = np.unique(bin_edges)
    if len(bin_edges) < 2:
        # Fallback to min-max range
        bin_edges = np.linspace(np.min(all_values), np.max(all_values), n_bins + 1)
    
    return bin_edges

def extract_js_features(series, tstar, bins_list=[5, 10, 15, 20, 25, 30, 50]):
    """Extract JS divergence features for different bin counts."""
    features = {}
    
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 10 or len(post_segment) < 10:
        return {f'js_bins_{bins}': np.nan for bins in bins_list}
    
    for bins in bins_list:
        js_div = jensen_shannon_divergence(pre_segment, post_segment, bins)
        features[f'js_bins_{bins}'] = js_div
    
    return features

def test_global_bins_effect(all_series, all_tstars, all_labels):
    """Test globally determined bins approach."""
    
    bins_to_test = [5, 10, 15, 20, 25, 30, 40, 50]
    results = {}
    
    print("Testing Global Bins JS Divergence...")
    print("="*60)
    
    for n_bins in tqdm(bins_to_test, desc="Testing global bins"):
        # Compute global bins from all data
        global_bins = compute_global_bins(all_series, n_bins=n_bins)
        
        feature_list = []
        for series, tstar in zip(all_series, all_tstars):
            js_div = jensen_shannon_divergence_global_bins(
                series[:tstar], series[tstar:], global_bins
            )
            feature_list.append([js_div])
        
        # Convert to DataFrame
        feature_df = pd.DataFrame(feature_list, columns=[f'global_{n_bins}'])
        feature_df = feature_df.fillna(feature_df.median())
        
        # Skip if all NaN
        if feature_df.isna().all().all():
            results[n_bins] = {'auc_mean': np.nan, 'auc_std': np.nan}
            continue
        
        try:
            # Evaluate with XGBoost
            model = xgb.XGBClassifier(
                n_estimators=50, random_state=42, 
                tree_method='hist', eval_metric='auc'
            )
            cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
            auc_mean = np.mean(cv_scores)
            auc_std = np.std(cv_scores)
            
            results[n_bins] = {
                'auc_mean': auc_mean,
                'auc_std': auc_std
            }
            
            print(f"Global {n_bins:3d}: AUC = {auc_mean:.4f} ± {auc_std:.4f}")
            
        except Exception as e:
            results[n_bins] = {'auc_mean': np.nan, 'auc_std': np.nan}
            print(f"Global {n_bins:3d}: Error - {str(e)[:50]}")
    
    return results

def test_js_bins_effect(all_series, all_tstars, all_labels):
    """Test effect of different bin counts on JS divergence performance."""
    
    bins_to_test = [5, 10, 15, 20 ]
    results = {}
    
    print("Testing Fixed Histogram Bins JS Divergence...")
    print("="*60)
    
    for bins in tqdm(bins_to_test, desc="Testing fixed bins"):
        # Extract JS features for this bin count
        feature_list = []
        for series, tstar in zip(all_series, all_tstars):
            js_div = jensen_shannon_divergence(
                series[:tstar], series[tstar:], bins=bins
            )
            feature_list.append([js_div])
        
        # Convert to DataFrame
        feature_df = pd.DataFrame(feature_list, columns=[f'js_bins_{bins}'])
        feature_df = feature_df.fillna(feature_df.median())
        
        # Skip if all NaN
        if feature_df.isna().all().all():
            results[bins] = {'auc_mean': np.nan, 'auc_std': np.nan}
            continue
        
        try:
            # Evaluate with XGBoost
            model = xgb.XGBClassifier(
                n_estimators=50, random_state=42, 
                tree_method='hist', eval_metric='auc'
            )
            cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
            auc_mean = np.mean(cv_scores)
            auc_std = np.std(cv_scores)
            
            results[bins] = {
                'auc_mean': auc_mean,
                'auc_std': auc_std
            }
            
            print(f"Fixed {bins:3d}: AUC = {auc_mean:.4f} ± {auc_std:.4f}")
            
        except Exception as e:
            results[bins] = {'auc_mean': np.nan, 'auc_std': np.nan}
            print(f"Fixed {bins:3d}: Error - {str(e)[:50]}")
    
    return results

def load_data(data_path):
    """Load time series data from parquet files."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series, all_tstars, all_labels = [], [], []
        
        for id_, group in X_train.groupby(level='id'):
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(int(y_train.loc[id_]))
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    print("Jensen-Shannon Divergence: Fixed Bins vs Global Bins Comparison")
    print("="*70)
    
    # Load data
    all_series, all_tstars, all_labels = load_data('.')
    if all_series is None:
        return
    
    print(f"Loaded {len(all_series)} time series")
    print(f"Labels distribution: {np.bincount(all_labels)}")
    
    # Test 1: Fixed histogram bins
    print("\n" + "="*70)
    fixed_results = test_js_bins_effect(all_series, all_tstars, all_labels)
    
    # Test 2: Global bins
    print("\n" + "="*70)
    global_results = test_global_bins_effect(all_series, all_tstars, all_labels)
    
    # Compare results
    print("\n" + "="*70)
    print("COMPARISON: FIXED BINS vs GLOBAL BINS")
    print("="*70)
    
    # Best from each approach
    valid_fixed = {k: v for k, v in fixed_results.items() if not np.isnan(v['auc_mean'])}
    valid_global = {k: v for k, v in global_results.items() if not np.isnan(v['auc_mean'])}
    
    if valid_fixed:
        best_fixed = max(valid_fixed.items(), key=lambda x: x[1]['auc_mean'])
        print(f"🔹 Best Fixed Bins: {best_fixed[0]} bins → AUC = {best_fixed[1]['auc_mean']:.4f} ± {best_fixed[1]['auc_std']:.4f}")
    
    if valid_global:
        best_global = max(valid_global.items(), key=lambda x: x[1]['auc_mean'])
        print(f"🔸 Best Global Bins: {best_global[0]} bins → AUC = {best_global[1]['auc_mean']:.4f} ± {best_global[1]['auc_std']:.4f}")
    
    # Overall comparison
    if valid_fixed and valid_global:
        fixed_avg = np.mean([v['auc_mean'] for v in valid_fixed.values()])
        global_avg = np.mean([v['auc_mean'] for v in valid_global.values()])
        
        print(f"\n📊 Average Performance:")
        print(f"   Fixed Bins: {fixed_avg:.4f}")
        print(f"   Global Bins: {global_avg:.4f}")
        
        if global_avg > fixed_avg:
            improvement = ((global_avg - fixed_avg) / fixed_avg) * 100
            print(f"   🏆 Global bins wins by {improvement:.1f}%")
        else:
            improvement = ((fixed_avg - global_avg) / global_avg) * 100
            print(f"   🏆 Fixed bins wins by {improvement:.1f}%")
    
    # Detailed results
    print(f"\n📋 DETAILED RESULTS:")
    print(f"{'Method':<15} {'Param':<8} {'AUC':<12}")
    print("-" * 35)
    
    # Fixed bins results
    for bins, result in sorted(valid_fixed.items(), key=lambda x: x[1]['auc_mean'], reverse=True)[:5]:
        print(f"{'Fixed':<15} {bins:<8} {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
    
    # Global bins results  
    for bins, result in sorted(valid_global.items(), key=lambda x: x[1]['auc_mean'], reverse=True)[:5]:
        print(f"{'Global':<15} {bins:<8} {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")

if __name__ == "__main__":
    main()