import numpy as np
import pandas as pd
from scipy.signal import welch, butter, filtfilt, hilbert
from scipy.stats import entropy
import pywt
import xgboost as xgb
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import cross_val_score
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

def lempel_ziv_complexity(sequence, threshold=None):
    """Compute Lempel-Ziv complexity."""
    if len(sequence) < 2:
        return 0
    
    if threshold is None:
        threshold = np.median(sequence)
    binary = (sequence > threshold).astype(int)
    
    n = len(binary)
    complexity = 1
    i = 0
    
    while i < n - 1:
        k = 1
        current_pattern = binary[i:i+k]
        
        # Check if current pattern exists in previous patterns
        found = False
        for j in range(i):
            if j + k <= i and np.array_equal(binary[j:j+k], current_pattern):
                found = True
                break
        
        while i + k < n and found:
            k += 1
            if i + k <= n:
                current_pattern = binary[i:i+k]
                found = False
                for j in range(i):
                    if j + k <= i and np.array_equal(binary[j:j+k], current_pattern):
                        found = True
                        break
        
        complexity += 1
        i += k
    
    return complexity / n if n > 0 else 0

def higuchi_fractal_dimension(signal, k_max=10):
    """Compute Higuchi fractal dimension."""
    N = len(signal)
    L = np.zeros(k_max)
    
    for k in range(1, k_max + 1):
        Lk = 0
        for m in range(k):
            Lmk = 0
            for i in range(1, int((N - m) / k)):
                Lmk += abs(signal[m + i * k] - signal[m + (i - 1) * k])
            Lmk = Lmk * (N - 1) / (k * int((N - m) / k) * k)
            Lk += Lmk
        L[k - 1] = Lk / k
    
    # Fit line to log-log plot
    x = np.log(np.arange(1, k_max + 1))
    y = np.log(L)
    return -np.polyfit(x, y, 1)[0]

def hurst_exponent(signal):
    """Compute Hurst exponent using R/S analysis."""
    n = len(signal)
    if n < 20:
        return np.nan
    
    # Create cumulative sum
    Y = np.cumsum(signal - np.mean(signal))
    
    # Calculate R/S for different time scales
    scales = np.logspace(1, np.log10(n//4), 10).astype(int)
    rs = []
    
    for scale in scales:
        if scale >= n:
            continue
        
        # Divide into non-overlapping windows
        n_windows = n // scale
        rs_scale = []
        
        for i in range(n_windows):
            start = i * scale
            end = start + scale
            window = Y[start:end]
            
            R = np.max(window) - np.min(window)
            S = np.std(signal[start:end])
            
            if S > 0:
                rs_scale.append(R / S)
        
        if rs_scale:
            rs.append(np.mean(rs_scale))
    
    if len(rs) < 3:
        return np.nan
    
    # Fit line to log-log plot
    log_scales = np.log(scales[:len(rs)])
    log_rs = np.log(rs)
    return np.polyfit(log_scales, log_rs, 1)[0]

def sample_entropy(signal, m=2, r=None):
    """Compute sample entropy."""
    if r is None:
        r = 0.2 * np.std(signal)
    
    N = len(signal)
    if N < m + 1:
        return np.nan
    
    def _maxdist(xi, xj, m):
        return max([abs(ua - va) for ua, va in zip(xi, xj)])
    
    def _phi(m):
        patterns = np.array([signal[i:i + m] for i in range(N - m + 1)])
        C = np.zeros(N - m + 1)
        
        for i in range(N - m + 1):
            template = patterns[i]
            for j in range(N - m + 1):
                if _maxdist(template, patterns[j], m) <= r:
                    C[i] += 1
        
        phi = np.mean(np.log(C / (N - m + 1)))
        return phi
    
    return _phi(m) - _phi(m + 1)

def extract_residual_features(series, tstar, fs=500, bandwidth_ratio=0.2):
    """Extract advanced features from filtered residual signal."""
    features = {}
    
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 20 or len(post_segment) < 20:
        return {f'residual_{k}': np.nan for k in range(30)}
    
    # Get dominant frequency and filter
    f_pre, psd_pre = welch(pre_segment, fs=fs, nperseg=min(len(pre_segment)//2, 64))
    dominant_freq_pre = f_pre[np.argmax(psd_pre)]
    
    # # Debug print for first few series
    # if len(features) == 0:  # First call
    #     print(f"DEBUG: pre_len={len(pre_segment)}, post_len={len(post_segment)}")
    #     print(f"DEBUG: dominant_freq={dominant_freq_pre:.4f}, fs={fs}")
    
    bandwidth = dominant_freq_pre * bandwidth_ratio
    low_freq = max(0.01, dominant_freq_pre - bandwidth/2)
    high_freq = min(fs/2 - 0.01, dominant_freq_pre + bandwidth/2)
    
    # if len(features) == 0:  # First call
    #     print(f"DEBUG: low_freq={low_freq:.4f}, high_freq={high_freq:.4f}")
    
    # if low_freq >= high_freq:
    #     if len(features) == 0:
    #         print("DEBUG: Filter design failed - low_freq >= high_freq")
    #     return {f'residual_{k}': np.nan for k in range(30)}
    
    b, a = butter(4, [low_freq, high_freq], btype='band', fs=fs)
    post_filtered = filtfilt(b, a, post_segment)
    post_residual = post_segment - post_filtered
    
    # if len(features) == 0:  # First call
    #     print(f"DEBUG: residual_std={np.std(post_residual):.6f}")
    #     print(f"DEBUG: residual_range=[{np.min(post_residual):.6f}, {np.max(post_residual):.6f}]")
    
    # Check if residual has sufficient signal
    residual_std = np.std(post_residual)
    if residual_std < 1e-6:  # Too small residual
        if len(features) == 0:
            print(f"DEBUG: Residual too small (std={residual_std:.8f}), using original signal features")
        # Use original post segment instead of residual
        analysis_signal = post_segment
    else:
        analysis_signal = post_residual
    
    # 1. COMPLEXITY FEATURES
    hist_counts = np.histogram(analysis_signal, bins=20)[0]
    # features['residual_entropy'] = entropy(hist_counts + 1e-10)
    # features['residual_lz_complexity'] = lempel_ziv_complexity(analysis_signal)
    features['residual_fractal_dim'] = higuchi_fractal_dimension(analysis_signal)
    
    # 2. NON-LINEAR DYNAMICS
    # features['residual_hurst'] = hurst_exponent(analysis_signal)
    # features['residual_sample_entropy'] = sample_entropy(analysis_signal)
    
    # 3. WAVELET FEATURES
    if len(analysis_signal) >= 32:
        coeffs = pywt.wavedec(analysis_signal, 'db4', level=4)
        for i, coeff in enumerate(coeffs):
            features[f'residual_wavelet_energy_L{i}'] = np.sum(coeff**2)
            features[f'residual_wavelet_std_L{i}'] = np.std(coeff)
    
    # 4. STATISTICAL MOMENTS
    if np.std(analysis_signal) > 0:
        norm_signal = (analysis_signal - np.mean(analysis_signal)) / np.std(analysis_signal)
        features['residual_skewness'] = np.abs(np.mean(norm_signal**3))
        features['residual_kurtosis'] = np.mean(norm_signal**4)
        features['residual_hyperskewness'] = np.abs(np.mean(norm_signal**5))
    else:
        features['residual_skewness'] = 0
        features['residual_kurtosis'] = 0
        features['residual_hyperskewness'] = 0
    
    # 5. SPECTRAL FEATURES OF RESIDUAL
    if len(analysis_signal) > 10:
        f_res, psd_res = welch(analysis_signal, fs=fs, nperseg=min(len(analysis_signal)//2, 32))
        # features['residual_spectral_entropy'] = entropy(psd_res + 1e-10)
        features['residual_spectral_centroid'] = np.sum(f_res * psd_res) / np.sum(psd_res)
        features['residual_spectral_rolloff'] = f_res[np.where(np.cumsum(psd_res) >= 0.85 * np.sum(psd_res))[0][0]]
    
    # 6. AMPLITUDE DISTRIBUTION
    features['residual_amplitude_range'] = np.max(analysis_signal) - np.min(analysis_signal)
    features['residual_rms'] = np.sqrt(np.mean(analysis_signal**2))
    features['residual_peak_factor'] = np.max(np.abs(analysis_signal)) / features['residual_rms'] if features['residual_rms'] > 0 else np.nan
    
    # 7. TEMPORAL STRUCTURE
    # Zero crossing rate
    features['residual_zcr'] = np.sum(np.diff(np.sign(analysis_signal)) != 0) / len(analysis_signal)
    
    # Autocorrelation decay
    if len(analysis_signal) > 10:
        autocorr = np.correlate(analysis_signal, analysis_signal, mode='full')
        autocorr = autocorr[len(autocorr)//2:]
        if autocorr[0] > 0:
            autocorr = autocorr / autocorr[0]
            # Find where autocorr drops below 1/e
            decay_idx = np.where(autocorr < 1/np.e)[0]
            features['residual_autocorr_decay'] = decay_idx[0] if len(decay_idx) > 0 else len(autocorr)
        else:
            features['residual_autocorr_decay'] = 0
    
    # 8. COMPARISON WITH PRE-SEGMENT RESIDUAL
    pre_filtered = filtfilt(b, a, pre_segment)
    pre_residual = pre_segment - pre_filtered
    
    features['residual_energy_ratio'] = np.sum(post_residual**2) / np.sum(pre_residual**2) if np.sum(pre_residual**2) > 0 else np.nan
    # features['residual_complexity_ratio'] = features['residual_lz_complexity'] / lempel_ziv_complexity(pre_residual) if lempel_ziv_complexity(pre_residual) > 0 else np.nan
        
    # except Exception as e:
    #     return {f'residual_{k}': np.nan for k in range(30)}
    
    return features

def extract_residual_features_wrapper(args):
    """Wrapper for parallel processing of single series."""
    series, tstar, bandwidth_ratio = args
    return extract_residual_features(series, tstar, bandwidth_ratio=bandwidth_ratio)

def evaluate_residual_features(all_series, all_tstars, all_labels, bandwidth_ratios=[0.2]):
    """Evaluate residual-based features."""
    results = {}
    
    print("Evaluating residual signal features...")
    
    for bandwidth_ratio in bandwidth_ratios:
        print(f"\nTesting bandwidth ratio: {bandwidth_ratio}")
        
        # Process series in parallel with progress bar
        import multiprocessing as mp
        from tqdm import tqdm
        
        n_processes = max(1, mp.cpu_count() - 1)
        args_list = [(series, tstar, bandwidth_ratio) for series, tstar in zip(all_series, all_tstars)]
        
        with mp.Pool(processes=n_processes) as pool:
            feature_list = list(tqdm(
                pool.imap(extract_residual_features_wrapper, args_list),
                total=len(args_list),
                desc=f"Extracting features (ratio={bandwidth_ratio})"
            ))
        
        # Debug first few series
        for i in range(min(3, len(feature_list))):
            features = feature_list[i]
            valid_count = len([k for k, v in features.items() if not np.isnan(v)])
            print(f"Series {i}: {valid_count} valid features")
            if valid_count > 0:
                valid_features = {k: v for k, v in features.items() if not np.isnan(v)}
                print(f"Sample features: {list(valid_features.items())[:3]}")
        
        feature_df = pd.DataFrame(feature_list)
        print(f"Feature matrix shape: {feature_df.shape}")
        print(f"NaN percentage: {feature_df.isna().sum().sum() / feature_df.size * 100:.1f}%")
        print(f"Feature names: {list(feature_df.columns)[:5]}")
        
        feature_df = feature_df.fillna(feature_df.median())
        
        if feature_df.shape[1] == 0:
            continue
        
        try:
            model = xgb.XGBClassifier(
                n_estimators=100, random_state=42, 
                tree_method='gpu_hist', gpu_id=0, eval_metric='auc'
            )
            cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
            auc_score = np.mean(cv_scores)
            auc_std = np.std(cv_scores)
            
            results[bandwidth_ratio] = {
                'auc_mean': auc_score,
                'auc_std': auc_std,
                'n_features': feature_df.shape[1]
            }
            
            print(f"AUC: {auc_score:.4f} ± {auc_std:.4f} ({feature_df.shape[1]} features)")
            
            # Feature importance
            model.fit(feature_df, all_labels)
            feature_importance = pd.DataFrame({
                'feature': feature_df.columns,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("Top 5 features:")
            for _, row in feature_importance.head().iterrows():
                print(f"  {row['feature']}: {row['importance']:.4f}")
                
        except Exception as e:
            print(f"Error: {e}")
            results[bandwidth_ratio] = {'auc_mean': np.nan, 'auc_std': np.nan, 'n_features': 0}
    
    return results

def load_data(data_path):
    """Load time series data."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series, all_tstars, all_labels = [], [], []
        
        for id_, group in X_train.groupby(level='id'):
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(int(y_train.loc[id_]))
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    parser = argparse.ArgumentParser(description='Residual Signal Analysis for Structural Break Detection')
    parser.add_argument('--data_path', type=str, default='./data')
    parser.add_argument('--bandwidth_ratios', nargs='+', type=float, default=[0.1, 0.2, 0.3])
    
    args = parser.parse_args()
    
    print("Residual Signal Analysis Pipeline")
    print("="*50)
    
    all_series, all_tstars, all_labels = load_data(args.data_path)
    if all_series is None:
        sys.exit(1)
    
    print(f"Loaded {len(all_series)} time series")
    
    results = evaluate_residual_features(all_series, all_tstars, all_labels, args.bandwidth_ratios)
    
    print("\n" + "="*50)
    print("SUMMARY RESULTS")
    print("="*50)
    
    best_auc = 0
    for bandwidth, result in results.items():
        if not np.isnan(result['auc_mean']):
            print(f"Bandwidth {bandwidth}: AUC = {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
            best_auc = max(best_auc, result['auc_mean'])
    
    print(f"\n🏆 Best AUC: {best_auc:.4f}")

if __name__ == "__main__":
    main()