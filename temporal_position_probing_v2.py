#!/usr/bin/env python3
"""
Temporal Position Probing v2
Compare first 25%, first 50% vs length-matched segments
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and labels"""
    print("📊 Loading data...")
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels)

def compute_comparison_features(normal_segment, post_segment):
    """Compute comparison features between segments"""
    if len(normal_segment) < 3 or len(post_segment) < 3:
        return {}
    
    features = {
        'mean_diff': np.mean(post_segment) - np.mean(normal_segment),
        'std_diff': np.std(post_segment, ddof=1) - np.std(normal_segment, ddof=1),
        'var_ratio': np.var(post_segment, ddof=1) / np.var(normal_segment, ddof=1) if np.var(normal_segment, ddof=1) > 0 else 1,
        'range_diff': (np.max(post_segment) - np.min(post_segment)) - (np.max(normal_segment) - np.min(normal_segment)),
        'cv_ratio': (np.std(post_segment, ddof=1) / abs(np.mean(post_segment))) / (np.std(normal_segment, ddof=1) / abs(np.mean(normal_segment))) if np.mean(normal_segment) != 0 and np.mean(post_segment) != 0 else 1,
        'skew_diff': pd.Series(post_segment).skew() - pd.Series(normal_segment).skew(),
        'kurt_diff': pd.Series(post_segment).kurtosis() - pd.Series(normal_segment).kurtosis()
    }
    
    # Statistical tests
    try:
        _, features['ks_pvalue'] = stats.ks_2samp(normal_segment, post_segment)
        _, features['mw_pvalue'] = stats.mannwhitneyu(normal_segment, post_segment, alternative='two-sided')
        features['t_stat'], features['t_pvalue'] = stats.ttest_ind(normal_segment, post_segment)
    except:
        features['ks_pvalue'] = features['mw_pvalue'] = features['t_stat'] = features['t_pvalue'] = np.nan
    
    return features

def extract_temporal_segments(series):
    """Extract different temporal segment combinations"""
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 20:
        return {}
    
    split_point = len(series_clean) // 2
    normal_full = series_clean[:split_point]
    post_full = series_clean[split_point:]
    
    methods = {}
    
    # Original best performers from v1
    if len(normal_full) >= 4:
        # First 25% vs full post
        first_25_len = max(3, len(normal_full) // 4)
        methods['first_25pct_vs_full'] = (normal_full[:first_25_len], post_full)
        
        # First 50% vs full post
        first_50_len = max(3, len(normal_full) // 2)
        methods['first_50pct_vs_full'] = (normal_full[:first_50_len], post_full)
    
    # Length-matched comparisons
    post_len = len(post_full)
    
    if len(normal_full) >= post_len:
        # First part of normal matched to post length
        methods['length_matched_first'] = (normal_full[:post_len], post_full)
        
        # Middle part of normal matched to post length
        if len(normal_full) >= post_len * 2:
            start_idx = (len(normal_full) - post_len) // 2
            methods['length_matched_middle'] = (normal_full[start_idx:start_idx + post_len], post_full)
        
        # Last part of normal matched to post length (closest to break)
        methods['length_matched_last'] = (normal_full[-post_len:], post_full)
    
    # Equal-length segments from different positions
    min_len = min(len(normal_full), len(post_full))
    if min_len >= 10:
        # Quarter-length segments
        quarter_len = min_len // 4
        if quarter_len >= 3:
            methods['equal_quarter_first'] = (normal_full[:quarter_len], post_full[:quarter_len])
            methods['equal_quarter_last'] = (normal_full[-quarter_len:], post_full[-quarter_len:])
        
        # Half-length segments
        half_len = min_len // 2
        if half_len >= 3:
            methods['equal_half_first'] = (normal_full[:half_len], post_full[:half_len])
            methods['equal_half_last'] = (normal_full[-half_len:], post_full[-half_len:])
        
        # Full equal-length segments
        methods['equal_full'] = (normal_full[:min_len], post_full[:min_len])
    
    return methods

def process_single_series(args):
    """Process a single time series for temporal comparisons"""
    series, label = args
    series_clean = np.array(series)[~np.isnan(series)]
    
    if len(series_clean) < 20:
        return {}
    
    try:
        methods = extract_temporal_segments(series)
    except Exception:
        return {}
    
    # Compute features for each method
    series_results = {}
    for method_name, (normal_seg, post_seg) in methods.items():
        try:
            features = compute_comparison_features(normal_seg, post_seg)
            features['label'] = label
            series_results[method_name] = features
        except Exception:
            continue
    
    return series_results

def evaluate_method_parallel(args):
    """Evaluate a single method with cross-validation"""
    method_name, method_features = args
    
    if len(method_features) < 20:
        return None
    
    try:
        # Convert to DataFrame
        features_df = pd.DataFrame(method_features)
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        
        # Prepare for classification
        feature_cols = [col for col in features_df.columns if col != 'label']
        X = features_df[feature_cols].fillna(features_df[feature_cols].median())
        y = features_df['label'].values
        
        if len(np.unique(y)) < 2:
            return None
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        model = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced', n_jobs=1)
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
        
        return {
            'method': method_name,
            'mean_auroc': cv_scores.mean(),
            'std_auroc': cv_scores.std(),
            'n_samples': len(features_df),
            'break_rate': y.mean()
        }
        
    except Exception:
        return None

def test_length_comparisons(time_series_list, labels, max_samples=100000):
    """Test length-based temporal comparisons"""
    print("🔧 Testing length-matched temporal comparisons...")
    
    # Limit samples for faster testing
    if len(time_series_list) > max_samples:
        indices = np.random.choice(len(time_series_list), max_samples, replace=False)
        time_series_list = [time_series_list[i] for i in indices]
        labels = labels[indices]
    
    print(f"   Processing {len(time_series_list)} series with {cpu_count()} cores...")
    
    # Parallel processing of time series
    n_processes = max(1, cpu_count() - 1)
    
    with Pool(processes=n_processes) as pool:
        # Process all series in parallel with progress bar
        series_args = [(series, label) for series, label in zip(time_series_list, labels)]
        
        results_list = list(tqdm(
            pool.imap(process_single_series, series_args),
            total=len(series_args),
            desc="   Processing series",
            ncols=80
        ))
    
    # Aggregate results by method
    all_method_results = {}
    for series_result in results_list:
        for method_name, features in series_result.items():
            if method_name not in all_method_results:
                all_method_results[method_name] = []
            all_method_results[method_name].append(features)
    
    print(f"   Collected results for {len(all_method_results)} methods")
    
    # Parallel evaluation of methods
    print("   Evaluating methods with cross-validation...")
    
    method_args = [(method_name, method_features) for method_name, method_features in all_method_results.items()]
    
    with Pool(processes=n_processes) as pool:
        evaluation_results = list(tqdm(
            pool.imap(evaluate_method_parallel, method_args),
            total=len(method_args),
            desc="   Evaluating methods",
            ncols=80
        ))
    
    # Filter out None results
    results = [result for result in evaluation_results if result is not None]
    
    return pd.DataFrame(results)

def analyze_length_results(results_df):
    """Analyze length-matched comparison results"""
    print("\n📈 LENGTH-MATCHED COMPARISON RESULTS")
    print("=" * 60)
    
    # Sort by AUROC performance
    results_sorted = results_df.sort_values('mean_auroc', ascending=False)
    
    print("🏆 All methods ranked by performance:")
    for i, (_, row) in enumerate(results_sorted.iterrows()):
        print(f"   {i+1:2d}. {row['method']:25}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    # Compare method categories
    print(f"\n📊 Method category analysis:")
    
    # Original winners vs length-matched
    original_methods = results_df[results_df['method'].str.contains('vs_full')]
    length_matched = results_df[results_df['method'].str.contains('length_matched')]
    equal_length = results_df[results_df['method'].str.contains('equal_')]
    
    if len(original_methods) > 0:
        print(f"\n🎯 Original methods (vs full post):")
        for _, row in original_methods.sort_values('mean_auroc', ascending=False).iterrows():
            print(f"   {row['method']:25}: {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    if len(length_matched) > 0:
        print(f"\n📏 Length-matched methods:")
        for _, row in length_matched.sort_values('mean_auroc', ascending=False).iterrows():
            print(f"   {row['method']:25}: {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    if len(equal_length) > 0:
        print(f"\n⚖️ Equal-length methods:")
        for _, row in equal_length.sort_values('mean_auroc', ascending=False).iterrows():
            print(f"   {row['method']:25}: {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    # Best overall method
    best_method = results_sorted.iloc[0]
    print(f"\n🎯 Best overall method: {best_method['method']}")
    print(f"   AUROC: {best_method['mean_auroc']:.4f} ± {best_method['std_auroc']:.4f}")
    print(f"   Samples: {best_method['n_samples']}")
    
    # Performance comparison
    if len(results_df) > 1:
        worst_method = results_sorted.iloc[-1]
        improvement = best_method['mean_auroc'] - worst_method['mean_auroc']
        print(f"\n📈 Performance range:")
        print(f"   Best:  {best_method['mean_auroc']:.4f} ({best_method['method']})")
        print(f"   Worst: {worst_method['mean_auroc']:.4f} ({worst_method['method']})")
        print(f"   Range: {improvement:.4f} ({improvement/worst_method['mean_auroc']*100:.2f}% improvement)")

def main():
    """Main pipeline execution"""
    print("🚀 TEMPORAL POSITION PROBING V2 - LENGTH COMPARISONS")
    print("=" * 60)
    
    # Load data
    time_series_list, labels = load_data()
    
    # Test length-matched comparisons
    results_df = test_length_comparisons(time_series_list, labels)
    
    # Analyze results
    analyze_length_results(results_df)
    
    # Save results
    results_df.to_csv('temporal_position_v2_results.csv', index=False)
    
    print(f"\n💾 Results saved to 'temporal_position_v2_results.csv'")
    
    return results_df

if __name__ == "__main__":
    results = main()