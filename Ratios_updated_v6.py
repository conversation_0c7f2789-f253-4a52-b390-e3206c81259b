#!/usr/bin/env python3
"""
Ratios_updated_v6.py - Complete V4 Features × All Temporal Positions
Extracts full V4 feature set for every pattern-aware temporal positioning method
"""

import numpy as np
import pandas as pd
from tsfresh.feature_extraction.feature_calculators import (
    energy_ratio_by_chunks, fft_aggregated, index_mass_quantile,
    binned_entropy, last_location_of_maximum, lempel_ziv_complexity,
    longest_strike_above_mean, longest_strike_below_mean,
    mean_second_derivative_central, percentage_of_reoccurring_datapoints_to_all_datapoints,
    percentage_of_reoccurring_values_to_all_values, ratio_beyond_r_sigma,
    ratio_value_number_to_time_series_length
)
from scipy.stats import (norm, kstest, anderson_ksamp, cramervonmises_2samp, wasserstein_distance, 
                        mannwhitneyu, kendalltau, spearmanr, pearsonr, normaltest, entropy)
from statsmodels.stats.diagnostic import lilliefors
from statsmodels.tsa.stattools import coint, grangercausalitytests
from statsmodels.stats.stattools import jarque_bera
from statsmodels.regression.linear_model import OLS
import pycatch22
from scipy.signal import (periodogram, welch, spectrogram, find_peaks, peak_prominences, 
                         savgol_filter, detrend, hilbert, coherence, correlate, correlation_lags, 
                         fftconvolve, csd)
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
from arch import arch_model
import warnings
from multiprocessing import Pool
from functools import partial
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_best_temporal_segments(series, tstar):
    """
    Extract ONLY the 4 best temporal positioning methods based on cluster analysis
    Uses actual tstar boundary for correct temporal positioning
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 20 or tstar <= 0 or tstar >= len(series_clean):
        return {}

    normal_full = series_clean[:tstar]
    post_full = series_clean[tstar:]

    segments = {}

    # ONLY THE 4 BEST METHODS FROM CLUSTER ANALYSIS:

    # 1. first_25pct_vs_full (Best for Spiky: 0.537)
    if len(normal_full) >= 4:
        pct_len = max(3, len(normal_full) // 4)
        segments['first_25pct_vs_full'] = (normal_full[:pct_len], post_full)

    # 2. first_75pct_vs_full (Best for Moderate/Calm: 0.582/0.545)
    if len(normal_full) >= 4:
        pct_len = max(3, int(len(normal_full) * 0.75))
        segments['first_75pct_vs_full'] = (normal_full[:pct_len], post_full)

    # 3. p20_first_vs_p20_first (Best for Volatile: 0.535)
    if len(normal_full) >= 10 and len(post_full) >= 10:
        pct_normal = max(3, int(len(normal_full) * 0.2))
        pct_post = max(3, int(len(post_full) * 0.2))
        segments['p20_first_vs_p20_first'] = (normal_full[:pct_normal], post_full[:pct_post])

    # 4. equal_quarter_last (Best for Clipped: 0.561)
    min_len = min(len(normal_full), len(post_full))
    if min_len >= 10:
        quarter_len = max(3, min_len // 4)
        segments['equal_quarter_last'] = (normal_full[-quarter_len:], post_full[-quarter_len:])

    return segments

def load_pattern_assignments():
    """Load volatility pattern assignments if available"""
    try:
        volatility_df = pd.read_csv('volatility_pattern_analysis.csv')
        pattern_map = dict(zip(volatility_df['series_id'], volatility_df['pattern_type']))
        print(f"✅ Loaded {len(pattern_map)} pattern assignments")
        return pattern_map
    except:
        try:
            clusters_df = pd.read_csv('time_series_statistics_with_clusters.csv')
            cluster_names = {0: 'spiky', 1: 'volatile', 2: 'moderate', 3: 'calm', 4: 'clipped'}
            pattern_map = {row['series_id']: cluster_names.get(row['cluster'], 'unknown') 
                          for _, row in clusters_df.iterrows()}
            print(f"✅ Loaded {len(pattern_map)} cluster-based patterns")
            return pattern_map
        except:
            print("⚠️  No pattern data found, using universal methods")
            return {}

# Import ALL V4 functions from Ratios_updated_v4.py
def jensen_shannon_divergence(pre, post):
    """Jensen-Shannon divergence between two distributions"""
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        pre, post = pre[~np.isnan(pre)], post[~np.isnan(post)]
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else np.ones_like(hist_pre) / len(hist_pre)
        hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else np.ones_like(hist_post) / len(hist_post)
        m = 0.5 * (hist_pre + hist_post)
        jsd = 0.5 * (entropy(hist_pre, m) + entropy(hist_post, m))
        return jsd if not np.isnan(jsd) else np.nan
    except Exception:
        return np.nan

def kl_divergence(pre, post):
    """Kullback-Leibler divergence"""
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        pre, post = pre[~np.isnan(pre)], post[~np.isnan(post)]
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else np.ones_like(hist_pre) / len(hist_pre)
        hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else np.ones_like(hist_post) / len(hist_post)
        eps = 1e-10
        hist_pre = hist_pre + eps
        hist_post = hist_post + eps
        return entropy(hist_pre, hist_post)
    except Exception:
        return np.nan

def compute_enhanced_divergence_tests(pre, post, segment_name_prefix=''):
    """Enhanced statistical divergence tests including new v4 features"""
    tests = {}
    
    if len(pre) < 5 or len(post) < 5:
        return {f'{segment_name_prefix}{k}': np.nan for k in [
            'jsd', 'kl_divergence', 'wasserstein_distance', 'ad_stat', 'cvm_stat', 
            'p_ks', 'p_ad', 'p_mannwhitney', 'kendall_tau', 'spearman_rho', 
            'pearson_r', 'dagostino_pearson_stat', 'granger_p'
        ]}
    
    pre_clean = pre[~np.isnan(pre)]
    post_clean = post[~np.isnan(post)]
    
    # Original divergence tests
    tests[f'{segment_name_prefix}jsd'] = jensen_shannon_divergence(pre_clean, post_clean)
    tests[f'{segment_name_prefix}kl_divergence'] = kl_divergence(pre_clean, post_clean)
    
    try:
        tests[f'{segment_name_prefix}wasserstein_distance'] = wasserstein_distance(pre_clean, post_clean)
    except Exception:
        tests[f'{segment_name_prefix}wasserstein_distance'] = np.nan
    
    try:
        ad_result = anderson_ksamp([pre_clean, post_clean])
        tests[f'{segment_name_prefix}ad_stat'] = ad_result.statistic
        tests[f'{segment_name_prefix}p_ad'] = ad_result.significance_level if hasattr(ad_result, 'significance_level') else np.nan
    except Exception:
        tests[f'{segment_name_prefix}ad_stat'] = np.nan
        tests[f'{segment_name_prefix}p_ad'] = np.nan
    
    try:
        cvm_result = cramervonmises_2samp(pre_clean, post_clean)
        tests[f'{segment_name_prefix}cvm_stat'] = cvm_result.statistic
    except Exception:
        tests[f'{segment_name_prefix}cvm_stat'] = np.nan
    
    try:
        ks_stat, p_ks = stats.ks_2samp(pre_clean, post_clean)
        tests[f'{segment_name_prefix}p_ks'] = p_ks
    except Exception:
        tests[f'{segment_name_prefix}p_ks'] = np.nan
    
    try:
        u_stat, p_mw = mannwhitneyu(pre_clean, post_clean, alternative='two-sided')
        tests[f'{segment_name_prefix}p_mannwhitney'] = p_mw
    except Exception:
        tests[f'{segment_name_prefix}p_mannwhitney'] = np.nan
    
    # V4 CORRELATION FEATURES
    try:
        tau, p_tau = kendalltau(pre_clean, post_clean)
        tests[f'{segment_name_prefix}kendall_tau'] = tau
    except Exception:
        tests[f'{segment_name_prefix}kendall_tau'] = np.nan
    
    try:
        rho, p_rho = spearmanr(pre_clean, post_clean)
        tests[f'{segment_name_prefix}spearman_rho'] = rho
    except Exception:
        tests[f'{segment_name_prefix}spearman_rho'] = np.nan
    
    try:
        r, p_r = pearsonr(pre_clean, post_clean)
        tests[f'{segment_name_prefix}pearson_r'] = r
    except Exception:
        tests[f'{segment_name_prefix}pearson_r'] = np.nan
    
    # D'Agostino-Pearson Test
    try:
        combined = np.concatenate([pre_clean, post_clean])
        stat, p_val = normaltest(combined)
        tests[f'{segment_name_prefix}dagostino_pearson_stat'] = stat
    except Exception:
        tests[f'{segment_name_prefix}dagostino_pearson_stat'] = np.nan
    
    # Granger Causality
    if len(pre_clean) > 30 and len(post_clean) > 30:
        try:
            min_len = min(len(pre_clean), len(post_clean))
            data = np.column_stack((pre_clean[:min_len], post_clean[:min_len]))
            gc_result = grangercausalitytests(data, maxlag=1, verbose=False)
            tests[f'{segment_name_prefix}granger_p'] = gc_result[1][0]['ssr_ftest'][1]
        except Exception:
            tests[f'{segment_name_prefix}granger_p'] = np.nan
    else:
        tests[f'{segment_name_prefix}granger_p'] = np.nan
    
    return tests

def compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix=''):
    """Compute all 4 relative measures for any pre/post feature pair"""
    relatives = {}
    
    relatives[f'{segment_name_prefix}ratio_{feature_name}'] = (
        post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
    )
    
    if pre_val and pre_val != 0 and not np.isnan(post_val):
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = abs(post_val - pre_val) / abs(pre_val)
    else:
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = np.nan
        
    if pre_val and pre_val > 0 and post_val and post_val > 0:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.log(post_val / pre_val)
    else:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.nan
        
    if not np.isnan(pre_val) and not np.isnan(post_val) and (abs(pre_val) + abs(post_val)) > 0:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = (post_val - pre_val) / (abs(post_val) + abs(pre_val))
    else:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = np.nan
        
    return relatives

def compute_normalization_features(feature_dict, whole_series, segment_name_prefix='', tstar=None):
    """Add _norm_zscore and _weighted variants of features"""
    normalized_features = {}
    
    mean_whole = np.nanmean(whole_series)
    std_whole = np.nanstd(whole_series, ddof=1) if len(whole_series) > 1 else np.nan
    
    if tstar is not None and 0 < tstar < len(whole_series):
        w_i = (len(whole_series) - tstar) / len(whole_series)
    else:
        w_i = 0.5
    
    for feature_name, feature_value in feature_dict.items():
        if not np.isnan(feature_value) and std_whole and std_whole > 0:
            normalized_features[f'{feature_name}_norm_zscore'] = (feature_value - mean_whole) / std_whole
            normalized_features[f'{feature_name}_weighted'] = w_i * feature_value
        else:
            normalized_features[f'{feature_name}_norm_zscore'] = np.nan
            normalized_features[f'{feature_name}_weighted'] = np.nan
    
    return normalized_features

# Import compute_features_for_segment_v4 function (complete V4 feature set)
from Ratios_updated_v4 import compute_features_for_segment_v4

def compute_temporal_position_features_v6(series, tstar, pattern_type='unknown', series_id=None):
    """
    V6: Extract COMPLETE V4 features for ALL temporal positioning methods
    """
    feats = {}
    
    try:
        series = np.array(series, dtype=float)
        series = series[~np.isnan(series)]
        
        if len(series) < 20:
            return feats
        
        # Get BEST temporal segments with correct tstar
        segments = get_best_temporal_segments(series, tstar)
        
        if not segments:
            return feats
        
        # Extract COMPLETE V4 features for each temporal segment pair
        for segment_name, (pre, post) in segments.items():
            if len(pre) >= 5 and len(post) >= 5:
                # Use complete V4 feature extraction
                segment_features = compute_features_for_segment_v4(
                    pre=pre, 
                    post=post, 
                    whole=series, 
                    segment_name_prefix=f'{segment_name}_',
                    tstar=len(pre)
                )
                feats.update(segment_features)
        
        # Add metadata
        feats['pattern_type'] = pattern_type
        feats['series_id'] = series_id if series_id is not None else -1
        feats['n_segments'] = len(segments)
        
        return feats
        
    except Exception as e:
        logging.error(f"Error processing series {series_id}: {str(e)}")
        return feats

def process_series_v6(args):
    """V6 process_series with complete V4 features × all temporal positions"""
    index, series, tstar, pattern_type = args
    return compute_temporal_position_features_v6(series, tstar, pattern_type, index)

def extract_features_parallel_v6(series_list, tstars_list, pattern_map=None, n_processes=None):
    """V6 parallel feature extraction: Complete V4 features × All temporal positions"""
    import multiprocessing as mp
    import time
    
    if n_processes is None:
        n_processes = max(1, mp.cpu_count() - 1)
    
    # Get pattern types for each series
    if pattern_map is None:
        pattern_map = {}
    
    pattern_types = [pattern_map.get(i, 'unknown') for i in range(len(series_list))]
    
    args = [(i, series_list[i], tstars_list[i], pattern_types[i]) for i in range(len(series_list))]
    total_series = len(args)
    
    print(f"V6: Initializing {n_processes} worker processes for {total_series} series...")
    print(f"Pattern distribution: {pd.Series(pattern_types).value_counts().to_dict()}")
    print(f"Expected features: ~15,000+ per series (V4 features × ~17 temporal positions)")
    start_time = time.time()
    
    with mp.Pool(processes=n_processes) as pool:
        results = []
        for arg in args:
            result = pool.apply_async(process_series_v6, (arg,))
            results.append(result)
        
        feature_results = []
        for i, result in enumerate(results):
            feature_results.append(result.get())
            if (i + 1) % 50 == 0:  # More frequent updates due to complexity
                elapsed = time.time() - start_time
                progress = (i + 1) / total_series * 100
                print(f"Progress: {progress:.1f}% ({i+1}/{total_series}) - {elapsed/60:.1f}m elapsed")
    
    elapsed_total = time.time() - start_time
    throughput = total_series / elapsed_total
    print(f"✓ V6 Extraction completed: {elapsed_total/60:.2f}m total ({throughput:.2f} series/s)")
    
    feature_df = pd.DataFrame(feature_results)
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    n_features = feature_df.shape[1]
    sparsity = (feature_df.isna().sum().sum() / feature_df.size) * 100
    print(f"V6 Feature matrix: {total_series} × {n_features} ({sparsity:.1f}% sparse)")
    
    # Pattern-specific feature analysis
    if 'pattern_type' in feature_df.columns:
        pattern_counts = feature_df['pattern_type'].value_counts()
        print(f"Complete V4 features extracted for all temporal positions:")
        for pattern, count in pattern_counts.items():
            print(f"  {pattern}: {count} series")
    
    return feature_df

if __name__ == "__main__":
    print("Ratios_updated_v6.py - Complete V4 Features × All Temporal Positions")
    print("=" * 80)
    print("V6 OPTIMIZED APPROACH:")
    print("• ONLY 4 BEST temporal positioning methods from cluster analysis")
    print("• COMPLETE V4 feature set (~900 features) for EACH temporal position")
    print("• Pattern-aware processing with metadata")
    print("• Expected: ~3,600 features per time series")
    print("• Best temporal methods include:")
    print("  - first_25pct_vs_full (Best for Spiky: 0.537)")
    print("  - first_75pct_vs_full (Best for Moderate/Calm: 0.582/0.545)")
    print("  - p20_first_vs_p20_first (Best for Volatile: 0.535)")
    print("  - equal_quarter_last (Best for Clipped: 0.561)")
    print("=" * 80)
    
    # Load pattern assignments
    pattern_map = load_pattern_assignments()
    
    # Example usage
    print("\nExample usage:")
    print("pattern_map = load_pattern_assignments()")
    print("features_df = extract_features_parallel_v6(series_list, pattern_map)")
    print("\nWARNING: This will generate massive feature matrices!")
    print("Consider using V5 for production unless you need exhaustive analysis.")