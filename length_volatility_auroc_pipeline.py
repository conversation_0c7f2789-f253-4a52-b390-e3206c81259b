#!/usr/bin/env python3
"""
Length-Volatility-AUROC Pipeline
Investigate functional interdependency between:
1. Total time series length
2. Normal/candidate segment length ratios  
3. Volatility/spikiness clustering patterns
4. AUROC performance for structural break detection
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics import roc_auc_score
from scipy import stats
from scipy.stats import spearmanr, pearsonr
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and labels"""
    print("📊 Loading data...")
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels)

def compute_volatility_metrics(series):
    """Compute volatility and spikiness metrics"""
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 10:
        return {}
    
    # Basic volatility
    std_val = np.std(series_clean, ddof=1)
    mean_val = np.mean(series_clean)
    cv = std_val / abs(mean_val) if mean_val != 0 else np.inf
    
    # Spikiness metrics
    z_scores = np.abs((series_clean - mean_val) / std_val) if std_val > 0 else np.zeros_like(series_clean)
    spike_rate = np.sum(z_scores > 3) / len(series_clean)
    
    # Direction changes (volatility proxy)
    returns = np.diff(series_clean)
    if len(returns) > 1:
        direction_changes = np.sum(np.diff(np.sign(returns)) != 0)
        direction_change_rate = direction_changes / len(returns)
    else:
        direction_change_rate = 0
    
    # Realized volatility
    realized_vol = np.sqrt(np.sum(returns**2)) if len(returns) > 0 else 0
    
    # Range-based volatility
    range_val = np.max(series_clean) - np.min(series_clean)
    
    return {
        'cv': cv,
        'spike_rate': spike_rate,
        'direction_change_rate': direction_change_rate,
        'realized_vol': realized_vol,
        'range_vol': range_val,
        'std': std_val
    }

def compute_segment_features(series, split_ratio=0.5):
    """Compute features for normal and candidate segments"""
    series_clean = np.array(series)[~np.isnan(series)]
    total_length = len(series_clean)
    
    # Split point (assume structural break at split_ratio for break series)
    split_point = int(total_length * split_ratio)
    
    normal_segment = series_clean[:split_point]
    candidate_segment = series_clean[split_point:]
    
    normal_length = len(normal_segment)
    candidate_length = len(candidate_segment)
    length_ratio = normal_length / candidate_length if candidate_length > 0 else np.inf
    
    # Volatility for each segment
    normal_vol = compute_volatility_metrics(normal_segment)
    candidate_vol = compute_volatility_metrics(candidate_segment)
    
    # Segment differences
    mean_diff = np.mean(candidate_segment) - np.mean(normal_segment) if len(candidate_segment) > 0 and len(normal_segment) > 0 else 0
    std_ratio = (np.std(candidate_segment, ddof=1) / np.std(normal_segment, ddof=1) 
                if len(candidate_segment) > 1 and len(normal_segment) > 1 and np.std(normal_segment, ddof=1) > 0 else 1)
    
    return {
        'total_length': total_length,
        'normal_length': normal_length,
        'candidate_length': candidate_length,
        'length_ratio': length_ratio,
        'normal_cv': normal_vol.get('cv', np.nan),
        'candidate_cv': candidate_vol.get('cv', np.nan),
        'normal_spike_rate': normal_vol.get('spike_rate', 0),
        'candidate_spike_rate': candidate_vol.get('spike_rate', 0),
        'mean_diff': mean_diff,
        'std_ratio': std_ratio,
        'cv_ratio': (candidate_vol.get('cv', np.nan) / normal_vol.get('cv', 1) 
                    if normal_vol.get('cv', 0) != 0 else np.nan)
    }

def perform_volatility_clustering(features_df):
    """Cluster series based on volatility patterns"""
    print("🎯 Performing volatility clustering...")
    
    # Select volatility features for clustering
    vol_features = ['cv', 'spike_rate', 'direction_change_rate', 'realized_vol']
    available_features = [f for f in vol_features if f in features_df.columns]
    
    if not available_features:
        print("   ⚠️ No volatility features available")
        return features_df
    
    X_vol = features_df[available_features].copy()
    X_vol = X_vol.replace([np.inf, -np.inf], np.nan).fillna(X_vol.median())
    
    # Standardize and cluster
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_vol)
    
    # Find optimal clusters (2-6)
    silhouette_scores = []
    for k in range(2, 7):
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(X_scaled)
        from sklearn.metrics import silhouette_score
        score = silhouette_score(X_scaled, labels)
        silhouette_scores.append(score)
    
    optimal_k = np.argmax(silhouette_scores) + 2
    
    # Final clustering
    kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
    features_df['volatility_cluster'] = kmeans.fit_predict(X_scaled)
    
    print(f"   ✅ Created {optimal_k} volatility clusters")
    
    # Characterize clusters
    for cluster_id in range(optimal_k):
        cluster_data = features_df[features_df['volatility_cluster'] == cluster_id]
        n_samples = len(cluster_data)
        break_rate = cluster_data['label'].mean() if 'label' in cluster_data.columns else 0
        avg_cv = cluster_data['cv'].mean() if 'cv' in cluster_data.columns else 0
        avg_spike = cluster_data['spike_rate'].mean() if 'spike_rate' in cluster_data.columns else 0
        
        print(f"   Cluster {cluster_id}: n={n_samples}, break_rate={break_rate:.3f}, cv={avg_cv:.3f}, spike_rate={avg_spike:.3f}")
    
    return features_df

def compute_auroc_by_groups(features_df, group_col, feature_cols):
    """Compute AUROC for different groups"""
    print(f"📈 Computing AUROC by {group_col}...")
    
    if 'label' not in features_df.columns:
        print("   ⚠️ No labels available")
        return {}
    
    results = {}
    
    for group_val in features_df[group_col].unique():
        group_data = features_df[features_df[group_col] == group_val]
        
        if len(group_data) < 20 or group_data['label'].nunique() < 2:
            continue
        
        X = group_data[feature_cols].copy()
        X = X.replace([np.inf, -np.inf], np.nan).fillna(X.median())
        y = group_data['label'].values
        
        # Quick RF model
        rf = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced')
        cv_scores = cross_val_score(rf, X, y, cv=3, scoring='roc_auc')
        
        results[group_val] = {
            'auroc_mean': cv_scores.mean(),
            'auroc_std': cv_scores.std(),
            'n_samples': len(group_data),
            'break_rate': y.mean()
        }
        
        print(f"   {group_col}={group_val}: AUROC={cv_scores.mean():.3f}±{cv_scores.std():.3f}, n={len(group_data)}")
    
    return results

def analyze_length_dependencies(features_df):
    """Analyze length-dependent patterns"""
    print("📏 Analyzing length dependencies...")
    
    # Create length bins
    try:
        length_bins = pd.qcut(features_df['total_length'], q=5, labels=['Very Short', 'Short', 'Medium', 'Long', 'Very Long'], duplicates='drop')
        features_df['length_bin'] = length_bins
    except ValueError:
        # Fallback to manual binning
        length_quantiles = features_df['total_length'].quantile([0.2, 0.4, 0.6, 0.8])
        features_df['length_bin'] = pd.cut(features_df['total_length'], 
                                         bins=[-np.inf] + length_quantiles.tolist() + [np.inf],
                                         labels=['Very Short', 'Short', 'Medium', 'Long', 'Very Long'])
    
    # Create ratio bins with better handling
    ratio_finite = features_df['length_ratio'][np.isfinite(features_df['length_ratio'])]
    if len(ratio_finite) > 0 and ratio_finite.nunique() > 3:
        try:
            ratio_bins = pd.qcut(ratio_finite, q=3, labels=['Low Ratio', 'Medium Ratio', 'High Ratio'], duplicates='drop')
            features_df.loc[np.isfinite(features_df['length_ratio']), 'ratio_bin'] = ratio_bins
        except ValueError:
            # Manual binning for ratios
            ratio_33 = ratio_finite.quantile(0.33)
            ratio_67 = ratio_finite.quantile(0.67)
            features_df.loc[np.isfinite(features_df['length_ratio']), 'ratio_bin'] = pd.cut(
                ratio_finite, bins=[-np.inf, ratio_33, ratio_67, np.inf], 
                labels=['Low Ratio', 'Medium Ratio', 'High Ratio']
            )
    else:
        print("   ⚠️ Insufficient unique ratio values for binning")
    
    # Analyze correlations
    correlations = {}
    
    if 'label' in features_df.columns:
        # Length vs break rate
        length_break_corr = features_df.groupby('length_bin')['label'].mean()
        print(f"   📊 Break rate by length: {length_break_corr.to_dict()}")
        
        # Ratio vs break rate
        if 'ratio_bin' in features_df.columns:
            ratio_break_corr = features_df.groupby('ratio_bin')['label'].mean()
            print(f"   📊 Break rate by ratio: {ratio_break_corr.to_dict()}")
        
        # Volatility cluster vs break rate
        if 'volatility_cluster' in features_df.columns:
            vol_break_corr = features_df.groupby('volatility_cluster')['label'].mean()
            print(f"   📊 Break rate by volatility cluster: {vol_break_corr.to_dict()}")
    
    return features_df

def investigate_interdependencies(features_df):
    """Investigate functional interdependencies"""
    print("🔍 Investigating interdependencies...")
    
    # Key variables
    key_vars = ['total_length', 'length_ratio', 'cv', 'spike_rate']
    available_vars = [v for v in key_vars if v in features_df.columns]
    
    if len(available_vars) < 2:
        print("   ⚠️ Insufficient variables for correlation analysis")
        return {}
    
    # Correlation matrix
    corr_data = features_df[available_vars].copy()
    corr_data = corr_data.replace([np.inf, -np.inf], np.nan)
    
    correlations = {}
    
    for i, var1 in enumerate(available_vars):
        for j, var2 in enumerate(available_vars):
            if i < j:
                data1 = corr_data[var1].dropna()
                data2 = corr_data[var2].dropna()
                
                # Find common indices
                common_idx = data1.index.intersection(data2.index)
                if len(common_idx) > 10:
                    pearson_r, pearson_p = pearsonr(data1[common_idx], data2[common_idx])
                    spearman_r, spearman_p = spearmanr(data1[common_idx], data2[common_idx])
                    
                    correlations[f"{var1}_vs_{var2}"] = {
                        'pearson_r': pearson_r,
                        'pearson_p': pearson_p,
                        'spearman_r': spearman_r,
                        'spearman_p': spearman_p
                    }
                    
                    print(f"   {var1} vs {var2}: r={pearson_r:.3f} (p={pearson_p:.3f}), ρ={spearman_r:.3f} (p={spearman_p:.3f})")
    
    return correlations

def create_comprehensive_visualization(features_df, auroc_results):
    """Create comprehensive visualization"""
    print("📊 Creating visualization...")
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    
    # Plot 1: Length distribution
    axes[0, 0].hist(features_df['total_length'], bins=50, alpha=0.7, edgecolor='black')
    axes[0, 0].set_xlabel('Total Length')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Length Distribution')
    
    # Plot 2: Length ratio distribution
    if 'length_ratio' in features_df.columns:
        ratio_finite = features_df['length_ratio'][np.isfinite(features_df['length_ratio'])]
        if len(ratio_finite) > 0:
            axes[0, 1].hist(ratio_finite, bins=50, alpha=0.7, edgecolor='black')
            axes[0, 1].set_xlabel('Normal/Candidate Length Ratio')
            axes[0, 1].set_ylabel('Frequency')
            axes[0, 1].set_title('Length Ratio Distribution')
    
    # Plot 3: Volatility clustering
    if 'volatility_cluster' in features_df.columns and 'cv' in features_df.columns:
        for cluster in features_df['volatility_cluster'].unique():
            cluster_data = features_df[features_df['volatility_cluster'] == cluster]
            axes[0, 2].scatter(cluster_data['cv'], cluster_data['spike_rate'], 
                             label=f'Cluster {cluster}', alpha=0.6)
        axes[0, 2].set_xlabel('Coefficient of Variation')
        axes[0, 2].set_ylabel('Spike Rate')
        axes[0, 2].set_title('Volatility Clusters')
        axes[0, 2].legend()
        axes[0, 2].set_xlim(0, min(10, features_df['cv'].quantile(0.95)))
    
    # Plot 4: AUROC by length bins
    if 'length_bin' in features_df.columns and auroc_results.get('length_auroc'):
        length_auroc = auroc_results['length_auroc']
        bins = list(length_auroc.keys())
        aurocs = [length_auroc[b]['auroc_mean'] for b in bins]
        axes[0, 3].bar(bins, aurocs)
        axes[0, 3].set_xlabel('Length Bin')
        axes[0, 3].set_ylabel('AUROC')
        axes[0, 3].set_title('AUROC by Length')
        axes[0, 3].tick_params(axis='x', rotation=45)
    
    # Plot 5: Length vs CV
    if 'cv' in features_df.columns:
        cv_finite = features_df['cv'][np.isfinite(features_df['cv'])]
        length_finite = features_df.loc[cv_finite.index, 'total_length']
        axes[1, 0].scatter(length_finite, cv_finite, alpha=0.5)
        axes[1, 0].set_xlabel('Total Length')
        axes[1, 0].set_ylabel('Coefficient of Variation')
        axes[1, 0].set_title('Length vs Volatility')
        axes[1, 0].set_ylim(0, min(10, cv_finite.quantile(0.95)))
    
    # Plot 6: Length vs Break Rate
    if 'label' in features_df.columns:
        length_break = features_df.groupby('length_bin')['label'].mean()
        axes[1, 1].bar(length_break.index, length_break.values)
        axes[1, 1].set_xlabel('Length Bin')
        axes[1, 1].set_ylabel('Break Rate')
        axes[1, 1].set_title('Break Rate by Length')
        axes[1, 1].tick_params(axis='x', rotation=45)
    
    # Plot 7: Volatility Cluster vs Break Rate
    if 'volatility_cluster' in features_df.columns and 'label' in features_df.columns:
        vol_break = features_df.groupby('volatility_cluster')['label'].mean()
        axes[1, 2].bar(vol_break.index, vol_break.values)
        axes[1, 2].set_xlabel('Volatility Cluster')
        axes[1, 2].set_ylabel('Break Rate')
        axes[1, 2].set_title('Break Rate by Volatility Cluster')
    
    # Plot 8: Length Ratio vs Break Rate
    if 'ratio_bin' in features_df.columns and 'label' in features_df.columns:
        ratio_break = features_df.groupby('ratio_bin')['label'].mean()
        axes[1, 3].bar(ratio_break.index, ratio_break.values)
        axes[1, 3].set_xlabel('Length Ratio Bin')
        axes[1, 3].set_ylabel('Break Rate')
        axes[1, 3].set_title('Break Rate by Length Ratio')
        axes[1, 3].tick_params(axis='x', rotation=45)
    
    # Plot 9-12: Additional analysis plots
    # Correlation heatmap
    corr_vars = ['total_length', 'length_ratio', 'cv', 'spike_rate']
    available_corr_vars = [v for v in corr_vars if v in features_df.columns]
    if len(available_corr_vars) > 1:
        corr_matrix = features_df[available_corr_vars].corr()
        im = axes[2, 0].imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
        axes[2, 0].set_xticks(range(len(available_corr_vars)))
        axes[2, 0].set_yticks(range(len(available_corr_vars)))
        axes[2, 0].set_xticklabels(available_corr_vars, rotation=45)
        axes[2, 0].set_yticklabels(available_corr_vars)
        axes[2, 0].set_title('Correlation Matrix')
        plt.colorbar(im, ax=axes[2, 0])
    
    # Summary statistics
    axes[2, 3].axis('off')
    summary_text = "SUMMARY STATISTICS\n\n"
    summary_text += f"Total Series: {len(features_df)}\n"
    if 'label' in features_df.columns:
        summary_text += f"Break Rate: {features_df['label'].mean():.3f}\n"
    summary_text += f"Avg Length: {features_df['total_length'].mean():.1f}\n"
    if 'volatility_cluster' in features_df.columns:
        summary_text += f"Volatility Clusters: {features_df['volatility_cluster'].nunique()}\n"
    
    axes[2, 3].text(0.1, 0.9, summary_text, transform=axes[2, 3].transAxes, 
                   fontsize=12, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('length_volatility_auroc_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("   ✅ Saved visualization to 'length_volatility_auroc_analysis.png'")

def main():
    """Main pipeline execution"""
    print("🚀 LENGTH-VOLATILITY-AUROC PIPELINE")
    print("=" * 50)
    
    # Load data
    time_series_list, labels = load_data()
    
    # Compute features for all series
    print("🔧 Computing features...")
    all_features = []
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        if i % 1000 == 0:
            print(f"   Processing {i+1}/{len(time_series_list)}...")
        
        # Volatility metrics
        vol_metrics = compute_volatility_metrics(series)
        
        # Segment features
        seg_features = compute_segment_features(series)
        
        # Combine
        combined = {**vol_metrics, **seg_features, 'label': label, 'series_id': i}
        all_features.append(combined)
    
    features_df = pd.DataFrame(all_features)
    print(f"   ✅ Computed features for {len(features_df)} series")
    
    # Perform volatility clustering
    features_df = perform_volatility_clustering(features_df)
    
    # Analyze length dependencies
    features_df = analyze_length_dependencies(features_df)
    
    # Investigate interdependencies
    correlations = investigate_interdependencies(features_df)
    
    # Compute AUROC by different groups
    feature_cols = ['cv', 'spike_rate', 'mean_diff', 'std_ratio']
    available_feature_cols = [f for f in feature_cols if f in features_df.columns]
    
    auroc_results = {}
    
    if 'length_bin' in features_df.columns:
        auroc_results['length_auroc'] = compute_auroc_by_groups(features_df, 'length_bin', available_feature_cols)
    
    if 'volatility_cluster' in features_df.columns:
        auroc_results['volatility_auroc'] = compute_auroc_by_groups(features_df, 'volatility_cluster', available_feature_cols)
    
    if 'ratio_bin' in features_df.columns:
        auroc_results['ratio_auroc'] = compute_auroc_by_groups(features_df, 'ratio_bin', available_feature_cols)
    
    # Create visualization
    create_comprehensive_visualization(features_df, auroc_results)
    
    # Save results
    features_df.to_csv('length_volatility_features.csv', index=False)
    
    # Summary report
    print("\n📋 FINAL SUMMARY")
    print("=" * 30)
    print(f"Total series analyzed: {len(features_df)}")
    if 'label' in features_df.columns:
        print(f"Overall break rate: {features_df['label'].mean():.3f}")
    if 'volatility_cluster' in features_df.columns:
        print(f"Volatility clusters: {features_df['volatility_cluster'].nunique()}")
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Features: 'length_volatility_features.csv'")
    print(f"   📈 Visualization: 'length_volatility_auroc_analysis.png'")
    
    return features_df, auroc_results, correlations

if __name__ == "__main__":
    results = main()