#!/usr/bin/env python3
"""
VAE Autoencoder v1.3 with JSD and Wasserstein Latent Space Regularization
PyTorch CUDA implementation with differentiable distance layers
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
from sklearn.preprocessing import StandardScaler
import logging
from typing import Dict, Tuple
import warnings
from tqdm import tqdm

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

class JensenShannonLayer(nn.Module):
    """Differentiable Jensen-Shannon divergence layer"""
    
    def __init__(self, n_bins=20, temperature=10.0):
        super().__init__()
        self.n_bins = n_bins
        self.temperature = temperature
        
    def _soft_histogram(self, x):
        """Create soft histogram using sigmoid gates"""
        x_flat = x.view(-1)
        if len(x_flat) == 0:
            return torch.zeros(self.n_bins, device=x.device)
        
        x_min, x_max = x_flat.min(), x_flat.max()
        if x_min == x_max:
            # Handle constant values
            bin_probs = torch.zeros(self.n_bins, device=x.device)
            bin_probs[self.n_bins // 2] = 1.0
            return bin_probs
        
        bin_edges = torch.linspace(x_min, x_max, self.n_bins + 1, device=x.device)
        bin_probs = torch.zeros(self.n_bins, device=x.device)
        
        for i in range(self.n_bins):
            left_gate = torch.sigmoid(self.temperature * (x_flat - bin_edges[i]))
            right_gate = torch.sigmoid(self.temperature * (bin_edges[i+1] - x_flat))
            bin_probs[i] = (left_gate * right_gate).sum()
        
        return bin_probs / (bin_probs.sum() + 1e-8)
    
    def forward(self, x1, x2):
        """Calculate Jensen-Shannon divergence between two tensors"""
        p1 = self._soft_histogram(x1)
        p2 = self._soft_histogram(x2)
        
        m = 0.5 * (p1 + p2)
        js = 0.5 * (p1 * torch.log(p1 / (m + 1e-8) + 1e-8)).sum() + \
             0.5 * (p2 * torch.log(p2 / (m + 1e-8) + 1e-8)).sum()
        return js

# Wasserstein layer removed for computational efficiency

class TimeSeriesDistributionDataset(Dataset):
    """Dataset for time series distribution pairs"""
    
    def __init__(self, X_features, y_labels):
        self.X = torch.FloatTensor(X_features)
        self.y = torch.LongTensor(y_labels)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

class DistributionVAE(nn.Module):
    """VAE with JSD and Wasserstein latent space regularization"""
    
    def __init__(self, input_dim, latent_dim=32, hidden_dims=[128, 64]):
        super(DistributionVAE, self).__init__()
        
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        # Encoder
        encoder_layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Latent space
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_logvar = nn.Linear(hidden_dims[-1], latent_dim)
        
        # Decoder
        decoder_layers = []
        prev_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        decoder_layers.append(nn.Linear(hidden_dims[0], input_dim))
        self.decoder = nn.Sequential(*decoder_layers)
        
        # Classifier head
        self.classifier = nn.Sequential(
            nn.Linear(latent_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
        
        # NEW: JSD layer for latent space regularization
        self.jsd_layer = JensenShannonLayer(n_bins=16, temperature=8.0)
    
    def encode(self, x):
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)
        recon_x = self.decode(z)
        class_pred = self.classifier(z)
        
        # NEW: Split latent space to represent pre/post segments
        z_pre = z[:, :self.latent_dim//2]
        z_post = z[:, self.latent_dim//2:]
        
        # NEW: Calculate JSD in latent space (batch-wise)
        batch_jsd = []
        
        for i in range(z.size(0)):
            jsd = self.jsd_layer(z_pre[i], z_post[i])
            batch_jsd.append(jsd)
        
        latent_jsd = torch.stack(batch_jsd)
        
        return recon_x, class_pred, mu, logvar, latent_jsd

def load_and_prepare_data():
    """Load raw time series data and extract distribution features"""
    logger.info("📊 Loading raw time series data...")
    
    try:
        # Load raw data
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        logger.info(f"   ✅ Raw data loaded: {X_data.shape}")
        
        # Extract time series and labels
        time_series_list = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            grouped = X_data.groupby(level='id')
            for id_, group in grouped:
                series_data = group.sort_index(level='time')
                values = series_data['value'].values
                periods = series_data['period'].values
                
                # Find break point
                period_changes = np.where(np.diff(periods) != 0)[0]
                break_point = period_changes[0] + 1 if len(period_changes) > 0 else len(values) // 2
                
                time_series_list.append((values, break_point))
                
                label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                labels.append(int(label))
        
        logger.info(f"   ✅ Extracted {len(time_series_list)} time series")
        logger.info(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        
        return time_series_list, labels
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def extract_distribution_features(time_series_list, n_bins=50):
    """Extract distribution features from time series segments"""
    logger.info(f"🔧 Extracting distribution features (n_bins={n_bins})...")
    
    features_list = []
    
    for i, (series, break_point) in enumerate(tqdm(time_series_list, desc="Processing series")):
        if i % 1000 == 0:
            logger.info(f"   Processing {i+1}/{len(time_series_list)}...")
        
        series_clean = series[~np.isnan(series)]
        if len(series_clean) < 20:
            continue
        
        # Split into pre and post segments
        pre_segment = series_clean[:break_point]
        post_segment = series_clean[break_point:]
        
        if len(pre_segment) < 5 or len(post_segment) < 5:
            continue
        
        features = []
        
        # Pre-segment distribution
        pre_hist, _ = np.histogram(pre_segment, bins=n_bins, density=True)
        features.extend(pre_hist)
        
        # Post-segment distribution
        post_hist, _ = np.histogram(post_segment, bins=n_bins, density=True)
        features.extend(post_hist)
        
        # Comparative statistics
        features.extend([
            np.mean(pre_segment), np.std(pre_segment), np.median(pre_segment),
            np.mean(post_segment), np.std(post_segment), np.median(post_segment),
            np.mean(post_segment) - np.mean(pre_segment),  # Mean difference
            np.std(post_segment) / (np.std(pre_segment) + 1e-8),  # Std ratio
            np.percentile(pre_segment, 25), np.percentile(pre_segment, 75),
            np.percentile(post_segment, 25), np.percentile(post_segment, 75)
        ])
        
        # KL divergence approximation
        pre_hist_smooth = pre_hist + 1e-8
        post_hist_smooth = post_hist + 1e-8
        kl_div = np.sum(pre_hist_smooth * np.log(pre_hist_smooth / post_hist_smooth))
        features.append(kl_div)
        
        # Wasserstein distance approximation (simplified)
        wasserstein_approx = np.sum(np.abs(np.cumsum(pre_hist) - np.cumsum(post_hist)))
        features.append(wasserstein_approx)
        
        features_list.append(features)
    
    features_array = np.array(features_list)
    logger.info(f"   ✅ Extracted features shape: {features_array.shape}")
    
    return features_array

def vae_loss_function(recon_x, x, mu, logvar, class_pred, y_true, 
                     latent_jsd, beta=1.0, alpha=1.0, gamma=0.1):
    """Enhanced VAE loss with JSD latent space regularization"""
    # Reconstruction loss
    recon_loss = nn.MSELoss()(recon_x, x)
    
    # KL divergence loss
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    kl_loss /= x.size(0) * x.size(1)  # Normalize by batch size and feature dim
    
    # Classification loss
    class_pred_flat = class_pred.view(-1)
    y_true_flat = y_true.float().view(-1)
    class_loss = nn.BCELoss()(class_pred_flat, y_true_flat)
    
    # NEW: JSD-based regularization in latent space
    # Encourage high JSD for structural breaks (positive samples)
    jsd_reg = torch.mean(latent_jsd * y_true_flat)
    
    # Combined loss
    total_loss = recon_loss + beta * kl_loss + alpha * class_loss + gamma * jsd_reg
    
    return total_loss, recon_loss, kl_loss, class_loss, jsd_reg

def train_vae_model(model, train_loader, val_loader, params, n_epochs=5):
    """Train VAE model with distance regularization and early stopping"""
    optimizer = optim.Adam(model.parameters(), lr=params['lr'], weight_decay=params['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    best_val_auc = 0
    patience_counter = 0
    patience = 15
    
    for epoch in tqdm(range(n_epochs)):
        # Training
        model.train()
        train_loss = 0
        train_preds = []
        train_targets = []
        
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            recon_x, class_pred, mu, logvar, latent_jsd = model(batch_x)
            
            loss, recon_loss, kl_loss, class_loss, jsd_reg = vae_loss_function(
                recon_x, batch_x, mu, logvar, class_pred, batch_y,
                latent_jsd,
                beta=params['beta'], alpha=params['alpha'], 
                gamma=params.get('gamma', 0.1)
            )
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_preds.extend(class_pred.cpu().detach().numpy())
            train_targets.extend(batch_y.cpu().numpy())
        
        # Validation
        model.eval()
        val_loss = 0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                
                recon_x, class_pred, mu, logvar, latent_jsd = model(batch_x)
                loss, _, _, _, _ = vae_loss_function(
                    recon_x, batch_x, mu, logvar, class_pred, batch_y,
                    latent_jsd,
                    beta=params['beta'], alpha=params['alpha'], 
                    gamma=params.get('gamma', 0.1)
                )
                
                val_loss += loss.item()
                val_preds.extend(class_pred.cpu().numpy())
                val_targets.extend(batch_y.cpu().numpy())
        
        # Calculate metrics
        val_auc = roc_auc_score(val_targets, val_preds)
        scheduler.step(val_loss)
        
        # Early stopping
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            break
    
    return best_val_auc

def optimize_vae_hyperparameters(X_features, y_labels, n_trials=50, n_folds=5):
    """Optimize VAE hyperparameters with distance regularization"""
    logger.info("🔧 Optimizing VAE hyperparameters with JSD/Wasserstein regularization...")
    
    def objective(trial):
        # Suggest hyperparameters
        params = {
            'latent_dim': trial.suggest_int('latent_dim', 16, 64),
            'hidden_dim1': trial.suggest_int('hidden_dim1', 64, 256),
            'hidden_dim2': trial.suggest_int('hidden_dim2', 32, 128),
            'lr': trial.suggest_float('lr', 1e-4, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [1024, 2048, 3072]),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
            'beta': trial.suggest_float('beta', 0.1, 2.0),
            'alpha': trial.suggest_float('alpha', 0.5, 3.0),
            'gamma': trial.suggest_float('gamma', 0.01, 0.5),  # JSD regularization
            # 'delta': removed since Wasserstein layer was excluded
        }
        
        # Cross-validation
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        cv_scores = []
        
        for train_idx, val_idx in skf.split(X_features, y_labels):
            X_train, X_val = X_features[train_idx], X_features[val_idx]
            y_train, y_val = y_labels[train_idx], y_labels[val_idx]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Create datasets
            train_dataset = TimeSeriesDistributionDataset(X_train_scaled, y_train)
            val_dataset = TimeSeriesDistributionDataset(X_val_scaled, y_val)
            
            train_loader = DataLoader(train_dataset, batch_size=params['batch_size'], shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=params['batch_size'], shuffle=False)
            
            # Create model
            model = DistributionVAE(
                input_dim=X_features.shape[1],
                latent_dim=params['latent_dim'],
                hidden_dims=[params['hidden_dim1'], params['hidden_dim2']]
            ).to(device)
            
            # Train model
            val_auc = train_vae_model(model, train_loader, val_loader, params, n_epochs=5)
            cv_scores.append(val_auc)
            print(cv_scores)
            # Clean up GPU memory
            del model
            torch.cuda.empty_cache()
        
        return np.mean(cv_scores)
    
    # Create and run study
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best AUC: {study.best_value:.4f}")
    logger.info(f"   📊 Best params: {study.best_params}")
    
    return study.best_params, study.best_value

def evaluate_vae_comprehensive(X_features, y_labels, best_params, n_folds=5):
    """Comprehensive evaluation of distance-regularized VAE model"""
    logger.info("📊 Comprehensive VAE evaluation with JSD/Wasserstein regularization...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_features, y_labels)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X_features[train_idx], X_features[val_idx]
        y_train, y_val = y_labels[train_idx], y_labels[val_idx]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # Create datasets
        train_dataset = TimeSeriesDistributionDataset(X_train_scaled, y_train)
        val_dataset = TimeSeriesDistributionDataset(X_val_scaled, y_val)
        
        train_loader = DataLoader(train_dataset, batch_size=best_params['batch_size'], shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=best_params['batch_size'], shuffle=False)
        
        # Create and train model
        model = DistributionVAE(
            input_dim=X_features.shape[1],
            latent_dim=best_params['latent_dim'],
            hidden_dims=[best_params['hidden_dim1'], best_params['hidden_dim2']]
        ).to(device)
        
        # Train model
        train_vae_model(model, train_loader, val_loader, best_params, n_epochs=5)
        
        # Evaluate
        model.eval()
        fold_preds = []
        fold_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                _, class_pred, _, _, _ = model(batch_x)
                
                fold_preds.extend(class_pred.cpu().numpy())
                fold_targets.extend(batch_y.cpu().numpy())
        
        # Calculate metrics
        y_pred_binary = (np.array(fold_preds) >= 0.5).astype(int)
        
        auc = roc_auc_score(fold_targets, fold_preds)
        f1 = f1_score(fold_targets, y_pred_binary)
        precision = precision_score(fold_targets, y_pred_binary, zero_division=0)
        recall = recall_score(fold_targets, y_pred_binary)
        accuracy = accuracy_score(fold_targets, y_pred_binary)
        
        # Confusion matrix
        tn, fp, fn, tp = confusion_matrix(fold_targets, y_pred_binary).ravel()
        
        # Store metrics
        all_metrics['auc'].append(auc)
        all_metrics['f1'].append(f1)
        all_metrics['precision'].append(precision)
        all_metrics['recall'].append(recall)
        all_metrics['accuracy'].append(accuracy)
        all_metrics['tp'].append(tp)
        all_metrics['tn'].append(tn)
        all_metrics['fp'].append(fp)
        all_metrics['fn'].append(fn)
        
        # Store for overall analysis
        all_y_true.extend(fold_targets)
        all_y_pred_proba.extend(fold_preds)
        
        logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
        
        # Clean up
        del model
        torch.cuda.empty_cache()
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
    overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba)
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting VAE v1.3 with JSD/Wasserstein Latent Space Regularization...")
    
    # Load and prepare data
    time_series_list, labels = load_and_prepare_data()
    
    # Extract distribution features
    X_features = extract_distribution_features(time_series_list, n_bins=20)
    y_labels = np.array(labels[:len(X_features)])  # Match lengths
    
    logger.info(f"📊 Final dataset: {X_features.shape[0]} samples, {X_features.shape[1]} features")
    logger.info(f"   📈 Enhanced with JSD and Wasserstein latent space regularization")
    
    # Optimize hyperparameters
    best_params, best_auc = optimize_vae_hyperparameters(X_features, y_labels, n_trials=5, n_folds=5)
    
    # Comprehensive evaluation
    results = evaluate_vae_comprehensive(X_features, y_labels, best_params, n_folds=5)
    
    # Print final results
    logger.info("\n🎉 VAE v1.3 ANALYSIS COMPLETED!")
    logger.info("=" * 70)
    logger.info(f"📊 FINAL RESULTS (with JSD/Wasserstein regularization):")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Save results
    import joblib
    final_results = {
        'best_params': best_params,
        'evaluation_results': results,
        'feature_shape': X_features.shape,
        'version': 'v1.3_jsd_wasserstein_latent'
    }
    joblib.dump(final_results, 'vae_autoencoder_results.joblib')
    logger.info("💾 Results saved to 'vae_autoencoder_results.joblib'")
    
    return final_results

if __name__ == "__main__":
    results = main()