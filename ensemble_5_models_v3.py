#!/usr/bin/env python3
"""
Ensemble V3 - CatBoost Only with Advanced Optuna Pruning
Based on advanced pruning techniques for efficient hyperparameter optimization
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, log_loss, f1_score, precision_score, recall_score, accuracy_score
import catboost as cb
import optuna
import logging
import warnings

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_prepared_features():
    """Load prepared features and labels separately"""
    logger.info("📊 Loading prepared_features.parquet and y_train.parquet...")
    
    try:
        X = pd.read_parquet('resources/processed_data/prepared_features.parquet')
        y = pd.read_parquet('y_train.parquet')
        
        if isinstance(y.index, pd.MultiIndex):
            y = y.reset_index(drop=True).iloc[:, 0] if y.shape[1] == 1 else y.reset_index(drop=True)
        else:
            y = y.iloc[:, 0] if y.shape[1] == 1 else y.squeeze()
        
        min_len = min(len(X), len(y))
        X = X.iloc[:min_len]
        y = y.iloc[:min_len] if hasattr(y, 'iloc') else y[:min_len]
        
        logger.info(f"   ✅ Features: {X.shape[0]} samples, {X.shape[1]} features")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def create_catboost_objective(X_train, y_train, cv_folds=5, early_stopping_rounds=50, verbose=False):
    """Creates an Optuna objective function for CatBoost hyperparameter optimization with pruning"""
    
    def objective(trial):
        early_stopping_count = []
        
        # Core tree structure parameters
        depth = trial.suggest_int('depth', 4, 10)
        min_data_in_leaf = trial.suggest_int('min_data_in_leaf', 1, 20)
        
        # Learning parameters
        learning_rate = trial.suggest_float('learning_rate', 0.001, 0.07, log=True)
        iterations = trial.suggest_int('iterations', 500, 3000)
        
        # Regularization parameters
        l2_leaf_reg = trial.suggest_float('l2_leaf_reg', 0.01, 1.0)
        model_size_reg = trial.suggest_float('model_size_reg', 0.1, 1.0)
        
        # Sampling parameters (rsm not supported on GPU for classification)
        
        # Bootstrap and sampling
        bootstrap_type = trial.suggest_categorical('bootstrap_type', ['Bayesian', 'Bernoulli', 'MVS'])
        
        # Bootstrap-specific parameters
        bootstrap_params = {}
        if bootstrap_type == 'Bayesian':
            bootstrap_params['bagging_temperature'] = trial.suggest_float('bagging_temperature', 0.0, 10.0)
            # Add langevin for Bayesian
            bootstrap_params['langevin'] = trial.suggest_categorical('langevin', [True, False])
        elif bootstrap_type in ['Bernoulli', 'MVS']:
            bootstrap_params['subsample'] = trial.suggest_float('subsample', 0.6, 1.0)
        
        # Border and feature processing
        border_count = trial.suggest_int('border_count', 128, 255)
        feature_border_type = trial.suggest_categorical('feature_border_type', 
            ['Median', 'Uniform', 'UniformAndQuantiles', 'MaxLogSum', 'MinEntropy', 'GreedyLogSum'])
        
        # Growth policy and leaf estimation
        grow_policy = trial.suggest_categorical('grow_policy', ['SymmetricTree', 'Depthwise', 'Lossguide'])
        leaf_estimation_method = trial.suggest_categorical('leaf_estimation_method',
            ['Newton', 'Gradient'])
        leaf_estimation_iterations = trial.suggest_int('leaf_estimation_iterations', 1, 20)
        
        # Score function for leaf value calculation
        score_function = trial.suggest_categorical('score_function',
            ['Cosine', 'L2'])
        
        # Advanced parameters
        random_strength = trial.suggest_float('random_strength', 0.1, 10.0)
        
        # Construct CatBoost parameters
        params = {
            # Core parameters
            'task_type': 'CPU',
            'loss_function': 'Logloss',
            'eval_metric': 'AUC',
            'random_seed': 42,
            'verbose': False,
            
            # Optimized parameters
            'depth': depth,
            'min_data_in_leaf': min_data_in_leaf,
            'learning_rate': learning_rate,
            'iterations': iterations,
            'l2_leaf_reg': l2_leaf_reg,
            'model_size_reg': model_size_reg,

            'bootstrap_type': bootstrap_type,
            'border_count': border_count,
            'feature_border_type': feature_border_type,
            'grow_policy': grow_policy,
            'leaf_estimation_method': leaf_estimation_method,
            'leaf_estimation_iterations': leaf_estimation_iterations,
            'score_function': score_function,
            'random_strength': random_strength,
            'early_stopping_rounds': early_stopping_rounds,
            
            # Fixed beneficial parameters
            'nan_mode': 'Min',
            'boosting_type': 'Plain',
        }
        
        # Add bootstrap-specific parameters
        params.update(bootstrap_params)
        
        try:
            # Cross-validation with pruning support
            skf = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
            scores = []
            
            for fold_idx, (train_idx, val_idx) in enumerate(skf.split(X_train, y_train)):
                X_tr, X_vl = X_train.iloc[train_idx], X_train.iloc[val_idx]
                y_tr, y_vl = y_train.iloc[train_idx], y_train.iloc[val_idx]
                
                temp_model = cb.CatBoostClassifier(**params)
                temp_model.fit(
                    X_tr, y_tr,
                    eval_set=(X_vl, y_vl),
                    use_best_model=True,
                    verbose=False
                )
                
                # Track early stopping iterations
                best_iteration = 1 if temp_model.best_iteration_ == 0 else temp_model.best_iteration_
                early_stopping_count.append(best_iteration)
                
                y_pred_proba = temp_model.predict_proba(X_vl)[:, 1]
                fold_auc = roc_auc_score(y_vl, y_pred_proba)
                scores.append(fold_auc)
                
                # Report intermediate value for pruning
                trial.report(np.mean(scores[:fold_idx+1]), fold_idx)
                
                # Check if trial should be pruned
                if trial.should_prune():
                    raise optuna.exceptions.TrialPruned()
            
            mean_auc = np.mean(scores)
            
            # Set user attribute for optimal iterations
            if early_stopping_count:
                trial.set_user_attr('c', np.max(early_stopping_count))
            
            if verbose:
                print(f"Trial completed. AUC: {mean_auc:.6f}")
            
            # Return negative AUC for minimization
            return mean_auc
            
        except optuna.exceptions.TrialPruned:
            raise
        except Exception as e:
            if verbose:
                print(f"Trial failed with error: {str(e)}")
            return float('inf')
    
    return objective

def optimize_catboost(X_train, y_train, n_trials=100, timeout=None, cv_folds=5,
                     early_stopping_rounds=50, verbose=True):
    """Optimize CatBoost hyperparameters using advanced Optuna pruning"""
    
    logger.info(f"🔧 Starting CatBoost optimization with {n_trials} trials...")
    
    # Create objective function
    objective = create_catboost_objective(X_train, y_train, cv_folds, early_stopping_rounds, verbose)
    
    # Create study with advanced pruning
    pruner = optuna.pruners.MedianPruner(
        n_startup_trials=10,
        n_warmup_steps=2,
        interval_steps=1
    )
    
    sampler = optuna.samplers.TPESampler(
        n_startup_trials=10,
        n_ei_candidates=24,
        seed=42
    )
    
    study = optuna.create_study(
        direction='maximize',
        pruner=pruner,
        sampler=sampler,
        study_name="catboost_optimization"
    )
    
    # Optimize
    study.optimize(
        objective, 
        n_trials=n_trials, 
        timeout=timeout,
        show_progress_bar=True
    )
    
    logger.info(f"   ✅ Best AUC: {study.best_value:.6f}")
    logger.info(f"   📊 Best params: {study.best_params}")
    
    # Get best params and add optimal iterations
    best_params = study.best_params.copy()
    if 'c' in study.best_trial.user_attrs:
        best_params['c'] = study.best_trial.user_attrs['c']
        logger.info(f"   📊 Optimal iterations: {best_params['c']}")
    
    return study, best_params

def build_final_model(best_params, X_train, y_train):
    """Build final CatBoost model with optimized parameters"""
    logger.info("🎯 Building final CatBoost model...")
    
    # Construct final parameters
    final_params = {
        'task_type': 'GPU',
        'loss_function': 'Logloss',
        'eval_metric': 'AUC',
        'random_seed': 42,
        'verbose': True,
        'nan_mode': 'Min',
        'boosting_type': 'Plain',
    }
    
    # Add bootstrap-specific parameters
    if best_params.get('bootstrap_type') == 'Bayesian':
        if 'bagging_temperature' in best_params:
            final_params['bagging_temperature'] = best_params['bagging_temperature']
        if 'langevin' in best_params:
            final_params['langevin'] = best_params['langevin']
    elif best_params.get('bootstrap_type') in ['Bernoulli', 'MVS']:
        if 'subsample' in best_params:
            final_params['subsample'] = best_params['subsample']
    
    # Use exact iterations if available
    if 'c' in best_params:
        final_params['iterations'] = best_params['c']
        final_params.pop('early_stopping_rounds', None)
    else:
        final_params['iterations'] = best_params.get('iterations', 1000)
    
    # Update with optimized parameters
    final_params.update({k: v for k, v in best_params.items() if k != 'c'})
    
    # Create and train final model
    model = cb.CatBoostClassifier(**final_params)
    model.fit(X_train, y_train)
    
    logger.info("   ✅ Final model trained")
    return model

def evaluate_model_cv(model_params, X, y, n_folds=5):
    """Evaluate model with cross-validation"""
    logger.info("📊 Evaluating model with cross-validation...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    cv_aucs = []
    cv_f1s = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # Create model with optimized params
        model = cb.CatBoostClassifier(**model_params)
        model.fit(X_train, y_train, verbose=False)
        
        # Predict
        y_pred_proba = model.predict_proba(X_val)[:, 1]
        y_pred_binary = (y_pred_proba >= 0.5).astype(int)
        
        # Calculate metrics
        auc = roc_auc_score(y_val, y_pred_proba)
        f1 = f1_score(y_val, y_pred_binary)
        
        cv_aucs.append(auc)
        cv_f1s.append(f1)
        
        logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}")
    
    mean_auc = np.mean(cv_aucs)
    std_auc = np.std(cv_aucs)
    mean_f1 = np.mean(cv_f1s)
    std_f1 = np.std(cv_f1s)
    
    logger.info(f"\n📈 Cross-Validation Results:")
    logger.info(f"   AUC: {mean_auc:.4f} ± {std_auc:.4f}")
    logger.info(f"   F1:  {mean_f1:.4f} ± {std_f1:.4f}")
    
    return {
        'mean_auc': mean_auc,
        'std_auc': std_auc,
        'mean_f1': mean_f1,
        'std_f1': std_f1,
        'cv_aucs': cv_aucs,
        'cv_f1s': cv_f1s
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting CatBoost V3 with Advanced Pruning...")
    
    # Load data
    X, y = load_prepared_features()
    
    # Optimize CatBoost hyperparameters
    study, best_params = optimize_catboost(
        X, y, 
        n_trials=50,
        cv_folds=5,
        early_stopping_rounds=50,
        verbose=True
    )
    
    # Build final model parameters
    final_params = best_params.copy()
    
    # Add fixed parameters
    final_params.update({
        'task_type': 'GPU',
        'loss_function': 'Logloss',
        'eval_metric': 'AUC',
        'random_seed': 42,
        'verbose': False,
        'nan_mode': 'Min',
        'boosting_type': 'Plain',
    })
    
    # Use exact iterations if available
    if 'c' in best_params:
        final_params['iterations'] = best_params['c']
        final_params.pop('early_stopping_rounds', None)
    
    # Evaluate with cross-validation
    cv_results = evaluate_model_cv(final_params, X, y, n_folds=5)
    
    # Print final results
    logger.info("\n🎉 CATBOOST V3 OPTIMIZATION COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   Cross-Validation AUC: {cv_results['mean_auc']:.4f} ± {cv_results['std_auc']:.4f}")
    logger.info(f"   Cross-Validation F1:  {cv_results['mean_f1']:.4f} ± {cv_results['std_f1']:.4f}")
    
    # Save results
    import joblib
    final_results = {
        'study': study,
        'best_params': best_params,
        'final_params': final_params,
        'cv_results': cv_results,
        'approach': 'catboost_v3_advanced_pruning'
    }
    joblib.dump(final_results, 'catboost_v3_results.joblib')
    logger.info("💾 Results saved to 'catboost_v3_results.joblib'")
    
    return final_results

if __name__ == "__main__":
    results = main()