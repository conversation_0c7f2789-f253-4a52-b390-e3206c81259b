import numpy as np
import pandas as pd
from tsfresh.feature_extraction.feature_calculators import (
    energy_ratio_by_chunks, fft_aggregated, index_mass_quantile,
    binned_entropy, last_location_of_maximum, lempel_ziv_complexity,
    longest_strike_above_mean, longest_strike_below_mean,
    mean_second_derivative_central, percentage_of_reoccurring_datapoints_to_all_datapoints,
    percentage_of_reoccurring_values_to_all_values, ratio_beyond_r_sigma,
    ratio_value_number_to_time_series_length
)
from scipy.stats import (norm, kstest, anderson_ksamp, cramervonmises_2samp, wasserstein_distance, 
                        mannwhitneyu, kendalltau, spearmanr, pearsonr, normaltest, entropy)
from statsmodels.stats.diagnostic import lilliefors
from statsmodels.tsa.stattools import coint, grangercausalitytests
from statsmodels.stats.stattools import jarque_bera
from statsmodels.regression.linear_model import OLS
import pycatch22
from scipy.signal import (periodogram, welch, spectrogram, find_peaks, peak_prominences, 
                         savgol_filter, detrend, hilbert, coherence, correlate, correlation_lags, 
                         fftconvolve, csd)
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
from arch import arch_model
import warnings
from multiprocessing import Pool
from functools import partial
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def jensen_shannon_divergence(pre, post):
    """Jensen-Shannon divergence between two distributions"""
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        pre, post = pre[~np.isnan(pre)], post[~np.isnan(post)]
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else np.ones_like(hist_pre) / len(hist_pre)
        hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else np.ones_like(hist_post) / len(hist_post)
        m = 0.5 * (hist_pre + hist_post)
        jsd = 0.5 * (entropy(hist_pre, m) + entropy(hist_post, m))
        return jsd if not np.isnan(jsd) else np.nan
    except Exception:
        return np.nan

def kl_divergence(pre, post):
    """Kullback-Leibler divergence"""
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        pre, post = pre[~np.isnan(pre)], post[~np.isnan(post)]
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else np.ones_like(hist_pre) / len(hist_pre)
        hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else np.ones_like(hist_post) / len(hist_post)
        # Add small epsilon to avoid log(0)
        eps = 1e-10
        hist_pre = hist_pre + eps
        hist_post = hist_post + eps
        return entropy(hist_pre, hist_post)
    except Exception:
        return np.nan

def compute_enhanced_divergence_tests(pre, post, segment_name_prefix=''):
    """Enhanced statistical divergence tests including new v4 features"""
    tests = {}
    
    if len(pre) < 5 or len(post) < 5:
        return {f'{segment_name_prefix}{k}': np.nan for k in [
            'jsd', 'kl_divergence', 'wasserstein_distance', 'ad_stat', 'cvm_stat', 
            'p_ks', 'p_ad', 'p_mannwhitney', 'kendall_tau', 'spearman_rho', 
            'pearson_r', 'dagostino_pearson_stat', 'granger_p'
        ]}
    
    pre_clean = pre[~np.isnan(pre)]
    post_clean = post[~np.isnan(post)]
    
    # Original divergence tests
    tests[f'{segment_name_prefix}jsd'] = jensen_shannon_divergence(pre_clean, post_clean)
    tests[f'{segment_name_prefix}kl_divergence'] = kl_divergence(pre_clean, post_clean)
    
    try:
        tests[f'{segment_name_prefix}wasserstein_distance'] = wasserstein_distance(pre_clean, post_clean)
    except Exception:
        tests[f'{segment_name_prefix}wasserstein_distance'] = np.nan
    
    try:
        ad_result = anderson_ksamp([pre_clean, post_clean])
        tests[f'{segment_name_prefix}ad_stat'] = ad_result.statistic
        tests[f'{segment_name_prefix}p_ad'] = ad_result.significance_level if hasattr(ad_result, 'significance_level') else np.nan
    except Exception:
        tests[f'{segment_name_prefix}ad_stat'] = np.nan
        tests[f'{segment_name_prefix}p_ad'] = np.nan
    
    try:
        cvm_result = cramervonmises_2samp(pre_clean, post_clean)
        tests[f'{segment_name_prefix}cvm_stat'] = cvm_result.statistic
    except Exception:
        tests[f'{segment_name_prefix}cvm_stat'] = np.nan
    
    try:
        ks_stat, p_ks = stats.ks_2samp(pre_clean, post_clean)
        tests[f'{segment_name_prefix}p_ks'] = p_ks
    except Exception:
        tests[f'{segment_name_prefix}p_ks'] = np.nan
    
    try:
        u_stat, p_mw = mannwhitneyu(pre_clean, post_clean, alternative='two-sided')
        tests[f'{segment_name_prefix}p_mannwhitney'] = p_mw
    except Exception:
        tests[f'{segment_name_prefix}p_mannwhitney'] = np.nan
    
    # NEW V4 CORRELATION FEATURES
    try:
        tau, p_tau = kendalltau(pre_clean, post_clean)
        tests[f'{segment_name_prefix}kendall_tau'] = tau
    except Exception:
        tests[f'{segment_name_prefix}kendall_tau'] = np.nan
    
    try:
        rho, p_rho = spearmanr(pre_clean, post_clean)
        tests[f'{segment_name_prefix}spearman_rho'] = rho
    except Exception:
        tests[f'{segment_name_prefix}spearman_rho'] = np.nan
    
    try:
        r, p_r = pearsonr(pre_clean, post_clean)
        tests[f'{segment_name_prefix}pearson_r'] = r
    except Exception:
        tests[f'{segment_name_prefix}pearson_r'] = np.nan
    
    # D'Agostino-Pearson Test
    try:
        combined = np.concatenate([pre_clean, post_clean])
        stat, p_val = normaltest(combined)
        tests[f'{segment_name_prefix}dagostino_pearson_stat'] = stat
    except Exception:
        tests[f'{segment_name_prefix}dagostino_pearson_stat'] = np.nan
    
    # Granger Causality
    if len(pre_clean) > 30 and len(post_clean) > 30:
        try:
            min_len = min(len(pre_clean), len(post_clean))
            data = np.column_stack((pre_clean[:min_len], post_clean[:min_len]))
            gc_result = grangercausalitytests(data, maxlag=1, verbose=False)
            tests[f'{segment_name_prefix}granger_p'] = gc_result[1][0]['ssr_ftest'][1]
        except Exception:
            tests[f'{segment_name_prefix}granger_p'] = np.nan
    else:
        tests[f'{segment_name_prefix}granger_p'] = np.nan
    
    return tests

def compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix=''):
    """Compute all 4 relative measures for any pre/post feature pair"""
    relatives = {}
    
    relatives[f'{segment_name_prefix}ratio_{feature_name}'] = (
        post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
    )
    
    if pre_val and pre_val != 0 and not np.isnan(post_val):
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = abs(post_val - pre_val) / abs(pre_val)
    else:
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = np.nan
        
    if pre_val and pre_val > 0 and post_val and post_val > 0:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.log(post_val / pre_val)
    else:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.nan
        
    if not np.isnan(pre_val) and not np.isnan(post_val) and (abs(pre_val) + abs(post_val)) > 0:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = (post_val - pre_val) / (abs(post_val) + abs(pre_val))
    else:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = np.nan
        
    return relatives

def compute_normalization_features(feature_dict, whole_series, segment_name_prefix='', tstar=None):
    """Add _norm_zscore and _weighted variants of features"""
    normalized_features = {}
    
    mean_whole = np.nanmean(whole_series)
    std_whole = np.nanstd(whole_series, ddof=1) if len(whole_series) > 1 else np.nan
    
    # if tstar is not None and 0 < tstar < len(whole_series):
    w_i = (len(whole_series) - tstar) / len(whole_series)
    # else:
    #     w_i = 0.5
    
    for feature_name, feature_value in feature_dict.items():
        if not np.isnan(feature_value) and std_whole and std_whole > 0:
            normalized_features[f'{feature_name}_norm_zscore'] = (feature_value - mean_whole) / std_whole
            normalized_features[f'{feature_name}_weighted'] = w_i * feature_value
        else:
            normalized_features[f'{feature_name}_norm_zscore'] = np.nan
            normalized_features[f'{feature_name}_weighted'] = np.nan
    
    return normalized_features

def safe_adf_stat(arr):
    try:
        if len(arr) < 10:
            return np.nan
        return adfuller(arr[~np.isnan(arr)], maxlag=1)[0]
    except Exception:
        return np.nan

def safe_kpss_stat(arr):
    try:
        if len(arr) < 10:
            return np.nan
        return kpss(arr[~np.isnan(arr)], nlags='auto')[0]
    except Exception:
        return np.nan

def compute_tsfresh_feature(feature_func, arr, params=None, min_length=2):
    try:
        if len(arr) < min_length:
            return np.nan
        if params:
            result = feature_func(arr, params)
            if isinstance(result, list) and len(result) > 0:
                return float(result[0][1]) if len(result[0]) > 1 else float(result)
            return float(result)
        return float(feature_func(arr))
    except Exception:
        return np.nan

def compute_features_for_segment_v4(pre, post, whole, segment_name_prefix='', tstar=None):
    """V4: Complete v3 features + Cross-Spectral Density, Phase Coherence, and advanced correlation features"""
    feats = {}
    
    if len(pre) < 5 or len(post) < 5:
        return feats
    
    # Define segments
    mean_whole = np.nanmean(whole)
    above_mean = whole[whole > mean_whole]
    below_mean = whole[whole < mean_whole]
    pre_above_mean = pre[pre > np.nanmean(pre)]
    pre_below_mean = pre[pre < np.nanmean(pre)]
    post_above_mean = post[post > np.nanmean(post)]
    post_below_mean = post[post < np.nanmean(post)]

    # 1. ENHANCED STATISTICAL DIVERGENCE TESTS (V4) - includes all v3 + new v4 features
    divergence_tests = compute_enhanced_divergence_tests(pre, post, segment_name_prefix)
    feats.update(divergence_tests)
    
    # Above/below mean comparisons
    if len(above_mean) >= 5 and len(below_mean) >= 5:
        above_below_tests = compute_enhanced_divergence_tests(
            above_mean, below_mean, f'{segment_name_prefix}above_below_'
        )
        feats.update(above_below_tests)
    
    # Pre above/below mean comparisons
    if len(pre_above_mean) >= 5 and len(pre_below_mean) >= 5:
        pre_above_below_tests = compute_enhanced_divergence_tests(
            pre_above_mean, pre_below_mean, f'{segment_name_prefix}pre_above_below_'
        )
        feats.update(pre_above_below_tests)
    
    # Post above/below mean comparisons
    if len(post_above_mean) >= 5 and len(post_below_mean) >= 5:
        post_above_below_tests = compute_enhanced_divergence_tests(
            post_above_mean, post_below_mean, f'{segment_name_prefix}post_above_below_'
        )
        feats.update(post_above_below_tests)

    # 2. NEW V4 SPECTRAL FEATURES
    
    # Cross-Spectral Density
    if len(pre) > 10 and len(post) > 10:
        try:
            f, Pxy = csd(pre, post, nperseg=min(100, len(pre)//2))
            feats[f'{segment_name_prefix}mean_csd_magnitude'] = np.mean(np.abs(Pxy))
            feats[f'{segment_name_prefix}max_csd_magnitude'] = np.max(np.abs(Pxy))
        except Exception:
            feats[f'{segment_name_prefix}mean_csd_magnitude'] = np.nan
            feats[f'{segment_name_prefix}max_csd_magnitude'] = np.nan
    else:
        feats[f'{segment_name_prefix}mean_csd_magnitude'] = np.nan
        feats[f'{segment_name_prefix}max_csd_magnitude'] = np.nan
    
    # Phase Coherence
    if len(pre) > 10 and len(post) > 10:
        try:
            f, Cxy = coherence(pre, post, nperseg=min(100, len(pre)//2))
            # Phase coherence from cross-spectral density
            f_csd, Pxy = csd(pre, post, nperseg=min(100, len(pre)//2))
            phase_coherence = np.abs(Pxy) / (np.sqrt(np.abs(csd(pre, pre, nperseg=min(100, len(pre)//2))[1])) * 
                                           np.sqrt(np.abs(csd(post, post, nperseg=min(100, len(post)//2))[1])))
            feats[f'{segment_name_prefix}mean_phase_coherence'] = np.mean(phase_coherence)
            feats[f'{segment_name_prefix}max_phase_coherence'] = np.max(phase_coherence)
        except Exception:
            feats[f'{segment_name_prefix}mean_phase_coherence'] = np.nan
            feats[f'{segment_name_prefix}max_phase_coherence'] = np.nan
    else:
        feats[f'{segment_name_prefix}mean_phase_coherence'] = np.nan
        feats[f'{segment_name_prefix}max_phase_coherence'] = np.nan
    
    # Spectral Granger Causality (simplified frequency domain version)
    if len(pre) > 20 and len(post) > 20:
        try:
            # Compute power spectral densities
            f_pre, Pxx_pre = welch(pre, nperseg=min(100, len(pre)//2))
            f_post, Pxx_post = welch(post, nperseg=min(100, len(post)//2))
            f_csd, Pxy = csd(pre, post, nperseg=min(100, len(pre)//2))
            
            # Spectral coherence as proxy for spectral Granger causality
            spectral_coherence = np.abs(Pxy)**2 / (Pxx_pre * Pxx_post + 1e-10)
            feats[f'{segment_name_prefix}mean_spectral_granger'] = np.mean(spectral_coherence)
            feats[f'{segment_name_prefix}max_spectral_granger'] = np.max(spectral_coherence)
        except Exception:
            feats[f'{segment_name_prefix}mean_spectral_granger'] = np.nan
            feats[f'{segment_name_prefix}max_spectral_granger'] = np.nan
    else:
        feats[f'{segment_name_prefix}mean_spectral_granger'] = np.nan
        feats[f'{segment_name_prefix}max_spectral_granger'] = np.nan

    # 3. LENGTH FEATURES - All 4 relatives (from v3)
    len_pre, len_post, len_whole = len(pre), len(post), len(whole)
    len_above, len_below = len(above_mean), len(below_mean)
    len_pre_above, len_pre_below = len(pre_above_mean), len(pre_below_mean)
    len_post_above, len_post_below = len(post_above_mean), len(post_below_mean)
    
    length_features = [
        ('post_pre_length', len_pre, len_post),
        ('above_below_length', len_below, len_above),
        ('pre_above_below_length', len_pre_below, len_pre_above),
        ('post_above_below_length', len_post_below, len_post_above),
        ('above_length_change', len_pre_above, len_post_above),
        ('below_length_change', len_pre_below, len_post_below)
    ]
    
    for feature_name, pre_val, post_val in length_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 4. CONDITIONAL EXPECTATION FEATURES - Enhanced with divergence tests (from v3)
    cond_exp_above_pre = np.nanmean(pre_above_mean) if len(pre_above_mean) > 0 else np.nan
    cond_exp_above_post = np.nanmean(post_above_mean) if len(post_above_mean) > 0 else np.nan
    cond_exp_below_pre = np.nanmean(pre_below_mean) if len(pre_below_mean) > 0 else np.nan
    cond_exp_below_post = np.nanmean(post_below_mean) if len(post_below_mean) > 0 else np.nan
    cond_exp_whole_above = np.nanmean(above_mean) if len(above_mean) > 0 else np.nan
    cond_exp_whole_below = np.nanmean(below_mean) if len(below_mean) > 0 else np.nan
    
    # Store conditional expectation values for normalization
    conditional_values = {
        f'{segment_name_prefix}cond_exp_above_pre': cond_exp_above_pre,
        f'{segment_name_prefix}cond_exp_above_post': cond_exp_above_post,
        f'{segment_name_prefix}cond_exp_below_pre': cond_exp_below_pre,
        f'{segment_name_prefix}cond_exp_below_post': cond_exp_below_post,
        f'{segment_name_prefix}cond_exp_whole_above': cond_exp_whole_above,
        f'{segment_name_prefix}cond_exp_whole_below': cond_exp_whole_below
    }
    feats.update(conditional_values)
    
    conditional_features = [
        ('conditional_exp_above', cond_exp_above_pre, cond_exp_above_post),
        ('conditional_exp_below', cond_exp_below_pre, cond_exp_below_post),
        ('pre_above_vs_below', cond_exp_below_pre, cond_exp_above_pre),
        ('post_above_vs_below', cond_exp_below_post, cond_exp_above_post),
        ('whole_above_vs_below', cond_exp_whole_below, cond_exp_whole_above)
    ]
    
    for feature_name, pre_val, post_val in conditional_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 5. BASIC STATISTICAL FEATURES - All 4 relatives (from v3)
    mean_pre, mean_post = np.nanmean(pre), np.nanmean(post)
    std_pre, std_post = np.nanstd(pre, ddof=1), np.nanstd(post, ddof=1)
    var_pre, var_post = np.nanvar(pre, ddof=1), np.nanvar(post, ddof=1)
    skew_pre, skew_post = stats.skew(pre, nan_policy='omit'), stats.skew(post, nan_policy='omit')
    kurt_pre, kurt_post = stats.kurtosis(pre, nan_policy='omit'), stats.kurtosis(post, nan_policy='omit')
    
    # Store basic statistical values for normalization
    basic_values = {
        f'{segment_name_prefix}mean_pre': mean_pre,
        f'{segment_name_prefix}mean_post': mean_post,
        f'{segment_name_prefix}std_pre': std_pre,
        f'{segment_name_prefix}std_post': std_post,
        f'{segment_name_prefix}var_pre': var_pre,
        f'{segment_name_prefix}var_post': var_post,
        f'{segment_name_prefix}skew_pre': skew_pre,
        f'{segment_name_prefix}skew_post': skew_post,
        f'{segment_name_prefix}kurt_pre': kurt_pre,
        f'{segment_name_prefix}kurt_post': kurt_post,
        f'{segment_name_prefix}mean_whole': np.nanmean(whole),
        f'{segment_name_prefix}std_whole': np.nanstd(whole, ddof=1),
        f'{segment_name_prefix}var_whole': np.nanvar(whole, ddof=1),
        f'{segment_name_prefix}skew_whole': stats.skew(whole, nan_policy='omit'),
        f'{segment_name_prefix}kurt_whole': stats.kurtosis(whole, nan_policy='omit')
    }
    feats.update(basic_values)
    
    basic_features = [
        ('mean', mean_pre, mean_post),
        ('std', std_pre, std_post),
        ('var', var_pre, var_post),
        ('skew', skew_pre, skew_post),
        ('kurt', kurt_pre, kurt_post)
    ]
    
    for feature_name, pre_val, post_val in basic_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 6. RANGE & VOLATILITY FEATURES - All 4 relatives (from v3)
    range_pre = np.nanmax(pre) - np.nanmin(pre) if len(pre) > 0 else np.nan
    range_post = np.nanmax(post) - np.nanmin(post) if len(post) > 0 else np.nan
    vol_pre = np.nanstd(np.diff(pre)) if len(pre) > 1 else np.nan
    vol_post = np.nanstd(np.diff(post)) if len(post) > 1 else np.nan
    min_pre, min_post = np.nanmin(pre), np.nanmin(post)
    max_pre, max_post = np.nanmax(pre), np.nanmax(post)
    
    range_features = [
        ('range', range_pre, range_post),
        ('volatility', vol_pre, vol_post),
        ('min', min_pre, min_post),
        ('max', max_pre, max_post)
    ]
    
    for feature_name, pre_val, post_val in range_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 7. STATIONARITY FEATURES - All 4 relatives (from v3)
    adf_pre, adf_post = safe_adf_stat(pre), safe_adf_stat(post)
    kpss_pre, kpss_post = safe_kpss_stat(pre), safe_kpss_stat(post)
    
    stationarity_features = [
        ('adf_stat', adf_pre, adf_post),
        ('kpss_stat', kpss_pre, kpss_post)
    ]
    
    for feature_name, pre_val, post_val in stationarity_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 8. QUANTILE FEATURES - All 4 relatives (from v3)
    if len(pre) > 0 and len(post) > 0:
        quantiles = [0.05, 0.25, 0.5, 0.75, 0.95]
        q_pre = np.nanquantile(pre, quantiles)
        q_post = np.nanquantile(post, quantiles)
        
        for i, q in enumerate([5, 25, 50, 75, 95]):
            relatives = compute_all_relatives(q_pre[i], q_post[i], f'q{q}', segment_name_prefix)
            feats.update(relatives)

    # 9. TSFRESH FEATURES - All 4 relatives (from v3)
    # Energy Ratio by Chunks
    for i in range(2):
        energy_pre = compute_tsfresh_feature(
            energy_ratio_by_chunks, pre, [{"num_segments": 2, "segment_focus": i}], min_length=2
        )
        energy_post = compute_tsfresh_feature(
            energy_ratio_by_chunks, post, [{"num_segments": 2, "segment_focus": i}], min_length=2
        )
        relatives = compute_all_relatives(energy_pre, energy_post, f'energy_ratio_chunk{i+1}', segment_name_prefix)
        feats.update(relatives)

    # FFT Aggregated
    for aggtype in ["centroid", "variance"]:
        fft_pre = compute_tsfresh_feature(fft_aggregated, pre, [{"aggtype": aggtype}], min_length=10)
        fft_post = compute_tsfresh_feature(fft_aggregated, post, [{"aggtype": aggtype}], min_length=10)
        relatives = compute_all_relatives(fft_pre, fft_post, f'fft_agg_{aggtype}', segment_name_prefix)
        feats.update(relatives)

    # Index Mass Quantile
    for q in [0.25, 0.5, 0.75]:
        imq_pre = compute_tsfresh_feature(index_mass_quantile, pre, [{"q": q}], min_length=5)
        imq_post = compute_tsfresh_feature(index_mass_quantile, post, [{"q": q}], min_length=5)
        relatives = compute_all_relatives(imq_pre, imq_post, f'index_mass_quantile_q{int(q*100)}', segment_name_prefix)
        feats.update(relatives)

    # Other TSFRESH features
    tsfresh_features = [
        (binned_entropy, {"max_bins": 10}, 'binned_entropy'),
        (last_location_of_maximum, None, 'last_location_of_maximum'),
        (lempel_ziv_complexity, 100, 'lempel_ziv_complexity'),
        (mean_second_derivative_central, None, 'mean_second_derivative_central'),
        (percentage_of_reoccurring_datapoints_to_all_datapoints, None, 'pct_reoccurring_datapoints'),
        (percentage_of_reoccurring_values_to_all_values, None, 'pct_reoccurring_values'),
        (ratio_value_number_to_time_series_length, None, 'unique_values')
    ]
    
    for func, params, name in tsfresh_features:
        pre_val = compute_tsfresh_feature(func, pre, params, min_length=5)
        post_val = compute_tsfresh_feature(func, post, params, min_length=5)
        relatives = compute_all_relatives(pre_val, post_val, name, segment_name_prefix)
        feats.update(relatives)

    # 10. SPECTRAL FEATURES - All 4 relatives (from v3)
    def spectral_power_periodogram(arr):
        if len(arr) > 10:
            try:
                f, Pxx = periodogram(arr, fs=1.0, scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan

    def spectral_power_welch(arr):
        if len(arr) > 10:
            try:
                f, Pxx = welch(arr, nperseg=min(len(arr)//2, 256), scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan

    def spectrogram_entropy(seg):
        if len(seg) < 20:
            return np.nan
        try:
            f, t, Sxx = spectrogram(seg, fs=1.0, nperseg=min(100, len(seg)//2), noverlap=None)
            Sxx = Sxx / np.sum(Sxx) if np.sum(Sxx) != 0 else np.ones_like(Sxx) / Sxx.size
            return -np.sum(Sxx * np.log2(Sxx + 1e-10))
        except Exception:
            return np.nan

    spectral_funcs = [
        (spectral_power_periodogram, 'power_periodogram'),
        (spectral_power_welch, 'power_welch'),
        (spectrogram_entropy, 'spec_entropy')
    ]
    
    for func, name in spectral_funcs:
        pre_val = func(pre)
        post_val = func(post)
        relatives = compute_all_relatives(pre_val, post_val, name, segment_name_prefix)
        feats.update(relatives)

    # 11. SIGNAL PROCESSING FEATURES - All 4 relatives (from v3)
    # Peak Finding
    if len(pre) > 5 and len(post) > 5:
        try:
            peaks_pre, _ = find_peaks(pre, height=np.mean(pre) + np.std(pre))
            peaks_post, _ = find_peaks(post, height=np.mean(post) + np.std(post))
            num_peaks_pre, num_peaks_post = len(peaks_pre), len(peaks_post)
            
            prominences_pre = peak_prominences(pre, peaks_pre)[0] if len(peaks_pre) > 0 else np.array([0])
            prominences_post = peak_prominences(post, peaks_post)[0] if len(peaks_post) > 0 else np.array([0])
            mean_prominence_pre = np.mean(prominences_pre)
            mean_prominence_post = np.mean(prominences_post)
            
            peak_features = [
                ('num_peaks', num_peaks_pre, num_peaks_post),
                ('mean_prominence', mean_prominence_pre, mean_prominence_post)
            ]
            
            for feature_name, pre_val, post_val in peak_features:
                relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
                feats.update(relatives)
        except Exception:
            pass

    # Savitzky-Golay Filtering
    if len(pre) > 7 and len(post) > 7:
        try:
            pre_smooth = savgol_filter(pre, window_length=7, polyorder=2)
            post_smooth = savgol_filter(post, window_length=7, polyorder=2)
            smooth_residuals_pre = np.std(pre - pre_smooth)
            smooth_residuals_post = np.std(post - post_smooth)
            
            relatives = compute_all_relatives(smooth_residuals_pre, smooth_residuals_post, 'smooth_residuals', segment_name_prefix)
            feats.update(relatives)
        except Exception:
            pass

    # Detrending
    if len(pre) > 5 and len(post) > 5:
        try:
            pre_detrended = detrend(pre, type='linear')
            post_detrended = detrend(post, type='linear')
            std_detrended_pre = np.std(pre_detrended)
            std_detrended_post = np.std(post_detrended)
            
            relatives = compute_all_relatives(std_detrended_pre, std_detrended_post, 'std_detrended', segment_name_prefix)
            feats.update(relatives)
        except Exception:
            pass

    # Hilbert Transform
    if len(pre) > 5 and len(post) > 5:
        try:
            analytic_pre = hilbert(pre)
            analytic_post = hilbert(post)
            env_pre = np.abs(analytic_pre)
            env_post = np.abs(analytic_post)
            mean_envelope_pre = np.mean(env_pre)
            mean_envelope_post = np.mean(env_post)
            
            relatives = compute_all_relatives(mean_envelope_pre, mean_envelope_post, 'mean_envelope', segment_name_prefix)
            feats.update(relatives)
        except Exception:
            pass

    # Cross-Correlation and Convolution (from v3)
    if len(pre) > 5 and len(post) > 5:
        try:
            pre_norm = pre - np.nanmean(pre)
            post_norm = post - np.nanmean(post)
            corr = correlate(pre_norm, post_norm, mode='full')
            lags = correlation_lags(len(pre), len(post), mode='full')
            max_corr = np.max(np.abs(corr)) / (np.nanstd(pre) * np.nanstd(post) * min(len(pre), len(post))) if np.nanstd(pre) * np.nanstd(post) > 0 else np.nan
            max_corr_lag = lags[np.argmax(np.abs(corr))] if not np.isnan(max_corr) else np.nan
            conv = fftconvolve(pre_norm, post_norm[::-1], mode='full')
            max_conv = np.max(np.abs(conv)) / (np.nanstd(pre) * np.nanstd(post) * min(len(pre), len(post))) if np.nanstd(pre) * np.nanstd(post) > 0 else np.nan
            
            # Store cross-correlation and convolution features
            feats[f'{segment_name_prefix}max_cross_corr'] = max_corr
            feats[f'{segment_name_prefix}max_cross_corr_lag'] = max_corr_lag
            feats[f'{segment_name_prefix}max_conv'] = max_conv
        except Exception:
            feats[f'{segment_name_prefix}max_cross_corr'] = np.nan
            feats[f'{segment_name_prefix}max_cross_corr_lag'] = np.nan
            feats[f'{segment_name_prefix}max_conv'] = np.nan
    else:
        feats[f'{segment_name_prefix}max_cross_corr'] = np.nan
        feats[f'{segment_name_prefix}max_cross_corr_lag'] = np.nan
        feats[f'{segment_name_prefix}max_conv'] = np.nan

    # Coherence (from v3)
    if len(pre) > 10 and len(post) > 10:
        try:
            f, Cxy = coherence(pre, post, nperseg=min(100, len(pre)//2))
            mean_coherence = np.mean(Cxy)
            feats[f'{segment_name_prefix}mean_coherence'] = mean_coherence
        except Exception:
            feats[f'{segment_name_prefix}mean_coherence'] = np.nan
    else:
        feats[f'{segment_name_prefix}mean_coherence'] = np.nan

    # 12. CATCH22 FEATURES - All 4 relatives (from v3)
    if len(pre) >= 20 and len(post) >= 20:
        try:
            pre_no_nan = pre[~np.isnan(pre)]
            post_no_nan = post[~np.isnan(post)]
            catch22_pre = pycatch22.catch22_all(pre_no_nan)
            catch22_post = pycatch22.catch22_all(post_no_nan)
            
            for feature_name, pre_val, post_val in zip(catch22_pre['names'], catch22_pre['values'], catch22_post['values']):
                relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
                feats.update(relatives)
        except Exception:
            pass

    # 13. AUTOCORRELATION FEATURES - All 4 relatives (from v3)
    def lag1_autocorr(arr):
        if len(arr) > 1:
            arr0 = arr - np.nanmean(arr)
            return np.corrcoef(arr0[:-1], arr0[1:])[0, 1] if len(arr0) > 1 else np.nan
        return np.nan

    def estimate_ar1(arr):
        if len(arr) > 1:
            x_prev = arr[:-1]
            x_next = arr[1:]
            phi = np.polyfit(x_prev, x_next, deg=1)[0]
            return phi
        return np.nan

    ac_pre, ac_post = lag1_autocorr(pre), lag1_autocorr(post)
    phi_pre, phi_post = estimate_ar1(pre), estimate_ar1(post)
    
    autocorr_features = [
        ('ac', ac_pre, ac_post),
        ('phi', phi_pre, phi_post)
    ]
    
    for feature_name, pre_val, post_val in autocorr_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 14. LONGEST STRIKE FEATURES - All 4 relatives (from v3)
    def longest_strike_above_mean(arr):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        streaks = []
        current_streak = 0
        for val in arr:
            if val > mean:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0

    def longest_strike_below_mean(arr):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        streaks = []
        current_streak = 0
        for val in arr:
            if val < mean:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0

    lsam_pre, lsam_post = longest_strike_above_mean(pre), longest_strike_above_mean(post)
    lsbm_pre, lsbm_post = longest_strike_below_mean(pre), longest_strike_below_mean(post)
    
    strike_features = [
        ('longest_strike_above_mean', lsam_pre, lsam_post),
        ('longest_strike_below_mean', lsbm_pre, lsbm_post)
    ]
    
    for feature_name, pre_val, post_val in strike_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 15. RATIO BEYOND R SIGMA - All 4 relatives (from v3)
    def ratio_beyond_r_sigma(arr, r):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        std = np.nanstd(arr, ddof=1)
        if std == 0:
            return np.nan
        return np.sum(np.abs(arr - mean) > r * std) / len(arr)

    for r in [1, 2]:
        rbs_pre = ratio_beyond_r_sigma(pre, r)
        rbs_post = ratio_beyond_r_sigma(post, r)
        relatives = compute_all_relatives(rbs_pre, rbs_post, f'beyond_{r}_sigma', segment_name_prefix)
        feats.update(relatives)

    # 16. GARCH VOLATILITY FEATURES - All 4 relatives (from v3)
    def garch_volatility(arr):
        if len(arr) > 30:
            try:
                from arch import arch_model
                model = arch_model(arr, vol='Garch', p=1, q=1, rescale=True).fit(disp='off')
                return np.mean(np.sqrt(model.conditional_volatility))
            except Exception:
                return np.nan
        return np.nan

    garch_pre = garch_volatility(pre)
    garch_post = garch_volatility(post)
    relatives = compute_all_relatives(garch_pre, garch_post, 'garch', segment_name_prefix)
    feats.update(relatives)

    # 17. SSA FEATURES - All 4 relatives for each SSA component (from v3)
    def ssa_features(arr, window_size=None, k=3):
        if len(arr) < 20:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }
        try:
            arr = arr[~np.isnan(arr)]
            n = len(arr)
            window_size = window_size or min(40, n // 2)
            if window_size < 2 or window_size > n:
                return {
                    'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                    'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
                }
            L = window_size
            K = n - L + 1
            X = np.array([arr[i:i+L] for i in range(K)])
            U, s, Vt = np.linalg.svd(X, full_matrices=False)
            singular_values = s[:k]
            singular_values = np.pad(singular_values, (0, max(0, k - len(singular_values))), constant_values=np.nan)
            X_reconstructed = np.zeros_like(X)
            for i in range(min(k, len(s))):
                X_reconstructed += s[i] * np.outer(U[:, i], Vt[i, :])
            reconstructed_series = np.zeros(n)
            count = np.zeros(n)
            for i in range(K):
                for j in range(L):
                    if i + j < n:
                        reconstructed_series[i + j] += X_reconstructed[i, j]
                        count[i + j] += 1
            reconstructed_series = reconstructed_series / count
            reconstruction_error = np.nanmean((arr - reconstructed_series) ** 2)
            trend = s[0] * np.outer(U[:, 0], Vt[0, :])
            trend_series = np.zeros(n)
            count = np.zeros(n)
            for i in range(K):
                for j in range(L):
                    if i + j < n:
                        trend_series[i + j] += trend[i, j]
                        count[i + j] += 1
            trend_series = trend_series / count
            trend_mean = np.nanmean(trend_series)
            trend_std = np.nanstd(trend_series, ddof=1) if len(trend_series) > 1 else np.nan
            return {
                'ssa_singular_value_1': singular_values[0],
                'ssa_singular_value_2': singular_values[1] if len(singular_values) > 1 else np.nan,
                'ssa_singular_value_3': singular_values[2] if len(singular_values) > 2 else np.nan,
                'ssa_reconstruction_error': reconstruction_error,
                'ssa_trend_mean': trend_mean,
                'ssa_trend_std': trend_std
            }
        except Exception:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }

    if len(pre) >= 20 and len(post) >= 20:
        try:
            ssa_pre = ssa_features(pre)
            ssa_post = ssa_features(post)
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                relatives = compute_all_relatives(ssa_pre[key], ssa_post[key], key, segment_name_prefix)
                feats.update(relatives)
        except Exception:
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                relatives = compute_all_relatives(np.nan, np.nan, key, segment_name_prefix)
                feats.update(relatives)
    else:
        for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                    'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
            relatives = compute_all_relatives(np.nan, np.nan, key, segment_name_prefix)
            feats.update(relatives)

    # 18. ADD NORMALIZATION FEATURES (_norm_zscore and _weighted) (from v3)
    normalization_features = compute_normalization_features(feats, whole, segment_name_prefix, tstar)
    feats.update(normalization_features)

    return feats

def process_series_v4(args, **kwargs):
    """V4 process_series with enhanced features"""
    index, series, split_point = args
    try:
        series = np.array(series, dtype=float)
        series = series[~np.isnan(series)]
        
        if len(series) < 10:
            return {}

        if split_point is None or not (0 < split_point < len(series)):
            split_point = len(series) // 2
        
        pre = series[:split_point]
        post = series[split_point:]
        
        if len(pre) < 5 or len(post) < 5:
            return {}

        feats = compute_features_for_segment_v4(pre=pre, post=post, whole=series, tstar=split_point)
        return feats
    
    except Exception as e:
        logging.error(f"Error processing series {index}: {str(e)}")
        return {}

def extract_features_parallel_v4(series_list, split_points, n_processes=None):
    """V4 parallel feature extraction"""
    import multiprocessing as mp
    import time
    
    if n_processes is None:
        n_processes = max(1, mp.cpu_count() - 1)
    
    args = [(i, series_list[i], split_points[i]) for i in range(len(series_list))]
    total_series = len(args)
    
    print(f"V4: Initializing {n_processes} worker processes for {total_series} series...")
    start_time = time.time()
    
    with mp.Pool(processes=n_processes) as pool:
        results = []
        for arg in args:
            result = pool.apply_async(process_series_v4, (arg,))
            results.append(result)
        
        feature_results = []
        for i, result in enumerate(results):
            feature_results.append(result.get())
            if (i + 1) % 100 == 0:
                elapsed = time.time() - start_time
                progress = (i + 1) / total_series * 100
                print(f"Progress: {progress:.1f}% ({i+1}/{total_series}) - {elapsed/60:.1f}m elapsed")
    
    elapsed_total = time.time() - start_time
    throughput = total_series / elapsed_total
    print(f"✓ V4 Extraction completed: {elapsed_total/60:.2f}m total ({throughput:.2f} series/s)")
    
    feature_df = pd.DataFrame(feature_results)
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    n_features = feature_df.shape[1]
    sparsity = (feature_df.isna().sum().sum() / feature_df.size) * 100
    print(f"V4 Feature matrix: {total_series} × {n_features} ({sparsity:.1f}% sparse)")
    
    return feature_df

if __name__ == "__main__":
    print("Ratios_updated_v4.py - Enhanced with Advanced Correlation & Spectral Features")
    print("NEW V4 FEATURES:")
    print("• Cross-Spectral Density (mean_csd_magnitude, max_csd_magnitude)")
    print("• Phase Coherence (mean_phase_coherence, max_phase_coherence)")
    print("• Spectral Granger Causality (mean_spectral_granger, max_spectral_granger)")
    print("• Kendall's Tau (kendall_tau)")
    print("• Spearman Correlation (spearman_rho)")
    print("• Pearson Correlation (pearson_r)")
    print("• D'Agostino-Pearson Test (dagostino_pearson_stat)")
    print("• Kullback-Leibler Divergence (kl_divergence)")
    print("• Enhanced Granger Causality (granger_p)")
    print("Expected ~900+ features per time series")