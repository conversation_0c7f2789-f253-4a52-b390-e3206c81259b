#!/usr/bin/env python3
"""
Ratios_updated_v6.py - Complete V4 Features × All Temporal Positions
Extracts full V4 feature set for every pattern-aware temporal positioning method
"""

import numpy as np
import pandas as pd
from tsfresh.feature_extraction.feature_calculators import (
    energy_ratio_by_chunks, fft_aggregated, index_mass_quantile,
    binned_entropy, last_location_of_maximum, lempel_ziv_complexity,
    longest_strike_above_mean, longest_strike_below_mean,
    mean_second_derivative_central, percentage_of_reoccurring_datapoints_to_all_datapoints,
    percentage_of_reoccurring_values_to_all_values, ratio_beyond_r_sigma,
    ratio_value_number_to_time_series_length
)
from scipy.stats import (norm, kstest, anderson_ksamp, cramervonmises_2samp, wasserstein_distance, 
                        mannwhitneyu, kendalltau, spearmanr, pearsonr, normaltest, entropy)
from statsmodels.stats.diagnostic import lilliefors
from statsmodels.tsa.stattools import coint, grangercausalitytests
from statsmodels.stats.stattools import jarque_bera
from statsmodels.regression.linear_model import OLS
import pycatch22
from scipy.signal import (periodogram, welch, spectrogram, find_peaks, peak_prominences, 
                         savgol_filter, detrend, hilbert, coherence, correlate, correlation_lags, 
                         fftconvolve, csd)
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
from arch import arch_model
import warnings
from multiprocessing import Pool
from functools import partial
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_optimal_temporal_segments(series, tstar, pattern_type='unknown'):
    """
    Extract optimal temporal segments based on pattern type and v3 analysis results
    Uses actual tstar boundary for correct temporal positioning
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 20 or tstar <= 0 or tstar >= len(series_clean):
        return {}
    
    normal_full = series_clean[:tstar]
    post_full = series_clean[tstar:]
    
    segments = {}
    
    # Pattern-specific optimal methods from v3 analysis
    if pattern_type == 'spiky':
        # Best: first_25pct_vs_full (0.5377 AUROC)
        first_25_len = max(3, len(normal_full) // 4)
        segments['optimal_spiky'] = (normal_full[:first_25_len], post_full)
        
    elif pattern_type == 'volatile':
        # Best: p20_first_vs_p20_first (0.5350 AUROC)
        p20_len = max(3, int(len(normal_full) * 0.2))
        segments['optimal_volatile'] = (normal_full[:p20_len], post_full[:p20_len])
        
    elif pattern_type == 'moderate':
        # Best: first_75pct_vs_full (0.5816 AUROC)
        first_75_len = max(3, int(len(normal_full) * 0.75))
        segments['optimal_moderate'] = (normal_full[:first_75_len], post_full)
        
    elif pattern_type == 'calm':
        # Best: first_75pct_vs_full (0.5451 AUROC)
        first_75_len = max(3, int(len(normal_full) * 0.75))
        segments['optimal_calm'] = (normal_full[:first_75_len], post_full)
        
    elif pattern_type == 'clipped':
        # Best: equal_quarter_last (0.5613 AUROC)
        min_len = min(len(normal_full), len(post_full))
        quarter_len = max(3, min_len // 4)
        segments['optimal_clipped'] = (normal_full[-quarter_len:], post_full[-quarter_len:])
    
    # Universal best performers (for unknown patterns or ensemble)
    # 1. Overall winner: q25_last_vs_q25_last (0.5505 AUROC)
    quarter_len = max(3, len(normal_full) // 4)
    segments['universal_best'] = (normal_full[-quarter_len:], post_full[-quarter_len:])
    
    # 2. Second best: q25_first_vs_q25_last (0.5452 AUROC)
    segments['universal_second'] = (normal_full[:quarter_len], post_full[-quarter_len:])
    
    # 3. Traditional winner: first_75pct_vs_full (0.5420 AUROC)
    first_75_len = max(3, int(len(normal_full) * 0.75))
    segments['traditional_best'] = (normal_full[:first_75_len], post_full)
    
    # Multi-scale temporal analysis
    # Early vs Late comparisons
    if len(normal_full) >= 8:
        early_len = max(3, len(normal_full) // 3)
        late_len = max(3, len(normal_full) // 3)
        segments['early_vs_late'] = (normal_full[:early_len], normal_full[-late_len:])
    
    # Quantile-based segments for robustness
    if len(normal_full) >= 10 and len(post_full) >= 10:
        # 10th percentile segments
        p10_normal = max(3, int(len(normal_full) * 0.1))
        p10_post = max(3, int(len(post_full) * 0.1))
        segments['p10_first_vs_last'] = (normal_full[:p10_normal], post_full[-p10_post:])
        
        # 20th percentile segments
        p20_normal = max(3, int(len(normal_full) * 0.2))
        p20_post = max(3, int(len(post_full) * 0.2))
        segments['p20_matched'] = (normal_full[:p20_normal], post_full[:p20_post])
    
    return segments

def load_pattern_assignments():
    """Load volatility pattern assignments if available"""
    try:
        volatility_df = pd.read_csv('volatility_pattern_analysis.csv')
        pattern_map = dict(zip(volatility_df['series_id'], volatility_df['pattern_type']))
        print(f"✅ Loaded {len(pattern_map)} pattern assignments")
        return pattern_map
    except:
        try:
            clusters_df = pd.read_csv('time_series_statistics_with_clusters.csv')
            cluster_names = {0: 'spiky', 1: 'volatile', 2: 'moderate', 3: 'calm', 4: 'clipped'}
            pattern_map = {row['series_id']: cluster_names.get(row['cluster'], 'unknown') 
                          for _, row in clusters_df.iterrows()}
            print(f"✅ Loaded {len(pattern_map)} cluster-based patterns")
            return pattern_map
        except:
            print("⚠️  No pattern data found, using universal methods")
            return {}

def jensen_shannon_divergence(pre, post):
    """Jensen-Shannon divergence between two distributions"""
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        pre, post = pre[~np.isnan(pre)], post[~np.isnan(post)]
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else np.ones_like(hist_pre) / len(hist_pre)
        hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else np.ones_like(hist_post) / len(hist_post)
        m = 0.5 * (hist_pre + hist_post)
        jsd = 0.5 * (entropy(hist_pre, m) + entropy(hist_post, m))
        return jsd if not np.isnan(jsd) else np.nan
    except Exception:
        return np.nan

def compute_enhanced_divergence_tests(pre, post, segment_name_prefix=''):
    """Enhanced statistical divergence tests"""
    tests = {}
    
    if len(pre) < 5 or len(post) < 5:
        return {f'{segment_name_prefix}{k}': np.nan for k in [
            'jsd', 'kl_divergence', 'wasserstein_distance', 'p_ks', 'p_mannwhitney', 
            'kendall_tau', 'spearman_rho', 'pearson_r'
        ]}
    
    pre_clean = pre[~np.isnan(pre)]
    post_clean = post[~np.isnan(post)]
    
    # Core divergence tests
    tests[f'{segment_name_prefix}jsd'] = jensen_shannon_divergence(pre_clean, post_clean)
    
    try:
        tests[f'{segment_name_prefix}wasserstein_distance'] = wasserstein_distance(pre_clean, post_clean)
    except Exception:
        tests[f'{segment_name_prefix}wasserstein_distance'] = np.nan
    
    try:
        ks_stat, p_ks = stats.ks_2samp(pre_clean, post_clean)
        tests[f'{segment_name_prefix}p_ks'] = p_ks
    except Exception:
        tests[f'{segment_name_prefix}p_ks'] = np.nan
    
    try:
        u_stat, p_mw = mannwhitneyu(pre_clean, post_clean, alternative='two-sided')
        tests[f'{segment_name_prefix}p_mannwhitney'] = p_mw
    except Exception:
        tests[f'{segment_name_prefix}p_mannwhitney'] = np.nan
    
    # Correlation features
    try:
        tau, p_tau = kendalltau(pre_clean, post_clean)
        tests[f'{segment_name_prefix}kendall_tau'] = tau
    except Exception:
        tests[f'{segment_name_prefix}kendall_tau'] = np.nan
    
    try:
        rho, p_rho = spearmanr(pre_clean, post_clean)
        tests[f'{segment_name_prefix}spearman_rho'] = rho
    except Exception:
        tests[f'{segment_name_prefix}spearman_rho'] = np.nan
    
    try:
        r, p_r = pearsonr(pre_clean, post_clean)
        tests[f'{segment_name_prefix}pearson_r'] = r
    except Exception:
        tests[f'{segment_name_prefix}pearson_r'] = np.nan
    
    return tests

def compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix=''):
    """Compute all 4 relative measures for any pre/post feature pair"""
    relatives = {}
    
    relatives[f'{segment_name_prefix}ratio_{feature_name}'] = (
        post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
    )
    
    if pre_val and pre_val != 0 and not np.isnan(post_val):
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = abs(post_val - pre_val) / abs(pre_val)
    else:
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = np.nan
        
    if pre_val and pre_val > 0 and post_val and post_val > 0:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.log(post_val / pre_val)
    else:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.nan
        
    if not np.isnan(pre_val) and not np.isnan(post_val) and (abs(pre_val) + abs(post_val)) > 0:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = (post_val - pre_val) / (abs(post_val) + abs(pre_val))
    else:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = np.nan
        
    return relatives

def compute_core_features_for_segment(pre, post, segment_name_prefix=''):
    """Compute core statistical and spectral features for a segment pair"""
    feats = {}
    
    if len(pre) < 3 or len(post) < 3:
        return feats
    
    # 1. Enhanced divergence tests
    divergence_tests = compute_enhanced_divergence_tests(pre, post, segment_name_prefix)
    feats.update(divergence_tests)
    
    # 2. Basic statistical features with relatives
    mean_pre, mean_post = np.nanmean(pre), np.nanmean(post)
    std_pre, std_post = np.nanstd(pre, ddof=1), np.nanstd(post, ddof=1)
    var_pre, var_post = np.nanvar(pre, ddof=1), np.nanvar(post, ddof=1)
    skew_pre, skew_post = stats.skew(pre, nan_policy='omit'), stats.skew(post, nan_policy='omit')
    kurt_pre, kurt_post = stats.kurtosis(pre, nan_policy='omit'), stats.kurtosis(post, nan_policy='omit')
    
    basic_features = [
        ('mean', mean_pre, mean_post),
        ('std', std_pre, std_post),
        ('var', var_pre, var_post),
        ('skew', skew_pre, skew_post),
        ('kurt', kurt_pre, kurt_post)
    ]
    
    for feature_name, pre_val, post_val in basic_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)
    
    # 3. Range and volatility features
    range_pre = np.nanmax(pre) - np.nanmin(pre) if len(pre) > 0 else np.nan
    range_post = np.nanmax(post) - np.nanmin(post) if len(post) > 0 else np.nan
    vol_pre = np.nanstd(np.diff(pre)) if len(pre) > 1 else np.nan
    vol_post = np.nanstd(np.diff(post)) if len(post) > 1 else np.nan
    
    range_features = [
        ('range', range_pre, range_post),
        ('volatility', vol_pre, vol_post)
    ]
    
    for feature_name, pre_val, post_val in range_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)
    
    # 4. Spectral features
    def spectral_power_welch(arr):
        if len(arr) > 10:
            try:
                f, Pxx = welch(arr, nperseg=min(len(arr)//2, 256), scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan
    
    spec_pre = spectral_power_welch(pre)
    spec_post = spectral_power_welch(post)
    relatives = compute_all_relatives(spec_pre, spec_post, 'spectral_power', segment_name_prefix)
    feats.update(relatives)
    
    # 5. Cross-spectral features (V4)
    if len(pre) > 10 and len(post) > 10:
        try:
            f, Pxy = csd(pre, post, nperseg=min(100, len(pre)//2))
            feats[f'{segment_name_prefix}mean_csd_magnitude'] = np.mean(np.abs(Pxy))
        except Exception:
            feats[f'{segment_name_prefix}mean_csd_magnitude'] = np.nan
        
        try:
            f, Cxy = coherence(pre, post, nperseg=min(100, len(pre)//2))
            feats[f'{segment_name_prefix}mean_coherence'] = np.mean(Cxy)
        except Exception:
            feats[f'{segment_name_prefix}mean_coherence'] = np.nan
    
    # 6. Autocorrelation features
    def lag1_autocorr(arr):
        if len(arr) > 1:
            arr0 = arr - np.nanmean(arr)
            return np.corrcoef(arr0[:-1], arr0[1:])[0, 1] if len(arr0) > 1 else np.nan
        return np.nan
    
    ac_pre, ac_post = lag1_autocorr(pre), lag1_autocorr(post)
    relatives = compute_all_relatives(ac_pre, ac_post, 'autocorr', segment_name_prefix)
    feats.update(relatives)
    
    # 7. Quantile features
    if len(pre) > 0 and len(post) > 0:
        quantiles = [0.25, 0.5, 0.75]
        q_pre = np.nanquantile(pre, quantiles)
        q_post = np.nanquantile(post, quantiles)
        
        for i, q in enumerate([25, 50, 75]):
            relatives = compute_all_relatives(q_pre[i], q_post[i], f'q{q}', segment_name_prefix)
            feats.update(relatives)
    
    return feats

def compute_temporal_position_features_v5(series, tstar, pattern_type='unknown', series_id=None):
    """
    V5: Extract features using optimal temporal positioning strategies with correct tstar
    """
    feats = {}
    
    try:
        series = np.array(series, dtype=float)
        series = series[~np.isnan(series)]
        
        if len(series) < 20:
            return feats
        
        # Get optimal temporal segments based on pattern type and tstar
        segments = get_optimal_temporal_segments(series, tstar, pattern_type)
        
        if not segments:
            return feats
        
        # Extract features for each temporal segment pair
        for segment_name, (pre, post) in segments.items():
            if len(pre) >= 3 and len(post) >= 3:
                segment_features = compute_core_features_for_segment(
                    pre, post, segment_name_prefix=f'{segment_name}_'
                )
                feats.update(segment_features)
        
        # Add pattern type as feature
        feats['pattern_type'] = pattern_type
        feats['series_id'] = series_id if series_id is not None else -1
        
        return feats
        
    except Exception as e:
        logging.error(f"Error processing series {series_id}: {str(e)}")
        return feats

def process_series_v5(args):
    """V5 process_series with temporal position awareness"""
    index, series, tstar, pattern_type = args
    return compute_temporal_position_features_v5(series, tstar, pattern_type, index)

def extract_features_parallel_v5(series_list, tstars_list, pattern_map=None, n_processes=None):
    """V5 parallel feature extraction with pattern awareness and correct tstar boundaries"""
    import multiprocessing as mp
    import time
    
    if n_processes is None:
        n_processes = max(1, mp.cpu_count() - 1)
    
    # Get pattern types for each series
    if pattern_map is None:
        pattern_map = {}
    
    pattern_types = [pattern_map.get(i, 'unknown') for i in range(len(series_list))]
    
    args = [(i, series_list[i], tstars_list[i], pattern_types[i]) for i in range(len(series_list))]
    total_series = len(args)
    
    print(f"V5: Initializing {n_processes} worker processes for {total_series} series...")
    print(f"Pattern distribution: {pd.Series(pattern_types).value_counts().to_dict()}")
    start_time = time.time()
    
    with mp.Pool(processes=n_processes) as pool:
        results = []
        for arg in args:
            result = pool.apply_async(process_series_v5, (arg,))
            results.append(result)
        
        feature_results = []
        for i, result in enumerate(results):
            feature_results.append(result.get())
            if (i + 1) % 100 == 0:
                elapsed = time.time() - start_time
                progress = (i + 1) / total_series * 100
                print(f"Progress: {progress:.1f}% ({i+1}/{total_series}) - {elapsed/60:.1f}m elapsed")
    
    elapsed_total = time.time() - start_time
    throughput = total_series / elapsed_total
    print(f"✓ V5 Extraction completed: {elapsed_total/60:.2f}m total ({throughput:.2f} series/s)")
    
    feature_df = pd.DataFrame(feature_results)
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    n_features = feature_df.shape[1]
    sparsity = (feature_df.isna().sum().sum() / feature_df.size) * 100
    print(f"V5 Feature matrix: {total_series} × {n_features} ({sparsity:.1f}% sparse)")
    
    # Pattern-specific feature analysis
    if 'pattern_type' in feature_df.columns:
        pattern_counts = feature_df['pattern_type'].value_counts()
        print(f"Pattern-specific features extracted:")
        for pattern, count in pattern_counts.items():
            print(f"  {pattern}: {count} series")
    
    return feature_df

if __name__ == "__main__":
    print("Ratios_updated_v5.py - Temporal Position-Aware Feature Engineering")
    print("=" * 70)
    print("NEW V5 CAPABILITIES:")
    print("• Pattern-aware optimal temporal positioning")
    print("• Spiky: first_25pct_vs_full (0.5377 AUROC)")
    print("• Volatile: p20_first_vs_p20_first (0.5350 AUROC)")
    print("• Moderate: first_75pct_vs_full (0.5816 AUROC)")
    print("• Calm: first_75pct_vs_full (0.5451 AUROC)")
    print("• Clipped: equal_quarter_last (0.5613 AUROC)")
    print("• Universal methods for unknown patterns")
    print("• Multi-scale temporal segment analysis")
    print("• Expected 200+ optimized features per series")
    print("=" * 70)
    
    # Load pattern assignments
    pattern_map = load_pattern_assignments()
    
    # Example usage
    print("\nExample usage:")
    print("pattern_map = load_pattern_assignments()")
    print("features_df = extract_features_parallel_v5(series_list, pattern_map)")