"""
Test Gradient-Based Branch Classifier on Real Fragment Data
Uses actual X_train.parquet and y_train.parquet data
"""

import sys
import os
import time
import json
from collections import defaultdict

try:
    import torch
    import numpy as np
    import pandas as pd
    from gradient_based_branch_classifier import GradientBranchClassifier
    
    class RealDataTester:
        """Test gradient classifier on real fragment reconstruction data"""
        
        def __init__(self):
            self.classifier = None
            self.X_data = None
            self.y_data = None
            self.results = {
                'training_metrics': [],
                'validation_metrics': [],
                'processing_times': [],
                'decision_distribution': defaultdict(int)
            }
        
        def load_real_data(self):
            """Load the actual time series and label data"""
            
            print("📊 Loading Real Fragment Data")
            print("=" * 40)
            
            try:
                # Load time series data
                print("   Loading X_train.parquet...")
                self.X_data = pd.read_parquet('X_train.parquet')
                print(f"   ✅ Time series data: {self.X_data.shape}")
                
                # Load labels
                print("   Loading y_train.parquet...")
                self.y_data = pd.read_parquet('y_train.parquet')
                print(f"   ✅ Labels data: {self.y_data.shape}")
                
                # Convert to numpy if needed
                if isinstance(self.X_data, pd.DataFrame):
                    if isinstance(self.X_data.index, pd.MultiIndex):
                        self.X_data = self.X_data.reset_index()
                    
                    # Check if we have individual time series or features
                    print(f"   📊 Data columns: {list(self.X_data.columns)[:10]}...")
                    print(f"   📊 Data types: {self.X_data.dtypes.value_counts().to_dict()}")
                
                # Extract labels
                if 'structural_breakpoint' in self.y_data.columns:
                    self.labels = self.y_data['structural_breakpoint'].astype(int).values
                else:
                    self.labels = self.y_data.iloc[:, 0].astype(int).values
                
                print(f"   ✅ Labels extracted: {len(self.labels)} samples")
                print(f"   📊 Label distribution: {np.bincount(self.labels)}")
                
                return True
                
            except Exception as e:
                print(f"   ❌ Error loading data: {e}")
                return False
        
        def prepare_time_series_segments(self, n_samples: int = 500, segment_length: int = 100):
            """
            Prepare time series segments from the real data
            
            Args:
                n_samples: Number of samples to use
                segment_length: Length of each time series segment
            """
            
            print(f"\n🔧 Preparing Time Series Segments")
            print(f"   Target samples: {n_samples}")
            print(f"   Segment length: {segment_length}")
            
            segments = []
            segment_labels = []
            
            # Get actual data dimensions
            if isinstance(self.X_data, pd.DataFrame):
                data_array = self.X_data.select_dtypes(include=[np.number]).values
            else:
                data_array = self.X_data
            
            print(f"   📊 Data array shape: {data_array.shape}")
            
            # Strategy 1: If data has many columns, treat each row as a time series
            if data_array.shape[1] >= segment_length:
                print("   📈 Using rows as time series (many features)")
                
                n_available = min(n_samples, data_array.shape[0], len(self.labels))
                
                for i in range(n_available):
                    # Take first segment_length features as time series
                    segment = data_array[i, :segment_length]
                    
                    # Skip if segment has NaN or infinite values
                    if np.isfinite(segment).all():
                        segments.append(segment)
                        segment_labels.append(self.labels[i])
                
            # Strategy 2: If data has few columns, create segments from sequential rows
            else:
                print("   📈 Creating segments from sequential rows")
                
                # Use first column as time series if available
                if data_array.shape[1] > 0:
                    time_series = data_array[:, 0]  # First column
                else:
                    print("   ❌ No numeric data available")
                    return [], []
                
                # Create overlapping segments
                for i in range(0, len(time_series) - segment_length, segment_length // 2):
                    if i + segment_length <= len(time_series) and len(segments) < n_samples:
                        segment = time_series[i:i + segment_length]
                        
                        if np.isfinite(segment).all():
                            segments.append(segment)
                            # Use label from middle of segment
                            mid_idx = min(i + segment_length // 2, len(self.labels) - 1)
                            segment_labels.append(self.labels[mid_idx])
            
            print(f"   ✅ Created {len(segments)} valid segments")
            print(f"   📊 Segment label distribution: {np.bincount(segment_labels)}")
            
            return segments, segment_labels
        
        def create_ground_truth_from_labels(self, segments, labels):
            """
            Create ground truth for training based on actual labels
            
            Args:
                segments: List of time series segments
                labels: Corresponding structural breakpoint labels
            """
            
            print("\n🎯 Creating Ground Truth from Real Labels")
            
            ground_truths = []
            
            for i, (segment, label) in enumerate(zip(segments, labels)):
                
                # Create ground truth based on label and position
                if i == 0:
                    # First segment is always new branch
                    action = 0  # NEW_BRANCH
                    similarity_scores = {}
                    break_detected = {}
                    
                elif label == 1:  # Structural breakpoint detected
                    action = 2  # STRUCTURAL_BREAK
                    similarity_scores = {'branch_0': 0.3}  # Low similarity
                    break_detected = {'branch_0': True}
                    
                else:  # No structural breakpoint
                    # Decide between continue or new based on recent history
                    recent_labels = labels[max(0, i-5):i]
                    
                    if len(recent_labels) > 0 and np.mean(recent_labels) < 0.3:
                        # Recent segments were mostly normal, continue
                        action = 1  # CONTINUE_BRANCH
                        similarity_scores = {'branch_0': 0.8}  # High similarity
                        break_detected = {'branch_0': False}
                    else:
                        # Mixed recent history, new branch
                        action = 0  # NEW_BRANCH
                        similarity_scores = {}
                        break_detected = {}
                
                ground_truth = {
                    'action': action,
                    'similarity_scores': similarity_scores,
                    'break_detected': break_detected
                }
                
                ground_truths.append(ground_truth)
            
            # Print ground truth distribution
            action_counts = defaultdict(int)
            for gt in ground_truths:
                action_counts[gt['action']] += 1
            
            action_names = {0: 'NEW_BRANCH', 1: 'CONTINUE_BRANCH', 2: 'STRUCTURAL_BREAK'}
            print("   📊 Ground truth distribution:")
            for action, count in action_counts.items():
                print(f"      {action_names[action]}: {count}")
            
            return ground_truths
        
        def test_on_real_data(self, n_train: int = 300, n_val: int = 100):
            """
            Test the gradient classifier on real data
            
            Args:
                n_train: Number of training samples
                n_val: Number of validation samples
            """
            
            print(f"\n🚀 Testing on Real Data")
            print(f"   Training samples: {n_train}")
            print(f"   Validation samples: {n_val}")
            
            # Prepare data
            all_segments, all_labels = self.prepare_time_series_segments(
                n_samples=n_train + n_val, 
                segment_length=100
            )
            
            if len(all_segments) < n_train + n_val:
                print(f"   ⚠️  Only {len(all_segments)} segments available, adjusting...")
                n_train = min(n_train, len(all_segments) * 2 // 3)
                n_val = len(all_segments) - n_train
            
            # Split data
            train_segments = all_segments[:n_train]
            train_labels = all_labels[:n_train]
            val_segments = all_segments[n_train:n_train + n_val]
            val_labels = all_labels[n_train:n_train + n_val]
            
            # Create ground truth
            train_ground_truths = self.create_ground_truth_from_labels(train_segments, train_labels)
            val_ground_truths = self.create_ground_truth_from_labels(val_segments, val_labels)
            
            # Initialize classifier
            self.classifier = GradientBranchClassifier(
                data_dim=100,
                learning_rate=0.005,  # Slightly higher for real data
                batch_size=16,
                buffer_size=1000
            )
            
            print(f"\n📚 Training Phase")
            print("=" * 30)
            
            # Training
            self.classifier.set_training_mode(True)
            train_correct = 0
            
            for i, (segment, ground_truth) in enumerate(zip(train_segments, train_ground_truths)):
                start_time = time.time()
                
                # Convert to numpy array
                segment_array = np.array(segment, dtype=np.float32)
                
                # Process with learning
                result = self.classifier.process_new_part(segment_array, ground_truth)
                
                processing_time = time.time() - start_time
                self.results['processing_times'].append(processing_time)
                
                # Track accuracy
                predicted_action = {'NEW_BRANCH': 0, 'CONTINUE_BRANCH': 1, 'STRUCTURAL_BREAK': 2}[result['decision']]
                actual_action = ground_truth['action']
                is_correct = predicted_action == actual_action
                train_correct += is_correct
                
                # Track decision distribution
                self.results['decision_distribution'][result['decision']] += 1
                
                # Progress update
                if (i + 1) % 50 == 0:
                    current_accuracy = train_correct / (i + 1)
                    avg_time = np.mean(self.results['processing_times'][-50:])
                    print(f"   Step {i+1:3d}: Accuracy={current_accuracy:.3f}, "
                          f"Confidence={result['confidence']:.3f}, "
                          f"Time={avg_time*1000:.1f}ms")
            
            train_accuracy = train_correct / len(train_segments)
            
            print(f"\n🎯 Validation Phase")
            print("=" * 30)
            
            # Validation
            self.classifier.set_training_mode(False)
            val_correct = 0
            val_confidences = []
            
            for i, (segment, ground_truth) in enumerate(zip(val_segments, val_ground_truths)):
                # Convert to numpy array
                segment_array = np.array(segment, dtype=np.float32)
                
                # Process without learning
                result = self.classifier.process_new_part(segment_array)
                
                # Track accuracy
                predicted_action = {'NEW_BRANCH': 0, 'CONTINUE_BRANCH': 1, 'STRUCTURAL_BREAK': 2}[result['decision']]
                actual_action = ground_truth['action']
                is_correct = predicted_action == actual_action
                val_correct += is_correct
                val_confidences.append(result['confidence'])
                
                if (i + 1) % 25 == 0:
                    current_accuracy = val_correct / (i + 1)
                    avg_confidence = np.mean(val_confidences[-25:])
                    print(f"   Step {i+1:3d}: Accuracy={current_accuracy:.3f}, "
                          f"Confidence={avg_confidence:.3f}")
            
            val_accuracy = val_correct / len(val_segments)
            
            # Results summary
            results = {
                'training_accuracy': train_accuracy,
                'validation_accuracy': val_accuracy,
                'avg_confidence': np.mean(val_confidences),
                'avg_processing_time': np.mean(self.results['processing_times']),
                'decision_distribution': dict(self.results['decision_distribution']),
                'data_info': {
                    'train_samples': len(train_segments),
                    'val_samples': len(val_segments),
                    'original_data_shape': self.X_data.shape,
                    'label_distribution': np.bincount(all_labels).tolist()
                }
            }
            
            return results
        
        def print_results(self, results):
            """Print comprehensive results"""
            
            print(f"\n🏆 REAL DATA TEST RESULTS")
            print("=" * 50)
            print(f"Training Accuracy:    {results['training_accuracy']:.3f}")
            print(f"Validation Accuracy:  {results['validation_accuracy']:.3f}")
            print(f"Average Confidence:   {results['avg_confidence']:.3f}")
            print(f"Avg Processing Time:  {results['avg_processing_time']*1000:.1f}ms")
            
            print(f"\n📊 Decision Distribution:")
            for decision, count in results['decision_distribution'].items():
                print(f"   {decision:15s}: {count:3d}")
            
            print(f"\n📈 Data Information:")
            print(f"   Training samples:     {results['data_info']['train_samples']}")
            print(f"   Validation samples:   {results['data_info']['val_samples']}")
            print(f"   Original data shape:  {results['data_info']['original_data_shape']}")
            print(f"   Label distribution:   {results['data_info']['label_distribution']}")
            
            # Performance assessment
            if results['validation_accuracy'] > 0.7:
                print(f"\n✅ EXCELLENT: Model performs well on real data!")
            elif results['validation_accuracy'] > 0.5:
                print(f"\n⚠️  GOOD: Model shows learning on real data")
            elif results['validation_accuracy'] > 0.4:
                print(f"\n🔄 PARTIAL: Some learning detected, needs tuning")
            else:
                print(f"\n❌ POOR: Model struggles with real data patterns")
            
            return results
        
        def save_results(self, results):
            """Save results to file"""
            
            with open('real_data_gradient_results.json', 'w') as f:
                json.dump(results, f, indent=2)
            
            print(f"\n💾 Results saved to 'real_data_gradient_results.json'")
    
    def main():
        """Main testing function"""
        
        print("🧠 Gradient Classifier - Real Fragment Data Test")
        print("=" * 60)
        
        tester = RealDataTester()
        
        # Load real data
        if not tester.load_real_data():
            print("❌ Failed to load real data")
            return
        
        # Test on real data
        try:
            results = tester.test_on_real_data(n_train=200, n_val=50)
            tester.print_results(results)
            tester.save_results(results)
            
            return results
            
        except Exception as e:
            print(f"❌ Error during testing: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    if __name__ == "__main__":
        results = main()

except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Required packages: torch, numpy, pandas")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()