"""
Test Hybrid Two-Part Processing on Real Dataset
Tests separate concatenation logic for normal vs candidate parts on actual fragment data
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from hybrid_branch_classifier_implementation import HybridBranchClassifier
import json
import time
from collections import defaultdict, Counter

def load_real_data():
    """Load and prepare real fragment data for two-part testing"""
    
    print("📊 Loading Real Fragment Data for Two-Part Testing")
    print("=" * 60)
    
    try:
        # Load real data
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        print(f"   ✅ Loaded X_train: {X_data.shape}")
        print(f"   ✅ Loaded y_train: {y_data.shape}")
        
        # Extract labels
        if 'structural_breakpoint' in y_data.columns:
            labels = y_data['structural_breakpoint'].astype(int).values
        else:
            labels = y_data.iloc[:, 0].astype(int).values
        
        print(f"   📊 Label distribution: {dict(Counter(labels))}")
        
        return X_data, y_data, labels
        
    except Exception as e:
        print(f"   ❌ Error loading real data: {e}")
        return None, None, None

def create_two_part_segments(X_data, labels, max_samples=500, segment_length=50):
    """Create normal and candidate part pairs from real data"""
    
    print(f"🔧 Creating Two-Part Segments from Real Data")
    print(f"   Target samples: {max_samples}")
    print(f"   Segment length per part: {segment_length}")
    
    normal_parts = []
    candidate_parts = []
    segment_labels = []
    
    # Get numeric data
    if isinstance(X_data, pd.DataFrame):
        numeric_cols = X_data.select_dtypes(include=[np.number]).columns
        numeric_data = X_data[numeric_cols].values
        
        print(f"   📊 Numeric columns: {len(numeric_cols)}")
        print(f"   📊 Numeric data shape: {numeric_data.shape}")
        
        # Strategy: Use feature vectors as time series, split into normal/candidate parts
        if numeric_data.shape[1] >= segment_length * 2:
            print("   📈 Using feature vectors split into normal/candidate parts")
            
            n_samples = min(max_samples, numeric_data.shape[0], len(labels))
            
            for i in range(n_samples):
                # Split features into two parts
                full_features = numeric_data[i, :segment_length * 2]
                
                if np.isfinite(full_features).all() and np.std(full_features) > 1e-6:
                    # First half as normal part
                    normal_part = full_features[:segment_length].astype(np.float32)
                    # Second half as candidate part
                    candidate_part = full_features[segment_length:segment_length*2].astype(np.float32)
                    
                    normal_parts.append(normal_part)
                    candidate_parts.append(candidate_part)
                    segment_labels.append(labels[i])
        
        # Alternative strategy: Use sliding window approach
        elif 'value' in X_data.columns and 'id' in X_data.columns:
            print("   📈 Creating normal/candidate pairs from time series values")
            
            unique_ids = X_data['id'].unique()[:max_samples // 10]
            
            for ts_id in unique_ids:
                ts_data = X_data[X_data['id'] == ts_id]['value'].values
                
                if len(ts_data) >= segment_length * 2:
                    # Create multiple normal/candidate pairs from this time series
                    step_size = max(1, len(ts_data) // 5)
                    
                    for start_idx in range(0, len(ts_data) - segment_length * 2 + 1, step_size):
                        if len(normal_parts) >= max_samples:
                            break
                        
                        # Normal part: first segment
                        normal_part = ts_data[start_idx:start_idx + segment_length]
                        # Candidate part: next segment
                        candidate_part = ts_data[start_idx + segment_length:start_idx + segment_length * 2]
                        
                        if (np.isfinite(normal_part).all() and np.isfinite(candidate_part).all() and
                            np.std(normal_part) > 1e-6 and np.std(candidate_part) > 1e-6):
                            
                            normal_parts.append(normal_part.astype(np.float32))
                            candidate_parts.append(candidate_part.astype(np.float32))
                            
                            label_idx = min(int(ts_id), len(labels) - 1)
                            segment_labels.append(labels[label_idx])
        
        # Fallback: Use first column as time series
        else:
            print("   📈 Using first numeric column split into normal/candidate parts")
            
            if numeric_data.shape[1] > 0:
                time_series = numeric_data[:, 0]
                
                # Create overlapping normal/candidate pairs
                step_size = segment_length
                
                for i in range(0, len(time_series) - segment_length * 2, step_size):
                    if len(normal_parts) >= max_samples:
                        break
                    
                    normal_part = time_series[i:i + segment_length]
                    candidate_part = time_series[i + segment_length:i + segment_length * 2]
                    
                    if (np.isfinite(normal_part).all() and np.isfinite(candidate_part).all() and
                        np.std(normal_part) > 1e-6 and np.std(candidate_part) > 1e-6):
                        
                        normal_parts.append(normal_part.astype(np.float32))
                        candidate_parts.append(candidate_part.astype(np.float32))
                        
                        mid_idx = min(i + segment_length, len(labels) - 1)
                        segment_labels.append(labels[mid_idx])
    
    print(f"   ✅ Created {len(normal_parts)} normal/candidate part pairs")
    
    return np.array(normal_parts), np.array(candidate_parts), np.array(segment_labels)

def test_two_part_classifier_on_real_data(normal_parts, candidate_parts, labels, test_samples=100):
    """Test the two-part classifier on real data"""
    
    print(f"\n🧪 Testing Two-Part Classifier on Real Data")
    print(f"   Test samples: {min(test_samples, len(normal_parts))}")
    print("=" * 50)
    
    # Initialize both classifiers for comparison
    single_classifier = HybridBranchClassifier(
        max_active_branches=8,
        separate_concatenation=False
    )
    
    two_part_classifier = HybridBranchClassifier(
        max_active_branches=8,
        separate_concatenation=True
    )
    
    # Test results storage
    single_results = []
    two_part_results = []
    
    n_test = min(test_samples, len(normal_parts))
    
    print(f"🔄 Processing {n_test} samples...")
    
    for i in range(n_test):
        normal_part = normal_parts[i]
        candidate_part = candidate_parts[i]
        true_label = labels[i]
        
        # Single-part processing (concatenated)
        combined_data = np.concatenate([normal_part, candidate_part])
        single_result = single_classifier.process_new_part(combined_data, timestamp=i)
        single_result['true_label'] = true_label
        single_results.append(single_result)
        
        # Two-part processing (separate)
        two_part_result = two_part_classifier.process_two_parts(normal_part, candidate_part, timestamp=i)
        two_part_result['true_label'] = true_label
        two_part_results.append(two_part_result)
        
        # Progress update
        if (i + 1) % 20 == 0:
            print(f"   Processed {i + 1}/{n_test} samples...")
    
    return single_results, two_part_results

def analyze_results(single_results, two_part_results):
    """Analyze and compare single vs two-part results"""
    
    print(f"\n📊 Analyzing Results")
    print("=" * 40)
    
    # Decision distribution
    single_decisions = Counter([r['decision'] for r in single_results])
    two_part_decisions = Counter([r['decision'] for r in two_part_results])
    
    print(f"📈 Decision Distribution:")
    print(f"   Single-Part: {dict(single_decisions)}")
    print(f"   Two-Part:    {dict(two_part_decisions)}")
    
    # Confidence analysis
    single_confidences = [r['confidence'] for r in single_results]
    two_part_confidences = [r['confidence'] for r in two_part_results]
    
    print(f"\n💪 Confidence Analysis:")
    print(f"   Single-Part: Mean={np.mean(single_confidences):.3f}, Std={np.std(single_confidences):.3f}")
    print(f"   Two-Part:    Mean={np.mean(two_part_confidences):.3f}, Std={np.std(two_part_confidences):.3f}")
    
    # Processing time analysis
    single_times = [r['processing_time'] for r in single_results]
    two_part_times = [r['processing_time'] for r in two_part_results]
    
    print(f"\n⏱️  Processing Time Analysis:")
    print(f"   Single-Part: Mean={np.mean(single_times)*1000:.1f}ms, Std={np.std(single_times)*1000:.1f}ms")
    print(f"   Two-Part:    Mean={np.mean(two_part_times)*1000:.1f}ms, Std={np.std(two_part_times)*1000:.1f}ms")
    
    # Label correlation analysis
    true_labels = [r['true_label'] for r in two_part_results]
    
    # Map decisions to binary predictions
    def decision_to_binary(decision):
        if 'STRUCTURAL_BREAK' in decision or 'BREAK' in decision:
            return 1
        else:
            return 0
    
    single_predictions = [decision_to_binary(r['decision']) for r in single_results]
    two_part_predictions = [decision_to_binary(r['decision']) for r in two_part_results]
    
    # Calculate accuracy
    single_accuracy = np.mean(np.array(single_predictions) == np.array(true_labels))
    two_part_accuracy = np.mean(np.array(two_part_predictions) == np.array(true_labels))
    
    print(f"\n🎯 Prediction Accuracy (vs true labels):")
    print(f"   Single-Part: {single_accuracy:.3f}")
    print(f"   Two-Part:    {two_part_accuracy:.3f}")
    
    # Two-part specific analysis
    if len(two_part_results) > 0 and 'part_analysis' in two_part_results[0]:
        print(f"\n🔍 Two-Part Specific Analysis:")
        
        # Similarity differences
        similarity_diffs = []
        for result in two_part_results:
            if 'part_analysis' in result and 'similarity_difference' in result['part_analysis']:
                sim_diffs = list(result['part_analysis']['similarity_difference'].values())
                if sim_diffs:
                    similarity_diffs.extend(sim_diffs)
        
        if similarity_diffs:
            print(f"   Similarity Differences: Mean={np.mean(similarity_diffs):.3f}, Max={np.max(similarity_diffs):.3f}")
        
        # Feature differences
        normal_means = []
        candidate_means = []
        for result in two_part_results:
            if 'part_analysis' in result:
                normal_means.append(result['part_analysis']['normal_features']['mean'])
                candidate_means.append(result['part_analysis']['candidate_features']['mean'])
        
        mean_differences = [abs(n - c) for n, c in zip(normal_means, candidate_means)]
        print(f"   Mean Feature Differences: Mean={np.mean(mean_differences):.3f}, Max={np.max(mean_differences):.3f}")
    
    return {
        'single_decisions': dict(single_decisions),
        'two_part_decisions': dict(two_part_decisions),
        'single_accuracy': single_accuracy,
        'two_part_accuracy': two_part_accuracy,
        'single_confidence_mean': np.mean(single_confidences),
        'two_part_confidence_mean': np.mean(two_part_confidences),
        'single_time_mean': np.mean(single_times),
        'two_part_time_mean': np.mean(two_part_times)
    }

def visualize_real_data_results(single_results, two_part_results, normal_parts, candidate_parts):
    """Create visualizations of the real data results"""
    
    print(f"\n📊 Creating Visualizations...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Two-Part Processing on Real Dataset', fontsize=16)
    
    # 1. Decision distribution comparison
    single_decisions = Counter([r['decision'] for r in single_results])
    two_part_decisions = Counter([r['decision'] for r in two_part_results])
    
    decisions = list(set(single_decisions.keys()) | set(two_part_decisions.keys()))
    single_counts = [single_decisions.get(d, 0) for d in decisions]
    two_part_counts = [two_part_decisions.get(d, 0) for d in decisions]
    
    x = np.arange(len(decisions))
    width = 0.35
    
    axes[0, 0].bar(x - width/2, single_counts, width, label='Single-Part', alpha=0.7)
    axes[0, 0].bar(x + width/2, two_part_counts, width, label='Two-Part', alpha=0.7)
    axes[0, 0].set_xlabel('Decision Type')
    axes[0, 0].set_ylabel('Count')
    axes[0, 0].set_title('Decision Distribution Comparison')
    axes[0, 0].set_xticks(x)
    axes[0, 0].set_xticklabels(decisions, rotation=45)
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Confidence distribution
    single_confidences = [r['confidence'] for r in single_results]
    two_part_confidences = [r['confidence'] for r in two_part_results]
    
    axes[0, 1].hist(single_confidences, bins=20, alpha=0.5, label='Single-Part', color='blue')
    axes[0, 1].hist(two_part_confidences, bins=20, alpha=0.5, label='Two-Part', color='red')
    axes[0, 1].set_xlabel('Confidence')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title('Confidence Distribution')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Processing time comparison
    single_times = np.array([r['processing_time'] for r in single_results]) * 1000
    two_part_times = np.array([r['processing_time'] for r in two_part_results]) * 1000
    
    axes[0, 2].boxplot([single_times, two_part_times], labels=['Single-Part', 'Two-Part'])
    axes[0, 2].set_ylabel('Processing Time (ms)')
    axes[0, 2].set_title('Processing Time Comparison')
    axes[0, 2].grid(True, alpha=0.3)
    
    # 4. Sample normal vs candidate parts
    sample_indices = np.random.choice(len(normal_parts), min(5, len(normal_parts)), replace=False)
    
    for i, idx in enumerate(sample_indices):
        if i >= 3:  # Only show first 3 samples
            break
        
        normal_part = normal_parts[idx]
        candidate_part = candidate_parts[idx]
        
        time_normal = np.arange(len(normal_part))
        time_candidate = np.arange(len(normal_part), len(normal_part) + len(candidate_part))
        
        axes[1, i].plot(time_normal, normal_part, 'b-', label='Normal Part', linewidth=2)
        axes[1, i].plot(time_candidate, candidate_part, 'r-', label='Candidate Part', linewidth=2)
        axes[1, i].axvline(x=len(normal_part), color='gray', linestyle='--', alpha=0.5)
        
        # Show decision for this sample
        decision = two_part_results[idx]['decision']
        confidence = two_part_results[idx]['confidence']
        
        axes[1, i].set_title(f'Sample {idx}: {decision}\n(Conf: {confidence:.3f})')
        axes[1, i].set_xlabel('Time')
        axes[1, i].set_ylabel('Value')
        axes[1, i].legend()
        axes[1, i].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('hybrid_two_part_real_data_results.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visualization saved to 'hybrid_two_part_real_data_results.png'")
    
    return fig

def save_results(single_results, two_part_results, analysis_results):
    """Save results to files"""
    
    print(f"\n💾 Saving Results...")
    
    # Save detailed results
    results_data = {
        'single_results': single_results,
        'two_part_results': two_part_results,
        'analysis': analysis_results,
        'metadata': {
            'total_samples': len(single_results),
            'timestamp': time.time()
        }
    }
    
    with open('hybrid_two_part_real_data_results.json', 'w') as f:
        json.dump(results_data, f, indent=2, default=str)
    
    # Save summary CSV
    summary_data = []
    for i, (single, two_part) in enumerate(zip(single_results, two_part_results)):
        summary_data.append({
            'sample_id': i,
            'true_label': single['true_label'],
            'single_decision': single['decision'],
            'single_confidence': single['confidence'],
            'single_processing_time': single['processing_time'],
            'two_part_decision': two_part['decision'],
            'two_part_confidence': two_part['confidence'],
            'two_part_processing_time': two_part['processing_time'],
            'two_part_normal_novelty': two_part.get('normal_novelty', 0),
            'two_part_candidate_novelty': two_part.get('candidate_novelty', 0)
        })
    
    summary_df = pd.DataFrame(summary_data)
    summary_df.to_csv('hybrid_two_part_real_data_summary.csv', index=False)
    
    print(f"   📄 Detailed results: hybrid_two_part_real_data_results.json")
    print(f"   📊 Summary CSV: hybrid_two_part_real_data_summary.csv")
    print(f"   📈 Visualization: hybrid_two_part_real_data_results.png")

def main():
    """Main test function"""
    
    print("🚀 Hybrid Two-Part Processing on Real Dataset")
    print("=" * 70)
    print("🔥 Testing separate concatenation logic on actual fragment data")
    print("🎯 Comparing single-part vs two-part processing approaches")
    print("=" * 70)
    
    # Load real data
    X_data, y_data, labels = load_real_data()
    
    if X_data is None:
        print("❌ Failed to load real data")
        return None
    
    # Create two-part segments
    normal_parts, candidate_parts, segment_labels = create_two_part_segments(
        X_data, labels, max_samples=200, segment_length=50
    )
    
    if len(normal_parts) == 0:
        print("❌ Failed to create two-part segments")
        return None
    
    print(f"\n📈 Dataset Overview:")
    print(f"   Normal parts: {len(normal_parts)} x {normal_parts.shape[1]}")
    print(f"   Candidate parts: {len(candidate_parts)} x {candidate_parts.shape[1]}")
    print(f"   Label distribution: {dict(Counter(segment_labels))}")
    
    # Test classifiers
    single_results, two_part_results = test_two_part_classifier_on_real_data(
        normal_parts, candidate_parts, segment_labels, test_samples=150
    )
    
    # Analyze results
    analysis_results = analyze_results(single_results, two_part_results)
    
    # Create visualizations
    visualize_real_data_results(single_results, two_part_results, normal_parts, candidate_parts)
    
    # Save results
    save_results(single_results, two_part_results, analysis_results)
    
    # Final summary
    print(f"\n🏆 FINAL SUMMARY")
    print("=" * 50)
    print(f"📊 Samples Processed: {len(single_results)}")
    print(f"🎯 Single-Part Accuracy: {analysis_results['single_accuracy']:.3f}")
    print(f"🎯 Two-Part Accuracy: {analysis_results['two_part_accuracy']:.3f}")
    print(f"💪 Single-Part Avg Confidence: {analysis_results['single_confidence_mean']:.3f}")
    print(f"💪 Two-Part Avg Confidence: {analysis_results['two_part_confidence_mean']:.3f}")
    print(f"⏱️  Single-Part Avg Time: {analysis_results['single_time_mean']*1000:.1f}ms")
    print(f"⏱️  Two-Part Avg Time: {analysis_results['two_part_time_mean']*1000:.1f}ms")
    
    # Performance assessment
    if analysis_results['two_part_accuracy'] > analysis_results['single_accuracy']:
        print(f"\n✅ TWO-PART PROCESSING SHOWS BETTER ACCURACY!")
        print(f"   Improvement: +{(analysis_results['two_part_accuracy'] - analysis_results['single_accuracy'])*100:.1f}%")
    elif analysis_results['two_part_accuracy'] < analysis_results['single_accuracy']:
        print(f"\n⚠️  Single-part processing shows better accuracy")
        print(f"   Difference: {(analysis_results['single_accuracy'] - analysis_results['two_part_accuracy'])*100:.1f}%")
    else:
        print(f"\n🤝 Both approaches show similar accuracy")
    
    print(f"\n🎉 Real Dataset Two-Part Testing Complete!")
    
    return {
        'single_results': single_results,
        'two_part_results': two_part_results,
        'analysis': analysis_results
    }

if __name__ == "__main__":
    results = main()