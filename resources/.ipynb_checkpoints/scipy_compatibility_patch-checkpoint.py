#!/usr/bin/env python3
"""
Scipy compatibility patch for TSFresh 1280 features
Handles the _lazywhere import issue in newer scipy versions
"""

import sys
import warnings

def patch_scipy_compatibility():
    """
    Patch scipy compatibility issues for TSFresh 1280
    """
    try:
        import scipy
        from scipy._lib._util import _lazywhere
        # If this works, no patch needed
        return True
    except ImportError:
        # Try alternative locations for _lazywhere
        try:
            # In newer scipy versions, _lazywhere might be in different location
            from scipy._lib import _util
            if hasattr(_util, '_lazywhere'):
                return True
            
            # Try to create a compatible _lazywhere function
            import numpy as np
            
            def _lazywhere(cond, arrays, f, fillvalue=None, f2=None):
                """
                Compatibility implementation of _lazywhere for newer scipy versions
                """
                if fillvalue is None:
                    fillvalue = np.nan
                
                # Simple implementation that works for most TSFresh use cases
                cond = np.asarray(cond)
                if f2 is None:
                    return np.where(cond, f(*arrays), fillvalue)
                else:
                    return np.where(cond, f(*arrays), f2(*arrays))
            
            # Monkey patch the function into the expected location
            if not hasattr(_util, '_lazywhere'):
                _util._lazywhere = _lazywhere
            
            # Also patch into the main scipy._lib._util if it exists
            try:
                import scipy._lib._util as main_util
                if not hasattr(main_util, '_lazywhere'):
                    main_util._lazywhere = _lazywhere
            except:
                pass
            
            print("✓ Applied scipy compatibility patch for _lazywhere")
            return True
            
        except Exception as e:
            print(f"❌ Could not patch scipy compatibility: {e}")
            return False

def test_scipy_patch():
    """Test if the scipy patch works"""
    try:
        from scipy._lib._util import _lazywhere
        import numpy as np
        
        # Test the function with simple data
        cond = np.array([True, False, True])
        arrays = [np.array([1, 2, 3])]
        result = _lazywhere(cond, arrays, lambda x: x * 2, fillvalue=0)
        
        expected = np.array([2, 0, 6])
        if np.allclose(result, expected):
            print("✓ Scipy patch test passed")
            return True
        else:
            print("❌ Scipy patch test failed - incorrect results")
            return False
            
    except Exception as e:
        print(f"❌ Scipy patch test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing scipy compatibility patch...")
    
    if patch_scipy_compatibility():
        if test_scipy_patch():
            print("🎉 Scipy compatibility patch successful!")
        else:
            print("⚠️  Patch applied but test failed")
    else:
        print("❌ Could not apply scipy compatibility patch")