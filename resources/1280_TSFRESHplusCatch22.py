"TSFRESH plus Catch 22"
from tsfresh.feature_extraction.feature_calculators import (
    symmetry_looking, energy_ratio_by_chunks, fft_aggregated, index_mass_quantile,
    binned_entropy, last_location_of_maximum, lempel_ziv_complexity,
    longest_strike_above_mean, longest_strike_below_mean, mean_second_derivative_central,
    percentage_of_reoccurring_datapoints_to_all_datapoints,
    percentage_of_reoccurring_values_to_all_values, ratio_beyond_r_sigma,
    ratio_value_number_to_time_series_length, time_reversal_asymmetry_statistic
)

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import entropy, relfreq
from scipy.signal import (welch, coherence, correlate, correlation_lags, convolve, fftconvolve,
                         periodogram, spectrogram, find_peaks, peak_prominences, peak_widths,
                         lfilter, filtfilt, savgol_filter, detrend, hilbert)
from scipy.stats import wasserstein_distance, cramervonmises_2samp
from tqdm import tqdm
from statsmodels.tsa.stattools import adfuller, kpss, coint, grangercausalitytests
from statsmodels.stats.diagnostic import kstest_fit
import statsmodels.api as sm
from arch import arch_model
from sklearn.feature_selection import mutual_info_regression
import warnings
from multiprocessing import Pool
import os
import pycatch22  # Added for pycatch22 features

warnings.filterwarnings('ignore')
 
def ryan_joiner_test(arr):
    if len(arr) < 4:
        return np.nan
    try:
        arr = arr[~np.isnan(arr)]
        n = len(arr)
        sorted_arr = np.sort(arr)
        z = stats.norm.ppf((np.arange(1, n + 1) - 0.375) / (n + 0.25))
        corr = np.corrcoef(sorted_arr, z)[0, 1]
        return corr if not np.isnan(corr) else np.nan
    except Exception:
        return np.nan

def cliffs_delta(x, y):
    if len(x) < 2 or len(y) < 2:
        return np.nan
    try:
        x, y = x[~np.isnan(x)], y[~np.isnan(y)]
        n_x, n_y = len(x), len(y)
        count = sum(1 if xi > yj else -1 if xi < yj else 0 for xi in x for yj in y)
        return count / (n_x * n_y) if n_x * n_y > 0 else np.nan
    except Exception:
        return np.nan

def jensen_shannon_divergence(pre, post):
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        pre, post = pre[~np.isnan(pre)], post[~np.isnan(post)]
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else np.ones_like(hist_pre) / len(hist_pre)
        hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else np.ones_like(hist_post) / len(hist_post)
        m = 0.5 * (hist_pre + hist_post)
        jsd = 0.5 * (entropy(hist_pre, m) + entropy(hist_post, m))
        return jsd if not np.isnan(jsd) else np.nan
    except Exception:
        return np.nan

def ssa_features(arr, window_size=None, k=3):
    """
    Compute Single Spectrum Analysis (SSA) features for a time series.
    
    Parameters:
    - arr: 1D numpy array, input time series.
    - window_size: int, size of the sliding window for SSA (default: min(40, len(arr)//2)).
    - k: int, number of singular values/components to extract (default: 3).
    
    Returns:
    - dict: SSA features (singular values, reconstruction error, trend mean/std).
    """
    if len(arr) < 20:
        return {
            'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
            'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
        }
    
    try:
        arr = arr[~np.isnan(arr)]  # Remove NaNs
        n = len(arr)
        window_size = window_size or min(40, n // 2)
        if window_size < 2 or window_size > n:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }
        
        # Create trajectory matrix
        L = window_size
        K = n - L + 1
        X = np.array([arr[i:i+L] for i in range(K)])
        
        # SVD
        U, s, Vt = np.linalg.svd(X, full_matrices=False)
        
        # Singular values (top k)
        singular_values = s[:k]
        singular_values = np.pad(singular_values, (0, max(0, k - len(singular_values))), constant_values=np.nan)
        
        # Reconstruction using top k components
        X_reconstructed = np.zeros_like(X)
        for i in range(min(k, len(s))):
            X_reconstructed += s[i] * np.outer(U[:, i], Vt[i, :])
        
        # Hankelization (average along anti-diagonals)
        reconstructed_series = np.zeros(n)
        count = np.zeros(n)
        for i in range(K):
            for j in range(L):
                if i + j < n:
                    reconstructed_series[i + j] += X_reconstructed[i, j]
                    count[i + j] += 1
        reconstructed_series = reconstructed_series / count
        reconstruction_error = np.nanmean((arr - reconstructed_series) ** 2)
        
        # Trend component (first component)
        trend = s[0] * np.outer(U[:, 0], Vt[0, :])
        trend_series = np.zeros(n)
        count = np.zeros(n)
        for i in range(K):
            for j in range(L):
                if i + j < n:
                    trend_series[i + j] += trend[i, j]
                    count[i + j] += 1
        trend_series = trend_series / count
        trend_mean = np.nanmean(trend_series)
        trend_std = np.nanstd(trend_series, ddof=1) if len(trend_series) > 1 else np.nan
        
        return {
            'ssa_singular_value_1': singular_values[0],
            'ssa_singular_value_2': singular_values[1] if len(singular_values) > 1 else np.nan,
            'ssa_singular_value_3': singular_values[2] if len(singular_values) > 2 else np.nan,
            'ssa_reconstruction_error': reconstruction_error,
            'ssa_trend_mean': trend_mean,
            'ssa_trend_std': trend_std
        }
    except Exception:
        return {
            'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
            'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
        }

def compute_features_for_segment(pre, post, whole, segment_name_prefix='', compute_pre_post=True, 
                                reference_features=None, normalize=False, tstar=None, 
                                whole_indices=None, weight_type='proportion', sigma=0.1, 
                                compute_jsd=False):
    feats = {}
    
    # Compute weight for time-based or value-based bins
    if tstar is not None and whole_indices is not None:
        n_total = len(whole_indices)
        if n_total > 0:
            if weight_type == 'proportion':
                n_post = np.sum(whole_indices >= tstar)
                w_i = n_post / n_total
                feats[f'{segment_name_prefix}post_length_proportion'] = w_i
            elif weight_type == 'kernel':
                distances = np.abs(whole_indices - tstar) / len(whole)
                kernel = np.exp(-distances**2 / (2 * sigma**2))
                w_i = np.mean(kernel) if len(kernel) > 0 else 0.0
                feats[f'{segment_name_prefix}post_length_proportion'] = np.sum(whole_indices >= tstar) / n_total
            else:
                raise ValueError("weight_type must be 'proportion' or 'kernel'")
        else:
            w_i = 0.0
            feats[f'{segment_name_prefix}post_length_proportion'] = np.nan
    else:
        w_i = 1.0
        feats[f'{segment_name_prefix}post_length_proportion'] = np.nan
    
    # Compute existing statistical features (mean, std, etc.) for whole segment
    # Compute existing features for whole segment
    feats[f'{segment_name_prefix}len_whole'] = len(whole)
    mean_whole = np.nanmean(whole) if len(whole) > 0 else np.nan
    feats[f'{segment_name_prefix}mean_whole'] = mean_whole
    feats[f'{segment_name_prefix}mean_whole_weighted'] = w_i * mean_whole if not np.isnan(mean_whole) else np.nan
    std_whole = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
    feats[f'{segment_name_prefix}std_whole'] = std_whole
    feats[f'{segment_name_prefix}std_whole_weighted'] = w_i * std_whole if not np.isnan(std_whole) else np.nan
    var_whole = np.nanvar(whole, ddof=1) if len(whole) > 1 else np.nan
    feats[f'{segment_name_prefix}var_whole'] = var_whole
    feats[f'{segment_name_prefix}var_whole_weighted'] = w_i * var_whole if not np.isnan(var_whole) else np.nan
    skew_whole = stats.skew(whole, nan_policy='omit') if len(whole) > 2 else np.nan
    feats[f'{segment_name_prefix}skew_whole'] = skew_whole
    feats[f'{segment_name_prefix}skew_whole_weighted'] = w_i * skew_whole if not np.isnan(skew_whole) else np.nan
    kurt_whole = stats.kurtosis(whole, nan_policy='omit') if len(whole) > 2 else np.nan
    feats[f'{segment_name_prefix}kurt_whole'] = kurt_whole
    feats[f'{segment_name_prefix}kurt_whole_weighted'] = w_i * kurt_whole if not np.isnan(kurt_whole) else np.nan



    # TSFRESH Features (excluding symmetry-related features)
    tsfresh_features = {}
    
    # Helper function to compute tsfresh feature
    def compute_tsfresh_feature(feature_func, arr, params=None, name=None, min_length=2):
        try:
            if len(arr) < min_length:
                return np.nan
            if params:
                result = feature_func(arr, params)
                if isinstance(result, list) and len(result) > 0:
                    return float(result[0][1]) if len(result[0]) > 1 else float(result)
                return float(result)
            return float(feature_func(arr))
        except Exception:
            return np.nan
    
    # Energy Ratio by Chunks
    if len(whole) >= 2:
        for i in range(2):
            ratio = compute_tsfresh_feature(
                energy_ratio_by_chunks, whole, [{"num_segments": 2, "segment_focus": i}],
                f'energy_ratio_chunk{i+1}_whole', min_length=2
            )
            tsfresh_features[f'{segment_name_prefix}energy_ratio_chunk{i+1}_whole'] = ratio
            tsfresh_features[f'{segment_name_prefix}energy_ratio_chunk{i+1}_whole_weighted'] = w_i * ratio if not np.isnan(ratio) else np.nan
            if normalize:
                feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
                tsfresh_features[f'{segment_name_prefix}energy_ratio_chunk{i+1}_whole_norm_zscore'] = (
                    (ratio - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
                )
            if compute_pre_post and len(pre) >= 2 and len(post) >= 2:
                ratio_pre = compute_tsfresh_feature(
                    energy_ratio_by_chunks, pre, [{"num_segments": 2, "segment_focus": i}],
                    min_length=2
                )
                ratio_post = compute_tsfresh_feature(
                    energy_ratio_by_chunks, post, [{"num_segments": 2, "segment_focus": i}],
                    min_length=2
                )
                tsfresh_features[f'{segment_name_prefix}energy_ratio_chunk{i+1}_pre'] = ratio_pre
                tsfresh_features[f'{segment_name_prefix}energy_ratio_chunk{i+1}_post'] = ratio_post
                tsfresh_features[f'{segment_name_prefix}diff_energy_ratio_chunk{i+1}'] = (
                    ratio_post - ratio_pre if not (np.isnan(ratio_pre) or np.isnan(ratio_post)) else np.nan
                )
    
    # FFT Aggregated (centroid, variance)
    if len(whole) >= 10:
        for aggtype, stat in zip(["centroid", "variance"], ["centroid", "variance"]):
            fft_val = compute_tsfresh_feature(
                fft_aggregated, whole, [{"aggtype": aggtype}],
                f'fft_agg_{stat}_whole', min_length=10
            )
            tsfresh_features[f'{segment_name_prefix}fft_agg_{stat}_whole'] = fft_val
            tsfresh_features[f'{segment_name_prefix}fft_agg_{stat}_whole_weighted'] = w_i * fft_val if not np.isnan(fft_val) else np.nan
            if normalize:
                feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
                tsfresh_features[f'{segment_name_prefix}fft_agg_{stat}_whole_norm_zscore'] = (
                    (fft_val - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
                )
    
    # Index Mass Quantile
    if len(whole) >= 5:
        for q in [0.25, 0.5, 0.75]:
            imq = compute_tsfresh_feature(
                index_mass_quantile, whole, [{"q": q}],
                f'index_mass_quantile_q{int(q*100)}_whole', min_length=5
            )
            tsfresh_features[f'{segment_name_prefix}index_mass_quantile_q{int(q*100)}_whole'] = imq
            tsfresh_features[f'{segment_name_prefix}index_mass_quantile_q{int(q*100)}_whole_weighted'] = w_i * imq if not np.isnan(imq) else np.nan
            if normalize:
                feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
                tsfresh_features[f'{segment_name_prefix}index_mass_quantile_q{int(q*100)}_whole_norm_zscore'] = (
                    (imq - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
                )
            if compute_pre_post and len(pre) >= 5 and len(post) >= 5:
                imq_pre = compute_tsfresh_feature(
                    index_mass_quantile, pre, [{"q": q}], min_length=5
                )
                imq_post = compute_tsfresh_feature(
                    index_mass_quantile, post, [{"q": q}], min_length=5
                )
                tsfresh_features[f'{segment_name_prefix}index_mass_quantile_q{int(q*100)}_pre'] = imq_pre
                tsfresh_features[f'{segment_name_prefix}index_mass_quantile_q{int(q*100)}_post'] = imq_post
                tsfresh_features[f'{segment_name_prefix}diff_index_mass_quantile_q{int(q*100)}'] = (
                    imq_post - imq_pre if not (np.isnan(imq_pre) or np.isnan(imq_post)) else np.nan
                )
    
    # Binned Entropy
    if len(whole) >= 5:
        be = compute_tsfresh_feature(
            binned_entropy, whole, params={"max_bins": 10},
            name='binned_entropy_whole', min_length=5
        )
        tsfresh_features[f'{segment_name_prefix}binned_entropy_whole'] = be
        tsfresh_features[f'{segment_name_prefix}binned_entropy_whole_weighted'] = w_i * be if not np.isnan(be) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}binned_entropy_whole_norm_zscore'] = (
                (be - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 5 and len(post) >= 5:
            be_pre = compute_tsfresh_feature(binned_entropy, pre, params={"max_bins": 10}, min_length=5)
            be_post = compute_tsfresh_feature(binned_entropy, post, params={"max_bins": 10}, min_length=5)
            tsfresh_features[f'{segment_name_prefix}binned_entropy_pre'] = be_pre
            tsfresh_features[f'{segment_name_prefix}binned_entropy_post'] = be_post
            tsfresh_features[f'{segment_name_prefix}diff_binned_entropy'] = (
                be_post - be_pre if not (np.isnan(be_pre) or np.isnan(be_post)) else np.nan
            )
    
    # Last Location of Maximum
    if len(whole) >= 2:
        llm = compute_tsfresh_feature(
            last_location_of_maximum, whole,
            name='last_location_of_maximum_whole', min_length=2
        )
        tsfresh_features[f'{segment_name_prefix}last_location_of_maximum_whole'] = llm
        tsfresh_features[f'{segment_name_prefix}last_location_of_maximum_whole_weighted'] = w_i * llm if not np.isnan(llm) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}last_location_of_maximum_whole_norm_zscore'] = (
                (llm - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 2 and len(post) >= 2:
            llm_pre = compute_tsfresh_feature(last_location_of_maximum, pre, min_length=2)
            llm_post = compute_tsfresh_feature(last_location_of_maximum, post, min_length=2)
            tsfresh_features[f'{segment_name_prefix}last_location_of_maximum_pre'] = llm_pre
            tsfresh_features[f'{segment_name_prefix}last_location_of_maximum_post'] = llm_post
            tsfresh_features[f'{segment_name_prefix}diff_last_location_of_maximum'] = (
                llm_post - llm_pre if not (np.isnan(llm_pre) or np.isnan(llm_post)) else np.nan
            )
    
    # Lempel-Ziv Complexity
    if len(whole) >= 10:
        lzc = compute_tsfresh_feature(
            lempel_ziv_complexity, whole, 100,
            name='lempel_ziv_complexity_whole', min_length=10
        )
        tsfresh_features[f'{segment_name_prefix}lempel_ziv_complexity_whole'] = lzc
        tsfresh_features[f'{segment_name_prefix}lempel_ziv_complexity_whole_weighted'] = w_i * lzc if not np.isnan(lzc) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}lempel_ziv_complexity_whole_norm_zscore'] = (
                (lzc - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 10 and len(post) >= 10:
            lzc_pre = compute_tsfresh_feature(lempel_ziv_complexity, pre, 100, min_length=10)
            lzc_post = compute_tsfresh_feature(lempel_ziv_complexity, post, 100, min_length=10)
            tsfresh_features[f'{segment_name_prefix}lempel_ziv_complexity_pre'] = lzc_pre
            tsfresh_features[f'{segment_name_prefix}lempel_ziv_complexity_post'] = lzc_post
            tsfresh_features[f'{segment_name_prefix}diff_lempel_ziv_complexity'] = (
                lzc_post - lzc_pre if not (np.isnan(lzc_pre) or np.isnan(lzc_post)) else np.nan
            )
    
    # Longest Strike Above/Below Mean
    if len(whole) >= 2:
        lsam = compute_tsfresh_feature(
            longest_strike_above_mean, whole,
            name='longest_strike_above_mean_whole', min_length=2
        )
        lsbm = compute_tsfresh_feature(
            longest_strike_below_mean, whole,
            name='longest_strike_below_mean_whole', min_length=2
        )
        tsfresh_features[f'{segment_name_prefix}longest_strike_above_mean_whole'] = lsam
        tsfresh_features[f'{segment_name_prefix}longest_strike_below_mean_whole'] = lsbm
        tsfresh_features[f'{segment_name_prefix}longest_strike_above_mean_whole_weighted'] = w_i * lsam if not np.isnan(lsam) else np.nan
        tsfresh_features[f'{segment_name_prefix}longest_strike_below_mean_whole_weighted'] = w_i * lsbm if not np.isnan(lsbm) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}longest_strike_above_mean_whole_norm_zscore'] = (
                (lsam - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
            tsfresh_features[f'{segment_name_prefix}longest_strike_below_mean_whole_norm_zscore'] = (
                (lsbm - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 2 and len(post) >= 2:
            lsam_pre = compute_tsfresh_feature(longest_strike_above_mean, pre, min_length=2)
            lsam_post = compute_tsfresh_feature(longest_strike_above_mean, post, min_length=2)
            lsbm_pre = compute_tsfresh_feature(longest_strike_below_mean, pre, min_length=2)
            lsbm_post = compute_tsfresh_feature(longest_strike_below_mean, post, min_length=2)
            tsfresh_features[f'{segment_name_prefix}longest_strike_above_mean_pre'] = lsam_pre
            tsfresh_features[f'{segment_name_prefix}longest_strike_above_mean_post'] = lsam_post
            tsfresh_features[f'{segment_name_prefix}longest_strike_below_mean_pre'] = lsbm_pre
            tsfresh_features[f'{segment_name_prefix}longest_strike_below_mean_post'] = lsbm_post
            tsfresh_features[f'{segment_name_prefix}diff_longest_strike_above_mean'] = (
                lsam_post - lsam_pre if not (np.isnan(lsam_pre) or np.isnan(lsam_post)) else np.nan
            )
            tsfresh_features[f'{segment_name_prefix}diff_longest_strike_below_mean'] = (
                lsbm_post - lsbm_pre if not (np.isnan(lsbm_pre) or np.isnan(lsbm_post)) else np.nan
            )
    
    # Mean Second Derivative Central
    if len(whole) >= 3:
        msdc = compute_tsfresh_feature(
            mean_second_derivative_central, whole,
            name='mean_second_derivative_central_whole', min_length=3
        )
        tsfresh_features[f'{segment_name_prefix}mean_second_derivative_central_whole'] = msdc
        tsfresh_features[f'{segment_name_prefix}mean_second_derivative_central_whole_weighted'] = w_i * msdc if not np.isnan(msdc) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}mean_second_derivative_central_whole_norm_zscore'] = (
                (msdc - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 3 and len(post) >= 3:
            msdc_pre = compute_tsfresh_feature(mean_second_derivative_central, pre, min_length=3)
            msdc_post = compute_tsfresh_feature(mean_second_derivative_central, post, min_length=3)
            tsfresh_features[f'{segment_name_prefix}mean_second_derivative_central_pre'] = msdc_pre
            tsfresh_features[f'{segment_name_prefix}mean_second_derivative_central_post'] = msdc_post
            tsfresh_features[f'{segment_name_prefix}diff_mean_second_derivative_central'] = (
                msdc_post - msdc_pre if not (np.isnan(msdc_pre) or np.isnan(msdc_post)) else np.nan
            )
    
    # Percentage of Reoccurring Datapoints/Values
    if len(whole) >= 2:
        prd = compute_tsfresh_feature(
            percentage_of_reoccurring_datapoints_to_all_datapoints, whole,
            name='pct_reoccurring_datapoints_whole', min_length=2
        )
        prv = compute_tsfresh_feature(
            percentage_of_reoccurring_values_to_all_values, whole,
            name='pct_reoccurring_values_whole', min_length=2
        )
        tsfresh_features[f'{segment_name_prefix}pct_reoccurring_datapoints_whole'] = prd
        tsfresh_features[f'{segment_name_prefix}pct_reoccurring_values_whole'] = prv
        tsfresh_features[f'{segment_name_prefix}pct_reoccurring_datapoints_whole_weighted'] = w_i * prd if not np.isnan(prd) else np.nan
        tsfresh_features[f'{segment_name_prefix}pct_reoccurring_values_whole_weighted'] = w_i * prv if not np.isnan(prv) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}pct_reoccurring_datapoints_whole_norm_zscore'] = (
                (prd - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
            tsfresh_features[f'{segment_name_prefix}pct_reoccurring_values_whole_norm_zscore'] = (
                (prv - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 2 and len(post) >= 2:
            prd_pre = compute_tsfresh_feature(percentage_of_reoccurring_datapoints_to_all_datapoints, pre, min_length=2)
            prd_post = compute_tsfresh_feature(percentage_of_reoccurring_datapoints_to_all_datapoints, post, min_length=2)
            prv_pre = compute_tsfresh_feature(percentage_of_reoccurring_values_to_all_values, pre, min_length=2)
            prv_post = compute_tsfresh_feature(percentage_of_reoccurring_values_to_all_values, post, min_length=2)
            tsfresh_features[f'{segment_name_prefix}pct_reoccurring_datapoints_pre'] = prd_pre
            tsfresh_features[f'{segment_name_prefix}pct_reoccurring_datapoints_post'] = prd_post
            tsfresh_features[f'{segment_name_prefix}pct_reoccurring_values_pre'] = prv_pre
            tsfresh_features[f'{segment_name_prefix}pct_reoccurring_values_post'] = prv_post
            tsfresh_features[f'{segment_name_prefix}diff_pct_reoccurring_datapoints'] = (
                prd_post - prd_pre if not (np.isnan(prd_pre) or np.isnan(prd_post)) else np.nan
            )
            tsfresh_features[f'{segment_name_prefix}diff_pct_reoccurring_values'] = (
                prv_post - prv_pre if not (np.isnan(prv_pre) or np.isnan(prv_post)) else np.nan
            )
    
    # Ratio Beyond r Sigma
    if len(whole) >= 2:
        for r in [1, 2]:
            rbs = compute_tsfresh_feature(
                ratio_beyond_r_sigma, whole, r,
                name=f'ratio_beyond_{r}_sigma_whole', min_length=2
            )
            tsfresh_features[f'{segment_name_prefix}ratio_beyond_{r}_sigma_whole'] = rbs
            tsfresh_features[f'{segment_name_prefix}ratio_beyond_{r}_sigma_whole_weighted'] = w_i * rbs if not np.isnan(rbs) else np.nan
            if normalize:
                feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
                tsfresh_features[f'{segment_name_prefix}ratio_beyond_{r}_sigma_whole_norm_zscore'] = (
                    (rbs - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
                )
            if compute_pre_post and len(pre) >= 2 and len(post) >= 2:
                rbs_pre = compute_tsfresh_feature(ratio_beyond_r_sigma, pre, r, min_length=2)
                rbs_post = compute_tsfresh_feature(ratio_beyond_r_sigma, post, r, min_length=2)
                tsfresh_features[f'{segment_name_prefix}ratio_beyond_{r}_sigma_pre'] = rbs_pre
                tsfresh_features[f'{segment_name_prefix}ratio_beyond_{r}_sigma_post'] = rbs_post
                tsfresh_features[f'{segment_name_prefix}diff_ratio_beyond_{r}_sigma'] = (
                    rbs_post - rbs_pre if not (np.isnan(rbs_pre) or np.isnan(rbs_post)) else np.nan
                )
    
    # Ratio Value Number to Time Series Length
    if len(whole) >= 2:
        ruv = compute_tsfresh_feature(
            ratio_value_number_to_time_series_length, whole,
            name='ratio_unique_values_whole', min_length=2
        )
        tsfresh_features[f'{segment_name_prefix}ratio_unique_values_whole'] = ruv
        tsfresh_features[f'{segment_name_prefix}ratio_unique_values_whole_weighted'] = w_i * ruv if not np.isnan(ruv) else np.nan
        if normalize:
            feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
            tsfresh_features[f'{segment_name_prefix}ratio_unique_values_whole_norm_zscore'] = (
                (ruv - np.nanmean(whole)) / feature_std if feature_std and feature_std > 0 else np.nan
            )
        if compute_pre_post and len(pre) >= 2 and len(post) >= 2:
            ruv_pre = compute_tsfresh_feature(ratio_value_number_to_time_series_length, pre, min_length=2)
            ruv_post = compute_tsfresh_feature(ratio_value_number_to_time_series_length, post, min_length=2)
            tsfresh_features[f'{segment_name_prefix}ratio_unique_values_pre'] = ruv_pre
            tsfresh_features[f'{segment_name_prefix}ratio_unique_values_post'] = ruv_post
            tsfresh_features[f'{segment_name_prefix}diff_ratio_unique_values'] = (
                ruv_post - ruv_pre if not (np.isnan(ruv_pre) or np.isnan(ruv_post)) else np.nan
            )
    
    feats.update(tsfresh_features)
    # Autocorrelation lag-1
    def lag1_autocorr(arr):
        if len(arr) > 1:
            arr0 = arr - np.nanmean(arr)
            return np.corrcoef(arr0[:-1], arr0[1:])[0, 1] if len(arr0) > 1 else np.nan
        return np.nan
    ac_whole = lag1_autocorr(whole)
    feats[f'{segment_name_prefix}ac_whole'] = ac_whole
    feats[f'{segment_name_prefix}ac_whole_weighted'] = w_i * ac_whole if not np.isnan(ac_whole) else np.nan
    
    # AR(1) coefficient
    def estimate_ar1(arr):
        if len(arr) > 1:
            x_prev = arr[:-1]
            x_next = arr[1:]
            phi = np.polyfit(x_prev, x_next, deg=1)[0]
            return phi
        return np.nan
    phi_whole = estimate_ar1(whole)
    feats[f'{segment_name_prefix}phi_whole'] = phi_whole
    feats[f'{segment_name_prefix}phi_whole_weighted'] = w_i * phi_whole if not np.isnan(phi_whole) else np.nan
    
    # GARCH(1,1) Volatility
    def garch_volatility(arr):
        if len(arr) > 30:
            try:
                model = arch_model(arr, vol='Garch', p=1, q=1, rescale=True).fit(disp='off')
                return np.mean(np.sqrt(model.conditional_volatility))
            except Exception:
                return np.nan
        return np.nan
    garch_whole = garch_volatility(whole)
    feats[f'{segment_name_prefix}garch_whole'] = garch_whole
    feats[f'{segment_name_prefix}garch_whole_weighted'] = w_i * garch_whole if not np.isnan(garch_whole) else np.nan
    
    # Spectral Power (Periodogram and Welch)
    def spectral_power_periodogram(arr):
        if len(arr) > 10:
            try:
                f, Pxx = periodogram(arr, fs=1.0, scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan
    power_whole = spectral_power_periodogram(whole)
    feats[f'{segment_name_prefix}power_whole_periodogram'] = power_whole
    feats[f'{segment_name_prefix}power_whole_periodogram_weighted'] = w_i * power_whole if not np.isnan(power_whole) else np.nan
    
    def spectral_power_welch(arr):
        if len(arr) > 10:
            try:
                f, Pxx = welch(arr, nperseg=min(len(arr)//2, 256), scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan
    power_whole_welch = spectral_power_welch(whole)
    feats[f'{segment_name_prefix}power_whole_welch'] = power_whole_welch
    feats[f'{segment_name_prefix}power_whole_welch_weighted'] = w_i * power_whole_welch if not np.isnan(power_whole_welch) else np.nan
    
    # Spectrogram Entropy
    if len(whole) > 20:
        try:
            def spectrogram_entropy(seg):
                if len(seg) < 2:
                    return np.nan
                f, t, Sxx = spectrogram(seg, fs=1.0, nperseg=min(100, len(seg)//2), noverlap=None)
                Sxx = Sxx / np.sum(Sxx) if np.sum(Sxx) != 0 else np.ones_like(Sxx) / Sxx.size
                return -np.sum(Sxx * np.log2(Sxx + 1e-10))
            spec_entropy = spectrogram_entropy(whole)
            feats[f'{segment_name_prefix}spec_entropy_whole'] = spec_entropy
            feats[f'{segment_name_prefix}spec_entropy_whole_weighted'] = w_i * spec_entropy if not np.isnan(spec_entropy) else np.nan
        except Exception:
            feats[f'{segment_name_prefix}spec_entropy_whole'] = np.nan
            feats[f'{segment_name_prefix}spec_entropy_whole_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}spec_entropy_whole'] = np.nan
        feats[f'{segment_name_prefix}spec_entropy_whole_weighted'] = np.nan
    
    # Peak Finding
    if len(whole) > 5:
        try:
            peaks_whole, _ = find_peaks(whole, height=np.mean(whole) + np.std(whole))
            prominences_whole = peak_prominences(whole, peaks_whole)[0] if len(peaks_whole) > 0 else np.array([np.nan])
            feats[f'{segment_name_prefix}num_peaks_whole'] = len(peaks_whole)
            feats[f'{segment_name_prefix}num_peaks_whole_weighted'] = w_i * len(peaks_whole) if not np.isnan(len(peaks_whole)) else np.nan
            feats[f'{segment_name_prefix}mean_prominence_whole'] = np.mean(prominences_whole) if len(prominences_whole) > 0 else np.nan
            feats[f'{segment_name_prefix}mean_prominence_whole_weighted'] = w_i * np.mean(prominences_whole) if len(prominences_whole) > 0 else np.nan
        except Exception:
            feats[f'{segment_name_prefix}num_peaks_whole'] = np.nan
            feats[f'{segment_name_prefix}num_peaks_whole_weighted'] = np.nan
            feats[f'{segment_name_prefix}mean_prominence_whole'] = np.nan
            feats[f'{segment_name_prefix}mean_prominence_whole_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}num_peaks_whole'] = np.nan
        feats[f'{segment_name_prefix}num_peaks_whole_weighted'] = np.nan
        feats[f'{segment_name_prefix}mean_prominence_whole'] = np.nan
        feats[f'{segment_name_prefix}mean_prominence_whole_weighted'] = np.nan
    
    # Filtering (Savitzky-Golay)
    if len(whole) > 7:
        try:
            whole_smooth = savgol_filter(whole, window_length=7, polyorder=2)
            smooth_residuals = np.std(whole - whole_smooth) if len(whole_smooth) > 0 else np.nan
            feats[f'{segment_name_prefix}smooth_residuals_whole'] = smooth_residuals
            feats[f'{segment_name_prefix}smooth_residuals_whole_weighted'] = w_i * smooth_residuals if not np.isnan(smooth_residuals) else np.nan
        except Exception:
            feats[f'{segment_name_prefix}smooth_residuals_whole'] = np.nan
            feats[f'{segment_name_prefix}smooth_residuals_whole_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}smooth_residuals_whole'] = np.nan
        feats[f'{segment_name_prefix}smooth_residuals_whole_weighted'] = np.nan
    
    # Detrend
    if len(whole) > 5:
        try:
            whole_detrended = detrend(whole, type='linear')
            std_detrended = np.std(whole_detrended) if len(whole_detrended) > 0 else np.nan
            feats[f'{segment_name_prefix}std_detrended_whole'] = std_detrended
            feats[f'{segment_name_prefix}std_detrended_whole_weighted'] = w_i * std_detrended if not np.isnan(std_detrended) else np.nan
        except Exception:
            feats[f'{segment_name_prefix}std_detrended_whole'] = np.nan
            feats[f'{segment_name_prefix}std_detrended_whole_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}std_detrended_whole'] = np.nan
        feats[f'{segment_name_prefix}std_detrended_whole_weighted'] = np.nan
    
    # Hilbert Transform
    if len(whole) > 5:
        try:
            analytic_whole = hilbert(whole)
            env_whole = np.abs(analytic_whole)
            mean_envelope = np.mean(env_whole) if len(env_whole) > 0 else np.nan
            feats[f'{segment_name_prefix}mean_envelope_whole'] = mean_envelope
            feats[f'{segment_name_prefix}mean_envelope_whole_weighted'] = w_i * mean_envelope if not np.isnan(mean_envelope) else np.nan
        except Exception:
            feats[f'{segment_name_prefix}mean_envelope_whole'] = np.nan
            feats[f'{segment_name_prefix}mean_envelope_whole_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}mean_envelope_whole'] = np.nan
        feats[f'{segment_name_prefix}mean_envelope_whole_weighted'] = np.nan
    
    # Ryan-Joiner Test
    rj_whole = ryan_joiner_test(whole)
    feats[f'{segment_name_prefix}rj_whole'] = rj_whole
    feats[f'{segment_name_prefix}rj_whole_weighted'] = w_i * rj_whole if not np.isnan(rj_whole) else np.nan
    
    # Relative Frequency Features
    if len(whole) > 5:
        try:
            bins = np.histogram(whole[~np.isnan(whole)], bins='auto')[1]
            relfreq_whole = stats.relfreq(whole[~np.isnan(whole)], numbins=len(bins)-1, defaultreallimits=(bins[0], bins[-1]))
            relfreq_mean = np.mean(relfreq_whole.frequency) if relfreq_whole.frequency.size > 0 else np.nan
            relfreq_std = np.std(relfreq_whole.frequency, ddof=1) if relfreq_whole.frequency.size > 1 else np.nan
            feats[f'{segment_name_prefix}relfreq_mean_whole'] = relfreq_mean
            feats[f'{segment_name_prefix}relfreq_mean_whole_weighted'] = w_i * relfreq_mean if not np.isnan(relfreq_mean) else np.nan
            feats[f'{segment_name_prefix}relfreq_std_whole'] = relfreq_std
            feats[f'{segment_name_prefix}relfreq_std_whole_weighted'] = w_i * relfreq_std if not np.isnan(relfreq_std) else np.nan
        except Exception:
            feats[f'{segment_name_prefix}relfreq_mean_whole'] = np.nan
            feats[f'{segment_name_prefix}relfreq_mean_whole_weighted'] = np.nan
            feats[f'{segment_name_prefix}relfreq_std_whole'] = np.nan
            feats[f'{segment_name_prefix}relfreq_std_whole_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}relfreq_mean_whole'] = np.nan
        feats[f'{segment_name_prefix}relfreq_mean_whole_weighted'] = np.nan
        feats[f'{segment_name_prefix}relfreq_std_whole'] = np.nan
        feats[f'{segment_name_prefix}relfreq_std_whole_weighted'] = np.nan
    
    # Feature Vector
    if len(whole) > 5:
        try:
            feature_vector = np.array([mean_whole, std_whole, skew_whole, kurt_whole, ac_whole, phi_whole])
            feature_vector = feature_vector[~np.isnan(feature_vector)]
            if len(feature_vector) > 0:
                norm = np.linalg.norm(feature_vector)
                normalized_feature_vector = feature_vector / norm if norm > 0 else np.zeros_like(feature_vector)
                norm_vec_mean = np.mean(normalized_feature_vector)
                norm_vec_std = np.std(normalized_feature_vector, ddof=1) if len(normalized_feature_vector) > 1 else np.nan
                feats[f'{segment_name_prefix}norm_feature_vec_mean'] = norm_vec_mean
                feats[f'{segment_name_prefix}norm_feature_vec_mean_weighted'] = w_i * norm_vec_mean if not np.isnan(norm_vec_mean) else np.nan
                feats[f'{segment_name_prefix}norm_feature_vec_std'] = norm_vec_std
                feats[f'{segment_name_prefix}norm_feature_vec_std_weighted'] = w_i * norm_vec_std if not np.isnan(norm_vec_std) else np.nan
        except Exception:
            feats[f'{segment_name_prefix}norm_feature_vec_mean'] = np.nan
            feats[f'{segment_name_prefix}norm_feature_vec_mean_weighted'] = np.nan
            feats[f'{segment_name_prefix}norm_feature_vec_std'] = np.nan
            feats[f'{segment_name_prefix}norm_feature_vec_std_weighted'] = np.nan
    else:
        feats[f'{segment_name_prefix}norm_feature_vec_mean'] = np.nan
        feats[f'{segment_name_prefix}norm_feature_vec_mean_weighted'] = np.nan
        feats[f'{segment_name_prefix}norm_feature_vec_std'] = np.nan
        feats[f'{segment_name_prefix}norm_feature_vec_std_weighted'] = np.nan
    
    # pycatch22 Features for whole segment
    pycatch22_features = [
        'DN_HistogramMode_5', 'DN_HistogramMode_10', 'DN_OutlierInclude_p_001_mdrmd',
        'DN_OutlierInclude_n_001_mdrmd', 'first1e_acf_tau', 'firstMin_acf',
        'SP_Summaries_welch_rect_area_5_1', 'SP_Summaries_welch_rect_centroid',
        'FC_LocalSimple_mean3_stderr', 'FC_LocalSimple_mean1_tauresrat',
        'MD_hrv_classic_pnn40', 'SB_BinaryStats_mean_longstretch1',
        'SB_BinaryStats_diff_longstretch0', 'SB_MotifThree_quantile_hh',
        'CO_HistogramAMI_even_2_5', 'CO_trev_1_num', 'IN_AutoMutualInfoStats_40_gaussian_fmmi',
        'SB_TransitionMatrix_3ac_sumdiagcov', 'PD_PeriodicityWang_th001',
        'CO_Embed2_Dist_tau_d_expfit_meandiff', 'SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1',
        'SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1'
    ]
    
    if len(whole) >= 20:
        try:
            whole_no_nan = whole[~np.isnan(whole)]
            catch22_out = pycatch22.catch22_all(whole_no_nan)
            for feature_name, feature_value in zip(catch22_out['names'], catch22_out['values']):
                feats[f'{segment_name_prefix}{feature_name}_whole'] = feature_value
                feats[f'{segment_name_prefix}{feature_name}_whole_weighted'] = w_i * feature_value if not np.isnan(feature_value) else np.nan
                # Per-part normalization
                if normalize:
                    feature_std = np.nanstd(whole_no_nan, ddof=1) if len(whole_no_nan) > 1 else np.nan
                    feats[f'{segment_name_prefix}{feature_name}_whole_norm_zscore'] = (
                        (feature_value - np.nanmean(whole_no_nan)) / feature_std
                        if feature_std and feature_std > 0 and not np.isnan(feature_value) else np.nan
                    )
        except Exception:
            for feature_name in pycatch22_features:
                feats[f'{segment_name_prefix}{feature_name}_whole'] = np.nan
                feats[f'{segment_name_prefix}{feature_name}_whole_weighted'] = np.nan
                if normalize:
                    feats[f'{segment_name_prefix}{feature_name}_whole_norm_zscore'] = np.nan
    else:
        for feature_name in pycatch22_features:
            feats[f'{segment_name_prefix}{feature_name}_whole'] = np.nan
            feats[f'{segment_name_prefix}{feature_name}_whole_weighted'] = np.nan
            if normalize:
                feats[f'{segment_name_prefix}{feature_name}_whole_norm_zscore'] = np.nan
    
    # SSA Features for whole segment
    if len(whole) >= 20:
        try:
            ssa_out = ssa_features(whole)
            for key, value in ssa_out.items():
                feats[f'{segment_name_prefix}{key}_whole'] = value
                feats[f'{segment_name_prefix}{key}_whole_weighted'] = w_i * value if not np.isnan(value) else np.nan
                # Per-part normalization
                if normalize:
                    feature_std = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan
                    feats[f'{segment_name_prefix}{key}_whole_norm_zscore'] = (
                        (value - np.nanmean(whole)) / feature_std
                        if feature_std and feature_std > 0 and not np.isnan(value) else np.nan
                    )
        except Exception:
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                feats[f'{segment_name_prefix}{key}_whole'] = np.nan
                feats[f'{segment_name_prefix}{key}_whole_weighted'] = np.nan
                if normalize:
                    feats[f'{segment_name_prefix}{key}_whole_norm_zscore'] = np.nan
    else:
        for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                    'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
            feats[f'{segment_name_prefix}{key}_whole'] = np.nan
            feats[f'{segment_name_prefix}{key}_whole_weighted'] = np.nan
            if normalize:
                feats[f'{segment_name_prefix}{key}_whole_norm_zscore'] = np.nan
                               
    # # Jensen-Shannon Divergence for pre/post in bins
    if compute_jsd and compute_pre_post and len(pre) > 5 and len(post) > 5:
        try:
            jsd = jensen_shannon_divergence(pre, post)
            feats[f'{segment_name_prefix}jsd'] = jsd
            if np.isnan(jsd):
                feats[f'{segment_name_prefix}is_nan_jsd'] = 1
            else:
                feats[f'{segment_name_prefix}is_nan_jsd'] = 0
        except Exception:
            feats[f'{segment_name_prefix}jsd'] = np.nan
            feats[f'{segment_name_prefix}is_nan_jsd'] = 1
    else:
        feats[f'{segment_name_prefix}jsd'] = np.nan
        feats[f'{segment_name_prefix}is_nan_jsd'] = 1 if compute_pre_post else 0
    
    # Compute pre and post features if enabled
    if compute_pre_post:
        feats[f'{segment_name_prefix}len_pre'] = len(pre)
        feats[f'{segment_name_prefix}len_post'] = len(post)
        mean_pre = np.nanmean(pre) if len(pre) > 0 else np.nan
        mean_post = np.nanmean(post) if len(post) > 0 else np.nan
        feats[f'{segment_name_prefix}mean_pre'] = mean_pre
        feats[f'{segment_name_prefix}mean_post'] = mean_post
        feats[f'{segment_name_prefix}diff_mean'] = mean_post - mean_pre if not (np.isnan(mean_pre) or np.isnan(mean_post)) else np.nan
        
        std_pre = np.nanstd(pre, ddof=1) if len(pre) > 1 else np.nan
        std_post = np.nanstd(post, ddof=1) if len(post) > 1 else np.nan
        feats[f'{segment_name_prefix}std_pre'] = std_pre
        feats[f'{segment_name_prefix}std_post'] = std_post
        feats[f'{segment_name_prefix}ratio_std'] = std_post / std_pre if (std_pre and std_pre > 0 and not np.isnan(std_post)) else np.nan
        
        var_pre = np.nanvar(pre, ddof=1) if len(pre) > 1 else np.nan
        var_post = np.nanvar(post, ddof=1) if len(post) > 1 else np.nan
        feats[f'{segment_name_prefix}var_pre'] = var_pre
        feats[f'{segment_name_prefix}var_post'] = var_post
        feats[f'{segment_name_prefix}ratio_var'] = var_post / var_pre if (var_pre and var_pre > 0 and not np.isnan(var_post)) else np.nan
        
        # Effect sizes
        if len(pre) > 1 and len(post) > 1:
            pooled_std = np.sqrt((var_pre * (len(pre) - 1) + var_post * (len(post) - 1)) / (len(pre) + len(post) - 2))
            feats[f'{segment_name_prefix}cohens_d'] = (mean_post - mean_pre) / pooled_std if pooled_std > 0 else np.nan
            feats[f'{segment_name_prefix}glass_delta'] = (mean_post - mean_pre) / std_pre if std_pre > 0 else np.nan
            feats[f'{segment_name_prefix}cliffs_delta'] = cliffs_delta(pre, post)
        else:
            feats[f'{segment_name_prefix}cohens_d'] = np.nan
            feats[f'{segment_name_prefix}glass_delta'] = np.nan
            feats[f'{segment_name_prefix}cliffs_delta'] = np.nan
        
        skew_pre = stats.skew(pre, nan_policy='omit') if len(pre) > 2 else np.nan
        skew_post = stats.skew(post, nan_policy='omit') if len(post) > 2 else np.nan
        feats[f'{segment_name_prefix}skew_pre'] = skew_pre
        feats[f'{segment_name_prefix}skew_post'] = skew_post
        feats[f'{segment_name_prefix}diff_skew'] = skew_post - skew_pre if not (np.isnan(skew_pre) or np.isnan(skew_post)) else np.nan
        feats[f'{segment_name_prefix}ratio_skew'] = skew_post / skew_pre if (skew_pre and skew_pre != 0 and not np.isnan(skew_post)) else np.nan
        
        kurt_pre = stats.kurtosis(pre, nan_policy='omit') if len(pre) > 2 else np.nan
        kurt_post = stats.kurtosis(post, nan_policy='omit') if len(post) > 2 else np.nan
        feats[f'{segment_name_prefix}kurt_pre'] = kurt_pre
        feats[f'{segment_name_prefix}kurt_post'] = kurt_post
        feats[f'{segment_name_prefix}diff_kurt'] = kurt_post - kurt_pre if not (np.isnan(kurt_pre) or np.isnan(kurt_post)) else np.nan
        feats[f'{segment_name_prefix}ratio_kurt'] = kurt_post / kurt_pre if (kurt_pre and kurt_pre != 0 and not np.isnan(kurt_post)) else np.nan
        
        ac_pre = lag1_autocorr(pre)
        ac_post = lag1_autocorr(post)
        feats[f'{segment_name_prefix}ac_pre'] = ac_pre
        feats[f'{segment_name_prefix}ac_post'] = ac_post
        feats[f'{segment_name_prefix}diff_ac'] = ac_post - ac_pre if not (np.isnan(ac_pre) or np.isnan(ac_post)) else np.nan
        
        # Cross-Correlation and Convolution
        if len(pre) > 5 and len(post) > 5:
            try:
                pre_norm = pre - np.nanmean(pre)
                post_norm = post - np.nanmean(post)
                corr = correlate(pre_norm, post_norm, mode='full')
                lags = correlation_lags(len(pre), len(post), mode='full')
                max_corr = np.max(np.abs(corr)) / (np.nanstd(pre) * np.nanstd(post) * min(len(pre), len(post))) if np.nanstd(pre) * np.nanstd(post) > 0 else np.nan
                max_corr_lag = lags[np.argmax(np.abs(corr))] if not np.isnan(max_corr) else np.nan
                feats[f'{segment_name_prefix}max_cross_corr'] = max_corr
                feats[f'{segment_name_prefix}max_cross_corr_lag'] = max_corr_lag
                conv = fftconvolve(pre_norm, post_norm[::-1], mode='full')
                feats[f'{segment_name_prefix}max_conv'] = np.max(np.abs(conv)) / (np.nanstd(pre) * np.nanstd(post) * min(len(pre), len(post))) if np.nanstd(pre) * np.nanstd(post) > 0 else np.nan
            except Exception:
                feats[f'{segment_name_prefix}max_cross_corr'] = np.nan
                feats[f'{segment_name_prefix}max_cross_corr_lag'] = np.nan
                feats[f'{segment_name_prefix}max_conv'] = np.nan
        else:
            feats[f'{segment_name_prefix}max_cross_corr'] = np.nan
            feats[f'{segment_name_prefix}max_cross_corr_lag'] = np.nan
            feats[f'{segment_name_prefix}max_conv'] = np.nan
        
        phi_pre = estimate_ar1(pre)
        phi_post = estimate_ar1(post)
        feats[f'{segment_name_prefix}phi_pre'] = phi_pre
        feats[f'{segment_name_prefix}phi_post'] = phi_post
        feats[f'{segment_name_prefix}diff_phi'] = phi_post - phi_pre if not (np.isnan(phi_pre) or np.isnan(phi_post)) else np.nan
        
        garch_pre = garch_volatility(pre)
        garch_post = garch_volatility(post)
        feats[f'{segment_name_prefix}garch_pre'] = garch_pre
        feats[f'{segment_name_prefix}garch_post'] = garch_post
        feats[f'{segment_name_prefix}diff_garch'] = abs(garch_post - garch_pre) if not (np.isnan(garch_pre) or np.isnan(garch_post)) else np.nan
        
        # Coherence
        if len(pre) > 10 and len(post) > 10:
            try:
                f, Cxy = coherence(pre, post, nperseg=min(100, len(pre)//2))
                feats[f'{segment_name_prefix}mean_coherence'] = np.mean(Cxy)
            except Exception:
                feats[f'{segment_name_prefix}mean_coherence'] = np.nan
        else:
            feats[f'{segment_name_prefix}mean_coherence'] = np.nan
        
        # Spectrogram Entropy
        if len(pre) > 20 and len(post) > 20:
            try:
                feats[f'{segment_name_prefix}spec_entropy_pre'] = spectrogram_entropy(pre)
                feats[f'{segment_name_prefix}spec_entropy_post'] = spectrogram_entropy(post)
                feats[f'{segment_name_prefix}spec_entropy_diff'] = feats[f'{segment_name_prefix}spec_entropy_post'] - feats[f'{segment_name_prefix}spec_entropy_pre'] if not np.isnan(feats[f'{segment_name_prefix}spec_entropy_pre']) else np.nan
            except Exception:
                feats[f'{segment_name_prefix}spec_entropy_pre'] = np.nan
                feats[f'{segment_name_prefix}spec_entropy_post'] = np.nan
                feats[f'{segment_name_prefix}spec_entropy_diff'] = np.nan
        else:
            feats[f'{segment_name_prefix}spec_entropy_pre'] = np.nan
            feats[f'{segment_name_prefix}spec_entropy_post'] = np.nan
            feats[f'{segment_name_prefix}spec_entropy_diff'] = np.nan
        
        # Peak Finding
        if len(pre) > 5 and len(post) > 5:
            try:
                peaks_pre, _ = find_peaks(pre, height=np.mean(pre) + np.std(pre))
                peaks_post, _ = find_peaks(post, height=np.mean(post) + np.std(post))
                prominences_pre = peak_prominences(pre, peaks_pre)[0] if len(peaks_pre) > 0 else np.array([np.nan])
                prominences_post = peak_prominences(post, peaks_post)[0] if len(peaks_post) > 0 else np.array([np.nan])
                feats[f'{segment_name_prefix}num_peaks_pre'] = len(peaks_pre)
                feats[f'{segment_name_prefix}num_peaks_post'] = len(peaks_post)
                feats[f'{segment_name_prefix}mean_prominence_pre'] = np.mean(prominences_pre) if len(prominences_pre) > 0 else np.nan
                feats[f'{segment_name_prefix}mean_prominence_post'] = np.mean(prominences_post) if len(prominences_post) > 0 else np.nan
                feats[f'{segment_name_prefix}diff_num_peaks'] = len(peaks_post) - len(peaks_pre)
            except Exception:
                feats[f'{segment_name_prefix}num_peaks_pre'] = np.nan
                feats[f'{segment_name_prefix}num_peaks_post'] = np.nan
                feats[f'{segment_name_prefix}mean_prominence_pre'] = np.nan
                feats[f'{segment_name_prefix}mean_prominence_post'] = np.nan
                feats[f'{segment_name_prefix}diff_num_peaks'] = np.nan
        else:
            feats[f'{segment_name_prefix}num_peaks_pre'] = np.nan
            feats[f'{segment_name_prefix}num_peaks_post'] = np.nan
            feats[f'{segment_name_prefix}mean_prominence_pre'] = np.nan
            feats[f'{segment_name_prefix}mean_prominence_post'] = np.nan
            feats[f'{segment_name_prefix}diff_num_peaks'] = np.nan
        
        # Filtering (Savitzky-Golay)
        if len(pre) > 7 and len(post) > 7:
            try:
                pre_smooth = savgol_filter(pre, window_length=7, polyorder=2)
                post_smooth = savgol_filter(post, window_length=7, polyorder=2)
                feats[f'{segment_name_prefix}smooth_residuals_pre'] = np.std(pre - pre_smooth) if len(pre_smooth) > 0 else np.nan
                feats[f'{segment_name_prefix}smooth_residuals_post'] = np.std(post - post_smooth) if len(post_smooth) > 0 else np.nan
                feats[f'{segment_name_prefix}diff_smooth_residuals'] = feats[f'{segment_name_prefix}smooth_residuals_post'] - feats[f'{segment_name_prefix}smooth_residuals_pre'] if not np.isnan(feats[f'{segment_name_prefix}smooth_residuals_pre']) else np.nan
            except Exception:
                feats[f'{segment_name_prefix}smooth_residuals_pre'] = np.nan
                feats[f'{segment_name_prefix}smooth_residuals_post'] = np.nan
                feats[f'{segment_name_prefix}diff_smooth_residuals'] = np.nan
        else:
            feats[f'{segment_name_prefix}smooth_residuals_pre'] = np.nan
            feats[f'{segment_name_prefix}smooth_residuals_post'] = np.nan
            feats[f'{segment_name_prefix}diff_smooth_residuals'] = np.nan
        
        # Detrend
        if len(pre) > 5 and len(post) > 5:
            try:
                pre_detrended = detrend(pre, type='linear')
                post_detrended = detrend(post, type='linear')
                feats[f'{segment_name_prefix}std_detrended_pre'] = np.std(pre_detrended) if len(pre_detrended) > 0 else np.nan
                feats[f'{segment_name_prefix}std_detrended_post'] = np.std(post_detrended) if len(post_detrended) > 0 else np.nan
                feats[f'{segment_name_prefix}diff_std_detrended'] = feats[f'{segment_name_prefix}std_detrended_post'] - feats[f'{segment_name_prefix}std_detrended_pre'] if not (np.isnan(feats[f'{segment_name_prefix}std_detrended_pre']) or np.isnan(feats[f'{segment_name_prefix}std_detrended_post'])) else np.nan
            except Exception:
                feats[f'{segment_name_prefix}std_detrended_pre'] = np.nan
                feats[f'{segment_name_prefix}std_detrended_post'] = np.nan
                feats[f'{segment_name_prefix}diff_std_detrended'] = np.nan
        else:
            feats[f'{segment_name_prefix}std_detrended_pre'] = np.nan
            feats[f'{segment_name_prefix}std_detrended_post'] = np.nan
            feats[f'{segment_name_prefix}diff_std_detrended'] = np.nan
        
        # Hilbert Transform
        if len(pre) > 5 and len(post) > 5:
            try:
                analytic_pre = hilbert(pre)
                analytic_post = hilbert(post)
                env_pre = np.abs(analytic_pre)
                env_post = np.abs(analytic_post)
                feats[f'{segment_name_prefix}mean_envelope_pre'] = np.mean(env_pre) if len(env_pre) > 0 else np.nan
                feats[f'{segment_name_prefix}mean_envelope_post'] = np.mean(env_post) if len(env_post) > 0 else np.nan
                feats[f'{segment_name_prefix}diff_envelope'] = feats[f'{segment_name_prefix}mean_envelope_post'] - feats[f'{segment_name_prefix}mean_envelope_pre'] if not (np.isnan(feats[f'{segment_name_prefix}mean_envelope_pre']) or np.isnan(feats[f'{segment_name_prefix}mean_envelope_post'])) else np.nan
            except Exception:
                feats[f'{segment_name_prefix}mean_envelope_pre'] = np.nan
                feats[f'{segment_name_prefix}mean_envelope_post'] = np.nan
                feats[f'{segment_name_prefix}diff_envelope'] = np.nan
        else:
            feats[f'{segment_name_prefix}mean_envelope_pre'] = np.nan
            feats[f'{segment_name_prefix}mean_envelope_post'] = np.nan
            feats[f'{segment_name_prefix}diff_envelope'] = np.nan
        
        # pycatch22 Features for pre and post
        if len(pre) >= 20:
            try:
                pre_no_nan = pre[~np.isnan(pre)]
                catch22_out = pycatch22.catch22_all(pre_no_nan)
                for feature_name, feature_value in zip(catch22_out['names'], catch22_out['values']):
                    feats[f'{segment_name_prefix}{feature_name}_pre'] = feature_value
                    # Per-part normalization
                    if normalize:
                        feature_std = np.nanstd(pre_no_nan, ddof=1) if len(pre_no_nan) > 1 else np.nan
                        feats[f'{segment_name_prefix}{feature_name}_pre_norm_zscore'] = (
                            (feature_value - np.nanmean(pre_no_nan)) / feature_std
                            if feature_std and feature_std > 0 and not np.isnan(feature_value) else np.nan
                        )
            except Exception:
                for feature_name in pycatch22_features:
                    feats[f'{segment_name_prefix}{feature_name}_pre'] = np.nan
                    if normalize:
                        feats[f'{segment_name_prefix}{feature_name}_pre_norm_zscore'] = np.nan
        else:
            for feature_name in pycatch22_features:
                feats[f'{segment_name_prefix}{feature_name}_pre'] = np.nan
                if normalize:
                    feats[f'{segment_name_prefix}{feature_name}_pre_norm_zscore'] = np.nan
        
        if len(post) >= 20:
            try:
                post_no_nan = post[~np.isnan(post)]
                catch22_out = pycatch22.catch22_all(post_no_nan)
                for feature_name, feature_value in zip(catch22_out['names'], catch22_out['values']):
                    feats[f'{segment_name_prefix}{feature_name}_post'] = feature_value
                    # Per-part normalization
                    if normalize:
                        feature_std = np.nanstd(post_no_nan, ddof=1) if len(post_no_nan) > 1 else np.nan
                        feats[f'{segment_name_prefix}{feature_name}_post_norm_zscore'] = (
                            (feature_value - np.nanmean(post_no_nan)) / feature_std
                            if feature_std and feature_std > 0 and not np.isnan(feature_value) else np.nan
                        )
            except Exception:
                for feature_name in pycatch22_features:
                    feats[f'{segment_name_prefix}{feature_name}_post'] = np.nan
                    if normalize:
                        feats[f'{segment_name_prefix}{feature_name}_post_norm_zscore'] = np.nan
        else:
            for feature_name in pycatch22_features:
                feats[f'{segment_name_prefix}{feature_name}_post'] = np.nan
                if normalize:
                    feats[f'{segment_name_prefix}{feature_name}_post_norm_zscore'] = np.nan
        
        # SSA Features for pre and post
        if len(pre) >= 20:
            try:
                ssa_out = ssa_features(pre)
                for key, value in ssa_out.items():
                    feats[f'{segment_name_prefix}{key}_pre'] = value
                    # Per-part normalization
                    if normalize:
                        feature_std = np.nanstd(pre, ddof=1) if len(pre) > 1 else np.nan
                        feats[f'{segment_name_prefix}{key}_pre_norm_zscore'] = (
                            (value - np.nanmean(pre)) / feature_std
                            if feature_std and feature_std > 0 and not np.isnan(value) else np.nan
                        )
            except Exception:
                for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                            'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                    feats[f'{segment_name_prefix}{key}_pre'] = np.nan
                    if normalize:
                        feats[f'{segment_name_prefix}{key}_pre_norm_zscore'] = np.nan
        else:
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                feats[f'{segment_name_prefix}{key}_pre'] = np.nan
                if normalize:
                    feats[f'{segment_name_prefix}{key}_pre_norm_zscore'] = np.nan
        
        if len(post) >= 20:
            try:
                ssa_out = ssa_features(post)
                for key, value in ssa_out.items():
                    feats[f'{segment_name_prefix}{key}_post'] = value
                    # Per-part normalization
                    if normalize:
                        feature_std = np.nanstd(post, ddof=1) if len(post) > 1 else np.nan
                        feats[f'{segment_name_prefix}{key}_post_norm_zscore'] = (
                            (value - np.nanmean(post)) / feature_std
                            if feature_std and feature_std > 0 and not np.isnan(value) else np.nan
                        )
            except Exception:
                for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                            'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                    feats[f'{segment_name_prefix}{key}_post'] = np.nan
                    if normalize:
                        feats[f'{segment_name_prefix}{key}_post_norm_zscore'] = np.nan
        else:
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                feats[f'{segment_name_prefix}{key}_post'] = np.nan
                if normalize:
                    feats[f'{segment_name_prefix}{key}_post_norm_zscore'] = np.nan
        
        # Meta-features for pycatch22 (differences and ratios)
        for feature_name in pycatch22_features:
            pre_key = f'{segment_name_prefix}{feature_name}_pre'
            post_key = f'{segment_name_prefix}{feature_name}_post'
            if pre_key in feats and post_key in feats:
                pre_val = feats[pre_key]
                post_val = feats[post_key]
                feats[f'{segment_name_prefix}diff_{feature_name}'] = post_val - pre_val if not (np.isnan(pre_val) or np.isnan(post_val)) else np.nan
                feats[f'{segment_name_prefix}ratio_{feature_name}'] = post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
                # Normalized difference
                if std_whole and std_whole > 0:
                    feats[f'{segment_name_prefix}diff_{feature_name}_norm'] = (post_val - pre_val) / std_whole if not (np.isnan(pre_val) or np.isnan(post_val)) else np.nan
                else:
                    feats[f'{segment_name_prefix}diff_{feature_name}_norm'] = np.nan
        
        # Meta-features for SSA (differences and ratios)
        for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                    'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
            pre_key = f'{segment_name_prefix}{key}_pre'
            post_key = f'{segment_name_prefix}{key}_post'
            if pre_key in feats and post_key in feats:
                pre_val = feats[pre_key]
                post_val = feats[post_key]
                feats[f'{segment_name_prefix}diff_{key}'] = post_val - pre_val if not (np.isnan(pre_val) or np.isnan(post_val)) else np.nan
                feats[f'{segment_name_prefix}ratio_{key}'] = post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
                # Normalized difference
                if std_whole and std_whole > 0:
                    feats[f'{segment_name_prefix}diff_{key}_norm'] = (post_val - pre_val) / std_whole if not (np.isnan(pre_val) or np.isnan(post_val)) else np.nan
                else:
                    feats[f'{segment_name_prefix}diff_{key}_norm'] = np.nan
 
        
        # Wasserstein Distance
        if len(pre) > 0 and len(post) > 0:
            try:
                wass_dist = wasserstein_distance(pre[~np.isnan(pre)], post[~np.isnan(post)])
                feats[f'{segment_name_prefix}wass_dist'] = wass_dist / std_whole if std_whole > 0 else np.nan
                feats[f'{segment_name_prefix}score_wass'] = np.tanh(wass_dist / std_whole) if std_whole > 0 else np.nan
            except Exception:
                feats[f'{segment_name_prefix}wass_dist'] = np.nan
                feats[f'{segment_name_prefix}score_wass'] = np.nan
        else:
            feats[f'{segment_name_prefix}wass_dist'] = np.nan
            feats[f'{segment_name_prefix}score_wass'] = np.nan
        
        # Cramér-von Mises Test
        if len(pre) > 5 and len(post) > 5:
            try:
                cvm_result = cramervonmises_2samp(pre[~np.isnan(pre)], post[~np.isnan(post)])
                feats[f'{segment_name_prefix}p_cvm'] = cvm_result.pvalue
                feats[f'{segment_name_prefix}score_cvm'] = 1.0 - cvm_result.pvalue if not np.isnan(cvm_result.pvalue) else np.nan
                feats[f'{segment_name_prefix}cvm_stat'] = cvm_result.statistic
            except Exception:
                feats[f'{segment_name_prefix}p_cvm'] = np.nan
                feats[f'{segment_name_prefix}score_cvm'] = np.nan
                feats[f'{segment_name_prefix}cvm_stat'] = np.nan
        else:
            feats[f'{segment_name_prefix}p_cvm'] = np.nan
            feats[f'{segment_name_prefix}score_cvm'] = np.nan
            feats[f'{segment_name_prefix}cvm_stat'] = np.nan
        
        # Statistical Tests
        if len(pre) > 3:
            try:
                sw_pre = stats.shapiro(pre[~np.isnan(pre)]).pvalue
            except Exception:
                sw_pre = np.nan
        else:
            sw_pre = np.nan
        if len(post) > 3:
            try:
                sw_post = stats.shapiro(post[~np.isnan(post)]).pvalue
            except Exception:
                sw_post = np.nan
        else:
            sw_post = np.nan
        if len(whole) > 3:
            try:
                sw_whole = stats.shapiro(whole[~np.isnan(whole)]).pvalue
            except Exception:
                sw_whole = np.nan
        else:
            sw_whole = np.nan
        feats[f'{segment_name_prefix}p_sw_pre'] = sw_pre
        feats[f'{segment_name_prefix}p_sw_post'] = sw_post
        feats[f'{segment_name_prefix}p_sw_whole'] = sw_whole
        feats[f'{segment_name_prefix}diff_sw_p'] = abs(sw_post - sw_pre) if not (np.isnan(sw_pre) or np.isnan(sw_post)) else np.nan
        feats[f'{segment_name_prefix}score_sw'] = feats[f'{segment_name_prefix}diff_sw_p'] / 0.1 if not np.isnan(feats[f'{segment_name_prefix}diff_sw_p']) else np.nan
        
        # Lilliefors Test
        if len(pre) > 5:
            try:
                lf_pre = kstest_fit(pre[~np.isnan(pre)], 'norm').pvalue
            except Exception:
                lf_pre = np.nan
        else:
            lf_pre = np.nan
        if len(post) > 5:
            try:
                lf_post = kstest_fit(post[~np.isnan(post)], 'norm').pvalue
            except Exception:
                lf_post = np.nan
        else:
            lf_post = np.nan
        if len(whole) > 5:
            try:
                lf_whole = kstest_fit(whole[~np.isnan(whole)], 'norm').pvalue
            except Exception:
                lf_whole = np.nan
        else:
            lf_whole = np.nan
        feats[f'{segment_name_prefix}p_lf_pre'] = lf_pre
        feats[f'{segment_name_prefix}p_lf_post'] = lf_post
        feats[f'{segment_name_prefix}p_lf_whole'] = lf_whole
        feats[f'{segment_name_prefix}diff_lf'] = abs(lf_post - lf_pre) if not (np.isnan(lf_pre) or np.isnan(lf_post)) else np.nan
        feats[f'{segment_name_prefix}score_lf'] = feats[f'{segment_name_prefix}diff_lf'] / 0.1 if not np.isnan(feats[f'{segment_name_prefix}diff_lf']) else np.nan
        
        # Chi-square test
        if len(pre) >= 5 and len(post) > 5:
            try:
                bins = np.histogram(whole[~np.isnan(whole)], bins=10)[1]
                hist_pre, _ = np.histogram(pre[~np.isnan(pre)], bins=bins)
                hist_post, _ = np.histogram(post[~np.isnan(post)], bins=bins)
                hist_pre = hist_pre / hist_pre.sum() if hist_pre.sum() > 0 else hist_pre
                hist_post = hist_post / hist_post.sum() if hist_post.sum() > 0 else hist_post
                chi2_stat, p_chi2 = stats.chisquare(hist_post, f_exp=hist_pre)
                feats[f'{segment_name_prefix}p_chi2'] = p_chi2
                feats[f'{segment_name_prefix}score_chi2'] = 1.0 - p_chi2 if not np.isnan(p_chi2) else np.nan
                feats[f'is_nan_chi2'] = 1 if np.isnan(p_chi2) else 0
            except Exception:
                feats[f'{segment_name_prefix}p_chi2'] = np.nan
                feats[f'{segment_name_prefix}score_chi2'] = np.nan
                feats[f'is_nan_chi2'] = 1
        else:
            feats[f'{segment_name_prefix}p_chi2'] = np.nan
            feats[f'{segment_name_prefix}score_chi2'] = np.nan
            feats[f'is_nan_chi2'] = 1
        
        # Ryan-Joiner Test
        feats[f'{segment_name_prefix}rj_pre'] = ryan_joiner_test(pre)
        feats[f'{segment_name_prefix}rj_post'] = ryan_joiner_test(post)
        feats[f'{segment_name_prefix}diff_rj'] = abs(feats[f'{segment_name_prefix}rj_post'] - feats[f'{segment_name_prefix}rj_pre']) if not (np.isnan(feats[f'{segment_name_prefix}rj_pre']) or np.isnan(feats[f'{segment_name_prefix}rj_post'])) else np.nan
        feats[f'{segment_name_prefix}score_rj'] = feats[f'{segment_name_prefix}diff_rj'] / 0.1 if not np.isnan(feats[f'{segment_name_prefix}diff_rj']) else np.nan
        
        # Cointegration Test
        if len(pre) > 30 and len(post) > 30:
            try:
                p_coint = coint(pre, post, trend='c')[1]
                feats[f'{segment_name_prefix}p_coint'] = p_coint
                feats[f'{segment_name_prefix}score_coint'] = 1 - p_coint if not np.isnan(p_coint) else np.nan
                feats[f'is_nan_coint'] = 0
            except Exception:
                feats[f'{segment_name_prefix}p_coint'] = np.nan
                feats[f'{segment_name_prefix}score_coint'] = np.nan
                feats[f'is_nan_coint'] = 1
        else:
            feats[f'{segment_name_prefix}p_coint'] = np.nan
            feats[f'{segment_name_prefix}score_coint'] = np.nan
            feats[f'is_nan_coint'] = 1
        
        # Granger Causality Test
        if len(pre) > 30 and len(post) > 30:
            try:
                data = np.column_stack((pre, post))
                gc_result = grangercausalitytests(data, maxlag=1, verbose=False)
                p_granger = gc_result[1][0]['ssr_ftest'][1]
                feats[f'{segment_name_prefix}p_granger'] = p_granger
                feats[f'{segment_name_prefix}score_granger'] = 1 - p_granger if not np.isnan(p_granger) else np.nan
                feats[f'is_nan_granger'] = 0
            except Exception:
                feats[f'{segment_name_prefix}p_granger'] = np.nan
                feats[f'{segment_name_prefix}score_granger'] = np.nan
                feats[f'is_nan_granger'] = 1
        else:
            feats[f'{segment_name_prefix}p_granger'] = np.nan
            feats[f'{segment_name_prefix}score_granger'] = np.nan
            feats[f'is_nan_granger'] = 1
        
        # Mutual Information
        if len(pre) > 5 and len(post) > 5:
            try:
                pre_reshaped = pre[~np.isnan(pre)].reshape(-1, 1)
                post_reshaped = post[~np.isnan(post)][:len(pre_reshaped)]
                mi = mutual_info_regression(pre_reshaped, post_reshaped)[0]
                feats[f'{segment_name_prefix}mutual_info'] = mi / np.log(2) if not np.isnan(mi) else np.nan
            except Exception:
                feats[f'{segment_name_prefix}mutual_info'] = np.nan
        else:
            feats[f'{segment_name_prefix}mutual_info'] = np.nan
        
        # Welch's t-test
        if len(pre) > 1 and len(post) > 1:
            try:
                t_stat, p_ttest = stats.ttest_ind(pre, post, equal_var=False, nan_policy='omit')
            except Exception:
                p_ttest = np.nan
        else:
            p_ttest = np.nan
        feats[f'{segment_name_prefix}p_ttest'] = p_ttest
        feats[f'{segment_name_prefix}score_ttest'] = 1 - p_ttest if not np.isnan(p_ttest) else np.nan
        
        # Mann-Whitney U Test
        if len(pre) > 0 and len(post) > 0:
            try:
                u_stat, p_mw = stats.mannwhitneyu(pre, post, alternative='two-sided')
            except Exception:
                p_mw = np.nan
        else:
            p_mw = np.nan
        feats[f'{segment_name_prefix}p_mannwhitney'] = p_mw
        feats[f'{segment_name_prefix}score_mannwhitney'] = 1 - p_mw if not np.isnan(p_mw) else np.nan
        
        # Kolmogorov-Smirnov Test
        if len(pre) > 0 and len(post) > 0:
            try:
                ks_stat, p_ks = stats.ks_2samp(pre, post)
            except Exception:
                p_ks = np.nan
        else:
            p_ks = np.nan
        feats[f'{segment_name_prefix}p_ks'] = p_ks
        feats[f'{segment_name_prefix}score_ks'] = 1 - p_ks if not np.isnan(p_ks) else np.nan
        
        # Anderson-Darling Test
        if len(pre) > 5 and len(post) > 5:
            try:
                ad_stat, crit_vals, p_ad = stats.anderson_ksamp([pre[~np.isnan(pre)], post[~np.isnan(post)]])
                feats[f'{segment_name_prefix}p_ad'] = p_ad
                feats[f'{segment_name_prefix}score_ad'] = 1 - p_ad if not np.isnan(p_ad) else np.nan
                feats[f'{segment_name_prefix}ad_stat'] = ad_stat
            except Exception:
                feats[f'{segment_name_prefix}p_ad'] = np.nan
                feats[f'{segment_name_prefix}score_ad'] = np.nan
                feats[f'{segment_name_prefix}ad_stat'] = np.nan
        else:
            feats[f'{segment_name_prefix}p_ad'] = np.nan
            feats[f'{segment_name_prefix}score_ad'] = np.nan
            feats[f'{segment_name_prefix}ad_stat'] = np.nan
        
        # Rank-Based CUSUM
        if len(pre) > 0 and len(post) > 0:
            try:
                combined = np.concatenate([pre, post])
                ranks = stats.rankdata(combined)
                ranks_post = ranks[len(pre):]
                cusum_ranks = np.cumsum(ranks_post - np.mean(ranks[:len(pre)]))
                feats[f'{segment_name_prefix}cusum_rank_max_abs'] = np.max(np.abs(cusum_ranks)) / len(post) if len(post) > 0 else np.nan
            except Exception:
                feats[f'{segment_name_prefix}cusum_rank_max_abs'] = np.nan
        else:
            feats[f'{segment_name_prefix}cusum_rank_max_abs'] = np.nan
        
        # Chow, Wald, supF, Bai-Perron Tests
        if len(pre) > 5 and len(post) > 5:
            try:
                y = np.concatenate([pre, post])
                n = len(y)
                X = np.ones((n, 2))
                X[:, 1] = np.arange(n)
                dummy = np.zeros(n)
                dummy[len(pre):] = 1
                X = np.column_stack((X, dummy, dummy * np.arange(n)))
                model_full = sm.OLS(y, X, missing='drop').fit()
                model_pre = sm.OLS(y[:len(pre)], X[:len(pre), :2], missing='drop').fit()
                model_post = sm.OLS(y[len(pre):], X[len(pre):, :2], missing='drop').fit()
                ssr_full = model_full.ssr
                ssr_pre = model_pre.ssr
                ssr_post = model_post.ssr
                k = 4
                n_pre, n_post = len(pre), len(post)
                chow_stat = ((ssr_full - (ssr_pre + ssr_post)) / k) / ((ssr_pre + ssr_post) / (n_pre + n_post - 2 * k))
                p_chow = 1 - stats.f.cdf(chow_stat, k, n_pre + n_post - 2 * k)
            except Exception:
                p_chow = np.nan
                chow_stat = np.nan
        else:
            p_chow = np.nan
            chow_stat = np.nan
        feats[f'{segment_name_prefix}p_chow'] = p_chow
        feats[f'{segment_name_prefix}score_chow'] = 1 - p_chow if not np.isnan(p_chow) else np.nan
        feats[f'{segment_name_prefix}chow_stat'] = chow_stat
        
        # Wald Test
        if len(pre) > 5 and len(post) > 5:
            try:
                y = np.concatenate([pre, post])
                n = len(y)
                X = np.ones(n)
                dummy = np.zeros(n)
                dummy[len(pre):] = 1
                X = np.column_stack((X, dummy))
                model = sm.OLS(y, X, missing='drop').fit()
                wald_stat = model.wald_test(np.array([[0, 1]]), scalar=True).stat
                p_wald = model.wald_test(np.array([[0, 1]])).pvalue
            except Exception:
                p_wald = np.nan
                wald_stat = np.nan
        else:
            p_wald = np.nan
            wald_stat = np.nan
        feats[f'{segment_name_prefix}p_wald'] = p_wald
        feats[f'{segment_name_prefix}score_wald'] = 1 - p_wald if not np.isnan(p_wald) else np.nan
        feats[f'{segment_name_prefix}wald_stat'] = wald_stat
        
        # supF Test
        if len(pre) > 5 and len(post) > 5:
            try:
                supf_stat = chow_stat
                p_supf = 1 - stats.f.cdf(supf_stat, k=1, dfn=k, dfd=n_pre + n_post - 2 * k)
            except Exception:
                p_supf = np.nan
                supf_stat = np.nan
        else:
            p_supf = np.nan
            supf_stat = np.nan
        feats[f'{segment_name_prefix}p_supf'] = p_supf
        feats[f'{segment_name_prefix}score_supf'] = 1 - p_supf if not np.isnan(p_supf) else np.nan
        feats[f'{segment_name_prefix}supf_stat'] = supf_stat
        
        # Bai–Perron Test
        if len(pre) > 5 and len(post) > 5:
            try:
                bp_stat = chow_stat
                p_bp = p_chow
            except Exception:
                p_bp = np.nan
                bp_stat = np.nan
        else:
            p_bp = np.nan
            bp_stat = np.nan
        feats[f'{segment_name_prefix}p_bp'] = p_bp
        feats[f'{segment_name_prefix}score_bp'] = 1 - p_bp if not np.isnan(p_bp) else np.nan
        feats[f'{segment_name_prefix}bp_stat'] = bp_stat
        
        # ADF Test
        if len(pre) > 5:
            try:
                adf_pre = adfuller(pre, maxlag=1, regression='c', autolag=None)[1]
            except Exception:
                adf_pre = np.nan
        else:
            adf_pre = np.nan
        if len(post) > 5:
            try:
                adf_post = adfuller(post, maxlag=1, regression='c', autolag=None)[1]
            except Exception:
                adf_post = np.nan
        else:
            adf_post = np.nan
        if len(whole) > 5:
            try:
                adf_whole = adfuller(whole, maxlag=1, regression='c', autolag=None)[1]
            except Exception:
                adf_whole = np.nan
        else:
            adf_whole = np.nan
        feats[f'{segment_name_prefix}p_adf_pre'] = adf_pre
        feats[f'{segment_name_prefix}p_adf_post'] = adf_post
        feats[f'{segment_name_prefix}p_adf_whole'] = adf_whole
        feats[f'{segment_name_prefix}diff_adf'] = abs(adf_post - adf_pre) if not (np.isnan(adf_pre) or np.isnan(adf_post)) else np.nan
        feats[f'{segment_name_prefix}score_adf'] = feats[f'{segment_name_prefix}diff_adf'] / 0.1 if not np.isnan(feats[f'{segment_name_prefix}diff_adf']) else np.nan
        
        # KPSS Test
        if len(pre) > 5:
            try:
                kpss_pre = kpss(pre, regression='c', nlags='auto')[1]
            except Exception:
                kpss_pre = np.nan
        else:
            kpss_pre = np.nan
        if len(post) > 5:
            try:
                kpss_post = kpss(post, regression='c', nlags='auto')[1]
            except Exception:
                kpss_post = np.nan
        else:
            kpss_post = np.nan
        if len(whole) > 5:
            try:
                kpss_whole = kpss(whole, regression='c', nlags='auto')[1]
            except Exception:
                kpss_whole = np.nan
        else:
            kpss_whole = np.nan
        feats[f'{segment_name_prefix}p_kpss_pre'] = kpss_pre
        feats[f'{segment_name_prefix}p_kpss_post'] = kpss_post
        feats[f'{segment_name_prefix}p_kpss_whole'] = kpss_whole
        feats[f'{segment_name_prefix}diff_kpss'] = abs(kpss_post - kpss_pre) if not (np.isnan(kpss_pre) or np.isnan(kpss_post)) else np.nan
        feats[f'{segment_name_prefix}score_kpss'] = feats[f'{segment_name_prefix}diff_kpss'] / 0.1 if not np.isnan(feats[f'{segment_name_prefix}diff_kpss']) else np.nan










        # Quantile-based features
        if len(pre) > 0 and len(post) > 0:
            try:
                quantiles = [0.25, 0.5, 0.75]
                q_pre = np.nanquantile(pre, quantiles)
                q_post = np.nanquantile(post, quantiles)
                for i, q in enumerate(quantiles):
                    feats[f'{segment_name_prefix}q{int(q*100)}_pre'] = q_pre[i]
                    feats[f'{segment_name_prefix}q{int(q*100)}_post'] = q_post[i]
                    feats[f'{segment_name_prefix}q{int(q*100)}_diff'] = q_post[i] - q_pre[i] if not (np.isnan(q_pre[i]) or np.isnan(q_post[i])) else np.nan
                    if std_whole and std_whole > 0:
                        feats[f'{segment_name_prefix}q{int(q*100)}_norm_diff'] = (q_post[i] - q_pre[i]) / std_whole if not (np.isnan(q_pre[i]) or np.isnan(q_post[i])) else np.nan
                    else:
                        feats[f'{segment_name_prefix}q{int(q*100)}_norm_diff'] = np.nan
            except Exception:
                for q in [0.25, 0.5, 0.75]:
                    feats[f'{segment_name_prefix}q{int(q*100)}_pre'] = np.nan
                    feats[f'{segment_name_prefix}q{int(q*100)}_post'] = np.nan
                    feats[f'{segment_name_prefix}q{int(q*100)}_diff'] = np.nan
                    feats[f'{segment_name_prefix}q{int(q*100)}_norm_diff'] = np.nan
        else:
            for q in [0.25, 0.5, 0.75]:
                feats[f'{segment_name_prefix}q{int(q*100)}_pre'] = np.nan
                feats[f'{segment_name_prefix}q{int(q*100)}_post'] = np.nan
                feats[f'{segment_name_prefix}q{int(q*100)}_diff'] = np.nan
                feats[f'{segment_name_prefix}q{int(q*100)}_norm_diff'] = np.nan

        # Normalized features using reference bin
        if normalize and reference_features is not None:
            try:
                # Normalize existing features
                for key in feats:
                    if key.endswith('_whole') or key.endswith('_pre') or key.endswith('_post'):
                        if key in reference_features:
                            ref_mean = reference_features.get(key, np.nan)
                            ref_std = reference_features.get(key.replace('_whole', '_std_whole').replace('_pre', '_std_pre').replace('_post', '_std_post'), np.nan)
                            if ref_std and ref_std > 0 and not np.isnan(feats[key]):
                                feats[f'{key}_norm'] = (feats[key] - ref_mean) / ref_std
                            else:
                                feats[f'{key}_norm'] = np.nan
                        else:
                            feats[f'{key}_norm'] = np.nan

                # Normalize pycatch22 features
                for feature_name in pycatch22_features:
                    for suffix in ['_whole', '_pre', '_post']:
                        key = f'{segment_name_prefix}{feature_name}{suffix}'
                        if key in feats:
                            ref_key = key.replace(segment_name_prefix, '')  # Use reference bin's feature
                            if ref_key in reference_features:
                                ref_mean = reference_features.get(ref_key, np.nan)
                                ref_std = reference_features.get(ref_key.replace(suffix, f'_std{suffix}'), np.nan)
                                if ref_std and ref_std > 0 and not np.isnan(feats[key]):
                                    feats[f'{key}_norm_ref'] = (feats[key] - ref_mean) / ref_std
                                else:
                                    feats[f'{key}_norm_ref'] = np.nan
                            else:
                                feats[f'{key}_norm_ref'] = np.nan

                # Normalize SSA features
                for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                            'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                    for suffix in ['_whole', '_pre', '_post']:
                        feat_key = f'{segment_name_prefix}{key}{suffix}'
                        if feat_key in feats:
                            ref_key = feat_key.replace(segment_name_prefix, '')
                            if ref_key in reference_features:
                                ref_mean = reference_features.get(ref_key, np.nan)
                                ref_std = reference_features.get(ref_key.replace(suffix, f'_std{suffix}'), np.nan)
                                if ref_std and ref_std > 0 and not np.isnan(feats[feat_key]):
                                    feats[f'{feat_key}_norm_ref'] = (feats[feat_key] - ref_mean) / ref_std
                                else:
                                    feats[f'{feat_key}_norm_ref'] = np.nan
                            else:
                                feats[f'{feat_key}_norm_ref'] = np.nan
                # Normalize tsfresh features
                tsfresh_feature_names = [
                    f'energy_ratio_chunk{i+1}' for i in range(2)
                ] + [
                    f'fft_agg_{stat}' for stat in ['centroid', 'variance']
                ] + [
                    f'index_mass_quantile_q{int(q*100)}' for q in [0.25, 0.5, 0.75]
                ] + [
                    'binned_entropy', 'last_location_of_maximum', 'lempel_ziv_complexity',
                    'longest_strike_above_mean', 'longest_strike_below_mean', 'mean_second_derivative_central',
                    'pct_reoccurring_datapoints', 'pct_reoccurring_values'
                ] + [
                    f'ratio_beyond_{r}_sigma' for r in [1, 2]
                ] + [
                    'ratio_unique_values'
                ]
                for feature_name in tsfresh_feature_names:
                    for suffix in ['_whole', '_pre', '_post']:
                        key = f'{segment_name_prefix}{feature_name}{suffix}'
                        if key in feats:
                            ref_key = key.replace(segment_name_prefix, '')
                            if ref_key in reference_features:
                                ref_mean = reference_features.get(ref_key, np.nan)
                                ref_std = reference_features.get(ref_key.replace(suffix, f'_std{suffix}'), np.nan)
                                if ref_std and ref_std > 0 and not np.isnan(feats[key]):
                                    feats[f'{key}_norm_ref'] = (feats[key] - ref_mean) / ref_std
                                else:
                                    feats[f'{key}_norm_ref'] = np.nan
                            else:
                                feats[f'{key}_norm_ref'] = np.nan
            except Exception:
                # Handle normalization errors
                for key in list(feats.keys()):
                    if key.endswith('_whole') or key.endswith('_pre') or key.endswith('_post'):
                        feats[f'{key}_norm'] = np.nan
                for feature_name in pycatch22_features:
                    for suffix in ['_whole', '_pre', '_post']:
                        feats[f'{segment_name_prefix}{feature_name}{suffix}_norm_ref'] = np.nan
                for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                            'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                    for suffix in ['_whole', '_pre', '_post']:
                        feats[f'{segment_name_prefix}{key}{suffix}_norm_ref'] = np.nan
                for feature_name in tsfresh_feature_names:
                    for suffix in ['_whole', '_pre', '_post']:
                        feats[f'{segment_name_prefix}{feature_name}{suffix}_norm_ref'] = np.nan
    # Return computed features
    return feats

def process_series(args):
    """
    Process a single time series and compute features for whole series and bins.

    Parameters:
    - args: tuple containing (series_id, series, tstar, compute_pre_post, compute_pre_post_bins, normalize, weight_type, sigma, compute_jsd_bins)

    Returns:
    - dict: features for the series
    """
    series_id, series, tstar, compute_pre_post, compute_pre_post_bins, normalize, weight_type, sigma, compute_jsd_bins = args
    feats = {'series_id': series_id}

    # Handle input series
    series = np.array(series, dtype=float)
    series = series[~np.isnan(series)]  # Remove NaNs for processing
    if len(series) == 0:
        return feats

    # Compute features for the whole series
    if tstar is not None and compute_pre_post:
        pre = series[:tstar]
        post = series[tstar:]
        whole = series
        whole_indices = np.arange(len(series))
        feats.update(compute_features_for_segment(
            pre=pre, post=post, whole=whole, segment_name_prefix='',
            compute_pre_post=compute_pre_post, normalize=normalize,
            tstar=tstar, whole_indices=whole_indices, weight_type=weight_type,
            sigma=sigma, compute_jsd=compute_jsd_bins
        ))
    else:
        feats.update(compute_features_for_segment(
            pre=np.array([]), post=np.array([]), whole=series, segment_name_prefix='',
            compute_pre_post=False, normalize=normalize, weight_type=weight_type,
            sigma=sigma, compute_jsd=False
        ))

    # Time-based bins
    n_bins = 2
    bin_size = len(series) // n_bins
    reference_bin_features = None
    if bin_size > 0:
        for i in range(n_bins):
            start_idx = i * bin_size
            end_idx = (i + 1) * bin_size if i < n_bins - 1 else len(series)
            bin_segment = series[start_idx:end_idx]
            bin_indices = np.arange(start_idx, end_idx)
            bin_tstar = tstar - start_idx if tstar is not None and start_idx <= tstar < end_idx else None
            if compute_pre_post_bins and bin_tstar is not None:
                bin_pre = bin_segment[:bin_tstar]
                bin_post = bin_segment[bin_tstar:]
            else:
                bin_pre = np.array([])
                bin_post = np.array([])
            bin_feats = compute_features_for_segment(
                pre=bin_pre, post=bin_post, whole=bin_segment,
                segment_name_prefix=f'time_bin{i+1}_',
                compute_pre_post=compute_pre_post_bins and bin_tstar is not None,
                normalize=normalize, tstar=bin_tstar, whole_indices=bin_indices,
                weight_type=weight_type, sigma=sigma, compute_jsd=compute_jsd_bins
            )
            feats.update(bin_feats)
            if i == 0:  # Use first bin as reference
                reference_bin_features = bin_feats

        # Normalize bin features using reference bin
        if normalize and reference_bin_features is not None:
            for i in range(1, n_bins):
                start_idx = i * bin_size
                end_idx = (i + 1) * bin_size if i < n_bins - 1 else len(series)
                bin_segment = series[start_idx:end_idx]
                bin_indices = np.arange(start_idx, end_idx)
                bin_tstar = tstar - start_idx if tstar is not None and start_idx <= tstar < end_idx else None
                if compute_pre_post_bins and bin_tstar is not None:
                    bin_pre = bin_segment[:bin_tstar]
                    bin_post = bin_segment[bin_tstar:]
                else:
                    bin_pre = np.array([])
                    bin_post = np.array([])
                bin_feats = compute_features_for_segment(
                    pre=bin_pre, post=bin_post, whole=bin_segment,
                    segment_name_prefix=f'time_bin{i+1}_',
                    compute_pre_post=compute_pre_post_bins and bin_tstar is not None,
                    reference_features=reference_bin_features, normalize=normalize,
                    tstar=bin_tstar, whole_indices=bin_indices,
                    weight_type=weight_type, sigma=sigma, compute_jsd=compute_jsd_bins
                )
                feats.update(bin_feats)

    # Value-based bins
    sorted_indices = np.argsort(series)
    sorted_series = series[sorted_indices]
    bin_size = len(series) // n_bins
    reference_bin_features = None
    if bin_size > 0:
        for i in range(n_bins):
            start_idx = i * bin_size
            end_idx = (i + 1) * bin_size if i < n_bins - 1 else len(series)
            bin_indices = sorted_indices[start_idx:end_idx]
            bin_segment = series[bin_indices]
            bin_tstar = None
            if tstar is not None:
                bin_tstar = np.sum(bin_indices < tstar)
            if compute_pre_post_bins and bin_tstar is not None and bin_tstar > 0 and bin_tstar < len(bin_indices):
                bin_pre = series[bin_indices[bin_indices < tstar]]
                bin_post = series[bin_indices[bin_indices >= tstar]]
            else:
                bin_pre = np.array([])
                bin_post = np.array([])
            bin_feats = compute_features_for_segment(
                pre=bin_pre, post=bin_post, whole=bin_segment,
                segment_name_prefix=f'value_bin{i+1}_',
                compute_pre_post=compute_pre_post_bins and bin_tstar is not None and bin_tstar > 0 and bin_tstar < len(bin_indices),
                normalize=normalize, tstar=bin_tstar, whole_indices=bin_indices,
                weight_type=weight_type, sigma=sigma, compute_jsd=compute_jsd_bins
            )
            feats.update(bin_feats)
            if i == 0:
                reference_bin_features = bin_feats

        # Normalize value-based bin features using reference bin
        if normalize and reference_bin_features is not None:
            for i in range(1, n_bins):
                start_idx = i * bin_size
                end_idx = (i + 1) * bin_size if i < n_bins - 1 else len(series)
                bin_indices = sorted_indices[start_idx:end_idx]
                bin_segment = series[bin_indices]
                bin_tstar = None
                if tstar is not None:
                    bin_tstar = np.sum(bin_indices < tstar)
                if compute_pre_post_bins and bin_tstar is not None and bin_tstar > 0 and bin_tstar < len(bin_indices):
                    bin_pre = series[bin_indices[bin_indices < tstar]]
                    bin_post = series[bin_indices[bin_indices >= tstar]]
                else:
                    bin_pre = np.array([])
                    bin_post = np.array([])
                bin_feats = compute_features_for_segment(
                    pre=bin_pre, post=bin_post, whole=bin_segment,
                    segment_name_prefix=f'value_bin{i+1}_',
                    compute_pre_post=compute_pre_post_bins and bin_tstar is not None and bin_tstar > 0 and bin_tstar < len(bin_indices),
                    reference_features=reference_bin_features, normalize=normalize,
                    tstar=bin_tstar, whole_indices=bin_indices,
                    weight_type=weight_type, sigma=sigma, compute_jsd=compute_jsd_bins
                )
                feats.update(bin_feats)

    return feats
 
        
def extract_features(series_data, tstar_data=None, compute_pre_post=True, compute_pre_post_bins=False,
                    normalize=True, weight_type='proportion', sigma=0.1, compute_jsd_bins=True, n_jobs=-1, 
                    disable_progressbar=False):
    """
    Extract features from a list of time series.

    Parameters:
    - series_data: list or dict of time series, where each series is a numpy array or list.
    - tstar_data: list or array of tstar values (break points) for each series, or None.
    - compute_pre_post: bool, whether to compute pre and post features for whole series.
    - compute_pre_post_bins: bool, whether to compute pre and post features for bins.
    - normalize: bool, whether to compute normalized features.
    - weight_type: str, 'proportion' or 'kernel' for weighting.
    - sigma: float, bandwidth for kernel weighting.
    - compute_jsd_bins: bool, whether to compute JSD for bins.
    - n_jobs: int, number of parallel jobs (-1 for all available cores).

    Returns:
    - pd.DataFrame: DataFrame with features for each series.
    """
    if isinstance(series_data, dict):
        series_ids = list(series_data.keys())
        series_list = list(series_data.values())
    else:
        series_ids = list(range(len(series_data)))
        series_list = series_data

    if tstar_data is None:
        tstar_data = [None] * len(series_list)
    elif isinstance(tstar_data, dict):
        tstar_data = [tstar_data.get(sid, None) for sid in series_ids]
    else:
        tstar_data = tstar_data

    args = [
        (sid, series, tstar, compute_pre_post, compute_pre_post_bins, normalize,
         weight_type, sigma, compute_jsd_bins)
        for sid, series, tstar in zip(series_ids, series_list, tstar_data)
    ]

    # Parallel processing
    if n_jobs == 1:
        if disable_progressbar:
            results = [process_series(arg) for arg in args]
        else:
            results = [process_series(arg) for arg in tqdm(args, desc="Processing series")]
    else:
        with Pool(processes=n_jobs if n_jobs > 0 else os.cpu_count()) as pool:
            if disable_progressbar:
                results = list(pool.imap(process_series, args))
            else:
                results = list(tqdm(pool.imap(process_series, args), total=len(args), desc="Processing series"))

    # Convert results to DataFrame
    feature_df = pd.DataFrame(results)
    feature_df.set_index('series_id', inplace=True)
    
    return feature_df