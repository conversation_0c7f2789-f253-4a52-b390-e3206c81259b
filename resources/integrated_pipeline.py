#!/usr/bin/env python3
"""
Integrated TSFresh + Thermodynamics Pipeline
Combines all 1280 TSFresh features with non-duplicate Thermodynamics features
With parallel processing and progress tracking
"""

import numpy as np
import pandas as pd
import time
import warnings
from multiprocessing import Pool
from tqdm import tqdm
import os
from scipy import stats, signal
import sys
sys.path.append('/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources')

try:
    from scipy.integrate import trapz
except ImportError:
    from scipy.integrate import trapezoid as trapz
from joblib import Parallel, delayed

warnings.filterwarnings('ignore')

# Import the actual feature extractors
try:
    from Thermodynamics_features import ThermodynamicTimeSeriesAnalyzer
    THERMO_AVAILABLE = True
except ImportError:
    print("Warning: Thermodynamics_features not found")
    THERMO_AVAILABLE = False

try:
    from existing_features import extract_features as extract_tsfresh_features
    TSFRESH_AVAILABLE = True
except ImportError:
    print("Warning: existing_features not found")
    TSFRESH_AVAILABLE = False

# Load TSFresh 1280 features with proper dependency and compatibility handling
def load_tsfresh_1280():
    """Load TSFresh 1280 module with dependency and compatibility handling"""
    import importlib.util
    
    # Check for required dependencies first
    required_deps = ['arch', 'pycatch22', 'statsmodels', 'tsfresh']
    missing_deps = []
    
    for dep in required_deps:
        try:
            __import__(dep)
        except ImportError:
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"Warning: Missing dependencies for TSFresh 1280: {missing_deps}")
        return None, False
    
    # Try to apply scipy compatibility patch
    try:
        from scipy_compatibility_patch import patch_scipy_compatibility
        if patch_scipy_compatibility():
            print("✓ Scipy compatibility patch applied")
        else:
            print("⚠️  Could not apply scipy compatibility patch")
    except ImportError:
        print("⚠️  Scipy compatibility patch not available")
    
    # Check scipy compatibility
    try:
        import scipy
        from scipy._lib._util import _lazywhere
        print("✓ Scipy compatibility check passed")
    except ImportError:
        print("Warning: Scipy compatibility issue - _lazywhere not available")
        print("This is likely due to scipy version incompatibility")
        return None, False
    
    # Try to load the TSFresh module with robust path resolution
    # Handle both local development and containerized environments
    
    # Get the directory containing this file (integrated_pipeline.py)
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
    except NameError:
        # __file__ not available (e.g., in notebooks), use current working directory
        current_dir = os.getcwd()
    
    possible_paths = [
        # First try in the same directory as this file (resources folder)
        os.path.join(current_dir, "1280_TSFRESHplusCatch22.py"),
        # Try relative paths from current working directory
        "resources/1280_TSFRESHplusCatch22.py",
        "./resources/1280_TSFRESHplusCatch22.py",
        "1280_TSFRESHplusCatch22.py",
        # Try common container paths
        "/app/CrunchDAO/structuralbreak/resources/1280_TSFRESHplusCatch22.py",
        "/app/resources/1280_TSFRESHplusCatch22.py",
        # Try paths relative to the resources folder
        os.path.join(os.path.dirname(current_dir), "resources", "1280_TSFRESHplusCatch22.py"),
        # Legacy path for backward compatibility
        os.path.join(os.path.dirname(__file__), "1280_TSFRESHplusCatch22.py") if '__file__' in globals() else None
    ]
    
    # Filter out None values
    possible_paths = [p for p in possible_paths if p is not None]
    
    file_path = None
    for path in possible_paths:
        if os.path.exists(path):
            file_path = path
            break
    
    if file_path is None:
        print(f"Could not find 1280_TSFRESHplusCatch22.py in any of these locations: {possible_paths}")
        return None, False
    
    try:
        spec = importlib.util.spec_from_file_location("tsfresh_1280", file_path)
        if spec and spec.loader:
            tsfresh_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(tsfresh_module)
            
            # Check if extract_features function exists (this is the main function)
            if hasattr(tsfresh_module, 'extract_features'):
                print(f"✓ TSFresh 1280 features loaded from {file_path}")
                return tsfresh_module.extract_features, True
            elif hasattr(tsfresh_module, 'compute_features_for_segment'):
                print(f"✓ TSFresh 1280 features (segment function) loaded from {file_path}")
                return tsfresh_module.compute_features_for_segment, True
            else:
                print(f"Warning: extract_features not found in {file_path}")
                
    except Exception as e:
        print(f"Could not load {file_path}: {e}")
        if "_lazywhere" in str(e):
            print("💡 Tip: This is a scipy version compatibility issue.")
            print("   Try: pip install scipy==1.10.1 or update the TSFresh code")
    
    return None, False

# Load TSFresh functionality with fallback strategy
compute_features_for_segment = None
FULL_TSFRESH_AVAILABLE = False

try:
    compute_features_for_segment, FULL_TSFRESH_AVAILABLE = load_tsfresh_1280()
except Exception as e:
    print(f"Warning: Could not load TSFresh 1280: {e}")

# If full TSFresh failed, try to use existing_features as enhanced fallback
if not FULL_TSFRESH_AVAILABLE and TSFRESH_AVAILABLE:
    print("🔄 Falling back to existing_features.py for TSFresh functionality")
    ENHANCED_TSFRESH_AVAILABLE = True
else:
    ENHANCED_TSFRESH_AVAILABLE = False

# Fallback simplified thermodynamics analyzer if import fails
class SimplifiedThermodynamicAnalyzer:
    def __init__(self, temperature=1.0, damping=0.1):
        self.temperature = temperature
        self.damping = damping
    
    def calibrate_parameters(self, ts):
        if len(ts) < 2:
            return
        self.temperature = np.nanvar(ts) + 1e-12
        velocity = np.diff(ts[~np.isnan(ts)])
        if len(velocity) > 1:
            autocorr = np.corrcoef(velocity[:-1], velocity[1:])[0, 1]
            if not np.isnan(autocorr) and autocorr > 0:
                self.damping = max(0, -np.log(autocorr + 1e-12))
    
    def compute_energy_features(self, ts, prefix=''):
        features = {}
        if len(ts) < 2:
            return features
        
        # Potential energies
        N = len(ts[~np.isnan(ts)])
        features[f'{prefix}potential_energy_length_weighted'] = N * np.log(N + 1)
        features[f'{prefix}potential_energy_harmonic'] = np.nansum(ts**2) / (N + 1e-12)
        
        if len(ts) > 2:
            d2x_dt2 = np.diff(ts[~np.isnan(ts)], n=2)
            features[f'{prefix}potential_energy_curvature'] = np.sum(np.abs(d2x_dt2)) if len(d2x_dt2) > 0 else 0.0
            dx_dt = np.diff(ts[~np.isnan(ts)])
            features[f'{prefix}potential_energy_gradient'] = np.sum(dx_dt**2) if len(dx_dt) > 0 else 0.0
        
        # Kinetic energies
        features[f'{prefix}kinetic_energy_variance_based'] = np.nanvar(ts)
        velocity = np.diff(ts[~np.isnan(ts)])
        features[f'{prefix}kinetic_energy_velocity'] = np.nanmean(velocity**2) if len(velocity) > 0 else 0.0
        features[f'{prefix}kinetic_energy_amplitude'] = np.ptp(ts[~np.isnan(ts)])**2 / 4
        
        # Spectral kinetic energy
        if N >= 4:
            try:
                freqs, psd = signal.welch(ts[~np.isnan(ts)], nperseg=min(N//8, 128))
                features[f'{prefix}kinetic_energy_spectral'] = trapz(freqs**2 * psd, freqs)
            except:
                features[f'{prefix}kinetic_energy_spectral'] = np.nanvar(ts)
        
        return features
    
    def compute_spike_features(self, ts, prefix=''):
        features = {}
        if len(ts) < 2:
            return features
        
        mean_ts = np.nanmean(ts)
        std_ts = np.nanstd(ts)
        N = len(ts[~np.isnan(ts)])
        
        if std_ts > 0:
            ts_clean = ts[~np.isnan(ts)]
            dev = ts_clean - mean_ts
            
            # 2-sigma spikes
            spikes_2sigma_pos = np.sum(dev > 2 * std_ts)
            spikes_2sigma_neg = np.sum(dev < -2 * std_ts)
            features[f'{prefix}spike_count_2sigma_pos'] = spikes_2sigma_pos
            features[f'{prefix}spike_count_2sigma_neg'] = spikes_2sigma_neg
            features[f'{prefix}spike_ratio_2sigma_pos'] = spikes_2sigma_pos / N
            features[f'{prefix}spike_ratio_2sigma_neg'] = spikes_2sigma_neg / N
            
            # 3-sigma spikes
            spikes_3sigma_pos = np.sum(dev > 3 * std_ts)
            spikes_3sigma_neg = np.sum(dev < -3 * std_ts)
            features[f'{prefix}spike_count_3sigma_pos'] = spikes_3sigma_pos
            features[f'{prefix}spike_count_3sigma_neg'] = spikes_3sigma_neg
            features[f'{prefix}spike_ratio_3sigma_pos'] = spikes_3sigma_pos / N
            features[f'{prefix}spike_ratio_3sigma_neg'] = spikes_3sigma_neg / N
            
            # Spike energies
            spikes_2sigma = ts_clean[np.abs(dev) > 2 * std_ts]
            spikes_3sigma = ts_clean[np.abs(dev) > 3 * std_ts]
            features[f'{prefix}kinetic_energy_spike_2sigma'] = np.sum((spikes_2sigma - mean_ts)**2) / (N + 1e-12)
            features[f'{prefix}kinetic_energy_spike_3sigma'] = np.sum((spikes_3sigma - mean_ts)**2) / (N + 1e-12)
            
            # Spike value sum
            spike_values = np.abs(dev[np.abs(dev) > 2 * std_ts])
            features[f'{prefix}spike_value_sum'] = np.sum(spike_values)
        
        return features
    
    def compute_thermodynamic_properties(self, ts, prefix=''):
        features = {}
        if len(ts) < 2:
            return features
        
        # Entropy production
        N = len(ts[~np.isnan(ts)])
        n_bins = max(10, int(np.sqrt(N)))
        hist, _ = np.histogram(ts[~np.isnan(ts)], bins=n_bins, density=True)
        p_i = hist[hist > 0]
        entropy = -np.sum(p_i * np.log(p_i + 1e-12)) if len(p_i) > 0 else 0.0
        features[f'{prefix}entropy_production'] = entropy
        
        # Energy efficiency
        potential = np.sum(ts**2) / len(ts)
        kinetic = np.var(ts)
        total_energy = potential + kinetic
        features[f'{prefix}total_energy'] = total_energy
        features[f'{prefix}energy_efficiency'] = kinetic / (total_energy + 1e-12)
        
        # Free energy
        features[f'{prefix}free_energy'] = potential + kinetic - self.temperature * entropy
        
        # Phase space volume (simplified)
        if N > 2:
            x = ts[~np.isnan(ts)][:-1]
            v = np.diff(ts[~np.isnan(ts)])
            features[f'{prefix}phase_space_volume'] = np.std(x) * np.std(v) * np.pi
        
        # Dissipation rate
        velocity = np.diff(ts[~np.isnan(ts)])
        if len(velocity) > 1:
            autocorr = np.corrcoef(velocity[:-1], velocity[1:])[0, 1]
            if not np.isnan(autocorr):
                features[f'{prefix}dissipation_rate'] = max(0, -autocorr * np.var(velocity))
        
        # Ergodic measure
        if N >= 10:
            n_windows = 10
            window_size = N // n_windows
            time_avg = np.nanmean(ts)
            ensemble_avgs = [np.nanmean(ts[i:i+window_size]) for i in range(0, N-window_size+1, window_size)]
            ensemble_avg = np.nanmean(ensemble_avgs)
            ensemble_std = np.nanstd(ensemble_avgs)
            features[f'{prefix}ergodic_measure'] = np.abs(time_avg - ensemble_avg) / (ensemble_std + 1e-12) if ensemble_std > 0 else 0.0
        
        return features
    
    def analyze_time_series(self, ts, tstar=None, dt=1.0):
        ts = np.array(ts)
        N = len(ts)
        
        self.calibrate_parameters(ts)
        
        all_features = {}
        
        # Whole series
        all_features.update(self.compute_energy_features(ts, ''))
        all_features.update(self.compute_spike_features(ts, ''))
        all_features.update(self.compute_thermodynamic_properties(ts, ''))
        
        # Pre/post tstar
        if tstar is not None and 0 <= tstar < N:
            if len(ts[:tstar][~np.isnan(ts[:tstar])]) >= 2:
                all_features.update(self.compute_energy_features(ts[:tstar], 'pre_'))
                all_features.update(self.compute_spike_features(ts[:tstar], 'pre_'))
                all_features.update(self.compute_thermodynamic_properties(ts[:tstar], 'pre_'))
            
            if len(ts[tstar:][~np.isnan(ts[tstar:])]) >= 2:
                all_features.update(self.compute_energy_features(ts[tstar:], 'post_'))
                all_features.update(self.compute_spike_features(ts[tstar:], 'post_'))
                all_features.update(self.compute_thermodynamic_properties(ts[tstar:], 'post_'))
        
        # Pre/post half
        half_point = N // 2
        if len(ts[:half_point][~np.isnan(ts[:half_point])]) >= 2:
            all_features.update(self.compute_energy_features(ts[:half_point], 'pre_half_'))
            all_features.update(self.compute_spike_features(ts[:half_point], 'pre_half_'))
            all_features.update(self.compute_thermodynamic_properties(ts[:half_point], 'pre_half_'))
        
        if len(ts[half_point:][~np.isnan(ts[half_point:])]) >= 2:
            all_features.update(self.compute_energy_features(ts[half_point:], 'post_half_'))
            all_features.update(self.compute_spike_features(ts[half_point:], 'post_half_'))
            all_features.update(self.compute_thermodynamic_properties(ts[half_point:], 'post_half_'))
        
        # Simple ratios (subset)
        if tstar is not None:
            pre_energy = all_features.get('pre_total_energy', 0)
            post_energy = all_features.get('post_total_energy', 0)
            pre_half_energy = all_features.get('pre_half_total_energy', 0)
            post_half_energy = all_features.get('post_half_total_energy', 0)
            
            if pre_half_energy > 0:
                all_features['ratio_pre_total_energy'] = pre_energy / pre_half_energy
            if post_half_energy > 0:
                all_features['ratio_post_total_energy'] = post_energy / post_half_energy
            
            # Spike value ratio
            pre_spike_sum = all_features.get('pre_spike_value_sum', 0)
            post_spike_sum = all_features.get('post_spike_value_sum', 0)
            all_features['spike_value_ratio_post_to_pre'] = post_spike_sum / (pre_spike_sum + 1e-12) if pre_spike_sum > 0 else np.nan
        
        # Position
        all_features['tstar_position'] = tstar / N if tstar is not None and N > 0 else np.nan
        
        return all_features

# Simplified TSFresh features (key ones for testing)
class SimplifiedTSFreshExtractor:
    def extract_features(self, series_data, tstar_data=None, compute_pre_post=True, 
                        compute_pre_post_bins=False, normalize=True, 
                        weight_type='proportion', sigma=0.1, compute_jsd_bins=True, n_jobs=-1):
        """
        Simplified TSFresh feature extraction for testing
        In real implementation, this would call the full 1280-feature TSFresh pipeline
        """
        features_list = []
        
        for series_id, series in series_data.items():
            features = {}
            tstar = tstar_data.get(series_id) if tstar_data else None
            
            # Simulate some key TSFresh features
            features.update(self._extract_basic_tsfresh(series, '', series))
            
            if compute_pre_post and tstar is not None:
                if tstar > 0 and tstar < len(series):
                    pre_seg = series[:tstar]
                    post_seg = series[tstar:]
                    
                    if len(pre_seg) > 0:
                        features.update(self._extract_basic_tsfresh(pre_seg, 'pre_', series))
                    if len(post_seg) > 0:
                        features.update(self._extract_basic_tsfresh(post_seg, 'post_', series))
            
            features['series_id'] = series_id
            features_list.append(features)
        
        return pd.DataFrame(features_list).set_index('series_id')
    
    def _extract_basic_tsfresh(self, segment, prefix, whole_series):
        """Extract basic TSFresh-style features"""
        features = {}
        
        if len(segment) < 2:
            return features
        
        # Basic statistical features
        features[f'{prefix}mean'] = np.mean(segment)
        features[f'{prefix}std'] = np.std(segment)
        features[f'{prefix}var'] = np.var(segment)
        features[f'{prefix}skewness'] = stats.skew(segment) if len(segment) > 2 else 0
        features[f'{prefix}kurtosis'] = stats.kurtosis(segment) if len(segment) > 2 else 0
        
        # Quantile features
        features[f'{prefix}quantile_25'] = np.percentile(segment, 25)
        features[f'{prefix}quantile_50'] = np.percentile(segment, 50)
        features[f'{prefix}quantile_75'] = np.percentile(segment, 75)
        
        # Energy features
        features[f'{prefix}energy'] = np.sum(segment**2)
        
        # Autocorrelation
        if len(segment) > 1:
            autocorr = np.corrcoef(segment[:-1], segment[1:])[0, 1]
            features[f'{prefix}autocorr_lag1'] = autocorr if not np.isnan(autocorr) else 0
        
        # Spectral features
        if len(segment) >= 10:
            try:
                freqs, psd = signal.welch(segment, nperseg=min(len(segment)//2, 64))
                features[f'{prefix}spectral_power'] = np.sum(psd)
                features[f'{prefix}spectral_centroid'] = np.sum(freqs * psd) / np.sum(psd)
            except:
                features[f'{prefix}spectral_power'] = 0
                features[f'{prefix}spectral_centroid'] = 0
        
        # Complexity features
        features[f'{prefix}num_peaks'] = len(signal.find_peaks(segment)[0])
        features[f'{prefix}range'] = np.max(segment) - np.min(segment)
        
        # Ratio features
        mean_val = np.mean(segment)
        features[f'{prefix}above_mean_ratio'] = np.sum(segment > mean_val) / len(segment)
        
        return features

class IntegratedTSFreshThermoPipeline:
    """
    Integrated pipeline combining TSFresh (1280) + Thermodynamics (non-duplicate)
    With parallel processing and progress tracking
    """
    
    def __init__(self, n_jobs=-1, use_full_features=True):
        self.n_jobs = n_jobs
        self.use_full_features = use_full_features
        
        # Initialize extractors based on availability
        if use_full_features and THERMO_AVAILABLE:
            self.thermo_analyzer = ThermodynamicTimeSeriesAnalyzer()
            print("✓ Using full Thermodynamics analyzer")
        else:
            self.thermo_analyzer = SimplifiedThermodynamicAnalyzer()
            print("⚠️  Using simplified Thermodynamics analyzer")
        
        # Check TSFresh availability with fallback strategy
        if use_full_features and FULL_TSFRESH_AVAILABLE:
            self.use_full_tsfresh = True
            print("✓ Using full 1280 TSFresh features")
        elif TSFRESH_AVAILABLE or ENHANCED_TSFRESH_AVAILABLE:
            self.use_full_tsfresh = False
            print("✓ Using enhanced TSFresh features from existing_features.py")
            print("  (This provides comprehensive features as fallback for 1280 TSFresh)")
        else:
            self.use_full_tsfresh = False
            self.tsfresh_extractor = SimplifiedTSFreshExtractor()
            print("⚠️  Using simplified TSFresh features")
        
        # Define duplicate features to remove from thermodynamics
        self.duplicate_features = {
            'mean_whole', 'mean_pre', 'mean_post', 'mean_pre_half', 'mean_post_half',
            'std_whole', 'std_pre', 'std_post', 'std_pre_half', 'std_post_half',
            'var_whole', 'var_pre', 'var_post', 'var_pre_half', 'var_post_half',
            'skew_whole', 'skew_pre', 'skew_post', 'skew_pre_half', 'skew_post_half',
            'kurt_whole', 'kurt_pre', 'kurt_post', 'kurt_pre_half', 'kurt_post_half',
            'ac_whole', 'ac_pre', 'ac_post', 'ac_pre_half', 'ac_post_half',
            'phi_whole', 'phi_pre', 'phi_post', 'phi_pre_half', 'phi_post_half',
            'len_whole', 'len_pre', 'len_post', 'len_pre_half', 'len_post_half'
        }
    
    def extract_single_series_features(self, args):
        """Extract features for a single time series (for parallel processing)"""
        series_id, series, tstar = args
        
        try:
            features = {}
            series = np.array(series, dtype=float)
            
            # Extract TSFresh features
            if self.use_full_tsfresh and FULL_TSFRESH_AVAILABLE:
                # Use the full 1280 TSFresh implementation
                series_data = {series_id: series}
                tstar_data = {series_id: tstar} if tstar is not None else None
                
                # Call the main extract_features function (this should give us all 1280 features)
                tsfresh_df = compute_features_for_segment(
                    series_data=series_data,
                    tstar_data=tstar_data,
                    compute_pre_post=True,
                    compute_pre_post_bins=False,
                    normalize=True,
                    weight_type='proportion',
                    sigma=0.1,
                    compute_jsd_bins=True,
                    n_jobs=1,
                    disable_progressbar=True
                )
                
                # Add tsfresh_ prefix
                for col in tsfresh_df.columns:
                    features[f'tsfresh_{col}'] = tsfresh_df.iloc[0][col]
                    
            elif TSFRESH_AVAILABLE:
                # Use basic TSFresh features
                series_data = {series_id: series}
                tstar_data = {series_id: tstar} if tstar is not None else None
                
                tsfresh_df = extract_tsfresh_features(
                    series_data=series_data,
                    tstar_data=tstar_data,
                    compute_pre_post=True,
                    n_jobs=1
                )
                
                # Add tsfresh_ prefix
                for col in tsfresh_df.columns:
                    features[f'tsfresh_{col}'] = tsfresh_df.iloc[0][col]
                    
            else:
                # Use simplified TSFresh features
                series_data = {series_id: series}
                tstar_data = {series_id: tstar} if tstar is not None else None
                
                tsfresh_features = self.tsfresh_extractor.extract_features(
                    series_data=series_data,
                    tstar_data=tstar_data,
                    compute_pre_post=True,
                    compute_pre_post_bins=False,
                    normalize=True,
                    weight_type='proportion',
                    sigma=0.1,
                    compute_jsd_bins=True,
                    n_jobs=1
                )
                
                # Add tsfresh_ prefix
                for col in tsfresh_features.columns:
                    features[f'tsfresh_{col}'] = tsfresh_features.iloc[0][col]
            
            # Extract thermodynamics features
            thermo_features = self.thermo_analyzer.analyze_time_series(series, tstar)
            
            # Filter out duplicates and add thermo_ prefix
            for feature_name, feature_value in thermo_features.items():
                if feature_name not in self.duplicate_features:
                    features[f'thermo_{feature_name}'] = feature_value
            
            features['series_id'] = series_id
            return features
            
        except Exception as e:
            print(f"Error processing series {series_id}: {e}")
            import traceback
            traceback.print_exc()
            return {'series_id': series_id}
    
    def extract_features_batch(self, time_series_list, tstar_list=None):
        """Extract features for multiple time series with parallel processing"""
        if tstar_list is None:
            tstar_list = [None] * len(time_series_list)
        
        # Prepare arguments for parallel processing
        args_list = [(i, series, tstar) for i, (series, tstar) in enumerate(zip(time_series_list, tstar_list))]
        
        if self.n_jobs == 1:
            # Sequential processing with progress bar
            features_list = []
            for args in tqdm(args_list, desc="Extracting integrated features (sequential)"):
                features_list.append(self.extract_single_series_features(args))
        else:
            # Parallel processing with progress bar
            n_processes = self.n_jobs if self.n_jobs > 0 else os.cpu_count()
            
            with Pool(processes=n_processes) as pool:
                features_list = list(tqdm(
                    pool.imap(self.extract_single_series_features, args_list),
                    total=len(args_list),
                    desc=f"Extracting integrated features (parallel, {n_processes} cores)"
                ))
        
        # Convert to DataFrame
        features_df = pd.DataFrame(features_list)
        features_df = features_df.set_index('series_id').fillna(0)
        
        return features_df

def test_integrated_pipeline():
    """Test the integrated pipeline on small dataset"""
    print("=" * 60)
    print("TESTING INTEGRATED TSFRESH + THERMODYNAMICS PIPELINE")
    print("=" * 60)
    
    # Generate test data
    np.random.seed(42)
    n_series = 20
    series_length = 500
    
    print(f"Generating {n_series} test time series...")
    test_series_list = []
    test_tstar_list = []
    
    for i in range(n_series):
        if i % 2 == 0:
            # Series with structural break
            tstar = np.random.randint(150, 350)
            pre_series = np.random.normal(0, 1, tstar)
            post_series = np.random.normal(2, 1.5, series_length - tstar)  # Different mean and variance
            series = np.concatenate([pre_series, post_series])
            test_tstar_list.append(tstar)
        else:
            # Normal series without break
            series = np.random.normal(0, 1, series_length)
            test_tstar_list.append(None)
        
        test_series_list.append(series)
    
    print(f"✓ Generated {len(test_series_list)} time series")
    print(f"✓ Series with breaks: {sum(1 for t in test_tstar_list if t is not None)}")
    
    # Test pipeline
    pipeline = IntegratedTSFreshThermoPipeline(n_jobs=-1, use_full_features=True)
    
    print(f"\nTesting feature extraction...")
    start_time = time.perf_counter()
    
    features_df = pipeline.extract_features_batch(test_series_list, test_tstar_list)
    
    extraction_time = time.perf_counter() - start_time
    
    print(f"\n✓ Feature extraction completed!")
    print(f"✓ Processing time: {extraction_time:.2f}s")
    print(f"✓ Processing rate: {len(test_series_list)/extraction_time:.1f} series/sec")
    print(f"✓ Features shape: {features_df.shape}")
    
    # Analyze feature types
    tsfresh_features = [col for col in features_df.columns if col.startswith('tsfresh_')]
    thermo_features = [col for col in features_df.columns if col.startswith('thermo_')]
    
    print(f"\n📊 FEATURE BREAKDOWN:")
    print(f"  - TSFresh features: {len(tsfresh_features)}")
    print(f"  - Thermodynamics features: {len(thermo_features)}")
    print(f"  - Total features: {features_df.shape[1]}")
    
    # Show sample features
    print(f"\n🔍 SAMPLE TSFRESH FEATURES:")
    for i, feat in enumerate(tsfresh_features[:5]):
        print(f"  {i+1}. {feat}")
    
    print(f"\n🔍 SAMPLE THERMODYNAMICS FEATURES:")
    for i, feat in enumerate(thermo_features[:5]):
        print(f"  {i+1}. {feat}")
    
    # Check for NaN values
    nan_count = features_df.isnull().sum().sum()
    print(f"\n⚠️  NaN values: {nan_count}")
    
    # Performance assessment
    print(f"\n🏆 PERFORMANCE ASSESSMENT:")
    if extraction_time < 60:
        print("🎉 EXCELLENT: Fast processing time")
    elif extraction_time < 120:
        print("✅ GOOD: Reasonable processing time")
    else:
        print("⚠️  SLOW: Consider optimization")
    
    if features_df.shape[1] > 100:
        print("✅ COMPREHENSIVE: Rich feature set extracted")
    else:
        print("⚠️  LIMITED: Feature set may be incomplete")
    
    return features_df

if __name__ == "__main__":
    # Run test
    test_features = test_integrated_pipeline()
    
    print("\n" + "=" * 60)
    print("INTEGRATED PIPELINE TEST COMPLETED!")
    print("=" * 60)
    print("✅ Pipeline working with parallel processing")
    print("✅ Progress tracking implemented")
    print("✅ Feature integration successful")
    print("✅ Ready for full dataset processing")