import numpy as np
import pandas as pd
from scipy import stats, signal
try:
    from scipy.integrate import trapz
except ImportError:
    from scipy.integrate import trapezoid as trapz
from joblib import Parallel, delayed
from tqdm import tqdm
from dataclasses import dataclass

@dataclass
class ThermodynamicMetrics:
    """Container for thermodynamic-inspired time series metrics with spike and half-split measures."""
    # Whole series metrics
    potential_energy_length_weighted: float
    potential_energy_curvature: float
    potential_energy_gradient: float
    potential_energy_harmonic: float
    kinetic_energy_variance_based: float
    kinetic_energy_velocity: float
    kinetic_energy_amplitude: float
    kinetic_energy_spectral: float
    kinetic_energy_spike_2sigma: float
    kinetic_energy_spike_3sigma: float
    spike_count_2sigma_pos: float
    spike_count_2sigma_neg: float
    spike_ratio_2sigma_pos: float
    spike_ratio_2sigma_neg: float
    spike_count_3sigma_pos: float
    spike_count_3sigma_neg: float
    spike_ratio_3sigma_pos: float
    spike_ratio_3sigma_neg: float
    total_energy: float
    energy_efficiency: float
    entropy_production: float
    free_energy: float
    action_integral: float
    phase_space_volume: float
    dissipation_rate: float
    ergodic_measure: float
    # Pre-tstar metrics
    pre_potential_energy_length_weighted: float
    pre_potential_energy_curvature: float
    pre_potential_energy_gradient: float
    pre_potential_energy_harmonic: float
    pre_kinetic_energy_variance_based: float
    pre_kinetic_energy_velocity: float
    pre_kinetic_energy_amplitude: float
    pre_kinetic_energy_spectral: float
    pre_kinetic_energy_spike_2sigma: float
    pre_kinetic_energy_spike_3sigma: float
    pre_spike_count_2sigma_pos: float
    pre_spike_count_2sigma_neg: float
    pre_spike_ratio_2sigma_pos: float
    pre_spike_ratio_2sigma_neg: float
    pre_spike_count_3sigma_pos: float
    pre_spike_count_3sigma_neg: float
    pre_spike_ratio_3sigma_pos: float
    pre_spike_ratio_3sigma_neg: float
    pre_spike_value_sum: float
    pre_total_energy: float
    pre_energy_efficiency: float
    pre_entropy_production: float
    pre_free_energy: float
    pre_action_integral: float
    pre_phase_space_volume: float
    pre_dissipation_rate: float
    pre_ergodic_measure: float
    # Post-tstar metrics
    post_potential_energy_length_weighted: float
    post_potential_energy_curvature: float
    post_potential_energy_gradient: float
    post_potential_energy_harmonic: float
    post_kinetic_energy_variance_based: float
    post_kinetic_energy_velocity: float
    post_kinetic_energy_amplitude: float
    post_kinetic_energy_spectral: float
    post_kinetic_energy_spike_2sigma: float
    post_kinetic_energy_spike_3sigma: float
    post_spike_count_2sigma_pos: float
    post_spike_count_2sigma_neg: float
    post_spike_ratio_2sigma_pos: float
    post_spike_ratio_2sigma_neg: float
    post_spike_count_3sigma_pos: float
    post_spike_count_3sigma_neg: float
    post_spike_ratio_3sigma_pos: float
    post_spike_ratio_3sigma_neg: float
    post_spike_value_sum: float
    post_total_energy: float
    post_energy_efficiency: float
    post_entropy_production: float
    post_free_energy: float
    post_action_integral: float
    post_phase_space_volume: float
    post_dissipation_rate: float
    post_ergodic_measure: float
    # Pre-half metrics
    pre_half_potential_energy_length_weighted: float
    pre_half_potential_energy_curvature: float
    pre_half_potential_energy_gradient: float
    pre_half_potential_energy_harmonic: float
    pre_half_kinetic_energy_variance_based: float
    pre_half_kinetic_energy_velocity: float
    pre_half_kinetic_energy_amplitude: float
    pre_half_kinetic_energy_spectral: float
    pre_half_kinetic_energy_spike_2sigma: float
    pre_half_kinetic_energy_spike_3sigma: float
    pre_half_spike_count_2sigma_pos: float
    pre_half_spike_count_2sigma_neg: float
    pre_half_spike_ratio_2sigma_pos: float
    pre_half_spike_ratio_2sigma_neg: float
    pre_half_spike_count_3sigma_pos: float
    pre_half_spike_count_3sigma_neg: float
    pre_half_spike_ratio_3sigma_pos: float
    pre_half_spike_ratio_3sigma_neg: float
    pre_half_spike_value_sum: float
    pre_half_total_energy: float
    pre_half_energy_efficiency: float
    pre_half_entropy_production: float
    pre_half_free_energy: float
    pre_half_action_integral: float
    pre_half_phase_space_volume: float
    pre_half_dissipation_rate: float
    pre_half_ergodic_measure: float
    # Post-half metrics
    post_half_potential_energy_length_weighted: float
    post_half_potential_energy_curvature: float
    post_half_potential_energy_gradient: float
    post_half_potential_energy_harmonic: float
    post_half_kinetic_energy_variance_based: float
    post_half_kinetic_energy_velocity: float
    post_half_kinetic_energy_amplitude: float
    post_half_kinetic_energy_spectral: float
    post_half_kinetic_energy_spike_2sigma: float
    post_half_kinetic_energy_spike_3sigma: float
    post_half_spike_count_2sigma_pos: float
    post_half_spike_count_2sigma_neg: float
    post_half_spike_ratio_2sigma_pos: float
    post_half_spike_ratio_2sigma_neg: float
    post_half_spike_count_3sigma_pos: float
    post_half_spike_count_3sigma_neg: float
    post_half_spike_ratio_3sigma_pos: float
    post_half_spike_ratio_3sigma_neg: float
    post_half_spike_value_sum: float
    post_half_total_energy: float
    post_half_energy_efficiency: float
    post_half_entropy_production: float
    post_half_free_energy: float
    post_half_action_integral: float
    post_half_phase_space_volume: float
    post_half_dissipation_rate: float
    post_half_ergodic_measure: float
    # Ratios
    spike_value_ratio_post_to_pre: float
    # Pre/Post to Pre-Half/Post-Half ratios (for all metrics)
    ratio_pre_potential_energy_length_weighted: float
    ratio_pre_potential_energy_curvature: float
    ratio_pre_potential_energy_gradient: float
    ratio_pre_potential_energy_harmonic: float
    ratio_pre_kinetic_energy_variance_based: float
    ratio_pre_kinetic_energy_velocity: float
    ratio_pre_kinetic_energy_amplitude: float
    ratio_pre_kinetic_energy_spectral: float
    ratio_pre_kinetic_energy_spike_2sigma: float
    ratio_pre_kinetic_energy_spike_3sigma: float
    ratio_pre_spike_count_2sigma_pos: float
    ratio_pre_spike_count_2sigma_neg: float
    ratio_pre_spike_ratio_2sigma_pos: float
    ratio_pre_spike_ratio_2sigma_neg: float
    ratio_pre_spike_count_3sigma_pos: float
    ratio_pre_spike_count_3sigma_neg: float
    ratio_pre_spike_ratio_3sigma_pos: float
    ratio_pre_spike_ratio_3sigma_neg: float
    ratio_pre_spike_value_sum: float
    ratio_pre_total_energy: float
    ratio_pre_energy_efficiency: float
    ratio_pre_entropy_production: float
    ratio_pre_free_energy: float
    ratio_pre_action_integral: float
    ratio_pre_phase_space_volume: float
    ratio_pre_dissipation_rate: float
    ratio_pre_ergodic_measure: float
    ratio_post_potential_energy_length_weighted: float
    ratio_post_potential_energy_curvature: float
    ratio_post_potential_energy_gradient: float
    ratio_post_potential_energy_harmonic: float
    ratio_post_kinetic_energy_variance_based: float
    ratio_post_kinetic_energy_velocity: float
    ratio_post_kinetic_energy_amplitude: float
    ratio_post_kinetic_energy_spectral: float
    ratio_post_kinetic_energy_spike_2sigma: float
    ratio_post_kinetic_energy_spike_3sigma: float
    ratio_post_spike_count_2sigma_pos: float
    ratio_post_spike_count_2sigma_neg: float
    ratio_post_spike_ratio_2sigma_pos: float
    ratio_post_spike_ratio_2sigma_neg: float
    ratio_post_spike_count_3sigma_pos: float
    ratio_post_spike_count_3sigma_neg: float
    ratio_post_spike_ratio_3sigma_pos: float
    ratio_post_spike_ratio_3sigma_neg: float
    ratio_post_spike_value_sum: float
    ratio_post_total_energy: float
    ratio_post_energy_efficiency: float
    ratio_post_entropy_production: float
    ratio_post_free_energy: float
    ratio_post_action_integral: float
    ratio_post_phase_space_volume: float
    ratio_post_dissipation_rate: float
    ratio_post_ergodic_measure: float
    tstar_position: float

class ThermodynamicTimeSeriesAnalyzer:
    """
    Advanced time series analyzer with spike measures and half-split metrics.
    """
    
    def __init__(self, temperature: float = 1.0, damping: float = 0.1):
        self.temperature = temperature
        self.damping = damping
    
    def calibrate_parameters(self, ts: np.ndarray) -> None:
        """
        Calibrate temperature and damping based on series statistics.
        """
        if len(ts) < 2:
            return
        self.temperature = np.nanvar(ts) + 1e-12
        velocity = np.diff(ts[~np.isnan(ts)])
        if len(velocity) > 1:
            autocorr = np.correlate(velocity, velocity, mode='full')[len(velocity)-1:]
            autocorr = autocorr / (autocorr[0] + 1e-12)
            self.damping = max(0, -np.log(np.abs(autocorr[1]) + 1e-12) if autocorr[1] != 0 else 0.1)
    
    def compute_potential_energy(self, ts: np.ndarray) -> dict:
        """
        Compute potential energy for all methods.
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        results = {
            'length_weighted': 0.0,
            'curvature': 0.0,
            'gradient': 0.0,
            'harmonic': 0.0
        }
        
        if N < 2:
            return results
        
        results['length_weighted'] = N * np.log(N + 1)
        d2x_dt2 = np.diff(ts[~np.isnan(ts)], n=2)
        results['curvature'] = np.sum(np.abs(d2x_dt2)) if len(d2x_dt2) > 0 else 0.0
        dx_dt = np.diff(ts[~np.isnan(ts)])
        results['gradient'] = np.sum(dx_dt**2) if len(dx_dt) > 0 else 0.0
        results['harmonic'] = np.nansum(ts**2) / (N + 1e-12)
        
        return results
    
    def compute_kinetic_energy(self, ts: np.ndarray) -> dict:
        """
        Compute kinetic energy for all methods, including spike energy.
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        results = {
            'variance_based': 0.0,
            'velocity': 0.0,
            'amplitude': 0.0,
            'spectral': 0.0,
            'spike_2sigma': 0.0,
            'spike_3sigma': 0.0
        }
        
        if N < 2:
            return results
        
        mean_ts = np.nanmean(ts)
        std_ts = np.nanstd(ts)
        if std_ts > 0:
            spikes_2sigma = ts[~np.isnan(ts)][np.abs(ts[~np.isnan(ts)] - mean_ts) > 2 * std_ts]
            spikes_3sigma = ts[~np.isnan(ts)][np.abs(ts[~np.isnan(ts)] - mean_ts) > 3 * std_ts]
            results['spike_2sigma'] = np.sum((spikes_2sigma - mean_ts)**2) / (N + 1e-12)
            results['spike_3sigma'] = np.sum((spikes_3sigma - mean_ts)**2) / (N + 1e-12)
        
        results['variance_based'] = np.nanvar(ts)
        velocity = np.diff(ts[~np.isnan(ts)])
        results['velocity'] = np.nanmean(velocity**2) if len(velocity) > 0 else 0.0
        results['amplitude'] = np.ptp(ts[~np.isnan(ts)])**2 / 4
        if N < 4:
            results['spectral'] = np.nanvar(ts)
        else:
            freqs, psd = signal.welch(ts[~np.isnan(ts)], nperseg=min(N//8, 128))
            results['spectral'] = trapz(freqs**2 * psd, freqs)
        
        return results
    
    def compute_spike_metrics(self, ts: np.ndarray) -> dict:
        """
        Compute spike counts, ratios, and value sums for 2σ and 3σ thresholds (positive and negative).
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        if N < 2:
            return {
                'spike_count_2sigma_pos': 0.0,
                'spike_count_2sigma_neg': 0.0,
                'spike_ratio_2sigma_pos': 0.0,
                'spike_ratio_2sigma_neg': 0.0,
                'spike_count_3sigma_pos': 0.0,
                'spike_count_3sigma_neg': 0.0,
                'spike_ratio_3sigma_pos': 0.0,
                'spike_ratio_3sigma_neg': 0.0,
                'spike_value_sum': 0.0
            }
        
        mean_ts = np.nanmean(ts)
        std_ts = np.nanstd(ts)
        if std_ts == 0:
            return {
                'spike_count_2sigma_pos': 0.0,
                'spike_count_2sigma_neg': 0.0,
                'spike_ratio_2sigma_pos': 0.0,
                'spike_ratio_2sigma_neg': 0.0,
                'spike_count_3sigma_pos': 0.0,
                'spike_count_3sigma_neg': 0.0,
                'spike_ratio_3sigma_pos': 0.0,
                'spike_ratio_3sigma_neg': 0.0,
                'spike_value_sum': 0.0
            }
        
        ts_clean = ts[~np.isnan(ts)]
        dev = ts_clean - mean_ts
        spikes_2sigma_pos = np.sum(dev > 2 * std_ts)
        spikes_2sigma_neg = np.sum(dev < -2 * std_ts)
        spikes_3sigma_pos = np.sum(dev > 3 * std_ts)
        spikes_3sigma_neg = np.sum(dev < -3 * std_ts)
        spike_values = np.abs(dev[np.abs(dev) > 2 * std_ts])
        
        return {
            'spike_count_2sigma_pos': spikes_2sigma_pos,
            'spike_count_2sigma_neg': spikes_2sigma_neg,
            'spike_ratio_2sigma_pos': spikes_2sigma_pos / N,
            'spike_ratio_2sigma_neg': spikes_2sigma_neg / N,
            'spike_count_3sigma_pos': spikes_3sigma_pos,
            'spike_count_3sigma_neg': spikes_3sigma_neg,
            'spike_ratio_3sigma_pos': spikes_3sigma_pos / N,
            'spike_ratio_3sigma_neg': spikes_3sigma_neg / N,
            'spike_value_sum': np.sum(spike_values)
        }
    
    def compute_entropy_production(self, ts: np.ndarray, cached_autocorr: np.ndarray = None) -> float:
        """
        Compute entropy production rate.
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        if N < 2:
            return 0.0
        
        n_bins = max(10, int(np.sqrt(N)))
        hist, bin_edges = np.histogram(ts[~np.isnan(ts)], bins=n_bins, density=True)
        p_i = hist[hist > 0] * (bin_edges[1] - bin_edges[0])
        entropy = -np.sum(p_i * np.log(p_i + 1e-12))
        
        velocity = np.diff(ts[~np.isnan(ts)])
        if len(velocity) > 1:
            if cached_autocorr is None:
                autocorr = np.correlate(velocity, velocity, mode='full')[len(velocity)-1:]
                autocorr = autocorr / (autocorr[0] + 1e-12)
            else:
                autocorr = cached_autocorr
            try:
                decay_rate = -np.log(np.abs(autocorr[1]) + 1e-12)
            except:
                decay_rate = 1.0
            entropy += self.damping * decay_rate
        
        return entropy
    
    def compute_free_energy(self, potential: float, kinetic: float, entropy: float) -> float:
        """
        Compute Helmholtz free energy: F = U + K - T*S
        """
        return potential + kinetic - self.temperature * entropy
    
    def compute_action_integral(self, ts: np.ndarray, dt: float = 1.0) -> float:
        """
        Compute action integral: S = ∫(K - U)dt
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        if N < 2:
            return 0.0
        
        velocity = np.diff(ts[~np.isnan(ts)]) / dt
        kinetic_local = 0.5 * velocity**2
        x_mid = (ts[~np.isnan(ts)][1:] + ts[~np.isnan(ts)][:-1]) / 2
        potential_local = 0.5 * x_mid**2
        lagrangian = kinetic_local - potential_local
        return trapz(lagrangian, dx=dt)
    
    def compute_phase_space_volume(self, ts: np.ndarray) -> float:
        """
        Estimate phase space volume using bounding box approximation for large series.
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        if N < 2:
            return 1.0
        
        x = ts[~np.isnan(ts)][:-1]
        v = np.diff(ts[~np.isnan(ts)])
        
        if N > 1000:
            return np.ptp(x) * np.ptp(v) * np.pi
        
        try:
            from scipy.spatial import ConvexHull
            points = np.column_stack([x, v])
            hull = ConvexHull(points)
            return hull.volume
        except:
            return np.nanstd(x) * np.nanstd(v) * np.pi
    
    def compute_dissipation_rate(self, ts: np.ndarray, dt: float = 1.0, cached_autocorr: np.ndarray = None) -> float:
        """
        Compute energy dissipation rate based on velocity correlations.
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        if N < 3:
            return 0.0
        
        velocity = np.diff(ts[~np.isnan(ts)]) / dt
        
        if len(velocity) < 2:
            return np.nanvar(velocity)
        
        if cached_autocorr is None:
            autocorr = np.correlate(velocity, velocity, mode='full')[len(velocity)-1:]
            autocorr = autocorr / (autocorr[0] + 1e-12)
        else:
            autocorr = cached_autocorr
        
        dissipation = -(autocorr[1] - autocorr[0]) / dt if len(autocorr) > 1 else self.damping * np.nanvar(velocity)
        return max(0, dissipation)
    
    def compute_ergodic_measure(self, ts: np.ndarray, n_windows: int = 10) -> float:
        """
        Compute ergodicity measure by comparing time and ensemble averages.
        """
        ts = np.array(ts)
        N = len(ts[~np.isnan(ts)])
        
        if N < n_windows:
            return 0.0
        
        window_size = N // n_windows
        time_avg = np.nanmean(ts)
        
        ensemble_avgs = [np.nanmean(ts[i:i+window_size]) for i in range(0, N-window_size+1, window_size)]
        ensemble_avg = np.nanmean(ensemble_avgs)
        ensemble_std = np.nanstd(ensemble_avgs)
        
        return np.abs(time_avg - ensemble_avg) / (ensemble_std + 1e-12) if ensemble_std > 0 else 0.0
    
    def compute_metrics(self, ts: np.ndarray, dt: float = 1.0, prefix: str = '') -> dict:
        """
        Compute all metrics for a given time series segment with optional prefix.
        """
        velocity = np.diff(ts[~np.isnan(ts)])
        autocorr = None
        if len(velocity) > 1:
            autocorr = np.correlate(velocity, velocity, mode='full')[len(velocity)-1:]
            autocorr = autocorr / (autocorr[0] + 1e-12)
        
        potential = self.compute_potential_energy(ts)
        kinetic = self.compute_kinetic_energy(ts)
        spike_metrics = self.compute_spike_metrics(ts)
        entropy = self.compute_entropy_production(ts, cached_autocorr=autocorr)
        total_energy = potential['harmonic'] + kinetic['variance_based']
        energy_efficiency = kinetic['variance_based'] / (total_energy + 1e-12)
        free_energy = self.compute_free_energy(potential['harmonic'], kinetic['variance_based'], entropy)
        action_integral = self.compute_action_integral(ts, dt)
        phase_space_volume = self.compute_phase_space_volume(ts)
        dissipation_rate = self.compute_dissipation_rate(ts, dt, cached_autocorr=autocorr)
        ergodic_measure = self.compute_ergodic_measure(ts)
        
        return {
            f'{prefix}potential_energy_length_weighted': potential['length_weighted'],
            f'{prefix}potential_energy_curvature': potential['curvature'],
            f'{prefix}potential_energy_gradient': potential['gradient'],
            f'{prefix}potential_energy_harmonic': potential['harmonic'],
            f'{prefix}kinetic_energy_variance_based': kinetic['variance_based'],
            f'{prefix}kinetic_energy_velocity': kinetic['velocity'],
            f'{prefix}kinetic_energy_amplitude': kinetic['amplitude'],
            f'{prefix}kinetic_energy_spectral': kinetic['spectral'],
            f'{prefix}kinetic_energy_spike_2sigma': kinetic['spike_2sigma'],
            f'{prefix}kinetic_energy_spike_3sigma': kinetic['spike_3sigma'],
            f'{prefix}spike_count_2sigma_pos': spike_metrics['spike_count_2sigma_pos'],
            f'{prefix}spike_count_2sigma_neg': spike_metrics['spike_count_2sigma_neg'],
            f'{prefix}spike_ratio_2sigma_pos': spike_metrics['spike_ratio_2sigma_pos'],
            f'{prefix}spike_ratio_2sigma_neg': spike_metrics['spike_ratio_2sigma_neg'],
            f'{prefix}spike_count_3sigma_pos': spike_metrics['spike_count_3sigma_pos'],
            f'{prefix}spike_count_3sigma_neg': spike_metrics['spike_count_3sigma_neg'],
            f'{prefix}spike_ratio_3sigma_pos': spike_metrics['spike_ratio_3sigma_pos'],
            f'{prefix}spike_ratio_3sigma_neg': spike_metrics['spike_ratio_3sigma_neg'],
            f'{prefix}spike_value_sum': spike_metrics['spike_value_sum'],
            f'{prefix}total_energy': total_energy,
            f'{prefix}energy_efficiency': energy_efficiency,
            f'{prefix}entropy_production': entropy,
            f'{prefix}free_energy': free_energy,
            f'{prefix}action_integral': action_integral,
            f'{prefix}phase_space_volume': phase_space_volume,
            f'{prefix}dissipation_rate': dissipation_rate,
            f'{prefix}ergodic_measure': ergodic_measure
        }
    
    def compute_ratios(self, pre_metrics: dict, post_metrics: dict, pre_half_metrics: dict, post_half_metrics: dict) -> dict:
        """
        Compute ratios of pre/post to pre-half/post-half metrics.
        """
        ratios = {}
        for key in pre_metrics:
            pre_key = key.replace('pre_', '')
            pre_half_key = f'pre_half_{pre_key}'
            if pre_metrics[key] != 0 and not np.isnan(pre_metrics[key]) and not np.isnan(pre_half_metrics[pre_half_key]):
                ratios[f'ratio_{key}'] = pre_metrics[key] / (pre_half_metrics[pre_half_key] + 1e-12)
            else:
                ratios[f'ratio_{key}'] = np.nan
        
        for key in post_metrics:
            post_key = key.replace('post_', '')
            post_half_key = f'post_half_{post_key}'
            if post_metrics[key] != 0 and not np.isnan(post_metrics[key]) and not np.isnan(post_half_metrics[post_half_key]):
                ratios[f'ratio_{key}'] = post_metrics[key] / (post_half_metrics[post_half_key] + 1e-12)
            else:
                ratios[f'ratio_{key}'] = np.nan
        
        # Spike value ratio
        pre_spike_sum = pre_metrics.get('pre_spike_value_sum', 0.0)
        post_spike_sum = post_metrics.get('post_spike_value_sum', 0.0)
        ratios['spike_value_ratio_post_to_pre'] = post_spike_sum / (pre_spike_sum + 1e-12) if pre_spike_sum != 0 else np.nan
        
        return ratios
    
    def analyze_time_series(self, ts: np.ndarray, tstar: int = None, dt: float = 1.0) -> dict:
        """
        Comprehensive thermodynamic analysis with spike and half-split metrics.
        """
        ts = np.array(ts)
        N = len(ts)
        
        self.calibrate_parameters(ts)
        
        # Whole series metrics
        whole_metrics = self.compute_metrics(ts, dt, prefix='')
        
        # Pre- and post-tstar metrics
        pre_metrics = {f'pre_{k}': np.nan for k in whole_metrics if not k.startswith('pre_') and not k.startswith('post_')}
        post_metrics = {f'post_{k}': np.nan for k in whole_metrics if not k.startswith('pre_') and not k.startswith('post_')}
        if tstar is not None and 0 <= tstar < N and len(ts[:tstar][~np.isnan(ts[:tstar])]) >= 2:
            pre_metrics = self.compute_metrics(ts[:tstar], dt, prefix='pre_')
        if tstar is not None and 0 <= tstar < N and len(ts[tstar:][~np.isnan(ts[tstar:])]) >= 2:
            post_metrics = self.compute_metrics(ts[tstar:], dt, prefix='post_')
        
        # Pre-half and post-half metrics
        half_point = N // 2
        pre_half_metrics = {f'pre_half_{k}': np.nan for k in whole_metrics if not k.startswith('pre_') and not k.startswith('post_')}
        post_half_metrics = {f'post_half_{k}': np.nan for k in whole_metrics if not k.startswith('pre_') and not k.startswith('post_')}
        if len(ts[:half_point][~np.isnan(ts[:half_point])]) >= 2:
            pre_half_metrics = self.compute_metrics(ts[:half_point], dt, prefix='pre_half_')
        if len(ts[half_point:][~np.isnan(ts[half_point:])]) >= 2:
            post_half_metrics = self.compute_metrics(ts[half_point:], dt, prefix='post_half_')
        
        # Ratios
        ratios = self.compute_ratios(pre_metrics, post_metrics, pre_half_metrics, post_half_metrics)
        
        return {
            **whole_metrics,
            **pre_metrics,
            **post_metrics,
            **pre_half_metrics,
            **post_half_metrics,
            **ratios,
            'tstar_position': tstar / N if tstar is not None and N > 0 else np.nan
        }
    
    def batch_analyze(self, series_list: list, tstar_list: list = None, dt: float = 1.0, n_jobs: int = -1) -> list:
        """
        Analyze multiple time series in parallel with a progress bar.
        """
        if tstar_list is None:
            tstar_list = [None] * len(series_list)
        if len(tstar_list) != len(series_list):
            raise ValueError("tstar_list must have same length as series_list")
        
        def process_series(ts, tstar):
            return self.analyze_time_series(ts, tstar, dt)
        
        return Parallel(n_jobs=n_jobs)(
            delayed(process_series)(ts, tstar) for ts, tstar in tqdm(zip(series_list, tstar_list), total=len(series_list), desc="Analyzing time series")
        )
 
# if __name__ == "__main__":
#     np.random.seed(42)
#     df = demonstrate_framework(500)
"""Demonstrate the thermodynamic time series framework."""
print("=== Thermodynamic Time Series Analysis Framework ===\n")

# Initialize analyzer with knee detection and audio features
analyzer = ThermodynamicTimeSeriesAnalyzer(temperature=.9, damping=0.1)
# n_samples=100
# Generate test signals
# signals = generate_test_signals(n_samples) 