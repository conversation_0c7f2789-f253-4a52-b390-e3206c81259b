#!/usr/bin/env python3
"""
Analyze Time Series Input Standardization
Comprehensive analysis of how time series in the dataset deviate from mean
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from collections import defaultdict
from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.metrics import silhouette_score
from sklearn.cross_decomposition import CCA
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from scipy.spatial.distance import pdist, squareform
from scipy.cluster.hierarchy import dendrogram, linkage
import seaborn as sns
import warnings

warnings.filterwarnings('ignore')

def load_time_series_data_with_labels():
    """Load and extract time series with labels from X_train.parquet and y_train.parquet"""
    try:
        print("📊 Loading X_train.parquet and y_train.parquet...")
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        print(f"   ✅ Loaded X data shape: {X_data.shape}")
        print(f"   ✅ Loaded y data shape: {y_data.shape}")
        
        # Extract individual time series with labels
        time_series_list = []
        series_ids = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            # MultiIndex structure ['id', 'time']
            print("   📋 MultiIndex structure detected")
            grouped = X_data.groupby(level='id')
            
            for id_, group in grouped:
                # Sort by time and extract values
                series_data = group.sort_index(level='time').values.flatten()
                time_series_list.append(series_data)
                series_ids.append(id_)
                
                # Get corresponding label
                if hasattr(y_data, 'loc') and id_ in y_data.index:
                    label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                else:
                    label = y_data.iloc[id_] if id_ < len(y_data) else 0
                labels.append(label)
                
        else:
            # Regular DataFrame - each row is a time series
            print("   📋 Regular DataFrame structure detected")
            for idx, row in X_data.iterrows():
                series_data = row.values
                time_series_list.append(series_data)
                series_ids.append(idx)
                
                # Get corresponding label
                label = y_data.iloc[idx] if idx < len(y_data) else 0
                labels.append(label)
        
        print(f"   ✅ Extracted {len(time_series_list)} time series with labels")
        print(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        return time_series_list, series_ids, labels
        
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        return None, None, None

def split_series_pre_post(time_series_list, labels, split_ratio=0.5):
    """Split each time series into pre and post segments based on labels"""
    print(f"\n🔄 SPLITTING TIME SERIES INTO PRE/POST SEGMENTS")
    print("=" * 50)
    
    pre_segments = []
    post_segments = []
    segment_labels = []
    segment_types = []
    original_series_ids = []
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        series = np.array(series)
        
        # Determine split point
        if label == 1:  # Structural break exists
            # For structural break series, split at the break point (assume middle for now)
            split_point = len(series) // 2
        else:  # No structural break
            # For normal series, split at specified ratio
            split_point = int(len(series) * split_ratio)
        
        # Create pre and post segments
        pre_segment = series[:split_point]
        post_segment = series[split_point:]
        
        if len(pre_segment) > 10 and len(post_segment) > 10:  # Minimum length requirement
            pre_segments.append(pre_segment)
            post_segments.append(post_segment)
            
            # Determine segment type based on original label
            if label == 0:
                segment_type = "normal_normal"  # Normal + Normal
            else:
                segment_type = "normal_break"   # Normal + Structural Break
            
            segment_labels.append(label)
            segment_types.append(segment_type)
            original_series_ids.append(i)
    
    print(f"   ✅ Created {len(pre_segments)} pre/post segment pairs")
    print(f"   📊 Segment type distribution:")
    type_counts = pd.Series(segment_types).value_counts()
    for seg_type, count in type_counts.items():
        print(f"      {seg_type}: {count} ({count/len(segment_types)*100:.1f}%)")
    
    # Calculate minimal lengths
    pre_lengths = [len(seg) for seg in pre_segments]
    post_lengths = [len(seg) for seg in post_segments]
    
    min_pre_length = min(pre_lengths)
    min_post_length = min(post_lengths)
    
    print(f"   📏 Segment length statistics:")
    print(f"      Pre-segments: min={min_pre_length}, max={max(pre_lengths)}, mean={np.mean(pre_lengths):.1f}")
    print(f"      Post-segments: min={min_post_length}, max={max(post_lengths)}, mean={np.mean(post_lengths):.1f}")
    
    return pre_segments, post_segments, segment_labels, segment_types, original_series_ids, min_pre_length, min_post_length

def compute_deep_statistics(series):
    """Compute comprehensive statistical features for a time series"""
    series = np.array(series)
    series_clean = series[~np.isnan(series)]
    
    if len(series_clean) < 3:
        return {}
    
    # Basic moments
    mean_val = np.mean(series_clean)
    std_val = np.std(series_clean, ddof=1)
    var_val = np.var(series_clean, ddof=1)
    skew_val = stats.skew(series_clean)
    kurt_val = stats.kurtosis(series_clean)
    
    # Quantiles and percentiles
    q10, q25, q50, q75, q90 = np.percentile(series_clean, [10, 25, 50, 75, 90])
    iqr = q75 - q25
    
    # Robust statistics
    mad = np.median(np.abs(series_clean - np.median(series_clean)))  # Median Absolute Deviation
    trimmed_mean = stats.trim_mean(series_clean, 0.1)  # 10% trimmed mean
    
    # Distribution shape
    cv = std_val / abs(mean_val) if mean_val != 0 else np.inf
    range_val = np.max(series_clean) - np.min(series_clean)
    
    # Autocorrelation features
    if len(series_clean) > 10:
        # Lag-1 autocorrelation
        autocorr_1 = np.corrcoef(series_clean[:-1], series_clean[1:])[0, 1] if len(series_clean) > 1 else 0
        # Ljung-Box test statistic (simplified)
        ljung_box = np.sum([(autocorr_1**2) / (len(series_clean) - k) for k in range(1, min(11, len(series_clean)))])
    else:
        autocorr_1 = 0
        ljung_box = 0
    
    # Trend analysis
    if len(series_clean) > 5:
        x = np.arange(len(series_clean))
        slope, intercept, r_value, p_value, std_err = stats.linregress(x, series_clean)
        trend_strength = abs(r_value)
        trend_direction = np.sign(slope)
    else:
        trend_strength = 0
        trend_direction = 0
        slope = 0
    
    # Stationarity indicators
    # Simple variance ratio test
    if len(series_clean) > 20:
        mid = len(series_clean) // 2
        var_first_half = np.var(series_clean[:mid], ddof=1)
        var_second_half = np.var(series_clean[mid:], ddof=1)
        variance_ratio = var_second_half / var_first_half if var_first_half > 0 else 1
    else:
        variance_ratio = 1
    
    # Outlier detection
    z_scores = np.abs((series_clean - mean_val) / std_val) if std_val > 0 else np.zeros_like(series_clean)
    n_outliers_zscore = np.sum(z_scores > 3)
    
    # IQR outliers
    lower_bound = q25 - 1.5 * iqr
    upper_bound = q75 + 1.5 * iqr
    n_outliers_iqr = np.sum((series_clean < lower_bound) | (series_clean > upper_bound))
    
    # Normality tests
    if len(series_clean) > 8:
        shapiro_stat, shapiro_p = stats.shapiro(series_clean[:5000])  # Limit for computational efficiency
        jarque_bera_stat, jarque_bera_p = stats.jarque_bera(series_clean)
    else:
        shapiro_stat = shapiro_p = jarque_bera_stat = jarque_bera_p = np.nan
    
    # Entropy (approximate)
    if len(series_clean) > 10:
        hist, _ = np.histogram(series_clean, bins=min(50, len(series_clean)//10), density=True)
        hist = hist[hist > 0]  # Remove zero bins
        entropy_val = -np.sum(hist * np.log(hist)) if len(hist) > 0 else 0
    else:
        entropy_val = 0
    
    # Peaks and valleys
    if len(series_clean) > 5:
        from scipy.signal import find_peaks
        peaks, _ = find_peaks(series_clean)
        valleys, _ = find_peaks(-series_clean)
        n_peaks = len(peaks)
        n_valleys = len(valleys)
        peak_density = n_peaks / len(series_clean)
    else:
        n_peaks = n_valleys = peak_density = 0
    
    return {
        'mean': mean_val,
        'std': std_val,
        'variance': var_val,
        'skewness': skew_val,
        'kurtosis': kurt_val,
        'cv': cv,
        'range': range_val,
        'q10': q10,
        'q25': q25,
        'median': q50,
        'q75': q75,
        'q90': q90,
        'iqr': iqr,
        'mad': mad,
        'trimmed_mean': trimmed_mean,
        'autocorr_1': autocorr_1,
        'ljung_box': ljung_box,
        'trend_strength': trend_strength,
        'trend_direction': trend_direction,
        'slope': slope,
        'variance_ratio': variance_ratio,
        'n_outliers_zscore': n_outliers_zscore,
        'n_outliers_iqr': n_outliers_iqr,
        'outlier_rate_zscore': n_outliers_zscore / len(series_clean),
        'outlier_rate_iqr': n_outliers_iqr / len(series_clean),
        'shapiro_stat': shapiro_stat,
        'shapiro_p': shapiro_p,
        'jarque_bera_stat': jarque_bera_stat,
        'jarque_bera_p': jarque_bera_p,
        'entropy': entropy_val,
        'n_peaks': n_peaks,
        'n_valleys': n_valleys,
        'peak_density': peak_density,
        'length': len(series_clean)
    }

def analyze_pre_post_statistics(pre_segments, post_segments, segment_labels, segment_types, original_series_ids, min_pre_length=None, min_post_length=None):
    """Analyze comprehensive statistics of pre and post segments"""
    print("\n🔍 ANALYZING PRE/POST SEGMENT STATISTICS")
    print("=" * 50)
    
    all_stats = []
    
    for i, (pre_seg, post_seg, label, seg_type, orig_id) in enumerate(zip(
        pre_segments, post_segments, segment_labels, segment_types, original_series_ids)):
        
        if i % 500 == 0:
            print(f"   Processing segment pair {i+1}/{len(pre_segments)}...")
        
        # Compute statistics for pre segment (full length)
        pre_stats = compute_deep_statistics(pre_seg)
        pre_stats = {f'pre_{k}': v for k, v in pre_stats.items()}
        
        # Compute statistics for post segment (full length)
        post_stats = compute_deep_statistics(post_seg)
        post_stats = {f'post_{k}': v for k, v in post_stats.items()}
        
        # Compute comparative features (full length)
        comparative_stats = compute_comparative_features(pre_seg, post_seg)
        
        # If minimal lengths are provided, also compute bounded features
        bounded_stats = {}
        if min_pre_length is not None and min_post_length is not None:
            # Truncate segments to minimal lengths
            pre_bounded = pre_seg[:min_pre_length] if len(pre_seg) >= min_pre_length else pre_seg
            post_bounded = post_seg[:min_post_length] if len(post_seg) >= min_post_length else post_seg
            
            # Compute bounded statistics
            pre_bounded_stats = compute_deep_statistics(pre_bounded)
            pre_bounded_stats = {f'pre_bounded_{k}': v for k, v in pre_bounded_stats.items()}
            
            post_bounded_stats = compute_deep_statistics(post_bounded)
            post_bounded_stats = {f'post_bounded_{k}': v for k, v in post_bounded_stats.items()}
            
            comparative_bounded_stats = compute_comparative_features(pre_bounded, post_bounded)
            comparative_bounded_stats = {f'bounded_{k}': v for k, v in comparative_bounded_stats.items()}
            
            bounded_stats = {**pre_bounded_stats, **post_bounded_stats, **comparative_bounded_stats}
        
        # Combine all statistics
        combined_stats = {
            'segment_id': i,
            'original_series_id': orig_id,
            'segment_type': seg_type,
            'label': label,
            **pre_stats,
            **post_stats,
            **comparative_stats,
            **bounded_stats
        }
        
        all_stats.append(combined_stats)
    
    stats_df = pd.DataFrame(all_stats)
    print(f"   ✅ Computed {len(stats_df.columns)-4} statistical features for {len(stats_df)} segment pairs")
    
    if min_pre_length is not None and min_post_length is not None:
        print(f"   📏 Included bounded features with min_pre={min_pre_length}, min_post={min_post_length}")
    
    return stats_df

def compute_comparative_features(pre_segment, post_segment):
    """Compute comparative features between pre and post segments"""
    pre_clean = np.array(pre_segment)[~np.isnan(pre_segment)]
    post_clean = np.array(post_segment)[~np.isnan(post_segment)]
    
    if len(pre_clean) < 3 or len(post_clean) < 3:
        return {}
    
    # Basic comparative statistics
    mean_diff = np.mean(post_clean) - np.mean(pre_clean)
    std_diff = np.std(post_clean, ddof=1) - np.std(pre_clean, ddof=1)
    var_ratio = np.var(post_clean, ddof=1) / np.var(pre_clean, ddof=1) if np.var(pre_clean, ddof=1) > 0 else np.inf
    mean_ratio = np.mean(post_clean) / np.mean(pre_clean) if np.mean(pre_clean) != 0 else np.inf
    
    # Distribution shape changes
    skew_diff = stats.skew(post_clean) - stats.skew(pre_clean)
    kurt_diff = stats.kurtosis(post_clean) - stats.kurtosis(pre_clean)
    
    # Range and quantile changes
    range_diff = (np.max(post_clean) - np.min(post_clean)) - (np.max(pre_clean) - np.min(pre_clean))
    iqr_pre = np.percentile(pre_clean, 75) - np.percentile(pre_clean, 25)
    iqr_post = np.percentile(post_clean, 75) - np.percentile(post_clean, 25)
    iqr_ratio = iqr_post / iqr_pre if iqr_pre > 0 else np.inf
    
    # Statistical tests
    try:
        # T-test for mean difference
        t_stat, t_pvalue = stats.ttest_ind(pre_clean, post_clean)
        
        # Mann-Whitney U test for distribution difference
        u_stat, u_pvalue = stats.mannwhitneyu(pre_clean, post_clean, alternative='two-sided')
        
        # Kolmogorov-Smirnov test for distribution difference
        ks_stat, ks_pvalue = stats.ks_2samp(pre_clean, post_clean)
        
        # Levene's test for variance equality
        levene_stat, levene_pvalue = stats.levene(pre_clean, post_clean)
        
    except Exception:
        t_stat = t_pvalue = u_stat = u_pvalue = np.nan
        ks_stat = ks_pvalue = levene_stat = levene_pvalue = np.nan
    
    # Effect sizes
    pooled_std = np.sqrt(((len(pre_clean) - 1) * np.var(pre_clean, ddof=1) + 
                         (len(post_clean) - 1) * np.var(post_clean, ddof=1)) / 
                        (len(pre_clean) + len(post_clean) - 2))
    cohens_d = mean_diff / pooled_std if pooled_std > 0 else np.nan
    
    # Autocorrelation change
    if len(pre_clean) > 10 and len(post_clean) > 10:
        pre_autocorr = np.corrcoef(pre_clean[:-1], pre_clean[1:])[0, 1] if len(pre_clean) > 1 else 0
        post_autocorr = np.corrcoef(post_clean[:-1], post_clean[1:])[0, 1] if len(post_clean) > 1 else 0
        autocorr_diff = post_autocorr - pre_autocorr
    else:
        autocorr_diff = np.nan
    
    # Trend change
    if len(pre_clean) > 5 and len(post_clean) > 5:
        x_pre = np.arange(len(pre_clean))
        x_post = np.arange(len(post_clean))
        
        slope_pre, _, r_pre, _, _ = stats.linregress(x_pre, pre_clean)
        slope_post, _, r_post, _, _ = stats.linregress(x_post, post_clean)
        
        slope_diff = slope_post - slope_pre
        trend_strength_diff = abs(r_post) - abs(r_pre)
    else:
        slope_diff = trend_strength_diff = np.nan
    
    return {
        'mean_diff': mean_diff,
        'std_diff': std_diff,
        'var_ratio': var_ratio,
        'mean_ratio': mean_ratio,
        'skew_diff': skew_diff,
        'kurt_diff': kurt_diff,
        'range_diff': range_diff,
        'iqr_ratio': iqr_ratio,
        't_stat': t_stat,
        't_pvalue': t_pvalue,
        'u_stat': u_stat,
        'u_pvalue': u_pvalue,
        'ks_stat': ks_stat,
        'ks_pvalue': ks_pvalue,
        'levene_stat': levene_stat,
        'levene_pvalue': levene_pvalue,
        'cohens_d': cohens_d,
        'autocorr_diff': autocorr_diff,
        'slope_diff': slope_diff,
        'trend_strength_diff': trend_strength_diff
    }

def prepare_clustering_features(stats_df):
    """Prepare features for clustering analysis"""
    print("\n🔧 PREPARING CLUSTERING FEATURES")
    print("=" * 50)
    
    # Select relevant features for clustering
    clustering_features = [
        'mean', 'std', 'cv', 'skewness', 'kurtosis', 'range',
        'iqr', 'mad', 'autocorr_1', 'trend_strength', 'variance_ratio',
        'outlier_rate_iqr', 'outlier_rate_zscore', 'entropy', 'peak_density',
        'shapiro_p', 'jarque_bera_p'
    ]
    
    # Filter available features
    available_features = [f for f in clustering_features if f in stats_df.columns]
    print(f"   📊 Using {len(available_features)} features for clustering")
    
    # Extract feature matrix
    X = stats_df[available_features].copy()
    
    # Handle infinite values
    X = X.replace([np.inf, -np.inf], np.nan)
    
    # Fill NaN values with median
    X = X.fillna(X.median())
    
    print(f"   ✅ Feature matrix shape: {X.shape}")
    print(f"   📋 Features: {available_features}")
    
    return X, available_features

def perform_clustering_analysis(X, stats_df, max_clusters=8):
    """Perform comprehensive clustering analysis"""
    print("\n🎯 CLUSTERING ANALYSIS")
    print("=" * 50)
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    print(f"   🔧 Standardized features for clustering")
    
    # 1. K-Means clustering with different k values
    print(f"\n📊 K-MEANS CLUSTERING ANALYSIS:")
    kmeans_results = {}
    silhouette_scores = []
    
    for k in range(2, max_clusters + 1):
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(X_scaled)
        silhouette = silhouette_score(X_scaled, labels)
        
        kmeans_results[k] = {
            'labels': labels,
            'silhouette': silhouette,
            'inertia': kmeans.inertia_
        }
        silhouette_scores.append(silhouette)
        
        print(f"   k={k}: Silhouette={silhouette:.3f}, Inertia={kmeans.inertia_:.1f}")
    
    # Find optimal k
    optimal_k = np.argmax(silhouette_scores) + 2
    print(f"   ✅ Optimal k: {optimal_k} (Silhouette: {max(silhouette_scores):.3f})")
    
    # 2. DBSCAN clustering
    print(f"\n🔍 DBSCAN CLUSTERING ANALYSIS:")
    eps_values = [0.3, 0.5, 0.7, 1.0, 1.5]
    dbscan_results = {}
    
    for eps in eps_values:
        dbscan = DBSCAN(eps=eps, min_samples=5)
        labels = dbscan.fit_predict(X_scaled)
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        n_noise = list(labels).count(-1)
        
        if n_clusters > 1:
            silhouette = silhouette_score(X_scaled[labels != -1], labels[labels != -1])
        else:
            silhouette = -1
        
        dbscan_results[eps] = {
            'labels': labels,
            'n_clusters': n_clusters,
            'n_noise': n_noise,
            'silhouette': silhouette
        }
        
        print(f"   eps={eps}: Clusters={n_clusters}, Noise={n_noise}, Silhouette={silhouette:.3f}")
    
    # 3. Hierarchical clustering
    print(f"\n🌳 HIERARCHICAL CLUSTERING ANALYSIS:")
    hierarchical = AgglomerativeClustering(n_clusters=optimal_k)
    hier_labels = hierarchical.fit_predict(X_scaled)
    hier_silhouette = silhouette_score(X_scaled, hier_labels)
    
    print(f"   Clusters={optimal_k}, Silhouette={hier_silhouette:.3f}")
    
    return {
        'kmeans_results': kmeans_results,
        'dbscan_results': dbscan_results,
        'hierarchical_labels': hier_labels,
        'optimal_k': optimal_k,
        'X_scaled': X_scaled,
        'scaler': scaler
    }

def analyze_cluster_characteristics(stats_df, clustering_results, feature_names):
    """Analyze characteristics of discovered clusters"""
    print("\n📈 CLUSTER CHARACTERISTICS ANALYSIS")
    print("=" * 50)
    
    # Use optimal K-means results
    optimal_k = clustering_results['optimal_k']
    labels = clustering_results['kmeans_results'][optimal_k]['labels']
    
    print(f"   🎯 Analyzing {optimal_k} clusters from K-means")
    
    # Add cluster labels to stats dataframe
    stats_with_clusters = stats_df.copy()
    stats_with_clusters['cluster'] = labels
    
    # Analyze each cluster
    cluster_summaries = {}
    
    for cluster_id in range(optimal_k):
        cluster_data = stats_with_clusters[stats_with_clusters['cluster'] == cluster_id]
        n_series = len(cluster_data)
        
        print(f"\n🏷️  CLUSTER {cluster_id} (n={n_series}, {n_series/len(stats_df)*100:.1f}%):")
        
        # Key characteristics
        characteristics = {}
        for feature in ['mean', 'std', 'cv', 'skewness', 'kurtosis', 'outlier_rate_iqr', 'trend_strength']:
            if feature in cluster_data.columns:
                mean_val = cluster_data[feature].mean()
                characteristics[feature] = mean_val
                print(f"   {feature:15}: {mean_val:.4f}")
        
        # Dominant patterns
        high_outlier = (cluster_data['outlier_rate_iqr'] > 0.3).sum()
        high_trend = (cluster_data.get('trend_strength', pd.Series([0])) > 0.5).sum()
        high_cv = (cluster_data['cv'] > 3).sum()
        
        print(f"   {'high_outliers':15}: {high_outlier} ({high_outlier/n_series*100:.1f}%)")
        if 'trend_strength' in cluster_data.columns:
            print(f"   {'high_trend':15}: {high_trend} ({high_trend/n_series*100:.1f}%)")
        print(f"   {'high_variability':15}: {high_cv} ({high_cv/n_series*100:.1f}%)")
        
        cluster_summaries[cluster_id] = {
            'size': n_series,
            'characteristics': characteristics,
            'patterns': {
                'high_outlier_rate': high_outlier / n_series,
                'high_trend_rate': high_trend / n_series if 'trend_strength' in cluster_data.columns else 0,
                'high_cv_rate': high_cv / n_series
            }
        }
    
    return cluster_summaries, stats_with_clusters

def visualize_clustering_results(stats_df, clustering_results, feature_names):
    """Create visualizations for clustering results"""
    print("\n📊 CREATING CLUSTERING VISUALIZATIONS")
    print("=" * 50)
    
    try:
        # PCA for dimensionality reduction
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(clustering_results['X_scaled'])
        
        optimal_k = clustering_results['optimal_k']
        labels = clustering_results['kmeans_results'][optimal_k]['labels']
        
        # Create visualization
        plt.figure(figsize=(15, 10))
        
        # Plot 1: PCA scatter plot
        plt.subplot(2, 3, 1)
        scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.6)
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.title(f'K-means Clusters (k={optimal_k})')
        plt.colorbar(scatter)
        
        # Plot 2: Silhouette scores
        plt.subplot(2, 3, 2)
        k_values = list(clustering_results['kmeans_results'].keys())
        silhouette_values = [clustering_results['kmeans_results'][k]['silhouette'] for k in k_values]
        plt.plot(k_values, silhouette_values, 'bo-')
        plt.xlabel('Number of Clusters (k)')
        plt.ylabel('Silhouette Score')
        plt.title('Optimal k Selection')
        plt.grid(True, alpha=0.3)
        
        # Plot 3: Cluster size distribution
        plt.subplot(2, 3, 3)
        cluster_sizes = [np.sum(labels == i) for i in range(optimal_k)]
        plt.bar(range(optimal_k), cluster_sizes)
        plt.xlabel('Cluster ID')
        plt.ylabel('Number of Series')
        plt.title('Cluster Size Distribution')
        
        # Plot 4: Feature importance (PCA components)
        plt.subplot(2, 3, 4)
        feature_importance = np.abs(pca.components_[0])
        top_features_idx = np.argsort(feature_importance)[-10:]
        plt.barh(range(len(top_features_idx)), feature_importance[top_features_idx])
        plt.yticks(range(len(top_features_idx)), [feature_names[i] for i in top_features_idx])
        plt.xlabel('Absolute PC1 Loading')
        plt.title('Top Features (PC1)')
        
        # Plot 5: Mean vs CV by cluster
        plt.subplot(2, 3, 5)
        for cluster_id in range(optimal_k):
            cluster_mask = labels == cluster_id
            plt.scatter(stats_df.loc[cluster_mask, 'mean'], 
                       stats_df.loc[cluster_mask, 'cv'], 
                       label=f'Cluster {cluster_id}', alpha=0.6)
        plt.xlabel('Mean')
        plt.ylabel('Coefficient of Variation')
        plt.title('Mean vs CV by Cluster')
        plt.legend()
        
        # Plot 6: Outlier rate distribution by cluster
        plt.subplot(2, 3, 6)
        outlier_rates_by_cluster = [stats_df.loc[labels == i, 'outlier_rate_iqr'].values 
                                   for i in range(optimal_k)]
        plt.boxplot(outlier_rates_by_cluster, labels=[f'C{i}' for i in range(optimal_k)])
        plt.xlabel('Cluster')
        plt.ylabel('Outlier Rate (IQR)')
        plt.title('Outlier Rate Distribution')
        
        plt.tight_layout()
        plt.savefig('time_series_clustering_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ Saved clustering visualization to 'time_series_clustering_analysis.png'")
        
    except Exception as e:
        print(f"   ⚠️  Visualization error: {e}")

def analyze_pre_post_clustering(stats_df):
    """Perform clustering analysis on pre/post segment features"""
    print("\n🎯 PRE/POST SEGMENT CLUSTERING ANALYSIS")
    print("=" * 50)
    
    # Prepare features for clustering (focus on comparative features)
    comparative_features = [
        'mean_diff', 'std_diff', 'var_ratio', 'mean_ratio', 'skew_diff', 'kurt_diff',
        'range_diff', 'iqr_ratio', 'cohens_d', 'autocorr_diff', 'slope_diff', 'trend_strength_diff',
        't_pvalue', 'ks_pvalue', 'levene_pvalue'
    ]
    
    # Also include some pre/post individual features
    individual_features = []
    for prefix in ['pre_', 'post_']:
        for feature in ['mean', 'std', 'cv', 'skewness', 'kurtosis', 'outlier_rate_iqr']:
            individual_features.append(f'{prefix}{feature}')
    
    all_features = comparative_features + individual_features
    available_features = [f for f in all_features if f in stats_df.columns]
    
    print(f"   📊 Using {len(available_features)} features for clustering")
    print(f"   📋 Comparative features: {len([f for f in available_features if f in comparative_features])}")
    print(f"   📋 Individual features: {len([f for f in available_features if f in individual_features])}")
    
    # Extract feature matrix
    X = stats_df[available_features].copy()
    
    # Handle infinite values and NaNs
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # K-means clustering
    silhouette_scores = []
    kmeans_results = {}
    
    for k in range(2, 8):
        kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
        labels = kmeans.fit_predict(X_scaled)
        silhouette = silhouette_score(X_scaled, labels)
        
        kmeans_results[k] = {
            'labels': labels,
            'silhouette': silhouette,
            'inertia': kmeans.inertia_
        }
        silhouette_scores.append(silhouette)
        
        print(f"   k={k}: Silhouette={silhouette:.3f}")
    
    optimal_k = np.argmax(silhouette_scores) + 2
    print(f"   ✅ Optimal k: {optimal_k} (Silhouette: {max(silhouette_scores):.3f})")
    
    return kmeans_results, optimal_k, available_features, X_scaled

def analyze_segment_type_patterns(stats_df):
    """Analyze patterns between normal_normal and normal_break segments"""
    print("\n📊 SEGMENT TYPE PATTERN ANALYSIS")
    print("=" * 50)
    
    # Group by segment type
    normal_normal = stats_df[stats_df['segment_type'] == 'normal_normal']
    normal_break = stats_df[stats_df['segment_type'] == 'normal_break']
    
    print(f"   📈 Normal→Normal segments: {len(normal_normal)} ({len(normal_normal)/len(stats_df)*100:.1f}%)")
    print(f"   📈 Normal→Break segments:  {len(normal_break)} ({len(normal_break)/len(stats_df)*100:.1f}%)")
    
    # Compare key features between segment types
    comparison_features = [
        'mean_diff', 'std_diff', 'var_ratio', 'mean_ratio', 'cohens_d',
        't_pvalue', 'ks_pvalue', 'pre_mean', 'post_mean', 'pre_std', 'post_std'
    ]
    
    print(f"\n🔍 FEATURE COMPARISON BETWEEN SEGMENT TYPES:")
    for feature in comparison_features:
        if feature in stats_df.columns:
            nn_mean = normal_normal[feature].mean()
            nb_mean = normal_break[feature].mean()
            nn_std = normal_normal[feature].std()
            nb_std = normal_break[feature].std()
            
            # Statistical test
            try:
                t_stat, p_val = stats.ttest_ind(
                    normal_normal[feature].dropna(), 
                    normal_break[feature].dropna()
                )
                significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
            except:
                significance = ""
            
            print(f"   {feature:20}: NN={nn_mean:8.4f}±{nn_std:6.4f} | NB={nb_mean:8.4f}±{nb_std:6.4f} {significance}")
    
    return normal_normal, normal_break

def create_pre_post_visualizations(stats_df, clustering_results, optimal_k, feature_names):
    """Create visualizations for pre/post analysis"""
    print("\n📊 CREATING PRE/POST VISUALIZATIONS")
    print("=" * 50)
    
    try:
        labels = clustering_results[optimal_k]['labels']
        
        plt.figure(figsize=(20, 12))
        
        # Plot 1: Mean difference vs Std difference by segment type
        plt.subplot(3, 4, 1)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            plt.scatter(stats_df.loc[mask, 'mean_diff'], 
                       stats_df.loc[mask, 'std_diff'], 
                       label=seg_type, alpha=0.6)
        plt.xlabel('Mean Difference (Post - Pre)')
        plt.ylabel('Std Difference (Post - Pre)')
        plt.title('Mean vs Std Changes by Segment Type')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot 2: Variance ratio vs Mean ratio by segment type
        plt.subplot(3, 4, 2)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            var_ratios = np.clip(stats_df.loc[mask, 'var_ratio'], 0, 10)  # Clip extreme values
            mean_ratios = np.clip(stats_df.loc[mask, 'mean_ratio'], 0, 5)
            plt.scatter(mean_ratios, var_ratios, label=seg_type, alpha=0.6)
        plt.xlabel('Mean Ratio (Post / Pre)')
        plt.ylabel('Variance Ratio (Post / Pre)')
        plt.title('Ratio Changes by Segment Type')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot 3: Cohen's d distribution by segment type
        plt.subplot(3, 4, 3)
        cohens_d_data = [stats_df[stats_df['segment_type'] == st]['cohens_d'].dropna().values 
                        for st in stats_df['segment_type'].unique()]
        plt.boxplot(cohens_d_data, labels=stats_df['segment_type'].unique())
        plt.ylabel("Cohen's d")
        plt.title('Effect Size Distribution')
        plt.xticks(rotation=45)
        
        # Plot 4: P-value distributions
        plt.subplot(3, 4, 4)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            p_values = stats_df.loc[mask, 't_pvalue'].dropna()
            plt.hist(p_values, alpha=0.6, label=seg_type, bins=20)
        plt.xlabel('T-test P-value')
        plt.ylabel('Frequency')
        plt.title('Statistical Significance Distribution')
        plt.legend()
        plt.yscale('log')
        
        # Plot 5: Pre vs Post means
        plt.subplot(3, 4, 5)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            plt.scatter(stats_df.loc[mask, 'pre_mean'], 
                       stats_df.loc[mask, 'post_mean'], 
                       label=seg_type, alpha=0.6)
        plt.xlabel('Pre-segment Mean')
        plt.ylabel('Post-segment Mean')
        plt.title('Pre vs Post Means')
        plt.plot([stats_df['pre_mean'].min(), stats_df['pre_mean'].max()], 
                [stats_df['pre_mean'].min(), stats_df['pre_mean'].max()], 'k--', alpha=0.5)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot 6: Pre vs Post stds
        plt.subplot(3, 4, 6)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            plt.scatter(stats_df.loc[mask, 'pre_std'], 
                       stats_df.loc[mask, 'post_std'], 
                       label=seg_type, alpha=0.6)
        plt.xlabel('Pre-segment Std')
        plt.ylabel('Post-segment Std')
        plt.title('Pre vs Post Standard Deviations')
        plt.plot([stats_df['pre_std'].min(), stats_df['pre_std'].max()], 
                [stats_df['pre_std'].min(), stats_df['pre_std'].max()], 'k--', alpha=0.5)
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Plot 7: Clustering results
        plt.subplot(3, 4, 7)
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(clustering_results['X_scaled'])
        scatter = plt.scatter(X_pca[:, 0], X_pca[:, 1], c=labels, cmap='tab10', alpha=0.6)
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
        plt.title(f'Segment Clusters (k={optimal_k})')
        plt.colorbar(scatter)
        
        # Plot 8: Cluster composition by segment type
        plt.subplot(3, 4, 8)
        cluster_composition = pd.crosstab(labels, stats_df['segment_type'], normalize='index')
        cluster_composition.plot(kind='bar', stacked=True, ax=plt.gca())
        plt.xlabel('Cluster')
        plt.ylabel('Proportion')
        plt.title('Cluster Composition by Segment Type')
        plt.legend(title='Segment Type')
        plt.xticks(rotation=0)
        
        # Plot 9: Autocorrelation changes
        plt.subplot(3, 4, 9)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            autocorr_diff = stats_df.loc[mask, 'autocorr_diff'].dropna()
            plt.hist(autocorr_diff, alpha=0.6, label=seg_type, bins=20)
        plt.xlabel('Autocorrelation Change')
        plt.ylabel('Frequency')
        plt.title('Autocorrelation Changes')
        plt.legend()
        
        # Plot 10: Trend changes
        plt.subplot(3, 4, 10)
        for seg_type in stats_df['segment_type'].unique():
            mask = stats_df['segment_type'] == seg_type
            trend_diff = stats_df.loc[mask, 'trend_strength_diff'].dropna()
            plt.hist(trend_diff, alpha=0.6, label=seg_type, bins=20)
        plt.xlabel('Trend Strength Change')
        plt.ylabel('Frequency')
        plt.title('Trend Strength Changes')
        plt.legend()
        
        # Plot 11: Significant changes (p < 0.05)
        plt.subplot(3, 4, 11)
        sig_counts = []
        seg_types = stats_df['segment_type'].unique()
        for seg_type in seg_types:
            mask = stats_df['segment_type'] == seg_type
            sig_count = (stats_df.loc[mask, 't_pvalue'] < 0.05).sum()
            total_count = mask.sum()
            sig_counts.append(sig_count / total_count * 100)
        
        plt.bar(seg_types, sig_counts)
        plt.ylabel('% Significant Changes (p < 0.05)')
        plt.title('Statistical Significance by Segment Type')
        plt.xticks(rotation=45)
        
        # Plot 12: Feature importance for clustering
        plt.subplot(3, 4, 12)
        if hasattr(pca, 'components_'):
            feature_importance = np.abs(pca.components_[0])
            top_features_idx = np.argsort(feature_importance)[-8:]
            plt.barh(range(len(top_features_idx)), feature_importance[top_features_idx])
            plt.yticks(range(len(top_features_idx)), [feature_names[i] for i in top_features_idx])
            plt.xlabel('Absolute PC1 Loading')
            plt.title('Top Clustering Features')
        
        plt.tight_layout()
        plt.savefig('pre_post_segment_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ Saved pre/post analysis visualization to 'pre_post_segment_analysis.png'")
        
    except Exception as e:
        print(f"   ⚠️  Visualization error: {e}")

def analyze_prepared_features_with_cca():
    """Analyze the prepared features dataset using CCA for feature clustering"""
    print("\n🔍 ANALYZING PREPARED FEATURES WITH CCA")
    print("=" * 50)
    
    try:
        # Load prepared features
        features_path = "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet"
        df = pd.read_parquet(features_path)
        print(f"   ✅ Loaded prepared features: {df.shape}")
        
        # Load labels for comparison
        y_data = pd.read_parquet('y_train.parquet')
        labels = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
        
        print(f"   📊 Features: {df.shape[1]} columns, {df.shape[0]} samples")
        print(f"   📊 Labels: {len(labels)} samples")
        print(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        
        # Prepare data for CCA analysis
        X = df.values
        y_binary = labels.astype(int)
        
        # Split features into two groups for CCA
        n_features = X.shape[1]
        mid_point = n_features // 2
        
        X1 = X[:, :mid_point]  # First half of features
        X2 = X[:, mid_point:]  # Second half of features
        
        print(f"\n🔗 CANONICAL CORRELATION ANALYSIS:")
        print(f"   Feature set 1: {X1.shape[1]} features")
        print(f"   Feature set 2: {X2.shape[1]} features")
        
        # Perform CCA
        n_components = min(10, min(X1.shape[1], X2.shape[1]))  # Use up to 10 components
        cca = CCA(n_components=n_components)
        
        try:
            X1_c, X2_c = cca.fit_transform(X1, X2)
            
            # Calculate canonical correlations
            canonical_corrs = []
            for i in range(n_components):
                corr = np.corrcoef(X1_c[:, i], X2_c[:, i])[0, 1]
                canonical_corrs.append(abs(corr))
            
            print(f"   📊 Canonical correlations:")
            for i, corr in enumerate(canonical_corrs):
                print(f"      Component {i+1}: {corr:.4f}")
            
            # Use CCA components for clustering
            print(f"\n🎯 CCA-BASED CLUSTERING:")
            
            # Combine CCA components
            cca_features = np.hstack([X1_c, X2_c])
            
            # Standardize CCA features
            scaler = StandardScaler()
            cca_scaled = scaler.fit_transform(cca_features)
            
            # K-means clustering on CCA components
            silhouette_scores = []
            kmeans_results = {}
            
            for k in range(2, 8):
                kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(cca_scaled)
                silhouette = silhouette_score(cca_scaled, cluster_labels)
                silhouette_scores.append(silhouette)
                kmeans_results[k] = {
                    'labels': cluster_labels,
                    'silhouette': silhouette
                }
                print(f"   k={k}: Silhouette={silhouette:.3f}")
            
            optimal_k = np.argmax(silhouette_scores) + 2
            print(f"   ✅ Optimal k: {optimal_k} (Silhouette: {max(silhouette_scores):.3f})")
            
            final_clusters = kmeans_results[optimal_k]['labels']
            
            # Analyze cluster composition
            normal_mask = labels == 0
            break_mask = labels == 1
            
            print(f"\n📊 CCA CLUSTER COMPOSITION:")
            cluster_purities = []
            for cluster_id in range(optimal_k):
                cluster_mask = final_clusters == cluster_id
                cluster_normal = np.sum(cluster_mask & normal_mask)
                cluster_break = np.sum(cluster_mask & break_mask)
                cluster_total = cluster_mask.sum()
                purity = max(cluster_normal, cluster_break) / cluster_total if cluster_total > 0 else 0
                cluster_purities.append(purity)
                
                print(f"   Cluster {cluster_id}: {cluster_total} samples")
                print(f"     Normal: {cluster_normal} ({cluster_normal/cluster_total*100:.1f}%)")
                print(f"     Break:  {cluster_break} ({cluster_break/cluster_total*100:.1f}%)")
                print(f"     Purity: {purity:.3f}")
            
            # Compare with LDA for reference
            print(f"\n📊 LINEAR DISCRIMINANT ANALYSIS COMPARISON:")
            try:
                lda = LinearDiscriminantAnalysis(n_components=1)
                X_lda = lda.fit_transform(X, y_binary)
                
                # Clustering on LDA projection
                lda_scaled = StandardScaler().fit_transform(X_lda)
                lda_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
                lda_clusters = lda_kmeans.fit_predict(lda_scaled)
                lda_silhouette = silhouette_score(lda_scaled, lda_clusters)
                
                print(f"   LDA clustering silhouette: {lda_silhouette:.3f}")
                
                # LDA cluster composition
                print(f"   📊 LDA CLUSTER COMPOSITION:")
                for cluster_id in range(optimal_k):
                    cluster_mask = lda_clusters == cluster_id
                    cluster_normal = np.sum(cluster_mask & normal_mask)
                    cluster_break = np.sum(cluster_mask & break_mask)
                    cluster_total = cluster_mask.sum()
                    purity = max(cluster_normal, cluster_break) / cluster_total if cluster_total > 0 else 0
                    
                    print(f"     Cluster {cluster_id}: Normal={cluster_normal}, Break={cluster_break}, Purity={purity:.3f}")
                
            except Exception as e:
                print(f"   ⚠️  LDA analysis failed: {e}")
                lda_clusters = None
                X_lda = None
            
            # Create comprehensive visualization
            print(f"\n📊 CREATING CCA ANALYSIS VISUALIZATION:")
            try:
                fig, axes = plt.subplots(3, 4, figsize=(20, 15))
                
                # Plot 1: CCA components scatter
                axes[0, 0].scatter(X1_c[:, 0], X2_c[:, 0], c=labels, cmap='RdYlBu', alpha=0.6)
                axes[0, 0].set_xlabel('CCA Component 1 (Set 1)')
                axes[0, 0].set_ylabel('CCA Component 1 (Set 2)')
                axes[0, 0].set_title('CCA Components by Label')
                
                # Plot 2: CCA clusters
                axes[0, 1].scatter(X1_c[:, 0], X2_c[:, 0], c=final_clusters, cmap='tab10', alpha=0.6)
                axes[0, 1].set_xlabel('CCA Component 1 (Set 1)')
                axes[0, 1].set_ylabel('CCA Component 1 (Set 2)')
                axes[0, 1].set_title(f'CCA Clusters (k={optimal_k})')
                
                # Plot 3: Canonical correlations
                axes[0, 2].bar(range(len(canonical_corrs)), canonical_corrs)
                axes[0, 2].set_xlabel('CCA Component')
                axes[0, 2].set_ylabel('Canonical Correlation')
                axes[0, 2].set_title('Canonical Correlations')
                
                # Plot 4: Cluster purity comparison
                methods = ['CCA']
                purities = [np.mean(cluster_purities)]
                if lda_clusters is not None:
                    lda_purities = []
                    for cluster_id in range(optimal_k):
                        cluster_mask = lda_clusters == cluster_id
                        cluster_normal = np.sum(cluster_mask & normal_mask)
                        cluster_break = np.sum(cluster_mask & break_mask)
                        cluster_total = cluster_mask.sum()
                        purity = max(cluster_normal, cluster_break) / cluster_total if cluster_total > 0 else 0
                        lda_purities.append(purity)
                    methods.append('LDA')
                    purities.append(np.mean(lda_purities))
                
                axes[0, 3].bar(methods, purities)
                axes[0, 3].set_ylabel('Average Cluster Purity')
                axes[0, 3].set_title('Method Comparison')
                axes[0, 3].set_ylim(0, 1)
                
                # Plot 5-8: Individual CCA components
                for i in range(min(4, n_components)):
                    row = 1 + i // 2
                    col = i % 2
                    if row < 3:
                        axes[row, col].scatter(X1_c[:, i], X2_c[:, i], c=labels, cmap='RdYlBu', alpha=0.6)
                        axes[row, col].set_xlabel(f'CCA Component {i+1} (Set 1)')
                        axes[row, col].set_ylabel(f'CCA Component {i+1} (Set 2)')
                        axes[row, col].set_title(f'Component {i+1} (r={canonical_corrs[i]:.3f})')
                
                # Plot 9: LDA projection (if available)
                if X_lda is not None:
                    axes[2, 2].hist([X_lda[normal_mask, 0], X_lda[break_mask, 0]], 
                                   bins=30, alpha=0.7, label=['Normal', 'Break'])
                    axes[2, 2].set_xlabel('LDA Component')
                    axes[2, 2].set_ylabel('Frequency')
                    axes[2, 2].set_title('LDA Projection')
                    axes[2, 2].legend()
                
                # Plot 10: Feature importance (CCA loadings)
                if hasattr(cca, 'x_loadings_'):
                    loadings = np.abs(cca.x_loadings_[:, 0])  # First component loadings
                    top_features = np.argsort(loadings)[-10:]
                    axes[2, 3].barh(range(len(top_features)), loadings[top_features])
                    axes[2, 3].set_yticks(range(len(top_features)))
                    axes[2, 3].set_yticklabels([f'Feature {i}' for i in top_features])
                    axes[2, 3].set_xlabel('Absolute Loading')
                    axes[2, 3].set_title('Top CCA Feature Loadings')
                
                plt.tight_layout()
                plt.savefig('cca_features_analysis.png', dpi=300, bbox_inches='tight')
                plt.close()
                
                print(f"   ✅ Saved CCA visualization to 'cca_features_analysis.png'")
                
            except Exception as e:
                print(f"   ⚠️  Visualization error: {e}")
            
            return df, labels, final_clusters, cca, canonical_corrs, cluster_purities
            
        except Exception as e:
            print(f"   ❌ CCA analysis failed: {e}")
            return None, None, None, None, None, None
        
    except Exception as e:
        print(f"   ❌ Error loading prepared features: {e}")
        return None, None, None, None, None, None

def analyze_calm_vs_volatile_patterns():
    """Analyze calm vs volatile time series patterns"""
    print("\n🌊 ANALYZING CALM VS VOLATILE TIME SERIES PATTERNS")
    print("=" * 60)
    
    try:
        # Load time series data
        time_series_list, series_ids, labels = load_time_series_data_with_labels()
        if time_series_list is None:
            print("❌ Failed to load data")
            return None
        
        print(f"   ✅ Loaded {len(time_series_list)} time series")
        
        # Compute volatility and calmness metrics for each series
        volatility_metrics = []
        
        for i, series in enumerate(time_series_list):
            if i % 1000 == 0:
                print(f"   Processing series {i+1}/{len(time_series_list)}...")
            
            series_clean = np.array(series)[~np.isnan(series)]
            if len(series_clean) < 10:
                continue
            
            # Basic volatility measures
            std_val = np.std(series_clean, ddof=1)
            mean_val = np.mean(series_clean)
            cv = std_val / abs(mean_val) if mean_val != 0 else np.inf
            range_val = np.max(series_clean) - np.min(series_clean)
            
            # Advanced volatility measures
            # 1. Realized volatility (sum of squared returns)
            returns = np.diff(series_clean)
            realized_vol = np.sqrt(np.sum(returns**2)) if len(returns) > 0 else 0
            
            # 2. Mean absolute deviation
            mad = np.mean(np.abs(series_clean - mean_val))
            
            # 3. Interquartile range
            q75, q25 = np.percentile(series_clean, [75, 25])
            iqr = q75 - q25
            
            # 4. Number of direction changes (volatility proxy)
            if len(returns) > 1:
                direction_changes = np.sum(np.diff(np.sign(returns)) != 0)
                direction_change_rate = direction_changes / len(returns)
            else:
                direction_changes = 0
                direction_change_rate = 0
            
            # 5. Spike detection (values beyond 3 sigma)
            z_scores = np.abs((series_clean - mean_val) / std_val) if std_val > 0 else np.zeros_like(series_clean)
            n_spikes = np.sum(z_scores > 3)
            spike_rate = n_spikes / len(series_clean)
            
            # 6. Clipping detection (repeated boundary values)
            unique_vals = len(np.unique(series_clean))
            uniqueness_ratio = unique_vals / len(series_clean)
            
            # Check for potential clipping at boundaries
            sorted_vals = np.sort(series_clean)
            min_repeats = np.sum(series_clean == sorted_vals[0])
            max_repeats = np.sum(series_clean == sorted_vals[-1])
            boundary_repeat_rate = (min_repeats + max_repeats) / len(series_clean)
            
            # 7. Flatness measure (consecutive identical values)
            if len(series_clean) > 1:
                flat_segments = np.sum(np.diff(series_clean) == 0)
                flatness_rate = flat_segments / (len(series_clean) - 1)
            else:
                flatness_rate = 0
            
            # 8. Autocorrelation (smooth vs erratic)
            if len(series_clean) > 10:
                autocorr_1 = np.corrcoef(series_clean[:-1], series_clean[1:])[0, 1]
                autocorr_1 = autocorr_1 if not np.isnan(autocorr_1) else 0
            else:
                autocorr_1 = 0
            
            # 9. Trend strength
            if len(series_clean) > 5:
                x = np.arange(len(series_clean))
                slope, _, r_value, _, _ = stats.linregress(x, series_clean)
                trend_strength = abs(r_value)
            else:
                trend_strength = 0
            
            # 10. Entropy (predictability measure)
            if len(series_clean) > 10:
                hist, _ = np.histogram(series_clean, bins=min(20, len(series_clean)//5), density=True)
                hist = hist[hist > 0]
                entropy_val = -np.sum(hist * np.log(hist)) if len(hist) > 0 else 0
            else:
                entropy_val = 0
            
            volatility_metrics.append({
                'series_id': i,
                'label': labels[i],
                'length': len(series_clean),
                'std': std_val,
                'cv': cv,
                'range': range_val,
                'realized_vol': realized_vol,
                'mad': mad,
                'iqr': iqr,
                'direction_change_rate': direction_change_rate,
                'spike_rate': spike_rate,
                'uniqueness_ratio': uniqueness_ratio,
                'boundary_repeat_rate': boundary_repeat_rate,
                'flatness_rate': flatness_rate,
                'autocorr_1': autocorr_1,
                'trend_strength': trend_strength,
                'entropy': entropy_val
            })
        
        # Convert to DataFrame
        vol_df = pd.DataFrame(volatility_metrics)
        print(f"   ✅ Computed volatility metrics for {len(vol_df)} series")
        
        # Define calmness score (inverse of volatility)
        # Normalize metrics to 0-1 scale and combine
        metrics_to_normalize = ['cv', 'realized_vol', 'mad', 'direction_change_rate', 'spike_rate', 'entropy']
        
        normalized_metrics = vol_df.copy()
        for metric in metrics_to_normalize:
            if metric in vol_df.columns:
                values = vol_df[metric].replace([np.inf, -np.inf], np.nan)
                values = values.fillna(values.median())
                
                # Normalize to 0-1
                min_val, max_val = values.min(), values.max()
                if max_val > min_val:
                    normalized_metrics[f'{metric}_norm'] = (values - min_val) / (max_val - min_val)
                else:
                    normalized_metrics[f'{metric}_norm'] = 0
        
        # Compute calmness score (higher = calmer)
        volatility_components = [f'{m}_norm' for m in metrics_to_normalize if f'{m}_norm' in normalized_metrics.columns]
        vol_df['volatility_score'] = normalized_metrics[volatility_components].mean(axis=1)
        vol_df['calmness_score'] = 1 - vol_df['volatility_score']
        
        # Add positive indicators for calmness
        positive_indicators = ['autocorr_1', 'trend_strength', 'uniqueness_ratio']
        for indicator in positive_indicators:
            if indicator in vol_df.columns:
                values = vol_df[indicator].replace([np.inf, -np.inf], np.nan).fillna(0)
                min_val, max_val = values.min(), values.max()
                if max_val > min_val:
                    vol_df[f'{indicator}_norm'] = (values - min_val) / (max_val - min_val)
                else:
                    vol_df[f'{indicator}_norm'] = 0
        
        # Enhanced calmness score
        positive_components = [f'{i}_norm' for i in positive_indicators if f'{i}_norm' in vol_df.columns]
        if positive_components:
            vol_df['calmness_score'] = (vol_df['calmness_score'] + vol_df[positive_components].mean(axis=1)) / 2
        
        # Categorize series
        calm_threshold = vol_df['calmness_score'].quantile(0.8)  # Top 20% calmest
        volatile_threshold = vol_df['calmness_score'].quantile(0.2)  # Bottom 20% most volatile
        
        vol_df['pattern_type'] = 'moderate'
        vol_df.loc[vol_df['calmness_score'] >= calm_threshold, 'pattern_type'] = 'calm'
        vol_df.loc[vol_df['calmness_score'] <= volatile_threshold, 'pattern_type'] = 'volatile'
        
        # Identify specific patterns
        # Clipped series (high boundary repeat rate + low uniqueness)
        clipped_mask = (vol_df['boundary_repeat_rate'] > 0.1) & (vol_df['uniqueness_ratio'] < 0.5)
        vol_df.loc[clipped_mask, 'pattern_type'] = 'clipped'
        
        # Spiky series (high spike rate)
        spiky_mask = vol_df['spike_rate'] > 0.05
        vol_df.loc[spiky_mask, 'pattern_type'] = 'spiky'
        
        # Flat series (high flatness rate)
        flat_mask = vol_df['flatness_rate'] > 0.3
        vol_df.loc[flat_mask, 'pattern_type'] = 'flat'
        
        print(f"\n📊 PATTERN DISTRIBUTION:")
        pattern_counts = vol_df['pattern_type'].value_counts()
        for pattern, count in pattern_counts.items():
            print(f"   {pattern.capitalize()}: {count} ({count/len(vol_df)*100:.1f}%)")
        
        # Analyze by label
        print(f"\n🏷️ PATTERN DISTRIBUTION BY LABEL:")
        pattern_label_crosstab = pd.crosstab(vol_df['pattern_type'], vol_df['label'], normalize='columns')
        for pattern in pattern_label_crosstab.index:
            normal_pct = pattern_label_crosstab.loc[pattern, False] * 100
            break_pct = pattern_label_crosstab.loc[pattern, True] * 100
            print(f"   {pattern.capitalize()}: Normal={normal_pct:.1f}%, Break={break_pct:.1f}%")
        
        # Statistical comparison
        print(f"\n📈 STATISTICAL COMPARISON BY PATTERN:")
        key_metrics = ['calmness_score', 'cv', 'spike_rate', 'autocorr_1', 'trend_strength']
        
        for metric in key_metrics:
            if metric in vol_df.columns:
                print(f"\n   {metric.upper()}:")
                for pattern in ['calm', 'moderate', 'volatile', 'clipped', 'spiky', 'flat']:
                    if pattern in pattern_counts.index:
                        pattern_data = vol_df[vol_df['pattern_type'] == pattern][metric]
                        mean_val = pattern_data.mean()
                        std_val = pattern_data.std()
                        print(f"     {pattern.capitalize()}: {mean_val:.4f} ± {std_val:.4f}")
        
        # Create visualization
        print(f"\n📊 CREATING VOLATILITY PATTERN VISUALIZATION:")
        try:
            fig, axes = plt.subplots(3, 4, figsize=(20, 15))
            
            # Plot 1: Calmness score distribution
            axes[0, 0].hist(vol_df['calmness_score'], bins=50, alpha=0.7, edgecolor='black')
            axes[0, 0].axvline(calm_threshold, color='green', linestyle='--', label='Calm threshold')
            axes[0, 0].axvline(volatile_threshold, color='red', linestyle='--', label='Volatile threshold')
            axes[0, 0].set_xlabel('Calmness Score')
            axes[0, 0].set_ylabel('Frequency')
            axes[0, 0].set_title('Calmness Score Distribution')
            axes[0, 0].legend()
            
            # Plot 2: Pattern distribution
            pattern_counts.plot(kind='bar', ax=axes[0, 1])
            axes[0, 1].set_xlabel('Pattern Type')
            axes[0, 1].set_ylabel('Count')
            axes[0, 1].set_title('Pattern Type Distribution')
            axes[0, 1].tick_params(axis='x', rotation=45)
            
            # Plot 3: CV vs Spike Rate
            for pattern in vol_df['pattern_type'].unique():
                pattern_data = vol_df[vol_df['pattern_type'] == pattern]
                axes[0, 2].scatter(pattern_data['cv'], pattern_data['spike_rate'], 
                                 label=pattern, alpha=0.6)
            axes[0, 2].set_xlabel('Coefficient of Variation')
            axes[0, 2].set_ylabel('Spike Rate')
            axes[0, 2].set_title('CV vs Spike Rate by Pattern')
            axes[0, 2].legend()
            axes[0, 2].set_xlim(0, min(10, vol_df['cv'].quantile(0.95)))
            
            # Plot 4: Autocorrelation vs Trend Strength
            for pattern in vol_df['pattern_type'].unique():
                pattern_data = vol_df[vol_df['pattern_type'] == pattern]
                axes[0, 3].scatter(pattern_data['autocorr_1'], pattern_data['trend_strength'], 
                                 label=pattern, alpha=0.6)
            axes[0, 3].set_xlabel('Autocorrelation (lag-1)')
            axes[0, 3].set_ylabel('Trend Strength')
            axes[0, 3].set_title('Autocorrelation vs Trend by Pattern')
            axes[0, 3].legend()
            
            # Plot 5-8: Pattern by label
            for i, pattern in enumerate(['calm', 'volatile', 'clipped', 'spiky']):
                if pattern in pattern_counts.index:
                    row, col = 1 + i // 2, i % 2
                    pattern_data = vol_df[vol_df['pattern_type'] == pattern]
                    
                    normal_data = pattern_data[pattern_data['label'] == False]['calmness_score']
                    break_data = pattern_data[pattern_data['label'] == True]['calmness_score']
                    
                    axes[row, col].hist([normal_data, break_data], bins=20, alpha=0.7, 
                                       label=['Normal', 'Break'], edgecolor='black')
                    axes[row, col].set_xlabel('Calmness Score')
                    axes[row, col].set_ylabel('Frequency')
                    axes[row, col].set_title(f'{pattern.capitalize()} Series by Label')
                    axes[row, col].legend()
            
            # Plot 9: Boundary repeat rate vs Uniqueness ratio
            axes[2, 2].scatter(vol_df['boundary_repeat_rate'], vol_df['uniqueness_ratio'], 
                             c=vol_df['pattern_type'].astype('category').cat.codes, 
                             cmap='tab10', alpha=0.6)
            axes[2, 2].set_xlabel('Boundary Repeat Rate')
            axes[2, 2].set_ylabel('Uniqueness Ratio')
            axes[2, 2].set_title('Clipping Detection')
            
            # Plot 10: Flatness vs Direction Changes
            axes[2, 3].scatter(vol_df['flatness_rate'], vol_df['direction_change_rate'], 
                             c=vol_df['pattern_type'].astype('category').cat.codes, 
                             cmap='tab10', alpha=0.6)
            axes[2, 3].set_xlabel('Flatness Rate')
            axes[2, 3].set_ylabel('Direction Change Rate')
            axes[2, 3].set_title('Smoothness vs Volatility')
            
            plt.tight_layout()
            plt.savefig('volatility_pattern_analysis.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"   ✅ Saved volatility analysis to 'volatility_pattern_analysis.png'")
            
        except Exception as e:
            print(f"   ⚠️  Visualization error: {e}")
        
        # Save results
        vol_df.to_csv('volatility_pattern_analysis.csv', index=False)
        print(f"   💾 Saved detailed results to 'volatility_pattern_analysis.csv'")
        
        return vol_df
        
    except Exception as e:
        print(f"   ❌ Error in volatility analysis: {e}")
        return None

def compare_classification_performance(stats_df):
    """Compare classification performance between full-length and bounded features"""
    print("\n🎯 CLASSIFICATION PERFORMANCE COMPARISON")
    print("=" * 60)
    
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.linear_model import LogisticRegression
    from sklearn.model_selection import cross_val_score, StratifiedKFold
    from sklearn.metrics import roc_auc_score
    from sklearn.preprocessing import StandardScaler
    
    # Prepare labels
    y = stats_df['label'].values
    
    print(f"   📊 Dataset: {len(y)} samples, {np.sum(y)} positive ({np.sum(y)/len(y)*100:.1f}%)")
    
    # Define feature sets
    feature_sets = {
        'full_comparative': [
            'mean_diff', 'std_diff', 'var_ratio', 'mean_ratio', 'skew_diff', 'kurt_diff',
            'range_diff', 'iqr_ratio', 'cohens_d', 'autocorr_diff', 'slope_diff', 'trend_strength_diff',
            't_pvalue', 'ks_pvalue', 'levene_pvalue'
        ],
        'full_individual': [],
        'bounded_comparative': [
            'bounded_mean_diff', 'bounded_std_diff', 'bounded_var_ratio', 'bounded_mean_ratio', 
            'bounded_skew_diff', 'bounded_kurt_diff', 'bounded_range_diff', 'bounded_iqr_ratio', 
            'bounded_cohens_d', 'bounded_autocorr_diff', 'bounded_slope_diff', 'bounded_trend_strength_diff',
            'bounded_t_pvalue', 'bounded_ks_pvalue', 'bounded_levene_pvalue'
        ],
        'bounded_individual': []
    }
    
    # Add individual features
    for prefix in ['pre_', 'post_']:
        for feature in ['mean', 'std', 'cv', 'skewness', 'kurtosis', 'outlier_rate_iqr', 'trend_strength']:
            feature_sets['full_individual'].append(f'{prefix}{feature}')
            feature_sets['bounded_individual'].append(f'{prefix}bounded_{feature}')
    
    # Filter available features
    for set_name in feature_sets:
        available = [f for f in feature_sets[set_name] if f in stats_df.columns]
        feature_sets[set_name] = available
        print(f"   📋 {set_name}: {len(available)} features")
    
    # Combine feature sets
    feature_combinations = {
        'full_all': feature_sets['full_comparative'] + feature_sets['full_individual'],
        'bounded_all': feature_sets['bounded_comparative'] + feature_sets['bounded_individual'],
        'full_comparative_only': feature_sets['full_comparative'],
        'bounded_comparative_only': feature_sets['bounded_comparative'],
        'full_individual_only': feature_sets['full_individual'],
        'bounded_individual_only': feature_sets['bounded_individual']
    }
    
    # Classification models
    models = {
        'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced'),
        'LogisticRegression': LogisticRegression(random_state=42, class_weight='balanced', max_iter=1000)
    }
    
    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    results = {}
    
    print(f"\n🔬 CROSS-VALIDATION RESULTS:")
    print("=" * 80)
    
    for combo_name, features in feature_combinations.items():
        if not features:
            continue
            
        print(f"\n📊 {combo_name.upper()} ({len(features)} features):")
        
        # Prepare feature matrix
        X = stats_df[features].copy()
        
        # Handle missing values and infinities
        X = X.replace([np.inf, -np.inf], np.nan)
        X = X.fillna(X.median())
        
        # Check if we have valid data
        if X.shape[1] == 0 or X.isnull().all().all():
            print(f"   ⚠️  No valid features available")
            continue
        
        combo_results = {}
        
        for model_name, model in models.items():
            try:
                # Standardize features
                scaler = StandardScaler()
                X_scaled = scaler.fit_transform(X)
                
                # Cross-validation
                cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
                
                mean_score = cv_scores.mean()
                std_score = cv_scores.std()
                
                combo_results[model_name] = {
                    'mean_auroc': mean_score,
                    'std_auroc': std_score,
                    'cv_scores': cv_scores
                }
                
                print(f"   {model_name:15}: AUROC = {mean_score:.4f} ± {std_score:.4f}")
                
            except Exception as e:
                print(f"   {model_name:15}: Error - {e}")
                combo_results[model_name] = {'mean_auroc': np.nan, 'std_auroc': np.nan}
        
        results[combo_name] = combo_results
    
    # Compare full vs bounded performance
    print(f"\n📈 PERFORMANCE COMPARISON:")
    print("=" * 60)
    
    comparisons = [
        ('full_all', 'bounded_all', 'All Features'),
        ('full_comparative_only', 'bounded_comparative_only', 'Comparative Features Only'),
        ('full_individual_only', 'bounded_individual_only', 'Individual Features Only')
    ]
    
    comparison_results = {}
    
    for full_key, bounded_key, desc in comparisons:
        if full_key in results and bounded_key in results:
            print(f"\n🔍 {desc}:")
            
            for model_name in models.keys():
                if model_name in results[full_key] and model_name in results[bounded_key]:
                    full_score = results[full_key][model_name]['mean_auroc']
                    bounded_score = results[bounded_key][model_name]['mean_auroc']
                    
                    if not (np.isnan(full_score) or np.isnan(bounded_score)):
                        diff = bounded_score - full_score
                        pct_change = (diff / full_score) * 100 if full_score != 0 else 0
                        
                        print(f"   {model_name:15}: Full={full_score:.4f}, Bounded={bounded_score:.4f}")
                        print(f"   {'':15}  Difference={diff:+.4f} ({pct_change:+.1f}%)")
                        
                        comparison_results[f"{desc}_{model_name}"] = {
                            'full_score': full_score,
                            'bounded_score': bounded_score,
                            'difference': diff,
                            'pct_change': pct_change
                        }
    
    return results, comparison_results

def analyze_feature_importance_comparison(stats_df):
    """Analyze feature importance differences between full and bounded features"""
    print("\n🔍 FEATURE IMPORTANCE COMPARISON")
    print("=" * 60)
    
    from sklearn.ensemble import RandomForestClassifier
    from sklearn.preprocessing import StandardScaler
    
    # Prepare data
    y = stats_df['label'].values
    
    # Define feature pairs (full vs bounded)
    feature_pairs = []
    
    # Comparative features
    comparative_base = [
        'mean_diff', 'std_diff', 'var_ratio', 'mean_ratio', 'skew_diff', 'kurt_diff',
        'range_diff', 'iqr_ratio', 'cohens_d', 'autocorr_diff', 'slope_diff', 'trend_strength_diff'
    ]
    
    for base_feature in comparative_base:
        full_feature = base_feature
        bounded_feature = f'bounded_{base_feature}'
        if full_feature in stats_df.columns and bounded_feature in stats_df.columns:
            feature_pairs.append((full_feature, bounded_feature, 'comparative'))
    
    # Individual features
    individual_base = ['mean', 'std', 'cv', 'skewness', 'kurtosis', 'outlier_rate_iqr', 'trend_strength']
    
    for prefix in ['pre_', 'post_']:
        for base_feature in individual_base:
            full_feature = f'{prefix}{base_feature}'
            bounded_feature = f'{prefix}bounded_{base_feature}'
            if full_feature in stats_df.columns and bounded_feature in stats_df.columns:
                feature_pairs.append((full_feature, bounded_feature, f'{prefix}individual'))
    
    print(f"   📊 Analyzing {len(feature_pairs)} feature pairs")
    
    # Train models and get feature importance
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
    
    importance_comparison = []
    
    for full_feat, bounded_feat, feat_type in feature_pairs:
        # Prepare data for both features
        X_full = stats_df[[full_feat]].copy()
        X_bounded = stats_df[[bounded_feat]].copy()
        
        # Handle missing values
        X_full = X_full.replace([np.inf, -np.inf], np.nan).fillna(X_full.median())
        X_bounded = X_bounded.replace([np.inf, -np.inf], np.nan).fillna(X_bounded.median())
        
        # Standardize
        scaler_full = StandardScaler()
        scaler_bounded = StandardScaler()
        
        X_full_scaled = scaler_full.fit_transform(X_full)
        X_bounded_scaled = scaler_bounded.fit_transform(X_bounded)
        
        try:
            # Train on full feature
            model.fit(X_full_scaled, y)
            importance_full = model.feature_importances_[0]
            
            # Train on bounded feature
            model.fit(X_bounded_scaled, y)
            importance_bounded = model.feature_importances_[0]
            
            # Calculate correlation between features
            correlation = np.corrcoef(X_full.iloc[:, 0], X_bounded.iloc[:, 0])[0, 1]
            
            importance_comparison.append({
                'feature_base': full_feat.replace('pre_', '').replace('post_', '').replace('bounded_', ''),
                'feature_type': feat_type,
                'full_feature': full_feat,
                'bounded_feature': bounded_feat,
                'importance_full': importance_full,
                'importance_bounded': importance_bounded,
                'importance_ratio': importance_bounded / importance_full if importance_full > 0 else np.inf,
                'correlation': correlation
            })
            
        except Exception as e:
            print(f"   ⚠️  Error with {full_feat} vs {bounded_feat}: {e}")
    
    # Convert to DataFrame for analysis
    importance_df = pd.DataFrame(importance_comparison)
    
    if len(importance_df) > 0:
        print(f"\n📊 FEATURE IMPORTANCE SUMMARY:")
        print(f"   Average importance ratio (bounded/full): {importance_df['importance_ratio'].mean():.3f}")
        print(f"   Features where bounded > full: {np.sum(importance_df['importance_ratio'] > 1)} ({np.sum(importance_df['importance_ratio'] > 1)/len(importance_df)*100:.1f}%)")
        print(f"   Average correlation: {importance_df['correlation'].mean():.3f}")
        
        # Top features by importance difference
        importance_df['importance_diff'] = importance_df['importance_bounded'] - importance_df['importance_full']
        
        print(f"\n🔝 TOP FEATURES WHERE BOUNDED OUTPERFORMS FULL:")
        top_bounded = importance_df.nlargest(5, 'importance_diff')
        for _, row in top_bounded.iterrows():
            print(f"   {row['feature_base']:20}: Full={row['importance_full']:.4f}, Bounded={row['importance_bounded']:.4f} (+{row['importance_diff']:.4f})")
        
        print(f"\n🔻 TOP FEATURES WHERE FULL OUTPERFORMS BOUNDED:")
        top_full = importance_df.nsmallest(5, 'importance_diff')
        for _, row in top_full.iterrows():
            print(f"   {row['feature_base']:20}: Full={row['importance_full']:.4f}, Bounded={row['importance_bounded']:.4f} ({row['importance_diff']:.4f})")
        
        # Save detailed results
        importance_df.to_csv('feature_importance_comparison.csv', index=False)
        print(f"\n💾 Detailed importance comparison saved to 'feature_importance_comparison.csv'")
    
    return importance_df

def create_length_comparison_visualization(stats_df, classification_results, importance_df):
    """Create visualizations comparing full vs bounded length analysis"""
    print("\n📊 CREATING LENGTH COMPARISON VISUALIZATIONS")
    print("=" * 60)
    
    try:
        fig, axes = plt.subplots(3, 4, figsize=(20, 15))
        
        # Plot 1: Classification performance comparison
        if classification_results:
            models = ['RandomForest', 'LogisticRegression']
            comparisons = ['All Features', 'Comparative Features Only', 'Individual Features Only']
            
            full_scores = []
            bounded_scores = []
            
            for comp in comparisons:
                for model in models:
                    key = f"{comp}_{model}"
                    if key in classification_results:
                        full_scores.append(classification_results[key]['full_score'])
                        bounded_scores.append(classification_results[key]['bounded_score'])
            
            if full_scores and bounded_scores:
                x_pos = np.arange(len(full_scores))
                width = 0.35
                
                axes[0, 0].bar(x_pos - width/2, full_scores, width, label='Full Length', alpha=0.8)
                axes[0, 0].bar(x_pos + width/2, bounded_scores, width, label='Bounded Length', alpha=0.8)
                axes[0, 0].set_xlabel('Model-Feature Combination')
                axes[0, 0].set_ylabel('AUROC Score')
                axes[0, 0].set_title('Classification Performance Comparison')
                axes[0, 0].legend()
                axes[0, 0].set_xticks(x_pos)
                axes[0, 0].set_xticklabels([f"{c[:3]}-{m[:3]}" for c in comparisons for m in models], rotation=45)
        
        # Plot 2: Performance difference distribution
        if classification_results:
            differences = [classification_results[key]['difference'] for key in classification_results 
                          if 'difference' in classification_results[key]]
            if differences:
                axes[0, 1].hist(differences, bins=10, alpha=0.7, edgecolor='black')
                axes[0, 1].axvline(0, color='red', linestyle='--', label='No difference')
                axes[0, 1].set_xlabel('AUROC Difference (Bounded - Full)')
                axes[0, 1].set_ylabel('Frequency')
                axes[0, 1].set_title('Performance Difference Distribution')
                axes[0, 1].legend()
        
        # Plot 3: Feature importance comparison
        if len(importance_df) > 0:
            axes[0, 2].scatter(importance_df['importance_full'], importance_df['importance_bounded'], 
                             alpha=0.6, c=importance_df['feature_type'].astype('category').cat.codes)
            axes[0, 2].plot([0, importance_df['importance_full'].max()], 
                           [0, importance_df['importance_full'].max()], 'r--', alpha=0.5)
            axes[0, 2].set_xlabel('Full Length Importance')
            axes[0, 2].set_ylabel('Bounded Length Importance')
            axes[0, 2].set_title('Feature Importance Comparison')
        
        # Plot 4: Importance ratio distribution
        if len(importance_df) > 0:
            ratios = importance_df['importance_ratio'][importance_df['importance_ratio'] < 10]  # Remove extreme outliers
            axes[0, 3].hist(ratios, bins=20, alpha=0.7, edgecolor='black')
            axes[0, 3].axvline(1, color='red', linestyle='--', label='Equal importance')
            axes[0, 3].set_xlabel('Importance Ratio (Bounded/Full)')
            axes[0, 3].set_ylabel('Frequency')
            axes[0, 3].set_title('Importance Ratio Distribution')
            axes[0, 3].legend()
        
        # Plot 5: Correlation between full and bounded features
        if len(importance_df) > 0:
            axes[1, 0].hist(importance_df['correlation'], bins=20, alpha=0.7, edgecolor='black')
            axes[1, 0].set_xlabel('Correlation (Full vs Bounded)')
            axes[1, 0].set_ylabel('Frequency')
            axes[1, 0].set_title('Feature Correlation Distribution')
        
        # Plot 6-8: Feature type analysis
        if len(importance_df) > 0:
            feature_types = importance_df['feature_type'].unique()
            for i, feat_type in enumerate(feature_types[:3]):
                if i < 3:
                    type_data = importance_df[importance_df['feature_type'] == feat_type]
                    axes[1, i+1].scatter(type_data['importance_full'], type_data['importance_bounded'], alpha=0.6)
                    axes[1, i+1].plot([0, type_data['importance_full'].max()], 
                                     [0, type_data['importance_full'].max()], 'r--', alpha=0.5)
                    axes[1, i+1].set_xlabel('Full Length Importance')
                    axes[1, i+1].set_ylabel('Bounded Length Importance')
                    axes[1, i+1].set_title(f'{feat_type.capitalize()} Features')
        
        # Plot 9: Length statistics comparison
        if 'pre_length' in stats_df.columns and 'pre_bounded_length' in stats_df.columns:
            axes[2, 0].hist([stats_df['pre_length'], stats_df['pre_bounded_length']], 
                           bins=30, alpha=0.7, label=['Full', 'Bounded'], edgecolor='black')
            axes[2, 0].set_xlabel('Pre-segment Length')
            axes[2, 0].set_ylabel('Frequency')
            axes[2, 0].set_title('Pre-segment Length Distribution')
            axes[2, 0].legend()
        
        # Plot 10: Statistical significance comparison
        if 'bounded_t_pvalue' in stats_df.columns and 't_pvalue' in stats_df.columns:
            axes[2, 1].scatter(stats_df['t_pvalue'], stats_df['bounded_t_pvalue'], alpha=0.6)
            axes[2, 1].plot([0, 1], [0, 1], 'r--', alpha=0.5)
            axes[2, 1].set_xlabel('Full Length T-test P-value')
            axes[2, 1].set_ylabel('Bounded Length T-test P-value')
            axes[2, 1].set_title('Statistical Significance Comparison')
        
        # Plot 11: Effect size comparison
        if 'bounded_cohens_d' in stats_df.columns and 'cohens_d' in stats_df.columns:
            axes[2, 2].scatter(stats_df['cohens_d'], stats_df['bounded_cohens_d'], alpha=0.6)
            axes[2, 2].plot([stats_df['cohens_d'].min(), stats_df['cohens_d'].max()], 
                           [stats_df['cohens_d'].min(), stats_df['cohens_d'].max()], 'r--', alpha=0.5)
            axes[2, 2].set_xlabel("Full Length Cohen's d")
            axes[2, 2].set_ylabel("Bounded Length Cohen's d")
            axes[2, 2].set_title('Effect Size Comparison')
        
        # Plot 12: Summary statistics
        axes[2, 3].axis('off')
        summary_text = "SUMMARY STATISTICS\n\n"
        
        if classification_results:
            avg_diff = np.mean([classification_results[key]['difference'] for key in classification_results 
                               if 'difference' in classification_results[key]])
            summary_text += f"Avg AUROC Difference: {avg_diff:+.4f}\n"
        
        if len(importance_df) > 0:
            avg_ratio = importance_df['importance_ratio'].mean()
            avg_corr = importance_df['correlation'].mean()
            summary_text += f"Avg Importance Ratio: {avg_ratio:.3f}\n"
            summary_text += f"Avg Feature Correlation: {avg_corr:.3f}\n"
            summary_text += f"Features favoring bounded: {np.sum(importance_df['importance_ratio'] > 1)}/{len(importance_df)}\n"
        
        axes[2, 3].text(0.1, 0.9, summary_text, transform=axes[2, 3].transAxes, 
                       fontsize=12, verticalalignment='top', fontfamily='monospace')
        
        plt.tight_layout()
        plt.savefig('length_comparison_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"   ✅ Saved length comparison visualization to 'length_comparison_analysis.png'")
        
    except Exception as e:
        print(f"   ⚠️  Visualization error: {e}")

def main():
    """Main analysis function for pre/post segment analysis with length comparison"""
    print("🔍 PRE/POST SEGMENT ANALYSIS WITH LENGTH COMPARISON")
    print("=" * 60)
    
    # Load data with labels
    time_series_list, series_ids, labels = load_time_series_data_with_labels()
    if time_series_list is None:
        print("❌ Failed to load data")
        return
    
    # Split into pre/post segments and get minimal lengths
    pre_segments, post_segments, segment_labels, segment_types, original_series_ids, min_pre_length, min_post_length = split_series_pre_post(
        time_series_list, labels
    )
    
    print(f"\n📏 MINIMAL SEGMENT LENGTHS:")
    print(f"   Minimum pre-segment length: {min_pre_length}")
    print(f"   Minimum post-segment length: {min_post_length}")
    
    # Analyze pre/post statistics with both full and bounded features
    stats_df = analyze_pre_post_statistics(
        pre_segments, post_segments, segment_labels, segment_types, original_series_ids,
        min_pre_length, min_post_length
    )
    
    # Compare classification performance
    classification_results, comparison_results = compare_classification_performance(stats_df)
    
    # Analyze feature importance differences
    importance_df = analyze_feature_importance_comparison(stats_df)
    
    # Create comprehensive visualization
    create_length_comparison_visualization(stats_df, comparison_results, importance_df)
    
    # Analyze segment type patterns
    normal_normal, normal_break = analyze_segment_type_patterns(stats_df)
    
    # Perform clustering analysis
    clustering_results, optimal_k, feature_names, X_scaled = analyze_pre_post_clustering(stats_df)
    clustering_results['X_scaled'] = X_scaled
    
    # Create visualizations
    create_pre_post_visualizations(stats_df, clustering_results, optimal_k, feature_names)
    
    # Analyze prepared features with CCA
    prepared_df, prepared_labels, prepared_clusters, cca, canonical_corrs, cluster_purities = analyze_prepared_features_with_cca()
    
    # Analyze calm vs volatile patterns
    volatility_df = analyze_calm_vs_volatile_patterns()
    
    # Save results
    stats_df.to_csv('pre_post_segment_statistics.csv', index=False)
    
    # Save classification comparison results
    if comparison_results:
        comparison_df = pd.DataFrame([
            {
                'comparison': key,
                'full_score': data['full_score'],
                'bounded_score': data['bounded_score'],
                'difference': data['difference'],
                'pct_change': data['pct_change']
            }
            for key, data in comparison_results.items()
        ])
        comparison_df.to_csv('length_classification_comparison.csv', index=False)
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Pre/post statistics: 'pre_post_segment_statistics.csv'")
    print(f"   📊 Classification comparison: 'length_classification_comparison.csv'")
    print(f"   📊 Feature importance comparison: 'feature_importance_comparison.csv'")
    print(f"   📈 Length comparison visualization: 'length_comparison_analysis.png'")
    print(f"   📈 Analysis visualization: 'pre_post_segment_analysis.png'")
    print(f"   📈 CCA features visualization: 'cca_features_analysis.png'")
    print(f"   📈 Volatility pattern visualization: 'volatility_pattern_analysis.png'")
    
    return stats_df, clustering_results, normal_normal, normal_break, prepared_df, volatility_df, classification_results, importance_df

def analyze_deviation_patterns(stats_df):
    """Analyze how time series deviate from overall patterns"""
    print("\n📈 DEVIATION PATTERN ANALYSIS")
    print("=" * 50)
    
    # Overall statistics
    overall_mean_of_means = stats_df['mean'].mean()
    overall_std_of_means = stats_df['mean'].std()
    overall_mean_of_stds = stats_df['std'].mean()
    overall_std_of_stds = stats_df['std'].std()
    
    print(f"📊 OVERALL PATTERNS:")
    print(f"   Mean of series means: {overall_mean_of_means:.6f}")
    print(f"   Std of series means:  {overall_std_of_means:.6f}")
    print(f"   Mean of series stds:  {overall_mean_of_stds:.6f}")
    print(f"   Std of series stds:   {overall_std_of_stds:.6f}")
    
    # Analyze mean deviations
    mean_deviations = stats_df['mean'] - overall_mean_of_means
    mean_z_scores = mean_deviations / overall_std_of_means if overall_std_of_means > 0 else np.zeros_like(mean_deviations)
    
    print(f"\n📏 MEAN DEVIATION ANALYSIS:")
    print(f"   Mean deviation range: [{mean_deviations.min():.6f}, {mean_deviations.max():.6f}]")
    print(f"   Mean deviation std:   {mean_deviations.std():.6f}")
    print(f"   Series with |z| > 2:  {np.sum(np.abs(mean_z_scores) > 2)} ({np.sum(np.abs(mean_z_scores) > 2)/len(stats_df)*100:.1f}%)")
    print(f"   Series with |z| > 3:  {np.sum(np.abs(mean_z_scores) > 3)} ({np.sum(np.abs(mean_z_scores) > 3)/len(stats_df)*100:.1f}%)")
    
    # Analyze std deviations
    std_deviations = stats_df['std'] - overall_mean_of_stds
    std_z_scores = std_deviations / overall_std_of_stds if overall_std_of_stds > 0 else np.zeros_like(std_deviations)
    
    print(f"\n📐 STD DEVIATION ANALYSIS:")
    print(f"   Std deviation range:  [{std_deviations.min():.6f}, {std_deviations.max():.6f}]")
    print(f"   Std deviation std:    {std_deviations.std():.6f}")
    print(f"   Series with |z| > 2:  {np.sum(np.abs(std_z_scores) > 2)} ({np.sum(np.abs(std_z_scores) > 2)/len(stats_df)*100:.1f}%)")
    print(f"   Series with |z| > 3:  {np.sum(np.abs(std_z_scores) > 3)} ({np.sum(np.abs(std_z_scores) > 3)/len(stats_df)*100:.1f}%)")
    
    # Coefficient of variation analysis
    cv_finite = stats_df['cv'][np.isfinite(stats_df['cv'])]
    print(f"\n🔄 COEFFICIENT OF VARIATION ANALYSIS:")
    print(f"   CV range:             [{cv_finite.min():.6f}, {cv_finite.max():.6f}]")
    print(f"   CV mean:              {cv_finite.mean():.6f}")
    print(f"   CV std:               {cv_finite.std():.6f}")
    print(f"   High variability (CV>1): {np.sum(cv_finite > 1)} ({np.sum(cv_finite > 1)/len(cv_finite)*100:.1f}%)")
    
    return {
        'mean_deviations': mean_deviations,
        'std_deviations': std_deviations,
        'mean_z_scores': mean_z_scores,
        'std_z_scores': std_z_scores,
        'overall_stats': {
            'mean_of_means': overall_mean_of_means,
            'std_of_means': overall_std_of_means,
            'mean_of_stds': overall_mean_of_stds,
            'std_of_stds': overall_std_of_stds
        }
    }

def analyze_outlier_patterns(stats_df):
    """Analyze outlier patterns across time series"""
    print("\n🎯 OUTLIER PATTERN ANALYSIS")
    print("=" * 50)
    
    # IQR outliers
    total_outliers_iqr = stats_df['n_outliers_iqr'].sum()
    total_points = stats_df['length'].sum()
    outlier_rate_iqr = total_outliers_iqr / total_points * 100
    
    print(f"📊 IQR OUTLIER ANALYSIS:")
    print(f"   Total outliers:       {total_outliers_iqr}")
    print(f"   Total data points:    {total_points}")
    print(f"   Overall outlier rate: {outlier_rate_iqr:.3f}%")
    print(f"   Series with outliers: {np.sum(stats_df['n_outliers_iqr'] > 0)} ({np.sum(stats_df['n_outliers_iqr'] > 0)/len(stats_df)*100:.1f}%)")
    
    # Z-score outliers
    total_outliers_zscore = stats_df['n_outliers_zscore'].sum()
    outlier_rate_zscore = total_outliers_zscore / total_points * 100
    
    print(f"\n📊 Z-SCORE OUTLIER ANALYSIS:")
    print(f"   Total outliers:       {total_outliers_zscore}")
    print(f"   Overall outlier rate: {outlier_rate_zscore:.3f}%")
    print(f"   Series with outliers: {np.sum(stats_df['n_outliers_zscore'] > 0)} ({np.sum(stats_df['n_outliers_zscore'] > 0)/len(stats_df)*100:.1f}%)")
    
    # Outlier distribution
    print(f"\n📈 OUTLIER DISTRIBUTION:")
    outlier_counts_iqr = stats_df['n_outliers_iqr'].value_counts().sort_index()
    print(f"   IQR outliers per series:")
    for count, freq in outlier_counts_iqr.head(10).items():
        print(f"     {count} outliers: {freq} series ({freq/len(stats_df)*100:.1f}%)")
    
    return {
        'total_outliers_iqr': total_outliers_iqr,
        'total_outliers_zscore': total_outliers_zscore,
        'outlier_rate_iqr': outlier_rate_iqr,
        'outlier_rate_zscore': outlier_rate_zscore
    }

def analyze_scale_patterns(stats_df):
    """Analyze scale and magnitude patterns"""
    print("\n⚖️ SCALE PATTERN ANALYSIS")
    print("=" * 50)
    
    # Magnitude analysis
    abs_means = np.abs(stats_df['mean'])
    ranges = stats_df['range']
    
    print(f"📏 MAGNITUDE ANALYSIS:")
    print(f"   Absolute mean range:  [{abs_means.min():.6f}, {abs_means.max():.6f}]")
    print(f"   Range range:          [{ranges.min():.6f}, {ranges.max():.6f}]")
    
    # Scale categories
    small_scale = np.sum(abs_means < 0.1)
    medium_scale = np.sum((abs_means >= 0.1) & (abs_means < 1.0))
    large_scale = np.sum(abs_means >= 1.0)
    
    print(f"\n📊 SCALE DISTRIBUTION:")
    print(f"   Small scale (|mean| < 0.1):   {small_scale} ({small_scale/len(stats_df)*100:.1f}%)")
    print(f"   Medium scale (0.1 ≤ |mean| < 1): {medium_scale} ({medium_scale/len(stats_df)*100:.1f}%)")
    print(f"   Large scale (|mean| ≥ 1):     {large_scale} ({large_scale/len(stats_df)*100:.1f}%)")
    
    # Zero-centered analysis
    near_zero = np.sum(abs_means < 0.01)
    print(f"\n🎯 ZERO-CENTERED ANALYSIS:")
    print(f"   Near-zero mean (|mean| < 0.01): {near_zero} ({near_zero/len(stats_df)*100:.1f}%)")
    
    return {
        'scale_distribution': {
            'small': small_scale,
            'medium': medium_scale,
            'large': large_scale,
            'near_zero': near_zero
        }
    }

def generate_summary_report(stats_df, deviation_analysis, outlier_analysis, scale_analysis):
    """Generate comprehensive summary report"""
    print("\n" + "=" * 60)
    print("📋 COMPREHENSIVE SUMMARY REPORT")
    print("=" * 60)
    
    n_series = len(stats_df)
    
    print(f"📊 DATASET OVERVIEW:")
    print(f"   Total time series:    {n_series}")
    print(f"   Average length:       {stats_df['length'].mean():.1f}")
    print(f"   Length range:         [{stats_df['length'].min()}, {stats_df['length'].max()}]")
    
    print(f"\n📈 CENTRAL TENDENCY:")
    print(f"   Mean of means:        {deviation_analysis['overall_stats']['mean_of_means']:.6f}")
    print(f"   Std of means:         {deviation_analysis['overall_stats']['std_of_means']:.6f}")
    print(f"   Mean of stds:         {deviation_analysis['overall_stats']['mean_of_stds']:.6f}")
    print(f"   Std of stds:          {deviation_analysis['overall_stats']['std_of_stds']:.6f}")
    
    print(f"\n🎯 DEVIATION PATTERNS:")
    extreme_mean_dev = np.sum(np.abs(deviation_analysis['mean_z_scores']) > 3)
    extreme_std_dev = np.sum(np.abs(deviation_analysis['std_z_scores']) > 3)
    print(f"   Extreme mean deviations:  {extreme_mean_dev} ({extreme_mean_dev/n_series*100:.1f}%)")
    print(f"   Extreme std deviations:   {extreme_std_dev} ({extreme_std_dev/n_series*100:.1f}%)")
    
    print(f"\n🔍 OUTLIER SUMMARY:")
    print(f"   IQR outlier rate:     {outlier_analysis['outlier_rate_iqr']:.3f}%")
    print(f"   Z-score outlier rate: {outlier_analysis['outlier_rate_zscore']:.3f}%")
    
    print(f"\n⚖️ SCALE SUMMARY:")
    scale_dist = scale_analysis['scale_distribution']
    print(f"   Small scale series:   {scale_dist['small']} ({scale_dist['small']/n_series*100:.1f}%)")
    print(f"   Medium scale series:  {scale_dist['medium']} ({scale_dist['medium']/n_series*100:.1f}%)")
    print(f"   Large scale series:   {scale_dist['large']} ({scale_dist['large']/n_series*100:.1f}%)")
    print(f"   Near-zero centered:   {scale_dist['near_zero']} ({scale_dist['near_zero']/n_series*100:.1f}%)")

def main_original():
    """Original main analysis function with clustering"""
    print("🔍 TIME SERIES INPUT STANDARDIZATION & CLUSTERING ANALYSIS")
    print("=" * 60)
    
    # Load data
    time_series_list, series_ids, labels = load_time_series_data_with_labels()
    if time_series_list is None:
        print("❌ Failed to load data")
        return
    
    # Analyze comprehensive statistics
    stats_df = analyze_series_statistics(time_series_list)
    print(f"   ✅ Analyzed {len(stats_df)} time series")
    
    # Prepare clustering features
    X, feature_names = prepare_clustering_features(stats_df)
    
    # Perform clustering
    clustering_results = perform_clustering_analysis(X, stats_df)
    
    # Analyze cluster characteristics
    cluster_summaries, stats_with_clusters = analyze_cluster_characteristics(
        stats_df, clustering_results, feature_names
    )
    
    # Create visualizations
    visualize_clustering_results(stats_df, clustering_results, feature_names)
    
    # Also run original deviation analysis
    deviation_analysis = analyze_deviation_patterns(stats_df)
    outlier_analysis = analyze_outlier_patterns(stats_df)
    scale_analysis = analyze_scale_patterns(stats_df)
    generate_summary_report(stats_df, deviation_analysis, outlier_analysis, scale_analysis)
    
    # Save results
    stats_with_clusters.to_csv('time_series_statistics_with_clusters.csv', index=False)
    print(f"\n💾 Results saved:")
    print(f"   📊 Statistics with clusters: 'time_series_statistics_with_clusters.csv'")
    print(f"   📈 Clustering visualization: 'time_series_clustering_analysis.png'")
    
    return stats_df, clustering_results, cluster_summaries

def analyze_series_statistics(time_series_list):
    """Analyze comprehensive statistics of each time series"""
    print("\n🔍 ANALYZING COMPREHENSIVE TIME SERIES STATISTICS")
    print("=" * 50)
    
    all_stats = []
    
    for i, series in enumerate(time_series_list):
        if i % 1000 == 0:
            print(f"   Processing series {i+1}/{len(time_series_list)}...")
        
        stats_dict = compute_deep_statistics(series)
        stats_dict['series_id'] = i
        all_stats.append(stats_dict)
    
    stats_df = pd.DataFrame(all_stats)
    print(f"   ✅ Computed {len(stats_df.columns)-1} statistical features for {len(stats_df)} series")
    
    return stats_df

if __name__ == "__main__":
    results = main()