#!/usr/bin/env python3
"""
Test script for the analyze_truncation_in_series_list function
Demonstrates batch analysis of multiple time series for truncation detection
"""

import numpy as np
import matplotlib.pyplot as plt
from Thermodynamics_truncated import analyze_truncation_in_series_list

def create_mixed_dataset():
    """Create a mixed dataset with various truncation patterns"""
    np.random.seed(42)
    
    series_list = []
    series_labels = []
    
    # 1. Clean series (5 series)
    for i in range(5):
        clean_data = np.random.normal(0, 1, 200)
        series_list.append(clean_data)
        series_labels.append(f'clean_{i}')
    
    # 2. Precision truncated series (3 series)
    for i in range(3):
        raw_data = np.random.normal(0, 1, 200)
        precision_data = np.round(raw_data, 2)  # Round to 2 decimal places
        series_list.append(precision_data)
        series_labels.append(f'precision_truncated_{i}')
    
    # 3. Boundary truncated series (4 series)
    for i in range(4):
        raw_data = np.random.normal(0, 1, 200)
        boundary_data = np.clip(raw_data, -0.5, 0.5)  # Clip to boundaries
        series_list.append(boundary_data)
        series_labels.append(f'boundary_truncated_{i}')
    
    # 4. Quantized series (2 series)
    for i in range(2):
        raw_data = np.random.normal(0, 1, 200)
        quantized_data = np.round(raw_data / 0.1) * 0.1  # Quantize to 0.1 steps
        series_list.append(quantized_data)
        series_labels.append(f'quantized_{i}')
    
    # 5. Mixed truncation (severe) (3 series)
    for i in range(3):
        raw_data = np.random.normal(0, 1, 200)
        mixed_data = np.clip(raw_data, -0.3, 0.3)  # Boundary truncation
        mixed_data = np.round(mixed_data, 1)  # Precision truncation
        series_list.append(mixed_data)
        series_labels.append(f'mixed_severe_{i}')
    
    # 6. Flat segments (2 series)
    for i in range(2):
        raw_data = np.random.normal(0, 1, 200)
        # Set random segments to constant values
        flat_indices = np.random.choice(200, 50, replace=False)
        raw_data[flat_indices] = 0.0
        series_list.append(raw_data)
        series_labels.append(f'flat_segments_{i}')
    
    # 7. Subtle truncation (3 series)
    for i in range(3):
        raw_data = np.random.normal(0, 1, 200)
        # Slightly bias towards round numbers
        for j in range(len(raw_data)):
            if np.random.random() < 0.15:  # 15% chance
                raw_data[j] = np.round(raw_data[j] * 2) / 2  # Round to nearest 0.5
        series_list.append(raw_data)
        series_labels.append(f'subtle_truncation_{i}')
    
    # 8. Invalid series (1 series) - too short
    series_list.append(np.array([1.0]))
    series_labels.append('invalid_short')
    
    # 9. Invalid series (1 series) - all NaN
    series_list.append(np.full(100, np.nan))
    series_labels.append('invalid_nan')
    
    return series_list, series_labels

def test_basic_analysis():
    """Test basic truncation analysis functionality"""
    print("🧪 Testing Basic Truncation Analysis")
    print("=" * 60)
    
    # Create test dataset
    series_list, series_labels = create_mixed_dataset()
    
    print(f"Created test dataset with {len(series_list)} time series:")
    print(f"  - 5 clean series")
    print(f"  - 3 precision truncated series")
    print(f"  - 4 boundary truncated series")
    print(f"  - 2 quantized series")
    print(f"  - 3 mixed (severe) truncation series")
    print(f"  - 2 flat segment series")
    print(f"  - 3 subtle truncation series")
    print(f"  - 2 invalid series")
    
    # Analyze with default parameters
    results = analyze_truncation_in_series_list(
        series_list=series_list,
        truncation_threshold=0.5,
        calibrate_detector=True,
        detailed_output=False,
        n_jobs=2
    )
    
    return results, series_labels

def test_detailed_analysis():
    """Test detailed analysis with lower threshold"""
    print("\n\n🔬 Testing Detailed Analysis (Lower Threshold)")
    print("=" * 60)
    
    # Create smaller test dataset
    series_list, series_labels = create_mixed_dataset()
    
    # Analyze with detailed output and lower threshold
    results = analyze_truncation_in_series_list(
        series_list=series_list,
        truncation_threshold=0.7,  # More sensitive threshold
        calibrate_detector=True,
        detailed_output=True,
        n_jobs=2
    )
    
    # Show detailed results for truncated series
    if results['truncated_indices']:
        print(f"\n📋 Detailed Results for Truncated Series:")
        print("-" * 40)
        
        for idx in results['truncated_indices'][:5]:  # Show first 5
            if idx < len(series_labels):
                print(f"\n🔍 Series {idx} ({series_labels[idx]}):")
                if 'detailed_results' in results and idx in results['detailed_results']:
                    metrics = results['detailed_results'][idx]
                    print(f"   Truncation Confidence: {metrics['truncation_confidence']:.3f}")
                    print(f"   Precision Quality: {metrics['precision_quality_score']:.3f}")
                    print(f"   Value Quality: {metrics['value_quality_score']:.3f}")
                    print(f"   Boundary Constraint: {metrics['boundary_constraint_factor']:.3f}")
                    print(f"   Clustering Artifacts: {metrics['clustering_artifact_score']:.3f}")
                    print(f"   Flat Segments: {metrics['flat_segment_ratio']:.3f}")
    
    return results, series_labels

def test_without_calibration():
    """Test analysis without detector calibration"""
    print("\n\n⚙️  Testing Analysis Without Calibration")
    print("=" * 60)
    
    # Create test dataset
    series_list, series_labels = create_mixed_dataset()
    
    # Analyze without calibration
    results = analyze_truncation_in_series_list(
        series_list=series_list,
        truncation_threshold=0.5,
        calibrate_detector=False,  # No calibration
        detailed_output=False,
        n_jobs=2
    )
    
    return results, series_labels

def create_visualization(results_list, labels_list):
    """Create visualization comparing different analysis approaches"""
    print("\n\n📊 Creating Comparison Visualization...")
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Truncation Analysis Comparison', fontsize=16)
    
    # Extract data for plotting
    approach_names = ['Basic (threshold=0.5)', 'Detailed (threshold=0.7)', 'No Calibration']
    truncation_rates = []
    mean_confidences = []
    severe_counts = []
    
    for results in results_list:
        summary = results['truncation_summary']
        truncation_rates.append(summary['truncation_rate'])
        mean_confidences.append(summary.get('mean_truncation_confidence', 0))
        severe_counts.append(len(summary.get('severe_truncation_indices', [])))
    
    # Plot 1: Truncation Detection Rates
    axes[0, 0].bar(approach_names, truncation_rates, color=['skyblue', 'lightcoral', 'lightgreen'])
    axes[0, 0].set_title('Truncation Detection Rates')
    axes[0, 0].set_ylabel('Truncation Rate')
    axes[0, 0].tick_params(axis='x', rotation=45)
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Mean Truncation Confidence
    axes[0, 1].bar(approach_names, mean_confidences, color=['orange', 'purple', 'brown'])
    axes[0, 1].set_title('Mean Truncation Confidence')
    axes[0, 1].set_ylabel('Confidence Score')
    axes[0, 1].tick_params(axis='x', rotation=45)
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Severe Truncation Counts
    axes[1, 0].bar(approach_names, severe_counts, color=['red', 'darkred', 'maroon'])
    axes[1, 0].set_title('Severe Truncation Detection')
    axes[1, 0].set_ylabel('Count of Severe Cases')
    axes[1, 0].tick_params(axis='x', rotation=45)
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Summary Statistics
    basic_results = results_list[0]
    summary = basic_results['truncation_summary']
    
    categories = ['Total', 'Valid', 'Invalid', 'Truncated']
    counts = [
        summary['total_series'],
        summary['valid_series'], 
        summary['invalid_series'],
        summary['truncated_series_count']
    ]
    colors = ['lightblue', 'lightgreen', 'lightcoral', 'orange']
    
    axes[1, 1].bar(categories, counts, color=colors)
    axes[1, 1].set_title('Dataset Composition (Basic Analysis)')
    axes[1, 1].set_ylabel('Count')
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('series_list_truncation_analysis_results.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visualization saved as 'series_list_truncation_analysis_results.png'")

def demonstrate_practical_usage():
    """Demonstrate practical usage scenarios"""
    print("\n\n💼 Practical Usage Demonstration")
    print("=" * 60)
    
    # Scenario 1: Quick screening of a large dataset
    print("\n📋 Scenario 1: Quick Dataset Screening")
    print("-" * 40)
    
    # Create a larger dataset
    np.random.seed(123)
    large_series_list = []
    
    # Mix of clean and truncated data
    for i in range(50):
        if i < 30:  # 30 clean series
            data = np.random.normal(0, 1, 150)
        else:  # 20 truncated series
            data = np.random.normal(0, 1, 150)
            if i % 2 == 0:
                data = np.clip(data, -0.4, 0.4)  # Boundary truncation
            else:
                data = np.round(data, 1)  # Precision truncation
        large_series_list.append(data)
    
    # Quick analysis
    quick_results = analyze_truncation_in_series_list(
        series_list=large_series_list,
        truncation_threshold=0.6,
        calibrate_detector=True,
        detailed_output=False,
        n_jobs=-1  # Use all available cores
    )
    
    print(f"Quick screening results:")
    print(f"  - Analyzed {len(large_series_list)} series")
    print(f"  - Found {len(quick_results['truncated_indices'])} potentially truncated series")
    print(f"  - Truncation rate: {quick_results['truncation_summary']['truncation_rate']:.1%}")
    
    # Scenario 2: Detailed investigation of flagged series
    print(f"\n📋 Scenario 2: Detailed Investigation")
    print("-" * 40)
    
    if quick_results['truncated_indices']:
        # Take first few flagged series for detailed analysis
        flagged_indices = quick_results['truncated_indices'][:5]
        flagged_series = [large_series_list[i] for i in flagged_indices]
        
        detailed_results = analyze_truncation_in_series_list(
            series_list=flagged_series,
            truncation_threshold=0.5,
            calibrate_detector=False,  # Already calibrated
            detailed_output=True,
            n_jobs=2
        )
        
        print(f"Detailed investigation of {len(flagged_series)} flagged series:")
        for i, original_idx in enumerate(flagged_indices):
            if i in detailed_results['detailed_results']:
                metrics = detailed_results['detailed_results'][i]
                print(f"  Series {original_idx}: confidence={metrics['truncation_confidence']:.3f}")

def main():
    """Run all tests and demonstrations"""
    print("🚀 Testing analyze_truncation_in_series_list Function")
    print("=" * 80)
    
    # Run tests
    results1, labels1 = test_basic_analysis()
    results2, labels2 = test_detailed_analysis()
    results3, labels3 = test_without_calibration()
    
    # Create visualization
    create_visualization([results1, results2, results3], [labels1, labels2, labels3])
    
    # Demonstrate practical usage
    demonstrate_practical_usage()
    
    print("\n\n🎉 All tests completed successfully!")
    print("\nKey Features Demonstrated:")
    print("  ✅ Batch analysis of multiple time series")
    print("  ✅ Adaptive calibration on provided dataset")
    print("  ✅ Configurable truncation thresholds")
    print("  ✅ Detailed vs. summary output options")
    print("  ✅ Parallel processing for efficiency")
    print("  ✅ Severity categorization (severe/moderate/mild)")
    print("  ✅ Robust handling of invalid series")
    print("  ✅ Comprehensive summary statistics")
    
    print(f"\nFunction Usage:")
    print(f"```python")
    print(f"from Thermodynamics_truncated import analyze_truncation_in_series_list")
    print(f"")
    print(f"results = analyze_truncation_in_series_list(")
    print(f"    series_list=your_time_series_list,")
    print(f"    truncation_threshold=0.5,  # Adjust sensitivity")
    print(f"    calibrate_detector=True,   # Learn from your data")
    print(f"    detailed_output=True,      # Get detailed metrics")
    print(f"    n_jobs=-1                  # Use all CPU cores")
    print(f")")
    print(f"")
    print(f"# Get indices of truncated series")
    print(f"truncated_indices = results['truncated_indices']")
    print(f"```")

if __name__ == "__main__":
    main()