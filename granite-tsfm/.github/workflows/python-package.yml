# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: Python package
permissions:
  contents: read

on:
  push:
    branches: [ "main", "destiny", "destiny_v1_patches" ]
  pull_request:
    branches: [ "main", "destiny", "destiny_v1_patches" ]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.9", "3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        # python -m pip install flake8 pytest
        # if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
        pip install -e ".[dev,testing]"
    - name: Ruff quality checks
      run: |
        # use hf-style check
        make quality
    - name: Test with pytest
      run: |
        pytest --cov=tsfm_public --cov-fail-under=75 --cov-report term-missing tests
