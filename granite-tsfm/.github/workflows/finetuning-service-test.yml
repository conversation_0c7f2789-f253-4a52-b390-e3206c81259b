# This workflow will install Python dependencies, run tests and lint with a variety of Python versions
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python

name: tsfmfinetuning Service Tests

on:
  push:
    branches: [ "main", "destiny", "destiny_v1_patches" ]
  pull_request:
    branches: [ "main", "destiny", "destiny_v1_patches" ]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", "3.11", "3.12"]

    steps:
    - uses: actions/checkout@v4
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
    - name: Install deps into a virtual enviroment
      run: |
        python -m venv .venv
        source .venv/bin/activate
        python -m pip install --upgrade pip
        pip install poetry
        cd services/finetuning
        poetry self add "poetry-dynamic-versioning[plugin]"
        poetry install -n --with dev
    # for not we're not really using the image
    # for much, we just want to make sure
    # that it continues to build until we
    # do so
    # No need as image is now dependent on instance build
    # which gets run as part of instance tests
    # - name: image build
    #  run: |
    #    source .venv/bin/activate
    #    cd services/finetuning
    #    make image
    - name: test local
      run: |
        source .venv/bin/activate
        cd services/finetuning
        make test_local
        