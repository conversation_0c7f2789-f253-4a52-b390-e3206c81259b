[loggers]
keys=root,file

[handlers]
keys=console,file

[formatters]
keys=console,file

[logger_root]
level=DEBUG
handlers=console,file

[logger_file]
level=DEBUG
handlers=file
qualname=file

[handler_console]
class=StreamHandler
level=DEBUG
formatter=console
args=(sys.stdout,)

[handler_file]
class=FileHandler
level=DEBUG
formatter=file
args=('server.log','w',)

[formatter_console]
format=%(asctime)s - %(levelname)s - %(name)s - %(message)s

[formatter_file]
format=%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s
