# This file was automatically generated on Thu Jun 12 11:36:25 AM EDT 2025, your edits will be replaced.
accelerate==1.7.0 ; python_version >= "3.10" and python_version < "3.13"
aiohappyeyeballs==2.6.1 ; python_version >= "3.10" and python_version < "3.13"
aiohttp==3.11.18 ; python_version >= "3.10" and python_version < "3.13"
aiosignal==1.3.2 ; python_version >= "3.10" and python_version < "3.13"
annotated-types==0.7.0 ; python_version >= "3.10" and python_version < "3.13"
anyio==4.9.0 ; python_version >= "3.10" and python_version < "3.13"
async-timeout==5.0.1 ; python_version == "3.10"
attrs==23.2.0 ; python_version >= "3.10" and python_version < "3.13"
certifi==2025.4.26 ; python_version >= "3.10" and python_version < "3.13"
charset-normalizer==3.4.2 ; python_version >= "3.10" and python_version < "3.13"
click==8.2.1 ; python_version >= "3.10" and python_version < "3.13"
colorama==0.4.6 ; python_version >= "3.10" and python_version < "3.13" and platform_system == "Windows"
datasets==3.6.0 ; python_version >= "3.10" and python_version < "3.13"
deprecated==1.2.18 ; python_version >= "3.10" and python_version < "3.13"
dill==0.3.8 ; python_version >= "3.10" and python_version < "3.13"
exceptiongroup==1.3.0 ; python_version == "3.10"
fastapi==0.115.12 ; python_version >= "3.10" and python_version < "3.13"
filelock==3.18.0 ; python_version >= "3.10" and python_version < "3.13"
frozenlist==1.6.0 ; python_version >= "3.10" and python_version < "3.13"
fsspec==2025.3.0 ; python_version >= "3.10" and python_version < "3.13"
granite-tsfm==0.2.28 ; python_version >= "3.10" and python_version < "3.13"
gunicorn==23.0.0 ; python_version >= "3.10" and python_version < "3.13"
h11==0.16.0 ; python_version >= "3.10" and python_version < "3.13"
huggingface-hub==0.31.4 ; python_version >= "3.10" and python_version < "3.13"
idna==3.10 ; python_version >= "3.10" and python_version < "3.13"
jinja2==3.1.6 ; python_version >= "3.10" and python_version < "3.13"
joblib==1.5.0 ; python_version >= "3.10" and python_version < "3.13"
markupsafe==3.0.2 ; python_version >= "3.10" and python_version < "3.13"
mpmath==1.3.0 ; python_version >= "3.10" and python_version < "3.13"
multidict==6.4.4 ; python_version >= "3.10" and python_version < "3.13"
multiprocess==0.70.16 ; python_version >= "3.10" and python_version < "3.13"
networkx==3.4.2 ; python_version >= "3.10" and python_version < "3.13"
numpy==1.26.4 ; python_version >= "3.10" and python_version < "3.13"
nvidia-cublas-cu12==******** ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cuda-cupti-cu12==12.6.80 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cuda-nvrtc-cu12==12.6.77 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cuda-runtime-cu12==12.6.77 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cudnn-cu12==******** ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cufft-cu12==******** ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cufile-cu12==1.11.1.6 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-curand-cu12==10.3.7.77 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cusolver-cu12==11.7.1.2 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cusparse-cu12==12.5.4.2 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-cusparselt-cu12==0.6.3 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-nccl-cu12==2.26.2 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-nvjitlink-cu12==12.6.85 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
nvidia-nvtx-cu12==12.6.77 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
packaging==24.2 ; python_version >= "3.10" and python_version < "3.13"
pandas==2.2.3 ; python_version >= "3.10" and python_version < "3.13"
prometheus-client==0.22.0 ; python_version >= "3.10" and python_version < "3.13"
propcache==0.3.1 ; python_version >= "3.10" and python_version < "3.13"
psutil==7.0.0 ; python_version >= "3.10" and python_version < "3.13"
pyarrow==20.0.0 ; python_version >= "3.10" and python_version < "3.13"
pydantic-core==2.33.2 ; python_version >= "3.10" and python_version < "3.13"
pydantic==2.11.4 ; python_version >= "3.10" and python_version < "3.13"
python-dateutil==2.9.0.post0 ; python_version >= "3.10" and python_version < "3.13"
pytz==2025.2 ; python_version >= "3.10" and python_version < "3.13"
pyyaml==6.0.2 ; python_version >= "3.10" and python_version < "3.13"
regex==2024.11.6 ; python_version >= "3.10" and python_version < "3.13"
requests==2.32.3 ; python_version >= "3.10" and python_version < "3.13"
safetensors==0.5.3 ; python_version >= "3.10" and python_version < "3.13"
scikit-learn==1.6.1 ; python_version >= "3.10" and python_version < "3.13"
scipy==1.15.3 ; python_version >= "3.10" and python_version < "3.13"
setuptools==80.8.0 ; python_version >= "3.10" and python_version < "3.13"
six==1.17.0 ; python_version >= "3.10" and python_version < "3.13"
sniffio==1.3.1 ; python_version >= "3.10" and python_version < "3.13"
starlette==0.46.2 ; python_version >= "3.10" and python_version < "3.13"
sympy==1.14.0 ; python_version >= "3.10" and python_version < "3.13"
threadpoolctl==3.6.0 ; python_version >= "3.10" and python_version < "3.13"
tokenizers==0.21.1 ; python_version >= "3.10" and python_version < "3.13"
torch==2.7.0 ; python_version >= "3.10" and python_version < "3.13"
tqdm==4.67.1 ; python_version >= "3.10" and python_version < "3.13"
transformers==4.50.3 ; python_version >= "3.10" and python_version < "3.13"
triton==3.3.0 ; python_version >= "3.10" and python_version < "3.13" and platform_machine == "x86_64" and platform_system == "Linux"
typing-extensions==4.13.2 ; python_version >= "3.10" and python_version < "3.13"
typing-inspection==0.4.0 ; python_version >= "3.10" and python_version < "3.13"
tzdata==2025.2 ; python_version >= "3.10" and python_version < "3.13"
urllib3==2.4.0 ; python_version >= "3.10" and python_version < "3.13"
uvicorn-worker==0.3.0 ; python_version >= "3.10" and python_version < "3.13"
uvicorn==0.34.2 ; python_version >= "3.10" and python_version < "3.13"
werkzeug==3.1.3 ; python_version >= "3.10" and python_version < "3.13"
wrapt==1.17.2 ; python_version >= "3.10" and python_version < "3.13"
xxhash==3.5.0 ; python_version >= "3.10" and python_version < "3.13"
yarl==1.20.0 ; python_version >= "3.10" and python_version < "3.13"
