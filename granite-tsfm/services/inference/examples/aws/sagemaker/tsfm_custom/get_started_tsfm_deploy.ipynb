{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Deploy a TSFM Inference Endpoint on Amazon Sagemaker"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "In this notebook, we walk through the process of deploying an IBM granite timeseries inference service enpoint on Amazon SageMaker. This notebook will recreate the steps taken in [this tsfm-granite recipe](https://github.com/ibm-granite-community/granite-timeseries-cookbook/blob/main/recipes/Time_Series/Time_Series_Getting_Started.ipynb) with the only difference being that the deployment and inference will be on AWS Sagemaker."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Cloning into 'granite-tsfm'...\n", "Cloning into 'mytest-tsfm'...\n", "Filtering content: 100% (15/15), 104.18 MiB | 30.18 MiB/s, done.\n"]}, {"data": {"text/plain": ["CompletedProcess(args=['make', 'clone_models'], returncode=0)"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["# obtain tsfm requirements\n", "import shutil\n", "import subprocess\n", "from pathlib import Path\n", "\n", "\n", "# so that some code directories from the granite-tsfm repo can be added to the sys.path\n", "shutil.rmtree(\"code/granite-tsfm\", ignore_errors=True)\n", "assert not Path(\"code/granite-tsfm\").exists()\n", "subprocess.run(\n", "    check=True,\n", "    stdout=subprocess.DEVNULL,\n", "    cwd=\"code\",\n", "    args=[\n", "        \"git\",\n", "        \"clone\",\n", "        \"-b\",\n", "        \"sagemaker\",\n", "        \"--depth\",\n", "        \"1\",\n", "        \"https://github.com/ibm-granite/granite-tsfm.git\",\n", "    ],\n", ")\n", "# set up tsfm services layer\n", "# 1. boilerplate code\n", "subprocess.run(\n", "    check=True, stdout=subprocess.DEVNULL, cwd=\"code/granite-tsfm/services/inference\", args=[\"make\", \"boilerplate\"]\n", ")\n", "# 2. tsfm-granite public models\n", "# you must have git-lfs installed for this\n", "subprocess.run(check=True, stdout=subprocess.DEVNULL, args=[\"git-lfs\"])\n", "subprocess.run(\n", "    check=True, stdout=subprocess.DEVNULL, cwd=\"code/granite-tsfm/services/inference\", args=[\"make\", \"clone_models\"]\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["'tsfm-public-2906feb-model-release-fc98672'"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# obtain the mdhash of our repo so that models and endpoints can use that in their name\n", "result = subprocess.run(\n", "    cwd=\"code/granite-tsfm/services/inference\",\n", "    args=[\"git\", \"rev-parse\", \"--short\", \"HEAD\"],\n", "    capture_output=True,\n", "    text=True,\n", ")\n", "tsfm_release = f\"tsfm-public-{result.stdout.strip()}\"\n", "\n", "result = subprocess.run(\n", "    cwd=\"code/granite-tsfm/services/inference/mytest-tsfm\",\n", "    args=[\"git\", \"rev-parse\", \"--short\", \"HEAD\"],\n", "    capture_output=True,\n", "    text=True,\n", ")\n", "tsfm_model_release = f\"model-release-{result.stdout.strip()}\"\n", "identifier = f\"{tsfm_release}-{tsfm_model_release}\"\n", "identifier"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/29/25 08:41:16] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Found credentials in shared credentials file: ~<span style=\"color: #e100e1; text-decoration-color: #e100e1\">/.aws/credentials</span>   <a href=\"file:///home/<USER>/py312/lib64/python3.12/site-packages/botocore/credentials.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">credentials.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py312/lib64/python3.12/site-packages/botocore/credentials.py#1278\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">1278</span></a>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/29/25 08:41:16]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Found credentials in shared credentials file: ~\u001b[38;2;225;0;225m/.aws/\u001b[0m\u001b[38;2;225;0;225mcredentials\u001b[0m   \u001b]8;id=146446;file:///home/<USER>/py312/lib64/python3.12/site-packages/botocore/credentials.py\u001b\\\u001b[2mcredentials.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=624753;file:///home/<USER>/py312/lib64/python3.12/site-packages/botocore/credentials.py#1278\u001b\\\u001b[2m1278\u001b[0m\u001b]8;;\u001b\\\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["CompletedProcess(args=['tar', '--exclude', '*.git/**', '-czvf', 'model.tar.gz', 'code/inference.py', 'code/requirements.txt', 'code/granite-tsfm/services/inference'], returncode=0)"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# upload our code and model artifacts to our default S3 bucket\n", "\n", "import os\n", "import subprocess\n", "\n", "import boto3\n", "from sagemaker import Session\n", "from sagemaker.pytorch import PyTorchModel\n", "\n", "\n", "# Make sure you have run the aws cli command \"aws config\" to set up your system correctly\n", "# with your aws credentials\n", "\n", "boto3_session = boto3.Session()\n", "\n", "sess = Session(boto_session=boto3_session)\n", "\n", "# replace XXXXXX with your aws iam identifier\n", "# AWS_IAM_ARN should be set to  something like:\n", "# arn:aws:iam::XXXXXXXXXXX:role/SagemakerFullAccessRole\n", "# with the XXXXX replaced with your account id.\n", "role = os.environ.get(\"AWS_IAM_ARN\")\n", "\n", "# create the tarball of code and model artifacts\n", "subprocess.run(\n", "    check=True,\n", "    stdout=subprocess.DEVNULL,\n", "    args=[\n", "        \"tar\",\n", "        \"--exclude\",\n", "        \"*.git/**\",\n", "        \"-czvf\",\n", "        \"model.tar.gz\",\n", "        \"code/inference.py\",\n", "        \"code/requirements.txt\",\n", "        \"code/granite-tsfm/services/inference\",\n", "    ],\n", ")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# upload the tarball to our default s3 bucket so that sagemaker can see it\n", "tsfm_tarball = sess.upload_data(path=\"model.tar.gz\", bucket=sess.default_bucket(), key_prefix=\"model/pytorch\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## PyTorch Model Object\n", "\n", "The `PyTorchModel` class allows you to define an environment for making inference using your\n", "model artifact. Like the `PyTorch` class discussed \n", "[in this notebook for training an PyTorch model](get_started_mnist_train.ipynb), it is a high level API used to set up a docker image for your model hosting service.\n", "\n", "Once it is properly configured, it can be used to create a SageMaker\n", "endpoint on an EC2 instance. The SageMaker endpoint is a containerized environment that uses your trained model \n", "to make inference on incoming data via RESTful API calls. \n", "\n", "Some common parameters used to initiate the `PyTorchModel` class are:\n", "- `entry_point`: A user defined python file to be used by the inference image as handlers of incoming requests\n", "- `source_dir`: The directory of the `entry_point`\n", "- `role`: An IAM role to make AWS service requests\n", "- `model_data`: the S3 location of the compressed model artifact. It can be a path to a local file if the endpoint \n", "is to be deployed on the SageMaker instance you are using to run this notebook (local mode)\n", "- `framework_version`: version of the PyTorch package to be used\n", "- `py_version`: python version to be used"]}, {"cell_type": "code", "execution_count": 5, "metadata": {"tags": []}, "outputs": [], "source": ["model = PyTorchModel(\n", "    entry_point=\"inference.py\",\n", "    source_dir=\"code\",\n", "    role=role,\n", "    model_data=tsfm_tarball,\n", "    framework_version=\"2.5\",\n", "    py_version=\"py311\",\n", "    name=f\"model-{identifier}\",\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Deploy the inference container\n", "Once the `PyTorchModel` class is initiated, we can call its `deploy` method to run the container for the hosting\n", "service. Some common parameters needed to call `deploy` methods are:\n", "\n", "- `initial_instance_count`: the number of SageMaker instances to be used to run the hosting service.\n", "- `instance_type`: the type of SageMaker instance to run the hosting service. Set it to `local` if you want to run the hosting service on the local SageMaker instance. Local mode is typically used for debugging. \n", "- `serializer`: A python callable used to serialize (encode) the request data.\n", "- `deserializer`: A python callable used to deserialize (decode) the response data.\n", "\n", "Commonly used serializers and deserializers are implemented in `sagemaker.serializers` and `sagemaker.deserializers`\n", "submodules of the SageMaker Python SDK. \n", "\n", "Since in the `transform_fn` we declared that the incoming requests are json-encoded, we need to use a `json serializer`,\n", "to encode the incoming data into a json string."]}, {"cell_type": "code", "execution_count": 6, "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:47:15] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Found credentials in shared credentials file: ~<span style=\"color: #e100e1; text-decoration-color: #e100e1\">/.aws/credentials</span>   <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/botocore/credentials.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">credentials.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/botocore/credentials.py#1278\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">1278</span></a>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:47:15]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Found credentials in shared credentials file: ~\u001b[38;2;225;0;225m/.aws/\u001b[0m\u001b[38;2;225;0;225mcredentials\u001b[0m   \u001b]8;id=885429;file:///home/<USER>/py311/lib64/python3.11/site-packages/botocore/credentials.py\u001b\\\u001b[2mcredentials.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=597998;file:///home/<USER>/py311/lib64/python3.11/site-packages/botocore/credentials.py#1278\u001b\\\u001b[2m1278\u001b[0m\u001b]8;;\u001b\\\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consol<PERSON>,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:47:17] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Repacking model artifact                                                  <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/model.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">model.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/model.py#819\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">819</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"font-weight: bold\">(</span>s3:<span style=\"color: #e100e1; text-decoration-color: #e100e1\">//sagemaker-us-east-2-481118440516/model/pytorch/model.tar.gz</span><span style=\"font-weight: bold\">)</span>,       <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         script artifact <span style=\"font-weight: bold\">(</span>code<span style=\"font-weight: bold\">)</span>, and dependencies <span style=\"font-weight: bold\">([])</span> into single tar.gz file     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         located at                                                                <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         s3:<span style=\"color: #e100e1; text-decoration-color: #e100e1\">//sagemaker-us-east-2-481118440516/model-tsfm-public-5fa76c3-model-rel</span> <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         <span style=\"color: #e100e1; text-decoration-color: #e100e1\">ease-fc98672/model.tar.gz.</span> This may take some time depending on model     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         size<span style=\"color: #808000; text-decoration-color: #808000\">...</span>                                                                   <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">            </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:47:17]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Repacking model artifact                                                  \u001b]8;id=623410;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/model.py\u001b\\\u001b[2mmodel.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=94173;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/model.py#819\u001b\\\u001b[2m819\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         \u001b[1m(\u001b[0ms3:\u001b[38;2;225;0;225m/\u001b[0m\u001b[38;2;225;0;225m/sagemaker-us-east-2-481118440516/model/pytorch/\u001b[0m\u001b[38;2;225;0;225mmodel.tar.gz\u001b[0m\u001b[1m)\u001b[0m,       \u001b[2m            \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         script artifact \u001b[1m(\u001b[0mcode\u001b[1m)\u001b[0m, and dependencies \u001b[1m(\u001b[0m\u001b[1m[\u001b[0m\u001b[1m]\u001b[0m\u001b[1m)\u001b[0m into single tar.gz file     \u001b[2m            \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         located at                                                                \u001b[2m            \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         s3:\u001b[38;2;225;0;225m/\u001b[0m\u001b[38;2;225;0;225m/sagemaker-us-east-2-481118440516/model-tsfm-public-5fa76c3-model-rel\u001b[0m \u001b[2m            \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         \u001b[38;2;225;0;225mease-fc98672/\u001b[0m\u001b[38;2;225;0;225mmodel.tar.gz.\u001b[0m This may take some time depending on model     \u001b[2m            \u001b[0m\n", "\u001b[2;36m                    \u001b[0m         size\u001b[33m...\u001b[0m                                                                   \u001b[2m            \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:48:00] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Creating model with name:                                              <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">session.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4094\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">4094</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         model-tsfm-public-5fa76c3-model-release-fc98672                        <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">               </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:48:00]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Creating model with name:                                              \u001b]8;id=479663;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\u001b\\\u001b[2msession.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=353448;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4094\u001b\\\u001b[2m4094\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         model-tsfm-public-5fa76c3-model-release-fc98672                        \u001b[2m               \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consol<PERSON>,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:48:01] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Creating endpoint-config with name                                     <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">session.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#5889\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">5889</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">               </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:48:01]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Creating endpoint-config with name                                     \u001b]8;id=685055;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\u001b\\\u001b[2msession.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=921304;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#5889\u001b\\\u001b[2m5889\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     \u001b[2m               \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:48:02] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Creating endpoint with name                                            <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">session.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4711\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">4711</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">               </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:48:02]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Creating endpoint with name                                            \u001b]8;id=51413;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\u001b\\\u001b[2msession.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=508560;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4711\u001b\\\u001b[2m4711\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     \u001b[2m               \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["-------!"]}, {"data": {"text/plain": ["<sagemaker.pytorch.model.PyTorchPredictor at 0x7f7d66b117d0>"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["from sagemaker.deserializers import JSONDeserializer\n", "from sagemaker.serializers import JSONSerializer\n", "\n", "\n", "instance_type = \"ml.c4.xlarge\"\n", "\n", "# this could take up to five minutes to complete\n", "predictor = model.deploy(\n", "    initial_instance_count=1,\n", "    instance_type=instance_type,\n", "    serializer=JSONSerializer(),\n", "    deserializer=JSONDeserializer(),\n", "    endpoint_name=f\"endpoint-{identifier}\",\n", ")\n", "predictor"]}, {"cell_type": "markdown", "metadata": {}, "source": ["The `predictor` we get above can be used to make prediction requests against a SageMaker endpoint. \n", "For more information, check [the API reference for SageMaker Predictor](\n", "https://sagemaker.readthedocs.io/en/stable/api/inference/predictors.html#sagemaker.predictor.predictor)\n", "\n", "Now, let's test the endpoint with electricity ususage data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Read the data\n", "\n", "We parse the csv into a pandas dataframe, filling in any null values, and create a single window containing context_length time points. We ensure the timestamp column is a datetime."]}, {"cell_type": "code", "execution_count": 13, "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>generation biomass</th>\n", "      <th>generation fossil brown coal/lignite</th>\n", "      <th>generation fossil coal-derived gas</th>\n", "      <th>generation fossil gas</th>\n", "      <th>generation fossil hard coal</th>\n", "      <th>generation fossil oil</th>\n", "      <th>generation fossil oil shale</th>\n", "      <th>generation fossil peat</th>\n", "      <th>generation geothermal</th>\n", "      <th>...</th>\n", "      <th>generation waste</th>\n", "      <th>generation wind offshore</th>\n", "      <th>generation wind onshore</th>\n", "      <th>forecast solar day ahead</th>\n", "      <th>forecast wind offshore eday ahead</th>\n", "      <th>forecast wind onshore day ahead</th>\n", "      <th>total load forecast</th>\n", "      <th>total load actual</th>\n", "      <th>price day ahead</th>\n", "      <th>price actual</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>35059</th>\n", "      <td>2018-12-31 19:00:00+01:00</td>\n", "      <td>297.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7634.0</td>\n", "      <td>2628.0</td>\n", "      <td>178.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>277.0</td>\n", "      <td>0.0</td>\n", "      <td>3113.0</td>\n", "      <td>96.0</td>\n", "      <td>NaN</td>\n", "      <td>3253.0</td>\n", "      <td>30619.0</td>\n", "      <td>30653.0</td>\n", "      <td>68.85</td>\n", "      <td>77.02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35060</th>\n", "      <td>2018-12-31 20:00:00+01:00</td>\n", "      <td>296.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7241.0</td>\n", "      <td>2566.0</td>\n", "      <td>174.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>280.0</td>\n", "      <td>0.0</td>\n", "      <td>3288.0</td>\n", "      <td>51.0</td>\n", "      <td>NaN</td>\n", "      <td>3353.0</td>\n", "      <td>29932.0</td>\n", "      <td>29735.0</td>\n", "      <td>68.40</td>\n", "      <td>76.16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35061</th>\n", "      <td>2018-12-31 21:00:00+01:00</td>\n", "      <td>292.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>7025.0</td>\n", "      <td>2422.0</td>\n", "      <td>168.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>286.0</td>\n", "      <td>0.0</td>\n", "      <td>3503.0</td>\n", "      <td>36.0</td>\n", "      <td>NaN</td>\n", "      <td>3404.0</td>\n", "      <td>27903.0</td>\n", "      <td>28071.0</td>\n", "      <td>66.88</td>\n", "      <td>74.30</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35062</th>\n", "      <td>2018-12-31 22:00:00+01:00</td>\n", "      <td>293.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6562.0</td>\n", "      <td>2293.0</td>\n", "      <td>163.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>287.0</td>\n", "      <td>0.0</td>\n", "      <td>3586.0</td>\n", "      <td>29.0</td>\n", "      <td>NaN</td>\n", "      <td>3273.0</td>\n", "      <td>25450.0</td>\n", "      <td>25801.0</td>\n", "      <td>63.93</td>\n", "      <td>69.89</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35063</th>\n", "      <td>2018-12-31 23:00:00+01:00</td>\n", "      <td>290.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>6926.0</td>\n", "      <td>2166.0</td>\n", "      <td>163.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>287.0</td>\n", "      <td>0.0</td>\n", "      <td>3651.0</td>\n", "      <td>26.0</td>\n", "      <td>NaN</td>\n", "      <td>3117.0</td>\n", "      <td>24424.0</td>\n", "      <td>24455.0</td>\n", "      <td>64.27</td>\n", "      <td>69.88</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["                            time  generation biomass  \\\n", "35059  2018-12-31 19:00:00+01:00               297.0   \n", "35060  2018-12-31 20:00:00+01:00               296.0   \n", "35061  2018-12-31 21:00:00+01:00               292.0   \n", "35062  2018-12-31 22:00:00+01:00               293.0   \n", "35063  2018-12-31 23:00:00+01:00               290.0   \n", "\n", "       generation fossil brown coal/lignite  \\\n", "35059                                   0.0   \n", "35060                                   0.0   \n", "35061                                   0.0   \n", "35062                                   0.0   \n", "35063                                   0.0   \n", "\n", "       generation fossil coal-derived gas  generation fossil gas  \\\n", "35059                                 0.0                 7634.0   \n", "35060                                 0.0                 7241.0   \n", "35061                                 0.0                 7025.0   \n", "35062                                 0.0                 6562.0   \n", "35063                                 0.0                 6926.0   \n", "\n", "       generation fossil hard coal  generation fossil oil  \\\n", "35059                       2628.0                  178.0   \n", "35060                       2566.0                  174.0   \n", "35061                       2422.0                  168.0   \n", "35062                       2293.0                  163.0   \n", "35063                       2166.0                  163.0   \n", "\n", "       generation fossil oil shale  generation fossil peat  \\\n", "35059                          0.0                     0.0   \n", "35060                          0.0                     0.0   \n", "35061                          0.0                     0.0   \n", "35062                          0.0                     0.0   \n", "35063                          0.0                     0.0   \n", "\n", "       generation geothermal  ...  generation waste  generation wind offshore  \\\n", "35059                    0.0  ...             277.0                       0.0   \n", "35060                    0.0  ...             280.0                       0.0   \n", "35061                    0.0  ...             286.0                       0.0   \n", "35062                    0.0  ...             287.0                       0.0   \n", "35063                    0.0  ...             287.0                       0.0   \n", "\n", "       generation wind onshore  forecast solar day ahead  \\\n", "35059                   3113.0                      96.0   \n", "35060                   3288.0                      51.0   \n", "35061                   3503.0                      36.0   \n", "35062                   3586.0                      29.0   \n", "35063                   3651.0                      26.0   \n", "\n", "       forecast wind offshore eday ahead  forecast wind onshore day ahead  \\\n", "35059                                NaN                           3253.0   \n", "35060                                NaN                           3353.0   \n", "35061                                NaN                           3404.0   \n", "35062                                NaN                           3273.0   \n", "35063                                NaN                           3117.0   \n", "\n", "       total load forecast  total load actual  price day ahead  price actual  \n", "35059              30619.0            30653.0            68.85         77.02  \n", "35060              29932.0            29735.0            68.40         76.16  \n", "35061              27903.0            28071.0            66.88         74.30  \n", "35062              25450.0            25801.0            63.93         69.89  \n", "35063              24424.0            24455.0            64.27         69.88  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from datetime import datetime\n", "\n", "import pandas as pd\n", "\n", "\n", "timestamp_column = \"time\"\n", "target_columns = [\"total load actual\"]\n", "context_length = 512\n", "\n", "\n", "# Read in the data from the downloaded file.\n", "input_df = pd.read_csv(\n", "    \"hf://datasets/vitaliy-sharandin/energy-consumption-hourly-spain/energy_dataset.csv\",\n", "    parse_dates=[timestamp_column],  # Parse the timestamp values as dates.\n", ")\n", "\n", "# Fill NA/NaN values by propagating the last valid value.\n", "input_df = input_df.ffill()\n", "\n", "# Only use the last `context_length` rows for prediction.\n", "input_df = input_df.iloc[-context_length:,]\n", "\n", "# Show the last few rows of the dataset.\n", "input_df.tail()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot the target series\n", "\n", "Here we inspect a preview of the target time series column."]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m A new release of pip is available: \u001b[0m\u001b[31;49m24.2\u001b[0m\u001b[39;49m -> \u001b[0m\u001b[32;49m24.3.1\u001b[0m\n", "\u001b[1m[\u001b[0m\u001b[34;49mnotice\u001b[0m\u001b[1;39;49m]\u001b[0m\u001b[39;49m To update, run: \u001b[0m\u001b[32;49mpip install --upgrade pip\u001b[0m\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["! pip install -q matplotlib\n", "import matplotlib.pyplot as plt\n", "\n", "\n", "fig, axs = plt.subplots(len(target_columns), 1, figsize=(10, 3 * len(target_columns)), squeeze=False)\n", "for ax, target_column in zip(axs, target_columns):\n", "    ax[0].plot(input_df[timestamp_column], input_df[target_column])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Prepare the payload"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["# since we'll be making a REST call, we need to covert the time column to string type\n", "# so that it's json serializable\n", "import copy\n", "\n", "\n", "original_input_df = copy.deepcopy(input_df)\n", "\n", "input_df[timestamp_column] = input_df[timestamp_column].apply(lambda x: x.isoformat())\n", "\n", "payload = {\n", "    \"inference_type\": \"forecasting\",  # we currently support only 'forecasting'\n", "    \"model_id\": \"ttm-r2\",\n", "    \"parameters\": {},\n", "    \"schema\": {\n", "        \"timestamp_column\": timestamp_column,\n", "        \"id_columns\": [],  # single timeseries doesn't require id_columns\n", "        \"target_columns\": target_columns,  # what we're generating a forecast for\n", "    },\n", "    \"data\": input_df.to_dict(orient=\"list\"),\n", "    \"future_data\": {},  # used for things like exogenous data\n", "}"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Use the predictor to generate a forecast"]}, {"cell_type": "code", "execution_count": 16, "metadata": {"tags": []}, "outputs": [], "source": ["res = predictor.predict(payload)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Inspect the results\n", "\n", "Note that our forecasts begin on hour after the last time stamp in our ground truth data"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>total load actual</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2019-01-01 00:00:00+01:00</td>\n", "      <td>23465.367188</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2019-01-01 01:00:00+01:00</td>\n", "      <td>22127.960938</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2019-01-01 02:00:00+01:00</td>\n", "      <td>21162.064453</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2019-01-01 03:00:00+01:00</td>\n", "      <td>20757.097656</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2019-01-01 04:00:00+01:00</td>\n", "      <td>20833.941406</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       time  total load actual\n", "0 2019-01-01 00:00:00+01:00       23465.367188\n", "1 2019-01-01 01:00:00+01:00       22127.960938\n", "2 2019-01-01 02:00:00+01:00       21162.064453\n", "3 2019-01-01 03:00:00+01:00       20757.097656\n", "4 2019-01-01 04:00:00+01:00       20833.941406"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["# convert back to a pandas dataframe\n", "# note that [0] are the predictions for the first--and in this case only--timeseries \"A\"\n", "results_df = pd.DataFrame.from_dict(res[\"results\"][0])\n", "# so that we can plot correctly\n", "results_df[timestamp_column] = results_df[timestamp_column].apply(lambda x: datetime.fromisoformat(x))\n", "# the first 10 predictions\n", "results_df.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Plot the predicted values on the same axis as the ground truth values\n", "\n", "This gives a good sense of how well the model caputure the trends in the data"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1200x300 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, axs = plt.subplots(len(target_columns), 1, figsize=(12, 3 * len(target_columns)), squeeze=False)\n", "\n", "for ax, target_column in zip(axs, target_columns):\n", "    ax[0].plot(\n", "        original_input_df[timestamp_column], original_input_df[target_column], label=\"ground truth\", color=\"blue\"\n", "    )\n", "    ax[0].plot(results_df[timestamp_column], results_df[target_column], label=\"predicted\", color=\"red\")\n", "    ax[0].legend()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## (Optional) Clean up \n", "\n", "If you do not plan to use the endpoint, you should delete it to free up some computation \n", "resource. If you use local, you will need to manually delete the docker container bounded\n", "at port 8080 (the port that listens to the incoming request).\n"]}, {"cell_type": "code", "execution_count": 19, "metadata": {"tags": []}, "outputs": [{"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consolas,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:52:40] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Deleting model with name:                                              <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">session.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#5226\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">5226</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         model-tsfm-public-5fa76c3-model-release-fc98672                        <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">               </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:52:40]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Deleting model with name:                                              \u001b]8;id=16422;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\u001b\\\u001b[2msession.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=869243;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#5226\u001b\\\u001b[2m5226\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         model-tsfm-public-5fa76c3-model-release-fc98672                        \u001b[2m               \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consol<PERSON>,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Deleting endpoint configuration with name:                             <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">session.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4865\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">4865</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">               </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m                   \u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Deleting endpoint configuration with name:                             \u001b]8;id=357891;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\u001b\\\u001b[2msession.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=39379;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4865\u001b\\\u001b[2m4865\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     \u001b[2m               \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<pre style=\"white-space:pre;overflow-x:auto;line-height:normal;font-family:<PERSON><PERSON>,'DejaVu Sans Mono',consol<PERSON>,'Courier New',monospace\"><span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">[01/08/25 18:52:41] </span><span style=\"color: #0069ff; text-decoration-color: #0069ff; font-weight: bold\">INFO    </span> Deleting endpoint with name:                                           <a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">session.py</span></a><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">:</span><a href=\"file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4855\" target=\"_blank\"><span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">4855</span></a>\n", "<span style=\"color: #7fbfbf; text-decoration-color: #7fbfbf\">                    </span>         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     <span style=\"color: #7f7f7f; text-decoration-color: #7f7f7f\">               </span>\n", "</pre>\n"], "text/plain": ["\u001b[2;36m[01/08/25 18:52:41]\u001b[0m\u001b[2;36m \u001b[0m\u001b[1;38;2;0;105;255mINFO    \u001b[0m Deleting endpoint with name:                                           \u001b]8;id=506449;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py\u001b\\\u001b[2msession.py\u001b[0m\u001b]8;;\u001b\\\u001b[2m:\u001b[0m\u001b]8;id=150824;file:///home/<USER>/py311/lib64/python3.11/site-packages/sagemaker/session.py#4855\u001b\\\u001b[2m4855\u001b[0m\u001b]8;;\u001b\\\n", "\u001b[2;36m                    \u001b[0m         endpoint-tsfm-public-5fa76c3-model-release-fc98672                     \u001b[2m               \u001b[0m\n"]}, "metadata": {}, "output_type": "display_data"}], "source": ["predictor.delete_model()\n", "predictor.delete_endpoint()"]}], "metadata": {"availableInstances": [{"_defaultOrder": 0, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.t3.medium", "vcpuNum": 2}, {"_defaultOrder": 1, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.t3.large", "vcpuNum": 2}, {"_defaultOrder": 2, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.t3.xlarge", "vcpuNum": 4}, {"_defaultOrder": 3, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.t3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 4, "_isFastLaunch": true, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5.large", "vcpuNum": 2}, {"_defaultOrder": 5, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 6, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 7, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 8, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 9, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 10, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 11, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 12, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.m5d.large", "vcpuNum": 2}, {"_defaultOrder": 13, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.m5d.xlarge", "vcpuNum": 4}, {"_defaultOrder": 14, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.m5d.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 15, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.m5d.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 16, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.m5d.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 17, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.m5d.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 18, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.m5d.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 19, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.m5d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 20, "_isFastLaunch": false, "category": "General purpose", "gpuNum": 0, "hideHardwareSpecs": true, "memoryGiB": 0, "name": "ml.geospatial.interactive", "supportedImageNames": ["sagemaker-geospatial-v1-0"], "vcpuNum": 0}, {"_defaultOrder": 21, "_isFastLaunch": true, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 4, "name": "ml.c5.large", "vcpuNum": 2}, {"_defaultOrder": 22, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 8, "name": "ml.c5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 23, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.c5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 24, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.c5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 25, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 72, "name": "ml.c5.9xlarge", "vcpuNum": 36}, {"_defaultOrder": 26, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 96, "name": "ml.c5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 27, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 144, "name": "ml.c5.18xlarge", "vcpuNum": 72}, {"_defaultOrder": 28, "_isFastLaunch": false, "category": "Compute optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.c5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 29, "_isFastLaunch": true, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g4dn.xlarge", "vcpuNum": 4}, {"_defaultOrder": 30, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g4dn.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 31, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g4dn.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 32, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g4dn.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 33, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g4dn.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 34, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g4dn.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 35, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 61, "name": "ml.p3.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 36, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 244, "name": "ml.p3.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 37, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 488, "name": "ml.p3.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 38, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.p3dn.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 39, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.r5.large", "vcpuNum": 2}, {"_defaultOrder": 40, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.r5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 41, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.r5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 42, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.r5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 43, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.r5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 44, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.r5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 45, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 512, "name": "ml.r5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 46, "_isFastLaunch": false, "category": "Memory Optimized", "gpuNum": 0, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.r5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 47, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 16, "name": "ml.g5.xlarge", "vcpuNum": 4}, {"_defaultOrder": 48, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 32, "name": "ml.g5.2xlarge", "vcpuNum": 8}, {"_defaultOrder": 49, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 64, "name": "ml.g5.4xlarge", "vcpuNum": 16}, {"_defaultOrder": 50, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 128, "name": "ml.g5.8xlarge", "vcpuNum": 32}, {"_defaultOrder": 51, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 1, "hideHardwareSpecs": false, "memoryGiB": 256, "name": "ml.g5.16xlarge", "vcpuNum": 64}, {"_defaultOrder": 52, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 192, "name": "ml.g5.12xlarge", "vcpuNum": 48}, {"_defaultOrder": 53, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 4, "hideHardwareSpecs": false, "memoryGiB": 384, "name": "ml.g5.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 54, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 768, "name": "ml.g5.48xlarge", "vcpuNum": 192}, {"_defaultOrder": 55, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4d.24xlarge", "vcpuNum": 96}, {"_defaultOrder": 56, "_isFastLaunch": false, "category": "Accelerated computing", "gpuNum": 8, "hideHardwareSpecs": false, "memoryGiB": 1152, "name": "ml.p4de.24xlarge", "vcpuNum": 96}], "kernelspec": {"display_name": "py312", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.8"}}, "nbformat": 4, "nbformat_minor": 4}