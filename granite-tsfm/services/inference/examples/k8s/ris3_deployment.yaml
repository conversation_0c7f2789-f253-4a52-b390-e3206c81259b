apiVersion: v1
kind: Service
metadata:
  name: tsfmservices-fastapi-service
spec:
  type: NodePort
  selector:
    app: tsfmservices-fastapi
  ports:
    - protocol: TCP
      port: 80
      targetPort: 8000
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  annotations:
    ingress.kubernetes.io/allow-http: "false"
    ingress.kubernetes.io/ssl-redirect: "true"
    kubernetes.io/ingress.class: f5
    virtual-server.f5.com/balance: round-robin
    virtual-server.f5.com/ip: ************
    virtual-server.f5.com/partition: RIS3-INT-OCP-DAL12
    virtual-server.f5.com/clientssl: '[ { "bigIpProfile": "/Common/BlueMix" } ]'
  name: tsfmservices-fastapi-ingress
  namespace: fctk
spec:
  rules:
    - host: fctk.bx.cloud9.ibm.com
      http:
        paths:
          - backend:
              service:
                name: tsfmservices-fastapi-service
                port:
                  number: 80
            path: /
            pathType: ImplementationSpecific
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tsfmservices-fastapi
  labels:
    app: tsfmservices-fastapi
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tsfmservices-fastapi
  template:
    metadata:
      labels:
        app: tsfmservices-fastapi
    spec:
      containers:
        - name: tsfmservices-fastapi
          image: us.icr.io/fctkstus/tsfminference-cpu:latest
          imagePullPolicy: Always
          command: ["/bin/sh"]
          args:
            - "-c"
            - |
              gunicorn \
              --max-requests 250 \
              -w 8 \
              -k uvicorn.workers.UvicornWorker \
              --bind 0.0.0.0:8000 \
              --timeout 1200 \
              --access-logfile - \
              --error-logfile - \
              --log-level info \
              tsfminference.main:app
          volumeMounts:
            - mountPath: /local-storage
              name: local-storage
          env:
            - name: TSFM_ALLOW_LOAD_FROM_HF_HUB
              value: "0"
            - name: CUDA_VISIBLE_DEVICES
              value: ""
            - name: TSFM_MODEL_DIR
              value: /local-storage/mytest-tsfm
            - name: PROMETHEUS_MULTIPROC_DIR
              value: "/tmp"
            - name: TSFM_PYTHON_LOGGING_LEVEL
              value: "WARNING"
            - name: TSFM_PYTHON_LOGGING_FORMAT
              # lighter weight
              value: "%(asctime)s:%(levelname)s:p-%(process)d:t-%(thread)d:%(module)s:%(message)s"
              # intensive but costly
              # value: "%(asctime)s:%(levelname)s:p-%(process)d:t-%(thread)d:%(filename)s:%(funcName)s:%(message)s"
          # Readiness Probe
          readinessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
          # Liveness Probe
          livenessProbe:
            httpGet:
              path: /healthcheck
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 30
          ports:
            - containerPort: 8000
          resources:
            requests:
              # nvidia.com/gpu: 0
              cpu: 8000m
              memory: 32Gi
            limits:
              # nvidia.com/gpu: 0
              cpu: 8000m
              memory: 32Gi
          # Alpine Sidecar with Init Command
        - name: rest-client
          image: us.icr.io/fctkstus/tsfminference-cpu:latest
          command: ["/bin/sh", "-c"]
          args:
            - |
              echo "Installing required packages...";
              pip install locust pytest;
              echo "Installation complete. Running idle process...";
              while true; do sleep 3600; done
          volumeMounts:
            - mountPath: /local-storage
              name: local-storage
          resources:
            requests:
              # nvidia.com/gpu: 0
              cpu: 12000m
              memory: 8Gi
            limits:
              # nvidia.com/gpu: 0
              cpu: 16000m
              memory: 16Gi
      volumes:
        - name: local-storage
          persistentVolumeClaim:
            claimName: my-pvc
