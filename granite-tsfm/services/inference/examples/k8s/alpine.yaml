apiVersion: v1
kind: Pod
metadata:
  name: alpine
spec:
  containers:
    - name: alpine
      image: amd64/alpine:3.7
      imagePullPolicy: Always
      command:
        - sleep
        - "3600"
      volumeMounts:
        - mountPath: /local-storage
          name: local-storage
      resources:
        limits:
          memory: 1Gi
          cpu: 500m
        requests:
          memory: 256Mi
          cpu: 125m
  volumes:
    - name: local-storage
      persistentVolumeClaim:
        claimName: my-pvc
