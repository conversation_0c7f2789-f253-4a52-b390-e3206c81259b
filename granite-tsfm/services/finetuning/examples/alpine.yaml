apiVersion: v1
kind: Pod
metadata:
  name: alpine
spec:
  containers:
    - name: alpine
      image: amd64/alpine:latest
      imagePullPolicy: Always
      command:
        - sleep
        - "3600"
      volumeMounts:
        - mountPath: /data
          name: volv
      resources:
        limits:
          memory: 1Gi
          cpu: 500m
        requests:
          memory: 256Mi
          cpu: 125m
  volumes:
    - name: volv
      persistentVolumeClaim:
        claimName: local-path-pvc
