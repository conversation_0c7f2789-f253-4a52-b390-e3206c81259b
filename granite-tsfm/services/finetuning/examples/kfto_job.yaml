# see https://www.substratus.ai/blog/kind-with-gpus
# for info on using gpus with kind
apiVersion: "kubeflow.org/v1"
kind: PyTorchJob
metadata:
  name: tsfmfinetuning-job
  namespace: default
  annotations:
    sidecar.istio.io/inject: "false"
spec:
  pytorchReplicaSpecs:
    Master:
      replicas: 1
      restartPolicy: Never
      template:
        spec:
          containers:
            - name: pytorch
              image: localhost:5001/tsfmfinetuning:latest
              imagePullPolicy: Always
              volumeMounts:
                - name: volv
                  mountPath: /data
              resources:
                requests:
                  memory: 8Gi
                  cpu: 1000m
                  # nvidia.com/gpu: 1
                limits:
                  memory: 12Gi
                  cpu: 1500m
                  # nvidia.com/gpu: 1
              env:
              - name: TSFM_ALLOW_LOAD_FROM_HF_HUB
                value: "0"
              - name: TSFM_MODEL_DIR
                value: "/data"
              command:
                - "python"
                - "tsfmfinetuning/ftmain.py"
                - "--payload"
                - "/data/ftpayload.json"
                - "--target_dir"
                - "/data"
                - "--model_name"
                - "finetuned_from_kfto"
                - "--config_file"
                - "/data/default_config.yml"
                - "--model_arch"
                - "ttm"
                - "--task_type"
                - "forecasting"
          volumes:
          - name: volv
            persistentVolumeClaim:
              claimName: local-path-pvc


