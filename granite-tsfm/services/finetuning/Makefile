CONTAINER_BUILDER ?= docker

# copies boilerplate code to suitable locations
boilerplate:
	rm tsfmfinetuning/.gitignore || true
	echo "# THIS FILE IS AUTOMATICALLY GENERATED, YOUR CHANGES WILL BE OVERWRITTEN" > tsfmfinetuning/.gitignore
	for f in ../boilerplate/*.py; do \
		echo $$f; \
		cat ../boilerplate/warning.txt > tsfmfinetuning/$$(basename $$f); \
		cat $$f>>tsfmfinetuning/$$(basename $$f); \
		echo $$(basename $$f) >> tsfmfinetuning/.gitignore; \
		done 
	
image: boilerplate
	# build our base inference images first
	@cd ../inference ; make image
	$(CONTAINER_BUILDER) \
	build -t tsfmfinetuning-gpu \
	--build-arg CODEDIR="tsfmfinetuning" \
	-f Dockerfile.gpu .

clone_models:
	rm -rf mytest-tsfm || true
	git lfs install || true
	git clone --depth 1 -b refactor_v2 https://huggingface.co/ibm-research/test-tsfm mytest-tsfm 


fetchdata:
	python tests/fetchdata.py

test_local: clone_models fetchdata boilerplate
	pytest -s tests

install_dev: boilerplate
	pip install poetry && poetry install --with dev
