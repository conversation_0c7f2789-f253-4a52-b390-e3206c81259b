{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Fine Tuning a Time Series Model using Channel Independence PatchTST\n", "\n", "<ul>\n", "<li>Contributors: IBM AI Research team and IBM Research Technology Education team\n", "<li>Contact for questions and technical support: <EMAIL>\n", "<li>Provenance: IBM Research\n", "<li>Version: 1.0.0\n", "<li>Release date: \n", "<li>Compute requirements: 4 CPU (preferrably 1 GPU)\n", "<li>Memory requirements: 16 GB\n", "<li>Notebook set: Time Series Foundation Model\n", "</ul>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Summary\n", "\n", "**Patch Time Series Transformer (PatchTST)** is a new method for long-term forecasting based on Transformer modeling. In PatchTST, a time series is segmented into subseries-level patches that are served as input tokens to Transformer. PatchTST was first proposed in 2023 in [this paper](https://arxiv.org/pdf/2211.14730.pdf). It can achieve state-of-the-art results when compared to other Transformer-based models.\n", "\n", "**Channel Independence PatchTST** is a variant of PatchTST where each channel contains a single univariate time series that shares the same embedding and Transformer weights across all the series.\n", "\n", "This notebook shows how to fine tune a Channel Independence PatchTST model in a supervised way. Fine tuning will be set up to only affect the last linear layer of the model -- this is called *linear probing*. The model is trained using patches extracted from a longer segment of the time series as input, with the future values as a target.\n", "\n", "This is the second of three notebooks that should be run in sequence using training and test data from the ETTh1 benchmark dataset, which represents oil temperature in an electric transformer. After running the first notebook, `01_patch_tst_pretrain.ipynb`, a pretrained model was saved in your private storage. This notebook will load the pretrained model and create a fine tuned model, which will be also saved in your private storage. The third notebook, `03_patch_tst_inference.ipynb`, will perform inferencing using the fine tuned model, with a goal of predicting the future temperature of the oil in the electric transformer.\n", "\n", "*Maybe add a picture of the PatchTST with forecasting head?*"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Table of Contents\n", "\n", "* <a href=\"#TST2_intro\">Channel Independence PatchTST</a>\n", "* <a href=\"#TST2_codes\">Code Samples</a>\n", "    * <a href=\"#TST2_import\">Step 1. Imports</a>\n", "    * <a href=\"#TST2_datast\">Step 2. Load and prepare datasets </a>\n", "    * <a href=\"#TST2_config\">Step 3. Configure the PatchTST model </a>\n", "    * <a href=\"#TST2_modelp\">Step 4. Load model and freeze base model parameters </a>\n", "    * <a href=\"#TST2_ftunem\">Step 5. Fine-tune the model </a>\n", "* <a href=\"#TST2_concl\">Conclusion</a>\n", "* <a href=\"#TST2_learn\">Learn More</a>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_intro\"></a>\n", "# Channel Independence PatchTST\n", "\n", "**Channel Independence PatchTST** is an efficient design of Transformer-based models for multivariate time series forecasting and self-supervised representation learning. It is demonstrated in the following diagram. It is based on two key components:\n", "\n", "- segmentation of time series into subseries-level patches that are served as input tokens to Transformer\n", "\n", "- channel independence where each channel contains a single univariate time series that shares the same embedding and Transformer weights across all the series.\n", "\n", "Patching design naturally has three-fold benefit: local semantic information is retained in the embedding; computation and memory usage of the attention maps are quadratically reduced given the same look-back window; and the model can attend longer history.\n", "\n", "Channel independence allows each time series to have its own embedding and attention maps while sharing the same model parameters across different channels.\n", "\n", "<div> <img src=\"./data/figures/patchTST.png\" alt=\"Drawing\" style=\"width: 600px;\"/></div>"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_codes\"></a>\n", "# Code Samples\n", "\n", "This section includes documentation and code samples to demonstrate the use of the toolkit for fine tuning."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_import\"></a>\n", "## Step 1. Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from transformers import (\n", "    PatchTSTConfig,\n", "    PatchTSTForPrediction,\n", "    Trainer,\n", "    TrainingArguments,\n", ")\n", "\n", "from tsfm_public.toolkit.dataset import ForecastDFDataset\n", "from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor\n", "from tsfm_public.toolkit.util import select_by_index"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_datast\"></a>\n", "## Step 2. Load and prepare datasets\n", "\n", " In the next cell, please adjust the following parameters to suit your application:\n", " - dataset_path: path to local .csv file, or web address to a csv file for the data of interest. Data is loaded with pandas, so anything supported by\n", "   `pd.read_csv` is supported: (https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html).\n", " - timestamp_column: column name containing timestamp information, use None if there is no such column\n", " - id_columns: List of column names specifying the IDs of different time series. If no ID column exists, use []\n", " - forecast_columns: List of columns to be modeled\n", " - prediction_length: Specifies how many timepoints should be forecasted\n", " - context_length: The amount of historical data used as input to the model. Windows of the input time series data with length equal to\n", "   context_length will be extracted from the input dataframe. In the case of a multi-time series dataset, the context windows will be created\n", "   so that they are contained within a single time series (i.e., a single ID).\n", " - train_start_index, train_end_index: the start and end indices in the loaded data which delineate the training data.\n", " - eval_start_index, eval_end_index: the start and end indices in the loaded data which delineate the evaluation data.\n", "\n", " The data is first loaded into a Pandas dataframe and split into training and evaluation parts. Then the pandas dataframes are converted\n", " to the appropriate torch dataset needed for training.\n", " \n", " The specific data loaded here is Electricity Transformer Temperature (ETT) data - including load, oil temperature in an electric transformer."]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["dataset_path = \"https://raw.githubusercontent.com/zhouhaoyi/ETDataset/main/ETT-small/ETTh2.csv\"\n", "timestamp_column = \"date\"\n", "id_columns = []\n", "forecast_columns = [\"HUFL\", \"HULL\", \"MUFL\", \"MULL\", \"LUFL\", \"LULL\", \"OT\"]\n", "\n", "prediction_length = 96\n", "\n", "pretrained_model_path = \"model/pretrained\"\n", "\n", "# load pretrained model config, to access some previously defined parameters\n", "pretrained_config = PatchTSTConfig.from_pretrained(pretrained_model_path)\n", "context_length = pretrained_config.context_length  # use pretrained_config.context_length to match pretrained model\n", "\n", "train_start_index = None  # None indicates beginning of dataset\n", "train_end_index = 12 * 30 * 24\n", "\n", "# we shift the start of the evaluation period back by context length so that\n", "# the first evaluation timestamp is immediately following the training data\n", "eval_start_index = 12 * 30 * 24 - context_length\n", "eval_end_index = 12 * 30 * 24 + 4 * 30 * 24"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                 date       HUFL    HULL       MUFL   MULL   LUFL   LULL  \\\n", "0 2016-07-01 00:00:00  41.130001  12.481  36.535999  9.355  4.424  1.311   \n", "1 2016-07-01 01:00:00  37.528000  10.136  33.936001  7.532  4.435  1.215   \n", "2 2016-07-01 02:00:00  37.946999  11.309  35.330002  9.007  2.100  0.000   \n", "3 2016-07-01 03:00:00  38.952000  11.895  35.543999  9.436  3.380  1.215   \n", "4 2016-07-01 04:00:00  38.113998  11.476  35.410000  9.623  2.036  0.000   \n", "\n", "          OT  \n", "0  38.661999  \n", "1  37.124001  \n", "2  36.465000  \n", "3  33.608501  \n", "4  31.850500  \n"]}], "source": ["data = pd.read_csv(\n", "    dataset_path,\n", "    parse_dates=[timestamp_column],\n", ")\n", "\n", "train_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=train_start_index,\n", "    end_index=train_end_index,\n", ")\n", "eval_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=eval_start_index,\n", "    end_index=eval_end_index,\n", ")\n", "\n", "print(data.head())"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["tsp = TimeSeriesPreprocessor(\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    scaling=True,\n", ")\n", "tsp.train(train_data)\n", "train_dataset = ForecastDFDataset(\n", "    tsp.preprocess(train_data),\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=prediction_length,\n", ")\n", "eval_dataset = ForecastDFDataset(\n", "    tsp.preprocess(eval_data),\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=prediction_length,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_config\"></a>\n", "## Step 3. Configure the PatchTST model\n", "\n", " The PatchTSTConfig is created in the next cell. This leverages the configuration that\n", " is already present in the pretrained model, and adds the parameters necessary for the\n", " forecasting task. This includes:\n", " - context_length: As described above, the amount of historical data used as input to the model.\n", " - num_input_channels: The number of input channels. In this case, it is set equal to the n\n", "   number of dimensions we intend to forecast.\n", " - prediction_length: Prediction horizon for the forecasting task, as set above."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["pred_config = PatchTSTConfig.from_pretrained(\n", "    pretrained_model_path,\n", "    context_length=context_length,\n", "    num_input_channels=tsp.num_input_channels,\n", "    prediction_length=prediction_length,\n", "    do_mask_input=False,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_modelp\"></a>\n", "## Step 4. Load model and freeze base model parameters\n", "\n", " The follwoing cell loads the pretrained model and then freezes parameters in the base\n", " model. You will likely see a warning about weights not being initialized from the model\n", " checkpoint; this message is expected since the forecasting model has a head with weights\n", " which have not yet been trained."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of PatchTSTForPrediction were not initialized from the model checkpoint at model/pretrained and are newly initialized: ['head.projection.bias', 'head.projection.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}], "source": ["forecasting_model = PatchTSTForPrediction.from_pretrained(\n", "    \"model/pretrained\",\n", "    config=pred_config,\n", "    ignore_mismatched_sizes=True,\n", ")\n", "# This freezes the base model parameters\n", "# for param in forecasting_model.base_model.parameters():\n", "#     param.requires_grad = False"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_ftunem\"></a>\n", "## Step 5. Fine-tune the model\n", "\n", " Fine-tunes the PatchTST model using the pretrained base model loaded above. We recommend that the user keep the settings\n", " as they are below, with the exception of:\n", "  - num_train_epochs: The number of training epochs. This may need to be adjusted to ensure sufficient training.\n"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["CODECARBON : No CPU tracking mode found. Falling back on CPU constant mode.\n", "CODECARBON : Failed to match CPU TDP constant. Falling back on a global constant.\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "d48c08b6194548fa9cedb78d8afda910", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/3015 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'loss': 0.4393, 'learning_rate': 3.3333333333333335e-05, 'epoch': 1.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "7d6860d93aa54bafbbcf905f1842a3fd", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/44 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Checkpoint destination directory ./checkpoint/forecast/checkpoint-1005 already exists and is non-empty. Saving will proceed but saved results may be invalid.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.21061904728412628, 'eval_runtime': 1.5565, 'eval_samples_per_second': 1789.266, 'eval_steps_per_second': 28.268, 'epoch': 1.0}\n", "{'loss': 0.3652, 'learning_rate': 1.6666666666666667e-05, 'epoch': 2.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "f432aad5e8c94048aaef4d71da1073e7", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/44 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Checkpoint destination directory ./checkpoint/forecast/checkpoint-2010 already exists and is non-empty. Saving will proceed but saved results may be invalid.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.20807470381259918, 'eval_runtime': 1.1655, 'eval_samples_per_second': 2389.537, 'eval_steps_per_second': 37.752, 'epoch': 2.0}\n", "{'loss': 0.3293, 'learning_rate': 0.0, 'epoch': 3.0}\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "285c9b3fbf0049f9a6cec4c2a8b6e617", "version_major": 2, "version_minor": 0}, "text/plain": ["  0%|          | 0/44 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["Checkpoint destination directory ./checkpoint/forecast/checkpoint-3015 already exists and is non-empty. Saving will proceed but saved results may be invalid.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.21140334010124207, 'eval_runtime': 1.1507, 'eval_samples_per_second': 2420.339, 'eval_steps_per_second': 38.239, 'epoch': 3.0}\n", "{'train_runtime': 104.4883, 'train_samples_per_second': 230.638, 'train_steps_per_second': 28.855, 'train_loss': 0.3779245556883551, 'epoch': 3.0}\n"]}, {"data": {"text/plain": ["['preprocessor/preprocessor_config.json']"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["training_args = TrainingArguments(\n", "    output_dir=\"./checkpoint/forecast\",\n", "    per_device_train_batch_size=8,  # defaults to 8\n", "    per_device_eval_batch_size=64,  # defaults to 8\n", "    num_train_epochs=3,\n", "    evaluation_strategy=\"epoch\",\n", "    save_strategy=\"epoch\",\n", "    save_total_limit=5,\n", "    logging_strategy=\"epoch\",\n", "    load_best_model_at_end=True,\n", "    # max_steps=10,  # For a quick test\n", "    label_names=[\"future_values\"],\n", ")\n", "\n", "forecasting_trainer = Trainer(\n", "    model=forecasting_model,\n", "    args=training_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=eval_dataset,\n", ")\n", "\n", "forecasting_trainer.train()\n", "forecasting_trainer.save_model(\"model/forecasting\")\n", "tsp.save_pretrained(\"preprocessor\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_concl\"></a>\n", "# Conclusion\n", "\n", "This notebook showed how to fine tune a Channel Independence PatchTST model in a supervised way. Fine tuning was set up to only affect the last linear layer of the model (linear probing). The model was trained using patches extracted from a longer segment of the time series as input, with the future values as a target.\n", "\n", "This is the second of three notebooks that should be run in sequence using training and test data from the ETTh1 benchmark dataset, which represents sensor data from an electric transformer.\n", "\n", "The above output shows the performance (training loss and validation loss) of the model during the fine tuning process. In this case we are using mean squared error (MSE) as a loss function. As the epochs progress we want performance to improve. We would like to see the training and validation losses decrease rapidly for a few epochs and then converge. Validation loss should be relatively close to training loss. Large differences between training and validation losses may be indicative of overfitting (training much lower than validation) or distribution shift between training and validation datasets."]}, {"cell_type": "markdown", "metadata": {}, "source": ["<a id=\"TST2_learn\"></a>\n", "# Learn More\n", "\n", "[This paper](https://arxiv.org/pdf/2211.14730.pdf) provides detailed information on Channel Independence PatchTST, including evaluations of its performance on 8 popular datasets, including Weather, Traffic, Electricity, ILI and 4 Electricity Transformer Temperature datasets (ETTh1, ETTh2, ETTm1, ETTm2). These publicly available datasets have been extensively utilized for benchmarking. We featured one of them (ETTh1) in this notebook.\n", "\n", "If you have any questions or wish to schedule a technical deep dive, contact us by <NAME_EMAIL>."]}, {"cell_type": "markdown", "metadata": {}, "source": ["© 2023 IBM Corporation"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 4}