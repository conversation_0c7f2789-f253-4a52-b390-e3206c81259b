{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "b6b03c92-c01f-4974-a850-42268c65117d", "metadata": {"editable": true, "slideshow": {"slide_type": ""}, "tags": []}, "source": ["# Granite-TimeSeries-TTM   \n", "\n", "TinyTimeMixers (TTMs) are compact pre-trained models for Multivariate Time-Series Forecasting, open-sourced by IBM Research. With less than 1 Million parameters, TTM introduces the notion of the first-ever \"tiny\" pre-trained models for Time-Series Forecasting. TTM outperforms several popular benchmarks demanding billions of parameters in zero-shot and few-shot forecasting and can easily be fine-tuned for multi-variate forecasts."]}, {"cell_type": "code", "execution_count": 1, "id": "e7deb64f-9f1a-4f20-aa1d-01b46abfa7d5", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:p-75334:t-8214683904:config.py:<module>:PyTorch version 2.3.0 available.\n"]}], "source": ["import pathlib\n", "\n", "import pandas as pd\n", "\n", "from tsfm_public import TimeSeriesForecastingPipeline, TinyTimeMixerForPrediction\n", "from tsfm_public.toolkit.visualization import plot_predictions"]}, {"cell_type": "code", "execution_count": 2, "id": "306a8c42-3d2f-4511-baa5-ce985d54c38f", "metadata": {}, "outputs": [{"data": {"text/plain": ["'0.2.10.dev14+gc889210.d20240918'"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import tsfm_public\n", "\n", "\n", "tsfm_public.__version__"]}, {"cell_type": "markdown", "id": "73406eda-65aa-438e-aee6-9c65f1a3ee56", "metadata": {}, "source": ["## Initial setup\n", "1. Download energy_data.csv.zip and weather_data.csv.zip from https://www.kaggle.com/datasets/nicholasjhana/energy-consumption-generation-prices-and-weather\n", "2. Place the downloaded files into a folder and update the data_path below"]}, {"cell_type": "code", "execution_count": 5, "id": "1563d66d-bf38-4fcf-bdd0-57a9187ef8e4", "metadata": {}, "outputs": [], "source": ["data_path = pathlib.Path(\"~/Downloads\")"]}, {"cell_type": "markdown", "id": "d0ce984c", "metadata": {}, "source": ["## Load and prepare data"]}, {"cell_type": "code", "execution_count": 6, "id": "984ca0d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["(512, 29)\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>generation biomass</th>\n", "      <th>generation fossil brown coal/lignite</th>\n", "      <th>generation fossil coal-derived gas</th>\n", "      <th>generation fossil gas</th>\n", "      <th>generation fossil hard coal</th>\n", "      <th>generation fossil oil</th>\n", "      <th>generation fossil oil shale</th>\n", "      <th>generation fossil peat</th>\n", "      <th>generation geothermal</th>\n", "      <th>...</th>\n", "      <th>generation waste</th>\n", "      <th>generation wind offshore</th>\n", "      <th>generation wind onshore</th>\n", "      <th>forecast solar day ahead</th>\n", "      <th>forecast wind offshore eday ahead</th>\n", "      <th>forecast wind onshore day ahead</th>\n", "      <th>total load forecast</th>\n", "      <th>total load actual</th>\n", "      <th>price day ahead</th>\n", "      <th>price actual</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>34552</th>\n", "      <td>2018-12-10 16:00:00+01:00</td>\n", "      <td>308.0</td>\n", "      <td>683.0</td>\n", "      <td>0.0</td>\n", "      <td>3978.0</td>\n", "      <td>3080.0</td>\n", "      <td>306.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>289.0</td>\n", "      <td>0.0</td>\n", "      <td>5746.0</td>\n", "      <td>2494.0</td>\n", "      <td>NaN</td>\n", "      <td>6466.0</td>\n", "      <td>24484.0</td>\n", "      <td>24465.0</td>\n", "      <td>56.96</td>\n", "      <td>69.76</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34553</th>\n", "      <td>2018-12-10 17:00:00+01:00</td>\n", "      <td>314.0</td>\n", "      <td>686.0</td>\n", "      <td>0.0</td>\n", "      <td>4338.0</td>\n", "      <td>3241.0</td>\n", "      <td>303.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>289.0</td>\n", "      <td>0.0</td>\n", "      <td>5524.0</td>\n", "      <td>1838.0</td>\n", "      <td>NaN</td>\n", "      <td>6269.0</td>\n", "      <td>24033.0</td>\n", "      <td>24068.0</td>\n", "      <td>67.32</td>\n", "      <td>73.48</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34554</th>\n", "      <td>2018-12-10 18:00:00+01:00</td>\n", "      <td>313.0</td>\n", "      <td>711.0</td>\n", "      <td>0.0</td>\n", "      <td>5020.0</td>\n", "      <td>3436.0</td>\n", "      <td>305.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>295.0</td>\n", "      <td>0.0</td>\n", "      <td>5139.0</td>\n", "      <td>1119.0</td>\n", "      <td>NaN</td>\n", "      <td>5962.0</td>\n", "      <td>24053.0</td>\n", "      <td>24018.0</td>\n", "      <td>68.68</td>\n", "      <td>77.65</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34555</th>\n", "      <td>2018-12-10 19:00:00+01:00</td>\n", "      <td>315.0</td>\n", "      <td>716.0</td>\n", "      <td>0.0</td>\n", "      <td>5449.0</td>\n", "      <td>3410.0</td>\n", "      <td>294.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>297.0</td>\n", "      <td>0.0</td>\n", "      <td>4933.0</td>\n", "      <td>404.0</td>\n", "      <td>NaN</td>\n", "      <td>5690.0</td>\n", "      <td>25203.0</td>\n", "      <td>25036.0</td>\n", "      <td>70.46</td>\n", "      <td>76.23</td>\n", "    </tr>\n", "    <tr>\n", "      <th>34556</th>\n", "      <td>2018-12-10 20:00:00+01:00</td>\n", "      <td>316.0</td>\n", "      <td>711.0</td>\n", "      <td>0.0</td>\n", "      <td>5645.0</td>\n", "      <td>3419.0</td>\n", "      <td>295.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "      <td>...</td>\n", "      <td>294.0</td>\n", "      <td>0.0</td>\n", "      <td>4929.0</td>\n", "      <td>200.0</td>\n", "      <td>NaN</td>\n", "      <td>5680.0</td>\n", "      <td>27579.0</td>\n", "      <td>27411.0</td>\n", "      <td>72.82</td>\n", "      <td>75.54</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>5 rows × 29 columns</p>\n", "</div>"], "text/plain": ["                            time  generation biomass  \\\n", "34552  2018-12-10 16:00:00+01:00               308.0   \n", "34553  2018-12-10 17:00:00+01:00               314.0   \n", "34554  2018-12-10 18:00:00+01:00               313.0   \n", "34555  2018-12-10 19:00:00+01:00               315.0   \n", "34556  2018-12-10 20:00:00+01:00               316.0   \n", "\n", "       generation fossil brown coal/lignite  \\\n", "34552                                 683.0   \n", "34553                                 686.0   \n", "34554                                 711.0   \n", "34555                                 716.0   \n", "34556                                 711.0   \n", "\n", "       generation fossil coal-derived gas  generation fossil gas  \\\n", "34552                                 0.0                 3978.0   \n", "34553                                 0.0                 4338.0   \n", "34554                                 0.0                 5020.0   \n", "34555                                 0.0                 5449.0   \n", "34556                                 0.0                 5645.0   \n", "\n", "       generation fossil hard coal  generation fossil oil  \\\n", "34552                       3080.0                  306.0   \n", "34553                       3241.0                  303.0   \n", "34554                       3436.0                  305.0   \n", "34555                       3410.0                  294.0   \n", "34556                       3419.0                  295.0   \n", "\n", "       generation fossil oil shale  generation fossil peat  \\\n", "34552                          0.0                     0.0   \n", "34553                          0.0                     0.0   \n", "34554                          0.0                     0.0   \n", "34555                          0.0                     0.0   \n", "34556                          0.0                     0.0   \n", "\n", "       generation geothermal  ...  generation waste  generation wind offshore  \\\n", "34552                    0.0  ...             289.0                       0.0   \n", "34553                    0.0  ...             289.0                       0.0   \n", "34554                    0.0  ...             295.0                       0.0   \n", "34555                    0.0  ...             297.0                       0.0   \n", "34556                    0.0  ...             294.0                       0.0   \n", "\n", "       generation wind onshore  forecast solar day ahead  \\\n", "34552                   5746.0                    2494.0   \n", "34553                   5524.0                    1838.0   \n", "34554                   5139.0                    1119.0   \n", "34555                   4933.0                     404.0   \n", "34556                   4929.0                     200.0   \n", "\n", "       forecast wind offshore eday ahead  forecast wind onshore day ahead  \\\n", "34552                                NaN                           6466.0   \n", "34553                                NaN                           6269.0   \n", "34554                                NaN                           5962.0   \n", "34555                                NaN                           5690.0   \n", "34556                                NaN                           5680.0   \n", "\n", "       total load forecast  total load actual  price day ahead  price actual  \n", "34552              24484.0            24465.0            56.96         69.76  \n", "34553              24033.0            24068.0            67.32         73.48  \n", "34554              24053.0            24018.0            68.68         77.65  \n", "34555              25203.0            25036.0            70.46         76.23  \n", "34556              27579.0            27411.0            72.82         75.54  \n", "\n", "[5 rows x 29 columns]"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# Download energy_data.csv.zip from https://www.kaggle.com/datasets/nicholasjhana/energy-consumption-generation-prices-and-weather\n", "\n", "dataset_path = data_path / \"energy_dataset.csv.zip\"\n", "timestamp_column = \"time\"\n", "\n", "target_column = \"total load actual\"\n", "\n", "context_length = 512  # set by the pretrained model we will use\n", "\n", "data = pd.read_csv(\n", "    dataset_path,\n", "    parse_dates=[timestamp_column],\n", ")\n", "\n", "data = data.ffill()\n", "\n", "data = data.iloc[-context_length:,]\n", "\n", "print(data.shape)\n", "data.head()"]}, {"attachments": {}, "cell_type": "markdown", "id": "75c2d666-2404-4e78-b564-ea8aec8afa2d", "metadata": {}, "source": ["## Load pretrained Granite-TimeSeries-TTM model (zero-shot)\n", "The **TTM** model supports huggingface model interface, allowing easy API for loading the saved models."]}, {"cell_type": "code", "execution_count": 7, "id": "eed3fe8b-b654-4fa4-9671-ce5ecd9e0b7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["TTM Model parameters: 805280\n"]}], "source": ["zeroshot_model = TinyTimeMixerForPrediction.from_pretrained(\n", "    \"ibm-granite/granite-timeseries-ttm-v1\", num_input_channels=1\n", ")\n", "model_parameters = sum(p.numel() for p in zeroshot_model.parameters() if p.requires_grad)\n", "print(\"TTM Model parameters:\", model_parameters)"]}, {"cell_type": "markdown", "id": "b6ab206c", "metadata": {}, "source": ["### Create a time series forecasting pipeline"]}, {"cell_type": "code", "execution_count": 8, "id": "d9aa0f26", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>time</th>\n", "      <th>total load actual_prediction</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>2019-01-01 00:00:00+01:00</td>\n", "      <td>23504.996094</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>2019-01-01 01:00:00+01:00</td>\n", "      <td>22338.626953</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>2019-01-01 02:00:00+01:00</td>\n", "      <td>21448.902344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>2019-01-01 03:00:00+01:00</td>\n", "      <td>20982.527344</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2019-01-01 04:00:00+01:00</td>\n", "      <td>20697.183594</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                       time  total load actual_prediction\n", "0 2019-01-01 00:00:00+01:00                  23504.996094\n", "1 2019-01-01 01:00:00+01:00                  22338.626953\n", "2 2019-01-01 02:00:00+01:00                  21448.902344\n", "3 2019-01-01 03:00:00+01:00                  20982.527344\n", "4 2019-01-01 04:00:00+01:00                  20697.183594"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["pipeline = TimeSeriesForecastingPipeline(\n", "    zeroshot_model,\n", "    timestamp_column=timestamp_column,\n", "    target_columns=[target_column],\n", "    explode_forecasts=True,\n", "    freq=\"h\",\n", "    id_columns=[],\n", ")\n", "zeroshot_forecast = pipeline(data)\n", "zeroshot_forecast.head()"]}, {"cell_type": "markdown", "id": "5c4676bd", "metadata": {}, "source": ["### Plot the results"]}, {"cell_type": "code", "execution_count": 9, "id": "ba065a24", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_predictions(\n", "    input_df=data,\n", "    exploded_predictions_df=zeroshot_forecast,\n", "    freq=\"h\",\n", "    timestamp_column=timestamp_column,\n", "    channel=target_column,\n", "    indices=[-1],\n", "    num_plots=1,\n", ")"]}, {"cell_type": "markdown", "id": "5123e226-1a66-434c-a400-f7be59974d5d", "metadata": {}, "source": ["## Useful links\n", "\n", "TinyTimeMixer paper: https://arxiv.org/abs/2401.03955  \n", "\n", "Granite-TimeSeries-TTM model: https://huggingface.co/ibm-granite/granite-timeseries-ttm-v1  \n", "\n", "Publicly available tools for working with our models: https://github.com/ibm-granite/granite-tsfm"]}, {"cell_type": "markdown", "id": "53116412", "metadata": {}, "source": ["© 2024 IBM Corporation"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 5}