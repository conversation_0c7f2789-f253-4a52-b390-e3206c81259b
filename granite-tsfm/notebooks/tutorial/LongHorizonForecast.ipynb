{"cells": [{"cell_type": "code", "execution_count": 44, "id": "590aa4e9-cb5f-431c-ac50-c3c84c0528c7", "metadata": {}, "outputs": [], "source": ["%%capture\n", "try:\n", "    import pandas  # noqa: F401\n", "except ImportError:\n", "    !pip install pandas\n", "\n", "try:\n", "    import numpy  # noqa: F401\n", "except ImportError:\n", "    !pip install numpy\n", "\n", "try:\n", "    import matplotlib  # noqa: F401\n", "except ImportError:\n", "    !pip install matplotlib\n", "\n", "try:\n", "    import sklearn  # noqa: F401\n", "except ImportError:\n", "    !pip install scikit-learn\n", "\n", "try:\n", "    import scipy  # noqa: F401\n", "except ImportError:\n", "    !pip install scipy\n", "\n", "try:\n", "    import torch  # noqa: F401\n", "except ImportError:\n", "    !pip install torch\n", "\n", "try:\n", "    import tqdm  # noqa: F401\n", "except ImportError:\n", "    !pip install tqdm"]}, {"cell_type": "code", "execution_count": 2, "id": "3c7c61c4-bd34-400f-b596-929e65e0fcd7", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "from sklearn.ensemble import RandomForestRegressor\n", "from sklearn.preprocessing import StandardScaler"]}, {"cell_type": "markdown", "id": "2fbcb005-e507-498c-9e14-da2342d58ad8", "metadata": {}, "source": ["## Loading the Data"]}, {"cell_type": "code", "execution_count": 3, "id": "4d5068d2-a71c-42cf-b464-615f719966b7", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\n", "    \"https://raw.githubusercontent.com/zhouhaoyi/ETDataset/main/ETT-small/ETTh1.csv\", parse_dates=[\"date\"]\n", ")\n", "df = df.set_index(\"date\")\n", "df.index.freq = pd.infer_freq(df.index)"]}, {"cell_type": "markdown", "id": "97c40220-3ff4-4066-8736-d29789a70dbc", "metadata": {}, "source": ["## Training Configuration "]}, {"cell_type": "code", "execution_count": 18, "id": "9686bade-3942-4a19-9a05-69c198ec9d53", "metadata": {}, "outputs": [], "source": ["TRAIN_FRACTION = 0.8\n", "CONTEXT_LENGTH = 512\n", "FORECAST_LENGTH = 96\n", "STRIDE = 1\n", "N_SAMPLE_TO_PLOT = 4\n", "PAST_WINDOW = 100"]}, {"cell_type": "markdown", "id": "4050c2e6-7ac4-4eab-bc8d-d90b5c5505e1", "metadata": {}, "source": ["## Preparing the Training Data\n", "\n", "Data prepared from the data with a history and Forecast length `512` and `96` respectively, in a rolling window fashion."]}, {"cell_type": "code", "execution_count": 26, "id": "5491226d-4378-451f-bab9-e374c4694068", "metadata": {}, "outputs": [], "source": ["data = df.OT.values\n", "\n", "x = []\n", "\n", "for i in range(1, len(data) - CONTEXT_LENGTH - FORECAST_LENGTH, STRIDE):\n", "    x.append(data[i : (i + CONTEXT_LENGTH + FORECAST_LENGTH)])\n", "\n", "x = np.array(x).astype(float)\n", "\n", "X, y = x[:, :CONTEXT_LENGTH], x[:, CONTEXT_LENGTH:]\n", "\n", "train_length = int(TRAIN_FRACTION * len(X))\n", "\n", "trainX, testX = X[:train_length], X[train_length:]\n", "trainY, testY = y[:train_length], y[train_length:]"]}, {"cell_type": "markdown", "id": "02ea6944-c8c9-4660-ad1e-21b6123badc2", "metadata": {}, "source": ["## Instantiating the RandomForestRegressor\n", "\n", "Please feel free to change the `n_estimators` and `max_depth` parameter, and see their impact on the model forecast."]}, {"cell_type": "code", "execution_count": 27, "id": "f35d0432-2ef8-48d4-bb99-3de719049df7", "metadata": {}, "outputs": [], "source": ["rf_model = RandomForestRegressor(\n", "    n_estimators=50,\n", "    max_depth=5,\n", "    # n_jobs=4,\n", "    verbose=2,\n", ")"]}, {"cell_type": "code", "execution_count": 28, "id": "8181a5d5-7eaf-4d01-b03e-ef606315ad09", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=4)]: Using backend ThreadingBackend with 4 concurrent workers.\n", "[Parallel(n_jobs=4)]: Done  42 tasks      | elapsed:   49.6s\n", "[Parallel(n_jobs=4)]: Done  50 out of  50 | elapsed:   59.3s finished\n"]}, {"data": {"text/html": ["<style>#sk-container-id-2 {\n", "  /* Definition of color scheme common for light and dark mode */\n", "  --sklearn-color-text: black;\n", "  --sklearn-color-line: gray;\n", "  /* Definition of color scheme for unfitted estimators */\n", "  --sklearn-color-unfitted-level-0: #fff5e6;\n", "  --sklearn-color-unfitted-level-1: #f6e4d2;\n", "  --sklearn-color-unfitted-level-2: #ffe0b3;\n", "  --sklearn-color-unfitted-level-3: chocolate;\n", "  /* Definition of color scheme for fitted estimators */\n", "  --sklearn-color-fitted-level-0: #f0f8ff;\n", "  --sklearn-color-fitted-level-1: #d4ebff;\n", "  --sklearn-color-fitted-level-2: #b3dbfd;\n", "  --sklearn-color-fitted-level-3: cornflowerblue;\n", "\n", "  /* Specific color for light theme */\n", "  --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, white)));\n", "  --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, black)));\n", "  --sklearn-color-icon: #696969;\n", "\n", "  @media (prefers-color-scheme: dark) {\n", "    /* Redefinition of color scheme for dark theme */\n", "    --sklearn-color-text-on-default-background: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-background: var(--sg-background-color, var(--theme-background, var(--jp-layout-color0, #111)));\n", "    --sklearn-color-border-box: var(--sg-text-color, var(--theme-code-foreground, var(--jp-content-font-color1, white)));\n", "    --sklearn-color-icon: #878787;\n", "  }\n", "}\n", "\n", "#sk-container-id-2 {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "#sk-container-id-2 pre {\n", "  padding: 0;\n", "}\n", "\n", "#sk-container-id-2 input.sk-hidden--visually {\n", "  border: 0;\n", "  clip: rect(1px 1px 1px 1px);\n", "  clip: rect(1px, 1px, 1px, 1px);\n", "  height: 1px;\n", "  margin: -1px;\n", "  overflow: hidden;\n", "  padding: 0;\n", "  position: absolute;\n", "  width: 1px;\n", "}\n", "\n", "#sk-container-id-2 div.sk-dashed-wrapped {\n", "  border: 1px dashed var(--sklearn-color-line);\n", "  margin: 0 0.4em 0.5em 0.4em;\n", "  box-sizing: border-box;\n", "  padding-bottom: 0.4em;\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "#sk-container-id-2 div.sk-container {\n", "  /* jup<PERSON>r's `normalize.less` sets `[hidden] { display: none; }`\n", "     but bootstrap.min.css set `[hidden] { display: none !important; }`\n", "     so we also need the `!important` here to be able to override the\n", "     default hidden behavior on the sphinx rendered scikit-learn.org.\n", "     See: https://github.com/scikit-learn/scikit-learn/issues/21755 */\n", "  display: inline-block !important;\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-text-repr-fallback {\n", "  display: none;\n", "}\n", "\n", "div.sk-parallel-item,\n", "div.sk-serial,\n", "div.sk-item {\n", "  /* draw centered vertical line to link estimators */\n", "  background-image: linear-gradient(var(--sklearn-color-text-on-default-background), var(--sklearn-color-text-on-default-background));\n", "  background-size: 2px 100%;\n", "  background-repeat: no-repeat;\n", "  background-position: center center;\n", "}\n", "\n", "/* Parallel-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-parallel-item::after {\n", "  content: \"\";\n", "  width: 100%;\n", "  border-bottom: 2px solid var(--sklearn-color-text-on-default-background);\n", "  flex-grow: 1;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel {\n", "  display: flex;\n", "  align-items: stretch;\n", "  justify-content: center;\n", "  background-color: var(--sklearn-color-background);\n", "  position: relative;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item {\n", "  display: flex;\n", "  flex-direction: column;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:first-child::after {\n", "  align-self: flex-end;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:last-child::after {\n", "  align-self: flex-start;\n", "  width: 50%;\n", "}\n", "\n", "#sk-container-id-2 div.sk-parallel-item:only-child::after {\n", "  width: 0;\n", "}\n", "\n", "/* Serial-specific style estimator block */\n", "\n", "#sk-container-id-2 div.sk-serial {\n", "  display: flex;\n", "  flex-direction: column;\n", "  align-items: center;\n", "  background-color: var(--sklearn-color-background);\n", "  padding-right: 1em;\n", "  padding-left: 1em;\n", "}\n", "\n", "\n", "/* Toggleable style: style used for estimator/Pipeline/ColumnTransformer box that is\n", "clickable and can be expanded/collapsed.\n", "- Pipeline and ColumnTransformer use this feature and define the default style\n", "- Estimators will overwrite some part of the style using the `sk-estimator` class\n", "*/\n", "\n", "/* Pipeline and ColumnTransformer style (default) */\n", "\n", "#sk-container-id-2 div.sk-toggleable {\n", "  /* Default theme specific background. It is overwritten whether we have a\n", "  specific estimator or a Pipeline/ColumnTransformer */\n", "  background-color: var(--sklearn-color-background);\n", "}\n", "\n", "/* Toggleable label */\n", "#sk-container-id-2 label.sk-toggleable__label {\n", "  cursor: pointer;\n", "  display: block;\n", "  width: 100%;\n", "  margin-bottom: 0;\n", "  padding: 0.5em;\n", "  box-sizing: border-box;\n", "  text-align: center;\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:before {\n", "  /* <PERSON> on the left of the label */\n", "  content: \"▸\";\n", "  float: left;\n", "  margin-right: 0.25em;\n", "  color: var(--sklearn-color-icon);\n", "}\n", "\n", "#sk-container-id-2 label.sk-toggleable__label-arrow:hover:before {\n", "  color: var(--sklearn-color-text);\n", "}\n", "\n", "/* Toggleable content - dropdown */\n", "\n", "#sk-container-id-2 div.sk-toggleable__content {\n", "  max-height: 0;\n", "  max-width: 0;\n", "  overflow: hidden;\n", "  text-align: left;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content pre {\n", "  margin: 0.2em;\n", "  border-radius: 0.25em;\n", "  color: var(--sklearn-color-text);\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-toggleable__content.fitted pre {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~div.sk-toggleable__content {\n", "  /* Expand drop-down */\n", "  max-height: 200px;\n", "  max-width: 100%;\n", "  overflow: auto;\n", "}\n", "\n", "#sk-container-id-2 input.sk-toggleable__control:checked~label.sk-toggleable__label-arrow:before {\n", "  content: \"▾\";\n", "}\n", "\n", "/* Pipeline/ColumnTransformer-specific style */\n", "\n", "#sk-container-id-2 div.sk-label input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator-specific style */\n", "\n", "/* Colorize estimator box */\n", "#sk-container-id-2 div.sk-estimator input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted input.sk-toggleable__control:checked~label.sk-toggleable__label {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-label label.sk-toggleable__label,\n", "#sk-container-id-2 div.sk-label label {\n", "  /* The background is the default theme color */\n", "  color: var(--sklearn-color-text-on-default-background);\n", "}\n", "\n", "/* On hover, darken the color of the background */\n", "#sk-container-id-2 div.sk-label:hover label.sk-toggleable__label {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "/* Label box, darken color on hover, fitted */\n", "#sk-container-id-2 div.sk-label.fitted:hover label.sk-toggleable__label.fitted {\n", "  color: var(--sklearn-color-text);\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Estimator label */\n", "\n", "#sk-container-id-2 div.sk-label label {\n", "  font-family: monospace;\n", "  font-weight: bold;\n", "  display: inline-block;\n", "  line-height: 1.2em;\n", "}\n", "\n", "#sk-container-id-2 div.sk-label-container {\n", "  text-align: center;\n", "}\n", "\n", "/* Estimator-specific */\n", "#sk-container-id-2 div.sk-estimator {\n", "  font-family: monospace;\n", "  border: 1px dotted var(--sklearn-color-border-box);\n", "  border-radius: 0.25em;\n", "  box-sizing: border-box;\n", "  margin-bottom: 0.5em;\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-0);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-0);\n", "}\n", "\n", "/* on hover */\n", "#sk-container-id-2 div.sk-estimator:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-2);\n", "}\n", "\n", "#sk-container-id-2 div.sk-estimator.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-2);\n", "}\n", "\n", "/* Specification for estimator info (e.g. \"i\" and \"?\") */\n", "\n", "/* Common style for \"i\" and \"?\" */\n", "\n", ".sk-estimator-doc-link,\n", "a:link.sk-estimator-doc-link,\n", "a:visited.sk-estimator-doc-link {\n", "  float: right;\n", "  font-size: smaller;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1em;\n", "  height: 1em;\n", "  width: 1em;\n", "  text-decoration: none !important;\n", "  margin-left: 1ex;\n", "  /* unfitted */\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted,\n", "a:link.sk-estimator-doc-link.fitted,\n", "a:visited.sk-estimator-doc-link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "div.sk-estimator:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link:hover,\n", ".sk-estimator-doc-link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "div.sk-estimator.fitted:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover,\n", "div.sk-label-container:hover .sk-estimator-doc-link.fitted:hover,\n", ".sk-estimator-doc-link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "/* Span, style for the box shown on hovering the info icon */\n", ".sk-estimator-doc-link span {\n", "  display: none;\n", "  z-index: 9999;\n", "  position: relative;\n", "  font-weight: normal;\n", "  right: .2ex;\n", "  padding: .5ex;\n", "  margin: .5ex;\n", "  width: min-content;\n", "  min-width: 20ex;\n", "  max-width: 50ex;\n", "  color: var(--sklearn-color-text);\n", "  box-shadow: 2pt 2pt 4pt #999;\n", "  /* unfitted */\n", "  background: var(--sklearn-color-unfitted-level-0);\n", "  border: .5pt solid var(--sklearn-color-unfitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link.fitted span {\n", "  /* fitted */\n", "  background: var(--sklearn-color-fitted-level-0);\n", "  border: var(--sklearn-color-fitted-level-3);\n", "}\n", "\n", ".sk-estimator-doc-link:hover span {\n", "  display: block;\n", "}\n", "\n", "/* \"?\"-specific style due to the `<a>` HTML tag */\n", "\n", "#sk-container-id-2 a.estimator_doc_link {\n", "  float: right;\n", "  font-size: 1rem;\n", "  line-height: 1em;\n", "  font-family: monospace;\n", "  background-color: var(--sklearn-color-background);\n", "  border-radius: 1rem;\n", "  height: 1rem;\n", "  width: 1rem;\n", "  text-decoration: none;\n", "  /* unfitted */\n", "  color: var(--sklearn-color-unfitted-level-1);\n", "  border: var(--sklearn-color-unfitted-level-1) 1pt solid;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted {\n", "  /* fitted */\n", "  border: var(--sklearn-color-fitted-level-1) 1pt solid;\n", "  color: var(--sklearn-color-fitted-level-1);\n", "}\n", "\n", "/* On hover */\n", "#sk-container-id-2 a.estimator_doc_link:hover {\n", "  /* unfitted */\n", "  background-color: var(--sklearn-color-unfitted-level-3);\n", "  color: var(--sklearn-color-background);\n", "  text-decoration: none;\n", "}\n", "\n", "#sk-container-id-2 a.estimator_doc_link.fitted:hover {\n", "  /* fitted */\n", "  background-color: var(--sklearn-color-fitted-level-3);\n", "}\n", "</style><div id=\"sk-container-id-2\" class=\"sk-top-container\"><div class=\"sk-text-repr-fallback\"><pre>RandomForestRegressor(max_depth=5, n_estimators=50, n_jobs=4, verbose=1)</pre><b>In a Jupyter environment, please rerun this cell to show the HTML representation or trust the notebook. <br />On GitHub, the HTML representation is unable to render, please try loading this page with nbviewer.org.</b></div><div class=\"sk-container\" hidden><div class=\"sk-item\"><div class=\"sk-estimator fitted sk-toggleable\"><input class=\"sk-toggleable__control sk-hidden--visually\" id=\"sk-estimator-id-2\" type=\"checkbox\" checked><label for=\"sk-estimator-id-2\" class=\"sk-toggleable__label fitted sk-toggleable__label-arrow fitted\">&nbsp;&nbsp;RandomForestRegressor<a class=\"sk-estimator-doc-link fitted\" rel=\"noreferrer\" target=\"_blank\" href=\"https://scikit-learn.org/1.4/modules/generated/sklearn.ensemble.RandomForestRegressor.html\">?<span>Documentation for RandomForestRegressor</span></a><span class=\"sk-estimator-doc-link fitted\">i<span>Fitted</span></span></label><div class=\"sk-toggleable__content fitted\"><pre>RandomForestRegressor(max_depth=5, n_estimators=50, n_jobs=4, verbose=1)</pre></div> </div></div></div></div>"], "text/plain": ["RandomForestRegressor(max_depth=5, n_estimators=50, n_jobs=4, verbose=1)"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["rf_model.fit(trainX, trainY)"]}, {"cell_type": "code", "execution_count": 29, "id": "d27f8a3f-325c-4ffc-953d-754f6a8ded46", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["[Parallel(n_jobs=4)]: Using backend ThreadingBackend with 4 concurrent workers.\n", "[Parallel(n_jobs=4)]: Done  42 tasks      | elapsed:    0.0s\n", "[Parallel(n_jobs=4)]: Done  50 out of  50 | elapsed:    0.0s finished\n"]}], "source": ["predictY = rf_model.predict(testX)"]}, {"cell_type": "code", "execution_count": 30, "id": "33219a13-e403-4ca2-a784-1a84213828cf", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction RMSE = 2.3637\n"]}], "source": ["rmse = np.mean(np.sqrt(np.mean(np.square(testY - predictY), axis=-1)))\n", "print(f\"Prediction RMSE = {rmse:.4f}\")"]}, {"cell_type": "code", "execution_count": 143, "id": "f6126f91-2a05-4b9b-9972-dab64fb2c0ea", "metadata": {}, "outputs": [{"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2000x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["random_indices = np.random.default_rng(1).choice(len(testY), N_SAMPLE_TO_PLOT, replace=False)\n", "\n", "fig, axs = plt.subplots(N_SAMPLE_TO_PLOT, 1, sharex=\"col\", sharey=True, figsize=(20, 2 * N_SAMPLE_TO_PLOT))\n", "\n", "for i, index in enumerate(random_indices):\n", "    n_history = min(PAST_WINDOW, CONTEXT_LENGTH)\n", "    y_true = np.concatenate([testX[index, -n_history:], testY[index]])\n", "    y_pred = np.concatenate([testX[index, -n_history:], predictY[index]])\n", "    axs[i].plot(y_true, color=\"green\", linestyle=\"dashed\", linewidth=1, label=\"actual\")\n", "    axs[i].plot(y_pred, color=\"orange\", linewidth=1, label=\"predicted\")\n", "    for s in [\"top\", \"right\"]:\n", "        axs[i].spines[s].set_visible(False)\n", "    axs[i].plot([n_history - 1, n_history - 1], [0, 15], linewidth=2, linestyle=\"dotted\", color=\"gray\")\n", "    axs[i].legend()"]}, {"cell_type": "markdown", "id": "b9e8b4d7-3415-4262-bef2-2722ee812b99", "metadata": {}, "source": ["## Neural Network Example"]}, {"cell_type": "code", "execution_count": 71, "id": "d2444d5c-a8ed-45b4-9e8a-70ce224bbf7f", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "from torch.nn import functional as F\n", "from torch.utils.data import DataLoader, Dataset\n", "from tqdm import tqdm"]}, {"cell_type": "code", "execution_count": 34, "id": "11509a29-6ff2-4e77-8b4e-3e3377685515", "metadata": {}, "outputs": [], "source": ["border1s = [0, 12 * 30 * 24 - CONTEXT_LENGTH, 12 * 30 * 24 + 4 * 30 * 24 - CONTEXT_LENGTH]\n", "border2s = [12 * 30 * 24, 12 * 30 * 24 + 4 * 30 * 24, 12 * 30 * 24 + 8 * 30 * 24]"]}, {"cell_type": "code", "execution_count": 36, "id": "cb0f3654-37cb-48d3-9cd7-f751e40c707d", "metadata": {}, "outputs": [], "source": ["data = df.OT.values.reshape(-1, 1)\n", "scaler = StandardScaler()\n", "train_data = data[border1s[0] : border2s[0], :]\n", "scaler = StandardScaler()\n", "scaler.fit(train_data)\n", "\n", "data = scaler.transform(data)\n", "train_data = data[border1s[0] : border2s[0], :]\n", "valid_data = data[border1s[1] : border2s[1], :]\n", "test_data = data[border1s[2] : border2s[2], :]"]}, {"cell_type": "code", "execution_count": 196, "id": "f7bb48c0-67ca-4fdb-b24c-0af2c0cfb022", "metadata": {}, "outputs": [], "source": ["train_io_pairs = np.array(\n", "    [\n", "        train_data[i : (i + CONTEXT_LENGTH + FORECAST_LENGTH)]\n", "        for i in range(1, len(train_data) - CONTEXT_LENGTH - FORECAST_LENGTH, STRIDE)\n", "    ]\n", ")\n", "X_train = train_io_pairs[:, :CONTEXT_LENGTH]\n", "y_train = train_io_pairs[:, CONTEXT_LENGTH:]\n", "\n", "valid_io_pairs = np.array(\n", "    [\n", "        valid_data[i : (i + CONTEXT_LENGTH + FORECAST_LENGTH), 0]\n", "        for i in range(1, len(valid_data) - CONTEXT_LENGTH - FORECAST_LENGTH, STRIDE)\n", "    ]\n", ")\n", "\n", "X_valid = valid_io_pairs[:, :CONTEXT_LENGTH]\n", "y_valid = valid_io_pairs[:, CONTEXT_LENGTH:]\n", "\n", "test_io_pairs = np.array(\n", "    [\n", "        test_data[i : (i + CONTEXT_LENGTH + FORECAST_LENGTH), 0]\n", "        for i in range(1, len(test_data) - CONTEXT_LENGTH - FORECAST_LENGTH, STRIDE)\n", "    ]\n", ")\n", "X_test = test_io_pairs[:, :CONTEXT_LENGTH]\n", "y_test = test_io_pairs[:, CONTEXT_LENGTH:]"]}, {"cell_type": "markdown", "id": "a43a2417-6f68-46da-b84a-61822e916d67", "metadata": {}, "source": ["## Dataset Class\n", "This is an important class, as it prepare the data for the model.\n", "Any online data clearning, reshaping, augmentation implementation, can be incorporated in this `Dataset` class instance."]}, {"cell_type": "code", "execution_count": 197, "id": "a0147b6d-5ec8-4fee-bc18-62f63083691d", "metadata": {}, "outputs": [], "source": ["class Data(Dataset):\n", "    def __init__(self, x, y):\n", "        self.x = torch.from_numpy(x).float()\n", "        self.y = torch.from_numpy(y).float()\n", "        self.len = self.x.shape[0]\n", "\n", "    def __getitem__(self, index):\n", "        return self.x[index], self.y[index]\n", "\n", "    def __len__(self):\n", "        return self.len"]}, {"cell_type": "markdown", "id": "23a728ef-d649-4f3c-bab7-cd3d7c22a2ba", "metadata": {}, "source": ["## Simple 3 layer MLP\n"]}, {"cell_type": "code", "execution_count": 214, "id": "cd0beac4-8db5-4bb5-ba57-0dc9e867e687", "metadata": {}, "outputs": [], "source": ["class CreateModel(nn.Module):\n", "    def __init__(self, in_features, units, out_features):\n", "        super(<PERSON><PERSON><PERSON><PERSON><PERSON>, self).__init__()\n", "        self.l1 = nn.Linear(in_features, units)\n", "        self.l2 = nn.Linear(units, units)\n", "        self.l3 = nn.Linear(units, out_features)\n", "\n", "    def forward(self, x):\n", "        x = torch.flatten(x, start_dim=1)\n", "        x = F.tanh(self.l1(x))\n", "        x = F.tanh(self.l2(x))\n", "        x = self.l3(x)\n", "        x = x.unsqueeze(-1)\n", "        return x"]}, {"cell_type": "markdown", "id": "8f73e642-2135-4e0b-a650-a718f4109ba1", "metadata": {}, "source": ["## Preparing the data and model for training"]}, {"cell_type": "code", "execution_count": 269, "id": "6b2ef578-5535-481c-9f7a-083919bd79fa", "metadata": {}, "outputs": [], "source": ["HIDDEN_UNITS = 16\n", "EPOCHS = 25\n", "\n", "torch.manual_seed(42)\n", "tr_data = Data(X_train, y_train)\n", "tr_loader = DataLoader(dataset=tr_data, batch_size=64, shuffle=True)\n", "nn_model = CreateModel(CONTEXT_LENGTH, HIDDEN_UNITS, FORECAST_LENGTH)\n", "criterion = nn.MS<PERSON><PERSON>()\n", "optimizer = torch.optim.Adam(nn_model.parameters(), lr=0.001)"]}, {"cell_type": "code", "execution_count": null, "id": "859226f2-1dd8-4cc6-8256-db3756abca54", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Epoch[1 : 125/126]: train_loss 0.4470: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 317.26it/s]\n", "Epoch[2 : 125/126]: train_loss 0.2022: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 331.88it/s]\n", "Epoch[3 : 125/126]: train_loss 0.1697: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 324.24it/s]\n", "Epoch[4 : 125/126]: train_loss 0.1561: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 346.47it/s]\n", "Epoch[5 : 125/126]: train_loss 0.1449: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 335.73it/s]\n", "Epoch[6 : 125/126]: train_loss 0.1372: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 330.70it/s]\n", "Epoch[7 : 125/126]: train_loss 0.1300: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 331.79it/s]\n", "Epoch[8 : 125/126]: train_loss 0.1268: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 334.14it/s]\n", "Epoch[9 : 125/126]: train_loss 0.1179: 100%|█████████████████████████████████████████████████████| 126/126 [00:00<00:00, 313.75it/s]\n", "Epoch[10 : 125/126]: train_loss 0.1159: 100%|████████████████████████████████████████████████████| 126/126 [00:00<00:00, 323.32it/s]\n", "Epoch[11 : 9/126]: train_loss 0.1198:   0%|                                                                 | 0/126 [00:00<?, ?it/s]"]}], "source": ["loss_list = []\n", "use_l1 = False\n", "\n", "for t in range(EPOCHS):\n", "    running_loss = 0\n", "    pbar = tqdm(tr_loader)\n", "    for i, data in enumerate(pbar):\n", "        x_batch, y_batch = data\n", "        y_pred = nn_model(x_batch)\n", "        loss = criterion(y_pred, y_batch)\n", "        if use_l1:\n", "            l1_parameters = []\n", "            for parameter in nn_model.parameters():\n", "                l1_parameters.append(parameter.view(-1))\n", "            l1_loss = torch.abs(torch.cat(l1_parameters)).mean()\n", "            loss = loss + l1_loss\n", "        nn_model.zero_grad()\n", "        loss.backward()\n", "        optimizer.step()\n", "        running_loss += loss.item()\n", "        pbar.set_description(f\"Epoch[{t + 1} : {i}/{len(tr_loader)}]: train_loss {running_loss / (i + 1):.4f}\")\n", "    loss_list.append(running_loss / len(tr_loader))"]}, {"cell_type": "markdown", "id": "d2243e87-661c-493c-b794-a9de758214b3", "metadata": {}, "source": ["## Plotting the loss convergence"]}, {"cell_type": "code", "execution_count": 266, "id": "6f14f297-9019-47ea-9bd3-91dbf4c54f8c", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1000x200 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["step = np.linspace(0, EPOCHS, EPOCHS)\n", "fig, ax = plt.subplots(1, 1, figsize=(10, 2))\n", "ax.plot(step, np.array(loss_list), marker=\"*\")\n", "for s in [\"right\", \"top\"]:\n", "    ax.spines[s].set_visible(False)\n", "ax.set_xlabel(\"Epochs\", fontsize=12)\n", "ax.set_ylabel(\"Loss\", fontsize=12)\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 267, "id": "18de9eab-b93e-4a9e-8809-23af11d598c8", "metadata": {}, "outputs": [], "source": ["yp_test = nn_model(torch.from_numpy(X_test).float()).to(\"cpu\").detach().numpy()"]}, {"cell_type": "code", "execution_count": 268, "id": "294f2b4d-14d2-49e2-8c16-d5d1f409bbbd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction RMSE = 4.9151\n"]}], "source": ["y_test_ = np.array([scaler.inverse_transform(y_[..., np.newaxis]) for y_ in y_test])\n", "yp_test_ = np.array([scaler.inverse_transform(y_) for y_ in yp_test])\n", "\n", "rmse = np.mean(np.sqrt(np.mean(np.square(y_test_ - yp_test_), axis=1)))\n", "print(f\"Prediction RMSE = {rmse:.4f}\")"]}, {"cell_type": "code", "execution_count": 264, "id": "5725369c-ab16-4ca2-bbda-d96fc8031b8f", "metadata": {}, "outputs": [{"data": {"image/png": "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******************************************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***************************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", "text/plain": ["<Figure size 2000x800 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["random_indices = np.random.default_rng(1).choice(len(y_test), N_SAMPLE_TO_PLOT, replace=False)\n", "\n", "fig, axs = plt.subplots(N_SAMPLE_TO_PLOT, 1, sharex=\"col\", figsize=(20, 2 * N_SAMPLE_TO_PLOT))\n", "\n", "for i, index in enumerate(random_indices):\n", "    n_history = min(PAST_WINDOW, CONTEXT_LENGTH)\n", "    x_hist = scaler.inverse_transform(X_test[index, -n_history:].reshape(-1, 1))\n", "    y_true = np.concatenate([x_hist[..., 0], y_test_[index, :, 0]], axis=0)\n", "    y_true = y_true[..., np.newaxis]\n", "    y_pred = np.concatenate([x_hist[..., 0], yp_test_[index, :, 0]], axis=0)\n", "    y_pred = y_pred[..., np.newaxis]\n", "    axs[i].plot(y_true, color=\"green\", linestyle=\"dashed\", linewidth=1, label=\"actual\")\n", "    axs[i].plot(y_pred, color=\"orange\", linewidth=1, label=\"predicted\")\n", "    for s in [\"top\", \"right\"]:\n", "        axs[i].spines[s].set_visible(False)\n", "    axs[i].plot([n_history - 1, n_history - 1], [-1, 0], linewidth=2, linestyle=\"dotted\", color=\"gray\")\n", "    axs[i].legend()"]}, {"cell_type": "code", "execution_count": null, "id": "e1238a7c-fd5e-4ae4-8fca-b2f9a088e741", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}