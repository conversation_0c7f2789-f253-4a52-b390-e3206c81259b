{"cells": [{"cell_type": "markdown", "metadata": {}, "source": [" # PatchTSMixer in HuggingFace - Getting Started\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["<div> <img src=\"./figures/forecasting.png\" alt=\"Forecasting\" style=\"width: 600px; text-align: center;\"/></div>\n", "\n", "\n", "`PatchTSMixer` is a lightweight time-series modeling approach based on the MLP-Mixer architecture. It is proposed in [TSMixer: Lightweight MLP-Mixer Model for Multivariate Time Series Forecasting](https://arxiv.org/pdf/2306.09364.pdf) by IBM Research authors <PERSON><PERSON>`, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, `<PERSON><PERSON><PERSON><PERSON><PERSON> and `<PERSON><PERSON>.\n", "\n", "For effective mindshare and to promote opensourcing - IBM Research joins hands with the HuggingFace team to opensource this model in HF.\n", "\n", "In this [HuggingFace implementation](https://huggingface.co/docs/transformers/main/en/model_doc/patchtsmixer), we provide PatchTSMixer’s capabilities to facilitate lightweight mixing across patches, channels, and hidden features for effective multivariate time series modeling. It also supports various attention mechanisms starting from simple gated attention to more complex self-attention blocks that can be customized accordingly. The model can be pretrained and subsequently used for various downstream tasks such as forecasting, classification, and regression.\n", "\n", "`PatchTSMixer` outperforms state-of-the-art MLP and Transformer models in forecasting by a considerable margin of 8-60%. It also outperforms the latest strong benchmarks of Patch-Transformer models (by 1-2%) with a significant reduction in memory and runtime (2-3X). For more details, refer to the [paper](https://arxiv.org/pdf/2306.09364.pdf).\n", "\n", "In this blog, we will demonstrate examples of getting started with PatchTSMixer. We will first demonstrate the forecasting capability of `PatchTSMixer` on the Electricity data. We will then demonstrate the transfer learning capability of PatchTSMixer by using the model trained on the Electricity to do zero-shot forecasting on the ETTH2 dataset.\n", "\n", "`Blog authors`: <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON> and <PERSON><PERSON><PERSON>\n", "\n", "\n", "## Objectives\n", "\n", "1. Demonstrate the use of the recently contributed Hugging Face model architectures for forecasting use-cases, including pre-training and fine-tuning.\n", "\n", "2. Illustrate how to perform transfer learning from a pretrained model, with various approaches for fine-tuning the model on a target dataset.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## PatchTSMixer Quick Overview \n", "\n", "<div> <img src=\"./figures/patch-tsmixer-arch.png\" alt=\"Architecture\" style=\"width: 800px;\"/></div>\n", "\n", "\n", "`PatchTSMixer` begins by splitting a given input multivariate time series into a sequence of patches or windows. Subsequently, it passes the series to an embedding layer, which generates a multi-dimensional tensor.\n", "\n", "\n", "The multi-dimensional tensor is subsequently passed to the `PatchTSMixer` backbone, which is composed of a sequence of [MLP Mixer](https://arxiv.org/abs/2105.01601) layers. Each MLP Mixer layer learns inter-patch, intra-patch, and inter-channel correlations through a series of permutation and MLP operations.\n", "`PatchTSMixer` also employs residual connections and gated attentions to prioritize important features. \n", "\n", "\n", "<div> <img src=\"./figures/backbone_illustration.png\" alt=\"Mixing\" style=\"width: 800px;\"/></div>\n", "\n", "A sequence of these MLP Mixer layers like the above is used to construct the `PatchTSMixer` backbone. `PatchTSMixer` has a modular design to seamlessly support masked time series pre-training as well as direct time series forecasting. This is done by leveraging the backbone with a different masking strategies and model heads.\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installation\n", "This demo needs Huggingface [`transformers`](https://github.com/huggingface/transformers) for main modeling tasks, and IBM `tsfm` for auxiliary data pre-processing.\n", "We can install both by cloning the `tsfm` repository and following the below steps.\n", "\n", "1. <PERSON><PERSON> IBM Time Series Foundation Model Repository [`tsfm`](https://github.com/ibm/tsfm).\n", "    ```\n", "    <NAME_EMAIL>:IBM/tsfm.git\n", "    cd tsfm\n", "    ```\n", "2. Install `tsfm`. This will also install Huggingface `transformers` library and other dependencies.\n", "    ```\n", "    pip install .\n", "    ```\n", "3. Test it with the following commands in a `python` terminal.\n", "    ```\n", "    from transformers import PatchTSMixerConfig\n", "    from tsfm_public.toolkit.dataset import ForecastDFDataset\n", "    ```"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Part 1: Forecasting on Electricity dataset"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Standard\n", "import os\n", "\n", "# supress some warnings\n", "import warnings\n", "\n", "import pandas as pd\n", "\n", "# Third Party\n", "from transformers import (\n", "    EarlyStopping<PERSON><PERSON><PERSON>,\n", "    PatchTSMixerConfig,\n", "    PatchTSMixerForPrediction,\n", "    Trainer,\n", "    TrainingArguments,\n", "    set_seed,\n", ")\n", "\n", "# First Party\n", "from tsfm_public.toolkit.dataset import ForecastDFDataset\n", "from tsfm_public.toolkit.time_series_preprocessor import TimeSeriesPreprocessor\n", "from tsfm_public.toolkit.util import select_by_index\n", "\n", "\n", "warnings.filterwarnings(\"ignore\", module=\"torch\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Set seed"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["set_seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load and prepare datasets\n", "\n", "In the next cell, please adjust the following parameters to suit your application:\n", "- `PRETRAIN_AGAIN`: Set this to `True` if you want to perform pretraining again. Note that this might take some time depending on the GPU availability. Otherwise, the already pretrained model will be used.\n", "- `dataset_path`: path to local .csv file, or web address to a csv file for the data of interest. Data is loaded with pandas, so anything supported by\n", "`pd.read_csv` is supported: (https://pandas.pydata.org/pandas-docs/stable/reference/api/pandas.read_csv.html).\n", "- `timestamp_column`: column name containing timestamp information, use None if there is no such column\n", "- `id_columns`: List of column names specifying the IDs of different time series. If no ID column exists, use []\n", "- `forecast_columns`: List of columns to be modeled\n", "- `context_length`: The amount of historical data used as input to the model. Windows of the input time series data with length equal to\n", "`context_length` will be extracted from the input dataframe. In the case of a multi-time series dataset, the context windows will be created\n", "so that they are contained within a single time series (i.e., a single ID).\n", "- `forecast_horizon`: Number of timestamps to forecast in future.\n", "- `train_start_index`, `train_end_index`: the start and end indices in the loaded data which delineate the training data.\n", "- `valid_start_index`, `valid_end_index`: the start and end indices in the loaded data which delineate the validation data.\n", "- `test_start_index`, `test_end_index`: the start and end indices in the loaded data which delineate the test data.\n", "- `patch_length`: The patch length for the `PatchTSMixer` model. It is recommended to choose a value that evenly divides `context_length`.\n", "- `num_workers`: Number of dataloder workers in pytorch dataloader.\n", "- `batch_size`: Batch size.\n", "The data is first loaded into a Pandas dataframe and split into training, validation, and test parts. Then the pandas dataframes are converted\n", "to the appropriate torch dataset needed for training."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["PRETRAIN_AGAIN = True\n", "# Download ECL data from https://github.com/zhouhaoyi/Informer2020\n", "dataset_path = \"~/data/ECL.csv\"\n", "timestamp_column = \"date\"\n", "id_columns = []\n", "\n", "context_length = 512\n", "forecast_horizon = 96\n", "patch_length = 8\n", "num_workers = 16  # Reduce this if you have low number of CPU cores\n", "batch_size = 64  # Adjust according to GPU memory"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(\n", "    dataset_path,\n", "    parse_dates=[timestamp_column],\n", ")\n", "forecast_columns = list(data.columns[1:])\n", "\n", "# get split\n", "num_train = int(len(data) * 0.7)\n", "num_test = int(len(data) * 0.2)\n", "num_valid = len(data) - num_train - num_test\n", "border1s = [\n", "    0,\n", "    num_train - context_length,\n", "    len(data) - num_test - context_length,\n", "]\n", "border2s = [num_train, num_train + num_valid, len(data)]\n", "\n", "train_start_index = border1s[0]  # None indicates beginning of dataset\n", "train_end_index = border2s[0]\n", "\n", "# we shift the start of the evaluation period back by context length so that\n", "# the first evaluation timestamp is immediately following the training data\n", "valid_start_index = border1s[1]\n", "valid_end_index = border2s[1]\n", "\n", "test_start_index = border1s[2]\n", "test_end_index = border2s[2]\n", "\n", "train_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=train_start_index,\n", "    end_index=train_end_index,\n", ")\n", "valid_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=valid_start_index,\n", "    end_index=valid_end_index,\n", ")\n", "test_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=test_start_index,\n", "    end_index=test_end_index,\n", ")\n", "\n", "tsp = TimeSeriesPreprocessor(\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    scaling=True,\n", ")\n", "tsp = tsp.train(train_data)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["train_dataset = ForecastDFDataset(\n", "    tsp.preprocess(train_data),\n", "    id_columns=id_columns,\n", "    timestamp_column=\"date\",\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=forecast_horizon,\n", ")\n", "valid_dataset = ForecastDFDataset(\n", "    tsp.preprocess(valid_data),\n", "    id_columns=id_columns,\n", "    timestamp_column=\"date\",\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=forecast_horizon,\n", ")\n", "test_dataset = ForecastDFDataset(\n", "    tsp.preprocess(test_data),\n", "    id_columns=id_columns,\n", "    timestamp_column=\"date\",\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=forecast_horizon,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": [" ## Configure the PatchTSMixer model\n", "\n", " The settings below control the different components in the PatchTSMixer model.\n", "  - `num_input_channels`: the number of input channels (or dimensions) in the time series data. This is\n", "    automatically set to the number for forecast columns.\n", "  - `context_length`: As described above, the amount of historical data used as input to the model.\n", "  - `prediction_length`: This is same as the forecast horizon as decribed above.\n", "  - `patch_length`: The length of the patches extracted from the context window (of length `context_length`).\n", "  - `patch_stride`: The stride used when extracting patches from the context window.\n", "  - `d_model`: Hidden feature dimension of the model.\n", "  - `num_layers`: The number of model layers.\n", "  - `expansion_factor`: Expansion factor to use inside MLP. Recommended range is 2-5. Larger value indicates more complex model.\n", "  - `dropout`: Dropout probability for all fully connected layers in the encoder.\n", "  - `head_dropout`: Dropout probability used in the head of the model.\n", "  - `mode`: PatchTSMixer operating mode. \"common_channel\"/\"mix_channel\". Common-channel works in channel-independent mode. For pretraining, use \"common_channel\".\n", "  - `scaling`: Per-window standard scaling. Recommended value: \"std\".\n", "\n", "For full details on the parameters, refer to the [Hugging Face documentation](https://huggingface.co/docs/transformers/main/en/model_doc/patchtsmixer).\n"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["if PRETRAIN_AGAIN:\n", "    config = PatchTSMixerConfig(\n", "        context_length=context_length,\n", "        prediction_length=forecast_horizon,\n", "        patch_length=patch_length,\n", "        num_input_channels=len(forecast_columns),\n", "        patch_stride=patch_length,\n", "        d_model=16,\n", "        num_layers=8,\n", "        expansion_factor=2,\n", "        dropout=0.2,\n", "        head_dropout=0.2,\n", "        mode=\"common_channel\",\n", "        scaling=\"std\",\n", "    )\n", "    model = PatchTSMixerForPrediction(config)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train model\n", "\n", " Trains the PatchTSMixer model based on the direct forecasting strategy."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='3570' max='7000' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [3570/7000 30:38 < 29:27, 1.94 it/s, Epoch 51/100]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.247300</td>\n", "      <td>0.141137</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.168700</td>\n", "      <td>0.127693</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.156600</td>\n", "      <td>0.122126</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.150200</td>\n", "      <td>0.118831</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.146100</td>\n", "      <td>0.116918</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.142900</td>\n", "      <td>0.115001</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.140700</td>\n", "      <td>0.113701</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.139200</td>\n", "      <td>0.113152</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.137900</td>\n", "      <td>0.112459</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.136800</td>\n", "      <td>0.111976</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.136000</td>\n", "      <td>0.111908</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.135300</td>\n", "      <td>0.111850</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.134600</td>\n", "      <td>0.111377</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.133900</td>\n", "      <td>0.111204</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.133700</td>\n", "      <td>0.111033</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.133100</td>\n", "      <td>0.110903</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.132700</td>\n", "      <td>0.110834</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.132500</td>\n", "      <td>0.111198</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.132100</td>\n", "      <td>0.110564</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.131700</td>\n", "      <td>0.110474</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.131400</td>\n", "      <td>0.110540</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.131000</td>\n", "      <td>0.110685</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.130800</td>\n", "      <td>0.110402</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.130500</td>\n", "      <td>0.110426</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.130300</td>\n", "      <td>0.110207</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.130100</td>\n", "      <td>0.110285</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.129700</td>\n", "      <td>0.110344</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.129500</td>\n", "      <td>0.110189</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.129300</td>\n", "      <td>0.110345</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.129100</td>\n", "      <td>0.109723</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.129000</td>\n", "      <td>0.109840</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.128700</td>\n", "      <td>0.109825</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.128500</td>\n", "      <td>0.110094</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.128300</td>\n", "      <td>0.109619</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.128100</td>\n", "      <td>0.109592</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.127900</td>\n", "      <td>0.109415</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.127700</td>\n", "      <td>0.109450</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.127600</td>\n", "      <td>0.109296</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.127400</td>\n", "      <td>0.109671</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.127300</td>\n", "      <td>0.109650</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.127100</td>\n", "      <td>0.109087</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.127000</td>\n", "      <td>0.109250</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.126900</td>\n", "      <td>0.109490</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.126700</td>\n", "      <td>0.109065</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.126500</td>\n", "      <td>0.109115</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.126400</td>\n", "      <td>0.109277</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.126300</td>\n", "      <td>0.109314</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.126100</td>\n", "      <td>0.109050</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.126100</td>\n", "      <td>0.109168</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.125900</td>\n", "      <td>0.109095</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.125700</td>\n", "      <td>0.109028</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["if PRETRAIN_AGAIN:\n", "    training_args = TrainingArguments(\n", "        output_dir=\"./checkpoint/patchtsmixer_4/electricity/pretrain/output/\",\n", "        overwrite_output_dir=True,\n", "        learning_rate=0.001,\n", "        num_train_epochs=100,  # For a quick test of this notebook, set it to 1\n", "        do_eval=True,\n", "        evaluation_strategy=\"epoch\",\n", "        per_device_train_batch_size=batch_size,\n", "        per_device_eval_batch_size=batch_size,\n", "        dataloader_num_workers=num_workers,\n", "        report_to=\"tensorboard\",\n", "        save_strategy=\"epoch\",\n", "        logging_strategy=\"epoch\",\n", "        save_total_limit=3,\n", "        logging_dir=\"./checkpoint/patchtsmixer_4/electricity/pretrain/logs/\",  # Make sure to specify a logging directory\n", "        load_best_model_at_end=True,  # Load the best model when training ends\n", "        metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "        greater_is_better=False,  # For loss\n", "        label_names=[\"future_values\"],\n", "    )\n", "\n", "    # Create the early stopping callback\n", "    early_stopping_callback = EarlyStoppingCallback(\n", "        early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "        early_stopping_threshold=0.0001,  # Minimum improvement required to consider as improvement\n", "    )\n", "\n", "    # define trainer\n", "    trainer = Trainer(\n", "        model=model,\n", "        args=training_args,\n", "        train_dataset=train_dataset,\n", "        eval_dataset=valid_dataset,\n", "        callbacks=[early_stopping_callback],\n", "    )\n", "\n", "    # pretrain\n", "    trainer.train()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate model on the test set\n"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='21' max='21' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [21/21 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Test result:\n", "{'eval_loss': 0.12852181494235992, 'eval_runtime': 5.5197, 'eval_samples_per_second': 935.745, 'eval_steps_per_second': 3.805, 'epoch': 51.0}\n"]}], "source": ["if PRETRAIN_AGAIN:\n", "    results = trainer.evaluate(test_dataset)\n", "    print(\"Test result:\")\n", "    print(results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["We get MSE score of 0.128 which is the SOTA result on the Electricity data."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save model"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["if PRETRAIN_AGAIN:\n", "    save_dir = \"patchtsmixer_4/electricity/model/pretrain/\"\n", "    os.makedirs(save_dir, exist_ok=True)\n", "    trainer.save_model(save_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Part 2: Transfer Learning from Electicity to ETTH2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["In this section, we will demonstrate the transfer learning capability of the `PatchTSMixer` model.\n", "We use the model pretrained on Electricity dataset to do zeroshot testing on ETTH2 dataset.\n", "\n", "\n", "In Transfer Learning,  we will pretrain the model for a forecasting task on a `source` dataset. Then, we will use the\n", " pretrained model for zero-shot forecasting on a `target` dataset. The zero-shot forecasting\n", " performance will denote the `test` performance of the model in the `target` domain, without any\n", " training on the target domain. Subsequently, we will do linear probing and (then) finetuning of\n", " the pretrained model on the `train` part of the target data, and will validate the forecasting\n", " performance on the `test` part of the target data. In this example, the source dataset is the Electricity dataset and the target dataset is ETTH2"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Transfer Learing on `ETTh2` data. All evaluations are on the `test` part of the `ETTh2` data.\n", "Step 1: Directly evaluate the electricity-pretrained model. This is the zero-shot performance.  \n", "Step 2: Evaluate after doing linear probing.  \n", "Step 3: Evaluate after doing full finetuning.  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load ETTh2 data"]}, {"cell_type": "code", "execution_count": 51, "metadata": {}, "outputs": [], "source": ["dataset = \"ETTh2\""]}, {"cell_type": "code", "execution_count": 52, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading target dataset: ETTh2\n"]}], "source": ["print(f\"Loading target dataset: {dataset}\")\n", "dataset_path = f\"https://raw.githubusercontent.com/zhouhaoyi/ETDataset/main/ETT-small/{dataset}.csv\"\n", "timestamp_column = \"date\"\n", "id_columns = []\n", "forecast_columns = [\"HUFL\", \"HULL\", \"MUFL\", \"MULL\", \"LUFL\", \"LULL\", \"OT\"]\n", "train_start_index = None  # None indicates beginning of dataset\n", "train_end_index = 12 * 30 * 24\n", "\n", "# we shift the start of the evaluation period back by context length so that\n", "# the first evaluation timestamp is immediately following the training data\n", "valid_start_index = 12 * 30 * 24 - context_length\n", "valid_end_index = 12 * 30 * 24 + 4 * 30 * 24\n", "\n", "test_start_index = 12 * 30 * 24 + 4 * 30 * 24 - context_length\n", "test_end_index = 12 * 30 * 24 + 8 * 30 * 24"]}, {"cell_type": "code", "execution_count": 53, "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(\n", "    dataset_path,\n", "    parse_dates=[timestamp_column],\n", ")\n", "\n", "train_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=train_start_index,\n", "    end_index=train_end_index,\n", ")\n", "valid_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=valid_start_index,\n", "    end_index=valid_end_index,\n", ")\n", "test_data = select_by_index(\n", "    data,\n", "    id_columns=id_columns,\n", "    start_index=test_start_index,\n", "    end_index=test_end_index,\n", ")\n", "\n", "tsp = TimeSeriesPreprocessor(\n", "    timestamp_column=timestamp_column,\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    scaling=True,\n", ")\n", "tsp = tsp.train(train_data)"]}, {"cell_type": "code", "execution_count": 54, "metadata": {}, "outputs": [], "source": ["train_dataset = ForecastDFDataset(\n", "    tsp.preprocess(train_data),\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=forecast_horizon,\n", ")\n", "valid_dataset = ForecastDFDataset(\n", "    tsp.preprocess(valid_data),\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=forecast_horizon,\n", ")\n", "test_dataset = ForecastDFDataset(\n", "    tsp.preprocess(test_data),\n", "    id_columns=id_columns,\n", "    target_columns=forecast_columns,\n", "    context_length=context_length,\n", "    prediction_length=forecast_horizon,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Zero-shot forecasting on `ETTh2`"]}, {"cell_type": "code", "execution_count": 55, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading pretrained model\n", "Done\n"]}], "source": ["print(\"Loading pretrained model\")\n", "finetune_forecast_model = PatchTSMixerForPrediction.from_pretrained(\"patchtsmixer_4/electricity/model/pretrain/\")\n", "print(\"Done\")"]}, {"cell_type": "code", "execution_count": 56, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Doing zero-shot forecasting on target data\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='22' max='11' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [11/11 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Target data zero-shot forecasting result:\n", "{'eval_loss': 0.30616453289985657, 'eval_runtime': 1.277, 'eval_samples_per_second': 2180.942, 'eval_steps_per_second': 8.614}\n"]}], "source": ["finetune_forecast_args = TrainingArguments(\n", "    output_dir=\"./checkpoint/patchtsmixer/transfer/finetune/output/\",\n", "    overwrite_output_dir=True,\n", "    learning_rate=0.0001,\n", "    num_train_epochs=100,\n", "    do_eval=True,\n", "    evaluation_strategy=\"epoch\",\n", "    per_device_train_batch_size=batch_size,\n", "    per_device_eval_batch_size=batch_size,\n", "    dataloader_num_workers=num_workers,\n", "    report_to=\"tensorboard\",\n", "    save_strategy=\"epoch\",\n", "    logging_strategy=\"epoch\",\n", "    save_total_limit=3,\n", "    logging_dir=\"./checkpoint/patchtsmixer/transfer/finetune/logs/\",  # Make sure to specify a logging directory\n", "    load_best_model_at_end=True,  # Load the best model when training ends\n", "    metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "    greater_is_better=False,  # For loss\n", ")\n", "\n", "# Create a new early stopping callback with faster convergence properties\n", "early_stopping_callback = EarlyStoppingCallback(\n", "    early_stopping_patience=5,  # Number of epochs with no improvement after which to stop\n", "    early_stopping_threshold=0.001,  # Minimum improvement required to consider as improvement\n", ")\n", "\n", "finetune_forecast_trainer = Trainer(\n", "    model=finetune_forecast_model,\n", "    args=finetune_forecast_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=valid_dataset,\n", "    callbacks=[early_stopping_callback],\n", ")\n", "\n", "print(\"\\n\\nDoing zero-shot forecasting on target data\")\n", "result = finetune_forecast_trainer.evaluate(test_dataset)\n", "print(\"Target data zero-shot forecasting result:\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["By a direct zeroshot, we get MSE of 0.3 which is near to the SOTA result. Lets see, how we can do a simple linear probing to match the SOTA results."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Target data `ETTh2` linear probing\n", "We can do a quick linear probing on the `train` part of the target data to see any possible `test` performance improvement. "]}, {"cell_type": "code", "execution_count": 57, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Linear probing on the target data\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='192' max='3200' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 192/3200 00:22 < 05:53, 8.51 it/s, Epoch 6/100]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.449900</td>\n", "      <td>0.216939</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.442700</td>\n", "      <td>0.217594</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.430600</td>\n", "      <td>0.216781</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.423900</td>\n", "      <td>0.216197</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.421400</td>\n", "      <td>0.215837</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.413900</td>\n", "      <td>0.215604</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluating\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='11' max='11' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [11/11 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Target data head/linear probing result:\n", "{'eval_loss': 0.275984525680542, 'eval_runtime': 1.3131, 'eval_samples_per_second': 2120.981, 'eval_steps_per_second': 8.377, 'epoch': 6.0}\n"]}], "source": ["# Freeze the backbone of the model\n", "for param in finetune_forecast_trainer.model.model.parameters():\n", "    param.requires_grad = False\n", "\n", "print(\"\\n\\nLinear probing on the target data\")\n", "finetune_forecast_trainer.train()\n", "print(\"Evaluating\")\n", "result = finetune_forecast_trainer.evaluate(test_dataset)\n", "print(\"Target data head/linear probing result:\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {"vscode": {"languageId": "plaintext"}}, "source": ["By doing a simple linear probing, MSE decreased from 0.3 to 0.276 achiving state-of-the-art results."]}, {"cell_type": "code", "execution_count": 58, "metadata": {}, "outputs": [{"data": {"text/plain": ["['patchtsmixer_4/electricity/model/transfer/ETTh2/preprocessor/preprocessor_config.json']"]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["save_dir = f\"patchtsmixer_4/electricity/model/transfer/{dataset}/model/linear_probe/\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "finetune_forecast_trainer.save_model(save_dir)\n", "\n", "save_dir = f\"patchtsmixer_4/electricity/model/transfer/{dataset}/preprocessor/\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "tsp.save_pretrained(save_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Lets now see, if we get any more improvements by doing a full finetune."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Target data `ETTh2` full finetune\n", "\n", "We can do a full model finetune (instead of probing the last linear layer as shown above) on the `train` part of the target data to see a possible `test` performance improvement."]}, {"cell_type": "code", "execution_count": 59, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "Finetuning on the target data\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='256' max='3200' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 256/3200 00:32 < 06:20, 7.73 it/s, Epoch 8/100]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.434300</td>\n", "      <td>0.215311</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.413800</td>\n", "      <td>0.211736</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.395600</td>\n", "      <td>0.209889</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.385000</td>\n", "      <td>0.209884</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.377800</td>\n", "      <td>0.211479</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.366700</td>\n", "      <td>0.211604</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.360400</td>\n", "      <td>0.212635</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.355200</td>\n", "      <td>0.214360</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Evaluating\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='11' max='11' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [11/11 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Target data full finetune result:\n", "{'eval_loss': 0.27177363634109497, 'eval_runtime': 1.3045, 'eval_samples_per_second': 2134.936, 'eval_steps_per_second': 8.432, 'epoch': 8.0}\n"]}], "source": ["# Reload the model\n", "finetune_forecast_model = PatchTSMixerForPrediction.from_pretrained(\"patchtsmixer_4/electricity/model/pretrain/\")\n", "finetune_forecast_trainer = Trainer(\n", "    model=finetune_forecast_model,\n", "    args=finetune_forecast_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=valid_dataset,\n", "    callbacks=[early_stopping_callback],\n", ")\n", "print(\"\\n\\nFinetuning on the target data\")\n", "finetune_forecast_trainer.train()\n", "print(\"Evaluating\")\n", "result = finetune_forecast_trainer.evaluate(test_dataset)\n", "print(\"Target data full finetune result:\")\n", "print(result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["There is not much improvement with ETTH2 dataset with full finetuning. Lets save the model anyway."]}, {"cell_type": "code", "execution_count": 60, "metadata": {}, "outputs": [], "source": ["save_dir = f\"patchtsmixer_4/electricity/model/transfer/{dataset}/model/fine_tuning/\"\n", "os.makedirs(save_dir, exist_ok=True)\n", "finetune_forecast_trainer.save_model(save_dir)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "# Summary\n", "\n", "In this blog, we presented a step-by-step guide on leveraging PatchTSMixer for tasks related to forecasting and transfer learning. We intend to facilitate the seamless integration of the PatchTSMixer HF model for your forecasting use cases. We trust that this content serves as a useful resource to expedite your adoption of PatchTSMixer. Thank you for tuning in to our blog, and we hope you find this information beneficial for your projects.\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["© 2023 IBM Corporation"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.4"}}, "nbformat": 4, "nbformat_minor": 4}