{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ec0c30c4-e8a9-4e58-add0-84660c21d6a7", "metadata": {}, "outputs": [], "source": ["import matplotlib.pyplot as plt\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from tsfm_public.models.tspulse.modeling_tspulse import TSPulseForReconstruction\n", "from tsfm_public.toolkit.ad_helpers import AnomalyScoreMethods\n", "from tsfm_public.toolkit.time_series_anomaly_detection_pipeline import TimeSeriesAnomalyDetectionPipeline"]}, {"cell_type": "markdown", "id": "64faaffa-0faf-4478-b913-8ef2a17af7f2", "metadata": {}, "source": ["# Example Datafile\n", "\n", "Data instance from IOPS dataset ([Paper](https://arxiv.org/html/2402.10802v1)). The data example is a part of the IOPS dataset.\n", "\n", "The IOPS data set is an anonymized dataset with performance indicators that reflect the scale, quality of web services, and health status of a machine.\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "id": "ab7656fa-3193-4169-9929-643a3d4a80a2", "metadata": {}, "outputs": [], "source": ["data_path = \"https://huggingface.co/datasets/AutonLab/Timeseries-PILE/resolve/main/anomaly_detection/TSB-UAD-Public/IOPS/KPI-4d2af31a-9916-3d9f-8a8e-8a268a48c095.test.out\""]}, {"cell_type": "markdown", "id": "29356d84-583a-4846-9244-8a5f3e7aa23d", "metadata": {}, "source": ["\n", "## Helper Functions for Data Loading and Processing\n", "\n", "`load_data` loads the large dataset in chunks.\n", "\n", "`attach_timestamp_column` current pipeline expects a timestamp column in the input. The IOPS dataset does not have an explicit time column specified. This helper function attach a dummy timestamp column to the dataset.\n", "\n"]}, {"cell_type": "code", "execution_count": 3, "id": "1978a2fb-f1c3-4cc3-a06d-ff94fb86e3a7", "metadata": {}, "outputs": [], "source": ["def load_data(file_path: str, max_length: int, min_length: int):\n", "    data = []\n", "    extra_args = {}\n", "    if file_path.endswith(\"gz\"):\n", "        extra_args[\"compression\"] = \"gzip\"\n", "    for chunk in pd.read_csv(file_path, header=None, sep=\",\", chunksize=max_length, *extra_args):\n", "        if (chunk is None) or (chunk.ndim != 2):\n", "            break\n", "        chunk.columns = [\"x\", \"y\"]\n", "        chunk[\"x\"] = chunk.x.interpolate(method=\"cubic\").ffill().bfill()\n", "        chunk[\"y\"] = chunk.y.ffill().bfill()\n", "        if chunk.shape[0] > min_length:\n", "            data.append(chunk)\n", "        elif len(data) > 0:\n", "            data[-1] = pd.concat([data[-1], chunk], axis=0)\n", "    return data\n", "\n", "\n", "def attach_timestamp_column(\n", "    df: pd.<PERSON><PERSON>rame, time_col: str = \"timestamp\", freq: str = \"D\", start_date: str = \"2022-01-01\"\n", "):\n", "    n = df.shape[0]\n", "    if time_col not in df:\n", "        df[time_col] = pd.date_range(start_date, freq=freq, periods=n)\n", "    return df"]}, {"cell_type": "code", "execution_count": 4, "id": "cfb9e54d-6533-4c31-bbfb-ea13ef907b2c", "metadata": {}, "outputs": [], "source": ["max_length, min_length = 5_000, 2_000\n", "\n", "data = load_data(data_path, max_length, min_length)"]}, {"cell_type": "markdown", "id": "58701b89-963e-460f-a534-ccc72bf89196", "metadata": {}, "source": ["## Loading ZeroShort TSPulseReconstruction Model"]}, {"cell_type": "code", "execution_count": 5, "id": "39ca6575-07ab-41b2-9227-c826978394ba", "metadata": {}, "outputs": [], "source": ["path_to_tspulse_model = \"ibm-granite/granite-timeseries-tspulse-r1\"\n", "zeroshot_model = TSPulseForReconstruction.from_pretrained(\n", "    path_to_tspulse_model,\n", "    num_input_channels=1,\n", "    revision=\"main\",\n", "    mask_type=\"user\",\n", ")"]}, {"cell_type": "markdown", "id": "8f46d401-8f89-4d44-b7a9-d3fc6ad0fdbf", "metadata": {}, "source": ["## Instantiating pipeline"]}, {"cell_type": "code", "execution_count": 6, "id": "02b34161-40e1-4ef5-aaab-7d07661c85ae", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Device set to use cuda:0\n"]}], "source": ["pipeline = TimeSeriesAnomalyDetectionPipeline(\n", "    zeroshot_model,\n", "    timestamp_column=\"timestamp\",\n", "    target_columns=[\"x\"],\n", "    prediction_mode=[\n", "        AnomalyScoreMethods.TIME_RECONSTRUCTION.value,\n", "        AnomalyScoreMethods.FREQUENCY_RECONSTRUCTION.value,\n", "    ],\n", "    aggregation_length=64,\n", "    aggr_function=\"max\",\n", "    smoothing_length=8,\n", "    least_significant_scale=0.01,\n", "    least_significant_score=0.1,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "id": "21d7b13f-dab9-4e91-8719-baac17661e3d", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>timestamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50000</th>\n", "      <td>5.00</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50001</th>\n", "      <td>4.80</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50002</th>\n", "      <td>5.25</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50003</th>\n", "      <td>4.95</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50004</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          x  y           timestamp\n", "50000  5.00  0 2022-01-01 00:00:00\n", "50001  4.80  0 2022-01-01 00:00:01\n", "50002  5.25  0 2022-01-01 00:00:02\n", "50003  4.95  0 2022-01-01 00:00:03\n", "50004  5.05  0 2022-01-01 00:00:04"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["validation_df = attach_timestamp_column(data[10], time_col=\"timestamp\", freq=\"s\")\n", "validation_df.head()"]}, {"cell_type": "markdown", "id": "c4422346-da5a-4542-b519-cc2d02843787", "metadata": {}, "source": ["## Computing Anomaly Score With Anomaly Pipeline "]}, {"cell_type": "code", "execution_count": 8, "id": "7e8b8511-b57b-4fbf-a4ad-b57910ec218f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 2 μs, sys: 1 μs, total: 3 μs\n", "Wall time: 6.68 μs\n"]}], "source": ["%time\n", "result = pipeline(validation_df, batch_size=256, predictive_score_smoothing=False)"]}, {"cell_type": "markdown", "id": "ecf75a65-5dea-4d44-bdf5-6fad50c3563e", "metadata": {}, "source": ["## Plotting the score vs TRUE Anomaly Marks"]}, {"cell_type": "code", "execution_count": 9, "id": "42be67c5-2ac1-4ba5-9b15-b686c53e84e4", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1, figsize=(15, 3))\n", "ax2 = ax.twinx()\n", "result.x.plot(ax=ax, color=\"gray\", linewidth=0.5)\n", "result.anomaly_score.plot(ax=ax2, color=\"orange\", label=\"Anomaly Score\")\n", "result.y.plot(ax=ax2, color=\"red\", label=\"Anomaly Event\")\n", "ax2.legend()\n", "# ax2.set_yticks([])\n", "ax.set_title(\"Anomaly Detection Pipeline\", fontsize=16)\n", "for s in [\"top\", \"right\"]:\n", "    ax.spines[s].set_visible(False)\n", "    ax2.spines[s].set_visible(False)"]}, {"cell_type": "markdown", "id": "ee1c5b4e-b3a1-4a44-909e-4a522aed5a2e", "metadata": {}, "source": ["Anomlay result attaches anomaly scores as a new column. By default the scores are scaled between (0-1)"]}, {"cell_type": "code", "execution_count": 10, "id": "a0ced398-8257-4d1c-b061-27821d2ace6a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>timestamp</th>\n", "      <th>anomaly_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50000</th>\n", "      <td>5.00</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:00</td>\n", "      <td>0.053295</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50001</th>\n", "      <td>4.80</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:01</td>\n", "      <td>0.066619</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50002</th>\n", "      <td>5.25</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:02</td>\n", "      <td>0.079943</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50003</th>\n", "      <td>4.95</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:03</td>\n", "      <td>0.093267</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50004</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:04</td>\n", "      <td>0.106591</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          x  y           timestamp  anomaly_score\n", "50000  5.00  0 2022-01-01 00:00:00       0.053295\n", "50001  4.80  0 2022-01-01 00:00:01       0.066619\n", "50002  5.25  0 2022-01-01 00:00:02       0.079943\n", "50003  4.95  0 2022-01-01 00:00:03       0.093267\n", "50004  5.05  0 2022-01-01 00:00:04       0.106591"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["result.head(5)"]}, {"cell_type": "markdown", "id": "37f02989-76ee-4369-b92d-32f82ffe454a", "metadata": {}, "source": ["## Threshold Based Anomaly Marking"]}, {"cell_type": "code", "execution_count": 11, "id": "c60fb0b8-c857-4b94-887b-958d1ce50c1c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>timestamp</th>\n", "      <th>anomaly_score</th>\n", "      <th>detections</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>50000</th>\n", "      <td>5.00</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:00</td>\n", "      <td>0.053295</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50001</th>\n", "      <td>4.80</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:01</td>\n", "      <td>0.066619</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50002</th>\n", "      <td>5.25</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:02</td>\n", "      <td>0.079943</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50003</th>\n", "      <td>4.95</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:03</td>\n", "      <td>0.093267</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50004</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:04</td>\n", "      <td>0.106591</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          x  y           timestamp  anomaly_score  detections\n", "50000  5.00  0 2022-01-01 00:00:00       0.053295         0.0\n", "50001  4.80  0 2022-01-01 00:00:01       0.066619         0.0\n", "50002  5.25  0 2022-01-01 00:00:02       0.079943         0.0\n", "50003  4.95  0 2022-01-01 00:00:03       0.093267         0.0\n", "50004  5.05  0 2022-01-01 00:00:04       0.106591         0.0"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["anomaly_threshold = 0.6\n", "result[\"detections\"] = (result[\"anomaly_score\"] > anomaly_threshold).astype(float)\n", "\n", "result.head()"]}, {"cell_type": "markdown", "id": "49862381-89aa-402f-897a-110eebf1c30d", "metadata": {}, "source": ["## Computing F1 Score for detection"]}, {"cell_type": "code", "execution_count": 12, "id": "df59a042-1692-4320-8340-9095f03affb2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'F1 score: 0.74'"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Predicted Anomaly: No</th>\n", "      <th>Predicted Anomaly: Yes</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>True Anomaly: No</th>\n", "      <td>4952</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>True Anomaly: Yes</th>\n", "      <td>10</td>\n", "      <td>28</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Predicted Anomaly: No  Predicted Anomaly: Yes\n", "True Anomaly: No                    4952                      10\n", "True Anomaly: Yes                     10                      28"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import pandas as pd\n", "from sklearn.metrics import confusion_matrix, f1_score\n", "\n", "\n", "matrix = confusion_matrix(result[\"y\"].values, result[\"detections\"].values)\n", "matrix = pd.DataFrame(\n", "    matrix,\n", "    columns=[\"Predicted Anomaly: No\", \"Predicted Anomaly: Yes\"],\n", "    index=[\"True Anomaly: No\", \"True Anomaly: Yes\"],\n", ")\n", "\n", "f_score = f1_score(result[\"y\"].values, result[\"detections\"].values, average=\"binary\")\n", "\n", "display(f\"F1 score: {f_score:.2f}\")\n", "display(matrix)"]}, {"cell_type": "markdown", "id": "2eb08e19-624a-4b80-9a4c-37dfd2002f9a", "metadata": {}, "source": ["## TSPulse supports multiple prediction modes"]}, {"cell_type": "code", "execution_count": 13, "id": "bc486be9-dc49-481c-8292-5183b0274cbf", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"All Prediction Modes: ['forecast', 'time', 'fft', ['forecast', 'time', 'fft']]\""]}, "metadata": {}, "output_type": "display_data"}], "source": ["prediction_modes = [\n", "    AnomalyScoreMethods.PREDICTIVE.value,\n", "    AnomalyScoreMethods.TIME_RECONSTRUCTION.value,\n", "    AnomalyScoreMethods.FREQUENCY_RECONSTRUCTION.value,\n", "]\n", "\n", "prediction_modes.append(prediction_modes.copy())\n", "\n", "display(f\"All Prediction Modes: {prediction_modes}\")"]}, {"cell_type": "markdown", "id": "9210c509-9815-4283-ac27-7abd337f3290", "metadata": {}, "source": ["## Mode Triangulation\n", "\n", "Mode triangulation uses a validation data to identify best prediction mode for anomaly detection in a given dataset."]}, {"cell_type": "code", "execution_count": 14, "id": "d24831a7-6c38-441c-9e70-8182b6f3ecce", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Device set to use cuda:0\n", "Device set to use cuda:0\n", "Device set to use cuda:0\n", "Device set to use cuda:0\n"]}], "source": ["f_scores = []\n", "\n", "for mode in prediction_modes:\n", "    if not isinstance(mode, list):\n", "        mode = [mode]\n", "    pipeline = TimeSeriesAnomalyDetectionPipeline(\n", "        zeroshot_model,\n", "        timestamp_column=\"timestamp\",\n", "        target_columns=[\"x\"],\n", "        prediction_mode=mode,\n", "        aggr_win_size=64,\n", "        aggr_function=\"max\",\n", "        smoothing_window_size=16,\n", "        least_significant_scale=0.01,\n", "        least_significant_score=0.1,\n", "    )\n", "    result = pipeline(validation_df, batch_size=256, predictive_score_smoothing=True)\n", "    result[\"detections\"] = (result[\"anomaly_score\"] > anomaly_threshold).astype(float)\n", "    f_score = f1_score(result[\"y\"].values, result[\"detections\"].values, average=\"binary\")\n", "    f_scores.append(f_score)"]}, {"cell_type": "markdown", "id": "391680ec-1ff3-4bce-8dcd-f7c4c7a2c062", "metadata": {}, "source": ["Best mode selection is metric or KPI driven. Here we use F1 Score for mode selection"]}, {"cell_type": "code", "execution_count": 15, "id": "82cd13f2-fa5a-4907-bcdf-3febdc24b4f7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Best Prediction mode for train set: ['forecast', 'time', 'fft']\n"]}], "source": ["selected_mode = prediction_modes[np.argmax(f_scores)]\n", "print(f\"Best Prediction mode for train set: {selected_mode}\")"]}, {"cell_type": "markdown", "id": "7a643c63-9630-4cd0-a642-ce2c97d02705", "metadata": {}, "source": ["## Test Dataset"]}, {"cell_type": "code", "execution_count": 16, "id": "2ed1e41d-7014-4d0b-b2cb-f9e6b5898529", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>timestamp</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>75000</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75001</th>\n", "      <td>5.10</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:01</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75002</th>\n", "      <td>4.95</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:02</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75003</th>\n", "      <td>5.65</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:03</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75004</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:04</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          x  y           timestamp\n", "75000  5.05  0 2022-01-01 00:00:00\n", "75001  5.10  0 2022-01-01 00:00:01\n", "75002  4.95  0 2022-01-01 00:00:02\n", "75003  5.65  0 2022-01-01 00:00:03\n", "75004  5.05  0 2022-01-01 00:00:04"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["test_df = attach_timestamp_column(data[15], time_col=\"timestamp\", freq=\"s\")\n", "test_df.head()"]}, {"cell_type": "markdown", "id": "02557ad1-3db1-47a2-83e3-da1ffc73e1bd", "metadata": {}, "source": ["## Executing pipeline With Best Prediction Mode"]}, {"cell_type": "code", "execution_count": 17, "id": "72dd2e97-7899-4b0d-ae6a-604e92b7170f", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Device set to use cuda:0\n"]}], "source": ["best_pipeline = TimeSeriesAnomalyDetectionPipeline(\n", "    zeroshot_model,\n", "    timestamp_column=\"timestamp\",\n", "    target_columns=[\"x\"],\n", "    prediction_mode=selected_mode,\n", "    aggr_win_size=64,\n", "    aggr_function=\"max\",\n", "    smoothing_window_size=16,\n", "    least_significant_scale=0.01,\n", "    least_significant_score=0.1,\n", ")"]}, {"cell_type": "code", "execution_count": 18, "id": "0469e439-777a-4489-810b-77c9c307a908", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["CPU times: user 3 μs, sys: 1 μs, total: 4 μs\n", "Wall time: 6.91 μs\n"]}], "source": ["%time\n", "result_test = pipeline(test_df, batch_size=256, predictive_score_smoothing=True)"]}, {"cell_type": "code", "execution_count": 19, "id": "e210a5af-1dff-425f-885a-12fd40786fc8", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>x</th>\n", "      <th>y</th>\n", "      <th>timestamp</th>\n", "      <th>anomaly_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>75000</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:00</td>\n", "      <td>0.021878</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75001</th>\n", "      <td>5.10</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:01</td>\n", "      <td>0.027348</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75002</th>\n", "      <td>4.95</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:02</td>\n", "      <td>0.032818</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75003</th>\n", "      <td>5.65</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:03</td>\n", "      <td>0.038287</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75004</th>\n", "      <td>5.05</td>\n", "      <td>0</td>\n", "      <td>2022-01-01 00:00:04</td>\n", "      <td>0.043757</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["          x  y           timestamp  anomaly_score\n", "75000  5.05  0 2022-01-01 00:00:00       0.021878\n", "75001  5.10  0 2022-01-01 00:00:01       0.027348\n", "75002  4.95  0 2022-01-01 00:00:02       0.032818\n", "75003  5.65  0 2022-01-01 00:00:03       0.038287\n", "75004  5.05  0 2022-01-01 00:00:04       0.043757"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["result_test.head()"]}, {"cell_type": "markdown", "id": "491e9108-d998-41f7-ac95-58fa008bf0a5", "metadata": {}, "source": ["## Plotting Scores For Test Data Split"]}, {"cell_type": "code", "execution_count": 20, "id": "edfc82bf-b989-4f7f-8117-1a965d40a41c", "metadata": {}, "outputs": [{"data": {"image/png": "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**************************/DcsTlJSAIAiCIAiCIAiCIAg5/v5AZmbpHVsnixcvhs1mEyUh4DgOPj4+WLBgAYKDg/lyLy8v/rPBYAAAOBwOl7qm1IZWu++88w527tyJr776CvXr14efnx+ef/55WK1W1WOMGTMG77//PiIjI3Ho0CHUrVsXXbp0cdq3unXr8gYyKe3bt0e9evWwZs0aTJ48GRs2bBDFmsvMzMTEiRMxdepU2b7h4eGK58/O19VrWBYggxpBEARBEIRepLFYKKYaQRAEUZYxGICAgNLuhSY2mw0rVqzAnDlz8PTTT4u2DR48GKtXr8akSZN0tdWkSRMcPHgQY8eO5csOHjyIpk2bFqqPBw8exLhx4/Dss88CyDdcxcXFae4TEhKCwYMHY+nSpYiMjMT48eML1QfGyJEj8csvv6BWrVowGo3o378/v61Nmza4cOEC6tevX+D2vb29AeTHZyvrkEGNIAiCIAiCIAiCIAiPZPPmzbh37x5efvllkScaAAwZMgSLFy/WbVB79913MXToUDz66KPo1asXNm3ahPXr12tm5NRDgwYNsH79egwcOBAGgwEffvihLo+uCRMmYMCAAbDb7SIjnxZJSUnIzc0VlYWEhPBeZSNHjsQ//vEP/Otf/8Lzzz8PHx8fvt6MGTPw+OOP44033sCECRMQEBCACxcuYOfOnViwYIGu44eFhcHPzw/bt29HrVq14OvrK/tdygoUQ40gCIIgCKKAkH8aQRAEQZQuixcvRq9evRSNNkOGDMHx48dx5swZXW0NHjwY8+fPx1dffYVmzZrhxx9/xNKlS9GtW7dC9XHu3LmoVKkSOnXqhIEDB6J3795o06aN0/169eqF6tWro3fv3qLlrFo0atQI1atXF/2dOHGC316/fn089thjOHPmDJ/dk9GyZUvs27cPly9fRpcuXfDoo4/io48+0n1sID9+3TfffIMff/wRNWrUwDPPPKN7X0/DwHGlt1bh5s2bqF27Nm7cuIFatWqVVjcIgiAIgiD0kZsL+PnxXzmjEYZysKSBIAiCKB/k5uYiNjYWdevWha+vb2l3p9yTmZmJmjVrYunSpXjuuedKuztFSlm412jJJ0EQBEEQREGhGGoEQRAEQRQxDocDycnJmDNnDipWrIhBgwaVdpcIBcigRhAEQRAEQRAEQRAE4SbEx8ejbt26qFWrFpYtWwazmUw37gjFUCMIgiAIgiAIgiAIgnATIiIiwHEcbty4gZ49e5Z2dzyC7777DhEREfD19UWHDh1w9OhRzfrz5s1Do0aN4Ofnh9q1a+Ott96SJXNwBhnUCIIgCIIg9CJZ4mmgJZ8EQRAEQRClytq1azF9+nTMnj0bJ0+eRKtWrdC7d28kJSUp1l+1ahXef/99zJ49G9HR0Vi8eDHWrl2L//u//3PpuGRQIwiCIAiCIAiCIAiCIDySuXPn4pVXXsH48ePRtGlT/PDDD/D398eSJUsU6x86dAidO3fGiBEjEBERgaeffhrDhw936tUmhQxqBEEQBEEQBEEQBEHwOByO0u4CUcYpqnvMarXixIkT6NWrF19mNBrRq1cvREZGKu7TqVMnnDhxgjegXbt2DVu3bkW/fv1cOjZFtiMIgiAIgtALLfEkCIIgyjDe3t4wGo1ISEhAaGgovL29YTAYSrtbRBmC4zhYrVbcvXsXRqMR3t7eivUsFgssFouozMfHBz4+PqKy5ORk2O12VK1aVVRetWpVXLx4UbHtESNGIDk5GU888QQ4joPNZsOkSZNcXvJZaIParVu3MGPGDGzbtg3Z2dmoX78+li5dinbt2hW2aYIgCIIgCIIgCIIgSgij0Yi6desiMTERCQkJpd0dogzj7++P8PBwGI3KCyc///xzfPzxx6Ky2bNn4x//+Eehj71371589tln+P7779GhQwdcvXoV06ZNwz//+U98+OGHutsplEHt3r176Ny5M7p3745t27YhNDQUV65cQaVKlQrTLEEQBEEQBEEQBEEQpYC3tzfCw8Nhs9lgt9tLuztEGcRkMsFsNmt6P86cORPTp08XlUm90wCgSpUqMJlMuHPnjqj8zp07qFatmmLbH374IUaPHo0JEyYAAFq0aIGsrCy8+uqr+OCDD1SNfFIKZVD78ssvUbt2bSxdupQvq1u3bmGaJAiCIAiCIAiCIAiiFDEYDPDy8oKXl1dpd4Uopygt71TC29sbbdu2xe7duzF48GAA+fHZdu/ejTfeeENxn+zsbJnRzGQyAchfkqqXQiUl2LhxI9q1a4cXXngBYWFhePTRR7Fo0aLCNEkQBEEQBOG+KClZFFeNIAiCIAii1Jg+fToWLVqE5cuXIzo6GpMnT0ZWVhbGjx8PABgzZgxmzpzJ1x84cCAWLlyINWvWIDY2Fjt37sSHH36IgQMH8oY1PRTKQ+3atWtYuHAhpk+fjv/7v//DsWPHMHXqVHh7e2Ps2LGy+tKgchkZGYU5PEEQBEEQBEEQBEEQBFGOGTZsGO7evYuPPvoIt2/fRuvWrbF9+3Y+UUF8fLzII23WrFkwGAyYNWsWbt26hdDQUAwcOBD/+te/XDqugXPFn02Ct7c32rVrh0OHDvFlU6dOxbFjxxTTk/7jH/+QBZUDgBs3bqBWrVoF7QZBEARBEETJkJUFBAaKyxwOgDKgEQRBEARBlCsKteSzevXqaNq0qaisSZMmiI+PV6w/c+ZM3L9/n/+7cOFCYQ5PEARBEARRstDyToIgCIIgCAKFXPLZuXNnXLp0SVR2+fJl1KlTR7G+NKhcenp6YQ5PEARBEARR+nAceagRBEEQBEGUMwrlofbWW2/h8OHD+Oyzz3D16lWsWrUK//3vf/H666+71E52djauXLlSmK4QBEEQBEEQBEEQBEEQRIlQKINa+/btsWHDBqxevRrNmzfHP//5T8ybNw8jR450qZ3r169j1apVhekKQRAEQRAEQRAEQRAEQZQIhVryCQADBgzAgAEDCtWGgZZJEARBEAThCSjFUKO4agRBEARBEOWOQnmoFRVkUCMIgiAIgiAIgiAIgiA8BbcwqBEEQRAEQRAEQRAEQRCEp0AGNYIgCIIgCL3Q8k6CIAiCIAgCbmJQoyWfBEEQBEF4LGRkIwiCIAiCKHe4hUGNIAiCIAjCI8hOLO0eEARBEARBEG4AGdQIgiAIgiD0kn6htHtAEARBEARBuAFuYVCjJZ8EQRAEQXgEOXdLuwcEQRAEQRCEG+AWBrWrV6+WdhcIgiAIgiC0yUkEjk2Ul1MMNYIgCIIgiHKHWxjUoqOjS7sLBEEQBEEQ2hyZUNo9IAiCIAiCINwEtzColRSbN29GVlZWaXeDIAiCIAhPJO0sQM5oBEEQBEEQBMqZQe3EiRNIT08v7W4QBEEQBOGJeAUpl9OST4IgCIIgiHJHuTKoEQRBEARBFBhbTmn3gCAIgiAIgnATyp1BjTKKEgRBEARRIBzW0u4BQRAEQRAE4SaQQY0gCIIgCEIPXF5p94AgCIIgCIJwE8qdQY0gCIIgCKJA2FU81CiGGkEQBEEQRLmj3BnUyEONIAhn3L59G7/99pvL+8XFxWHLli3F0COCINwCtSWftuyS7QdBEARBEARR6pQ7gxpBEIQzUlJScO7cOZf3S0xMxJkzZ4qhRwRBuAUOK6DkjJZ6osS7QhAEQRAEQZQuZdagdvHiRWzdulVWTh5qBEEUFxzH6ZYxubm5WLhwYTH3iCCIIoPjAM6mvC3NdQM8QRAEQRAE4dm4lUGNK8IYJImJibh48aKsnAxqBEEUF64Y1CwWC5KSkoq5RwRBFBkOjYQEWddLrh8EQRAEQRCEW+A2BrWgoCCXDGq7d+/GqlWrsH79esXtHMfBaNR/egsWLEBmZqbu+gRBeBZffPGF7rpqsmj//v04ePCgy/s54+zZs9i8eXOB9iUIooRg8dOUHvOs+BLpws8//4xbt26VyLEIgiAIgiAIbdzGoFa7dm04HA7d9ePj43HlyhVcuXJFcbsrniJAfsyk3Nxc3fUJgvAsLBZLoduIj4/HzZs3NevolTtC49vt27dx7dq1QvWNIIhihtPwUMsuGYPatWvXcP/+/RI5FkEQBEEQBKGN2xjUjEajSwY1Z4NWNYNaUS4rJQiifOHMUO+KIX/+/Pmi77QcnSDcHLtKhk8AyCCDOEEQBEEQRHnD4w1qaoPQghjUaEBLEIQzisqgVhT7EQRRgrAln0Yv+TZrKpBLMREJgiAIgiDKEx5vUHMlThrjxIkTfOw1h8OBVatWudwGQRDlD2cerlqGsbNnz2LNmjUu70cQhJvAlnwavJW3379Qcn0hCIIgCIIgSh2PNag5Q8tDLSUlhQ/qa7fbKdMeQRC6KMySz9TUVAomThCeDFvyaTTLt3EA7p8v0e4QBEEQBEEQpYvHGtRcXfL58ccfK5bb7fYiNeQRBFF2cdWgxuTOhg0bcODAAZeWqH/66adIS0srfKcJgigS0tOS8z8YVTzU7vxVrMdn8oQgCMJTWbt2LY4dO1ba3SAIgigyPN6gprbkU+8SKofDAbvdrvu4BEGUb7TkisPhkG3nOA537tyBzWZTlFccx8nkFcdxsNvtsFo1gqATBFGiWHIy8j8YFDzUACDtbMl1hiAIwgO5c+cOZSomCKLY+O677xAREQFfX1906NABR48e1ayflpaG119/HdWrV4ePjw8aNmyIrVu3unRMjzeoqaE3KYHdbieDGkEQItRipTmLoXbgwAGZPHEm1z755BNZu5988omOXhIEUZI47Jb8D2oeatbUkusMQRAEQRAEwbN27VpMnz4ds2fPxsmTJ9GqVSv07t1bNbyX1WrFU089hbi4OPzvf//DpUuXsGjRItSsWdOl46pMs5Y8hVnyyfYTen84M6ix/w6Hg9+fln4SRNnHbrfDZDLxz77ZbObLnO2n5fnK5IfFYhGV5+XlwWaz8W2ote1qVmKCIEoWhy03/4NSlk8OgPUewDkAQ+HmKu12O4xGo+5EJRzHweFwOJVhBEEQpQ05MRAEUVzMnTsXr7zyCsaPHw8A+OGHH7BlyxYsWbIE77//vqz+kiVLkJqaikOHDsHLK1+3i4iIcPm4hdL6/vGPf8BgMIj+GjduXKC2CrPkc9OmTfjzzz9F27UGvtIYaky40+CVIMo+n376KRwOBw4dOoRffvkFALBjxw5kZGQ43U9Lrvz+++8A8g1oQrZv346UlBQA4A1rABAYGMi3dfLkScrySRDuDp+UQMGgBuQb02yZhT7MggULcOnSJd31r1y5gm+++abQxyUIgihu0tPTS7sLBEGUQaxWK06cOIFevXrxZUajEb169UJkZKTiPhs3bkTHjh3x+uuvo2rVqmjevDk+++wzlw3/hfZQa9asGXbt2vWwQXPBmizMks+MjAzZIDYvL8+px0dGRgZsNht/XDWDWl5eHm+1JAjC/WHxytRiLFosFuTm5iI1NRV2ux05OTm8DNGKq8hxnMgoJkQtgUB2djb/mRnwTSYTatSoAQC4fPkyf1xAbpAjCMI9cNgeeJ8aNPQBuwUopLqQlpYm83SVyiShXpKbm4v09HRRmfQzANJjCIIoEWjcRBBEUWGxWGQ6kY+PD3x8fERlycnJsNvtqFq1qqi8atWquHjxomLb165dw19//YWRI0di69atuHr1Kl577TXk5eVh9uzZuvtY6BhqZrMZ1apV4/+qVKnichtt2rSB0Wh0yRoozfIpNYadPn1a00CXkZGBuXPnYu/evXyZmkHts88+090vgiBKn2XLlmkGoczNzQXHcUhPT8fcuXNx7tw53lC2fft2bNy4UVRfKBvOnz+v2CarU6dOHdmxGDabDZ9++in/XWj0Z8cneUMQ7okt74FxXMmgxuKq2XOK5FjSCcHffvtN9P2PP/6QGfeZ7OA4TiRHPvvsM3z22WdITEwskr4RBEFoQXoMQRBFxeeff47g4GDR3+eff14kbTscDoSFheG///0v2rZti2HDhuGDDz7ADz/84FI7hTaoXblyBTVq1MAjjzyCkSNHIj4+XrWuxWJBeno6/8eWWLVo0cIlDzXhMk0tbw6lJVxs0Mv2F7oeay35zMjI4AfhBEGUPNLZCS3u378vMmQptcWeZeZBlp2dDYvFgszMTNlzzrJtqj3/FouFl1+VK1cW9VfYD7Y/kztCmacky0jeEIT7YMt78CwbFGKVmXzz/9sfPu9Wq9Xl2KxMbkgNalJ5lpeXh6ysLMU21OSGmnctQRCEq7iik7m6j1a9ghyXIAjPZebMmbh//77ob+bMmbJ6VapUgclkwp07d0Tld+7cQbVq1RTbrl69Oho2bCiKQdukSRPcvn2bH/vpoVAGtQ4dOmDZsmXYvn07Fi5ciNjYWHTp0kU1FpHUwti0aVMADwPw6lU8t2zZgitXrgBQNpoxpJ4iwMPYakzhFCqYWoPXuXPn4ssvv+SPSxBEyfLFF1+4VF8rJpnQAMZYvnw5vvjiC0U5sHjxYgDKMsLhcIj2Y/9Zf3NyHnqssGWeX3/9NTiOE3nl0mCXINwbm5Ut+VQwqBkfLD0QGNR++uknnDhxwqVjqMk56fIpu92OefPmKdalBEsEQRQ3GzZscKl+RkaGbj3uiy++UM3K98UXX+DevXsuHZsgCM/Fx8cHFSpUEP1Jl3sCgLe3N9q2bYvdu3fzZQ6HA7t370bHjh0V2+7cuTOuXr0q0psuX76M6tWrw9tbJaO7AoUyqPXt2xcvvPACWrZsid69e2Pr1q1IS0vDunXrFOtLLYwXLlwA8DDekV4lUE3I5ubm8m1UrVpVlvEqMDBQMQMfIycnx+myUxLiBOF5MO8OoYcq+yyVE0oyQMlwn5KSAo7jRBmDlcjMfBik3N/fn//MsosyhIY3giDcD1tevv7gUFKdFDzU7t+/X+CMdsIJgXv37olmSrOzs/l2lXQStclBSnxCEERR4Wq8V6GOpMf7XmtMSJlCCYJQYvr06Vi0aBGWL1+O6OhoTJ48GVlZWXzWzzFjxoi82yZPnozU1FRMmzYNly9fxpYtW/DZZ5/h9ddfd+m4hV7yKaRixYpo2LAhrl69qrhdamEMCgoCAISGhrpkUBMG+QYeKonz5s1DdHQ0X3bu3DlRPaPRyGf1Ywi9QlauXCmLuyQV+lrLyAiCcE8WLFgA4GECAJvNxj/b0kGm0nICqfeZxWLBggULcOPGDVlSEy1FMS8vD1WqVIHRaITZbKYBLkF4EHZbvlErO0dBDzA+MKg5Hm5zOByqiVGcwWSD3W7HN998w3u3Avle+rdv3wYAfPPNN7KseeShRhBEceOqV72r+o5WfdKdCIJQYtiwYfjqq6/w0UcfoXXr1oiKisL27dv5RAXx8fGieLK1a9fGjh07cOzYMbRs2RJTp07FtGnT8P7777t03EJn+RSSmZmJmJgYjB492qX9vLy8XDKoKQlxjuNgsVhgMBiQm5urWIfN8Ao9QaQzLNLBNCmmBOHecByHrKwsBAYGqtZhhnD2vAsNatJnXGnNvNRoJoxzJG0nMzOT/8y2e3l5IS8vD3l5eWjYsCEyMzNhNBply7ikBnuKoUYQ7oPDwbwi9C35ZOEspDCvVaHMYnJMCvPEsFgsCA4Oxv379wGI5RQrE7bFjhMQEMCX0yCUIIiiwlUvMa0QPUp6HMkrgiAKwhtvvIE33nhDcZswGSWjY8eOOHz4cKGOWSgPtXfeeQf79u1DXFwcDh06hGeffRYmkwnDhw93vSMuJiVQw9vbG2fOnEFycrJsGxsEC2OaSA1v0j6QWzFBuDdJSUmYM2eOqEzNECWMnVgUBjWTySTbFhMTw3vKsuWkbFCdl5cHs9nMe6iZzeI5jbVr1+o6D4IgSh7OwfQFhYGewpJPjuNkS8oBYM6cOTKZlZOTg507d/Lf2WCSyZcTJ06IjHNCuXX8+HHRMdm2OXPm4O7du7I2CYIgCourHmo//fST6raUlBSZTCR5RRCEp1Aog9rNmzcxfPhwNGrUCEOHDkVISAgOHz6M0NBQ1zvixKCWm5vLe28oCXE2Q5uVlSUaEDuLSyT1UGN9yM7ORl5ensygRgKeIEqOnJwcVU8M9llPvCD2HLO6iYmJSE1NVdxPyaCmtvTbZDIpLvVkniZsMM0McHfv3oXJZFI1qAkHv4Dc2Cf1RCEIogR5YFDjlFSnB0s+c7PSRDqK0KCWkZEhS0SSk5MDi8Uiyl4OyA1qAHQtH01LSxPJIjLKEwRRHEjHYmzcJIXJOCF6lqnTeIsgCE+hUAa1NWvWICEhARaLBTdv3sSaNWtQr169gnXEiUFty5Yt+P3331W3JyQkAAB+//13kUBftWqVS/1gfVi+fDn2799PmfcIohT55Zdf8Ndff/Hf582bxz/fahnutGCDy8OHDyM2NlaxjjCJAEPNQ83hcPDbhPJr27ZtAB4OpuvUqYOXX34ZAHgPNZPJJDOoSZd8SQfDBTlngiCKBo43qKl7qO3euQWnTp3ii4VGsHnz5uH8+fP896ioKKxbtw5//vmnzKDGcNWg9s0334jigwgHpTRAJQiiqJDKqyVLluDQoUOyer///ju2bt0qKpPGuCbDP0EQnkyRJiUoDFKDGsdxSE1Nhc1m4zNlWa1W3L9/X6YUSgWx0KCWkpKCtLQ0APnKpK+vr2Y/2AA5NTUVVqtVcekoQRAlQ25urux5NxgMosx2wu0pKSmqnmeAc6UtODhYVl/YHtufyQVhlk8mZ4SwAbC3tzcfVNxkMsFgMMBsNsPLy0sUbFypv6wPSoY+IF9mKR2bIIiihRnUHJxSls/8GGoGh0XR0wzIf1al3mMOhwM2mw13794tEg81QByr6NatW7r2IQiCcAUlDzWlJe5eXl6K8dPS09Nlk5VCtPQ1mhwgCMKdcFuDWmZmJr799lucOnUKCxYsgMFgQGxsLObNmydTKqWebUKDWk5ODubPny86jhYcxyE2NhY2mw02mw0rVqwozGkRBFEI7Ha7zIvL4XDgm2++kdV1OBxYsGABvv32W9m2Vq1aISAgwKlBbdy4caLv6enpovaYrGEeIEIPNSW8vLzQo0cPGI1GXgGULvmUnp8QjuOQkZGBb7/9FsuXL1esc+3aNZGMIwiimOAeLB1XUp0eeKh5Ge0iOSOVD1KPMZPJBLvdjlWrVonqKhnU9CJMbrJx40aX9ycIgtCC4ziZh5rFYoG3t7esLkvKJOXrr7/GtWvX+PaUjkEQBOEJuK1BjcUKSkhIkBnBpDMTKSkpou9ayzSVZk+kbbOXBEtLr0VSUhIJfYIoBJmZmcjOzgaQ/zwJ/9tsNpnBSe15Eyp30qWT3t7eqFy5stNntWLFiprHki7vTEpKwo0bN1Tb4zgONWvWhMFg4OWWNCmBlkFNaLCj5ecEUboYwLwpFLwjHmT5NBtsIp3E4XDAbrfzA0fZbkYjP9gUyjAme4RlDofDqZc9AFXPejX5d/fuXcpoThCEboTethzH8TIkKytLMTa1NGYagxn/hbqfUlxaZ7D9CYIgSgO3NaixrFVRUVGwWq0iIxozsI0YMQKAPLil0kxIWFgYGjdurGpQEyqpTIFVWiohFfALFy5UDGJOEIQ+fv31V+zYsQNA/vPE/ufl5cFms8meWekzyGSDVgIRlm1PTUFr0aIF/3nkyJH8Z2mb7Dszbu3Zswf/+9//VM/N4XDwHmkMo9EoiqHm5eXFbwsPDxd9F2bsUxvw6l0GRhBE4eANakox1B4Y1IxcriiTuMPhQHx8PH7++ef8NhQ81JgOoWRQEz73wcHBih4gUk6ePKlYrib/vv/+exqQEgShG6FBLSMjA99//z0A4O+//8bZs2dlddUMakz2sRjZCxcu5Ou6YlBjuiNBEERp4DYjMWZQu3nzJgBloxiDKaRq2USl2WQAoFKlSvwgVomQkBAA+TO1wuUSUqxWqy7PNYIgCgaTAffv3+c91ITxw4SDReEgUCmgN4PjOBiNRtU61apV4z9XrlyZ/8w856THYElQtGQF8NCgJh1EMw81Ly8vkYfaI488IvrOcRwvb4QK6a1bt/iBNsUSIYiiw+FwqMYdMyB/gJedLX/uLQ8cSI0QyxhmzFds74FBTctDTWpIFxrcXYW86Qmi9OA4TtOj3ZOw2x8ubXcW11oow4S6lslkQmxsrEzGMR2Q5BVBlA84juOfe0/F7QxqixcvBqDPoMaUUSk5OTmK+7FBtRJs1vfatWuiLFxSrl69ih9//FF1O0EQhYPJAIslP7i3yWTC7t27+e07d+7kPy9cuFBxaZQUh8OhaVBTG/AeOHBA9J3tz4xbzpZhKnmoCQ1qQg+10aNHo3379jAajWjdujWAfJn166+/ytr96aef+GWt5KFGEEXH/fv38dNPPyluYx5qafflCUKuX78pqgM81GukiQn49h7IAqbvSBMzCcueeuop5ObmIigoqEDnJWxTCTLME0Tx4nA4sGTJktLuRpGgFT9WKmeEetKoUaP4z97e3jh37pxsiTrz+tdahk7GNoIoO2RlZfFjP0/FbUZi0iWfUoNabGysqC6QrwAGBATI2lIyqLFseWoDZ2HsJC1jHglxgigZWCwNi8Wi6hVqMBh4uaHHQ03NACaUC8I6QrkDOPdIk6LHQ40d+5FHHoG/vz84juO95JQUyjt37gDIl4M3btzg5VVCQgLy8vJw/fp1l/pIEMRD1N7xWVlZyMpUXrYEADDkP8dGw8P9mXwSyhShfsF0ElYmlC+pqalITU3lZUClSpVgsVhUPfP1QPoLQZQenvj8xcTEKJZLMxYLYeVs4lGomwnHbGysFh0drdjOnTt3FLODCo9BEITnUxYm9DzCoNauXTvR0iuhhxpTVBs2bMhvly7TYu1JDWoREREA8mdM+vfvj1dffRUANGOiUSYagigZDh8+DAA4dOiQqkHNaDTq8lBjBjWr1YrBgwfj7bffBgC89dZbAICgoCBMnjwZQP5Scn9/fwDaxnU1Xn75Zf6zkoca67fZbEbjxo3RqFEjWV+ZjFOSLWvXrgWQf75LlizhFd7ly5fj1q1bWLZsmct9JghCmzNnzsBqyR8AOpRiqBnyn/GQyhVRtWpV/tlXM6j5+vryWYxZmIq0tDQA+fJhz549+OOPP3i5ZjAYMG7cOHTt2rXA50C6CkEQrrBy5UrFcuGST+nEHytnHv7OksHt3buX/8zGZQCwadMmHDlyRHEfoSwjuUYQRGnjVgY1oduvcCCrFpRcmJFTOOuh5EVisVhkSz4DAwMB5MdPM5lMCA4OBqBskGMoZc8iYU4QhUNpdoLNXqot4QbEBjVpINy0tDQ+1hkzpkdFRSE0NJR/9itUqMDXDwsL4/vCvECkWT/1UKtWLf4zW2qq5qHm4+MjM7b5+Pjwy0BPnTqlehymxLIJAK2kCwRB6INl47x16xafbRzIN7o/zPIpV524Bx5qBnAIDAzElStXEBwcDI7jRAY15o1RsWJFXLlyBYBcxrH9MjIycOHChfx2DQb4+/vz4SkKstSblnwSROnhae/ne/fuKZZzHIcLFy6A4zhER0fLDGrx8fEA8nWUw4cPq7ajBItnzWByKTExUbM/BEF4LuwZLogTg7vgVga1qKgo/rt0WYQQYTBuh8OBJ554AjVr1uS3S9PKBwcHIzc3V+ahxtpl9ZmCKnQxrlmzJipVqqTZdxLmBFH0aBnSGAaDgX/+/v77b9G2kydP8pmjmGErMzMTPj4+snakCuHw4cMBABMmTEDbtm0L0n306NEDNpsN3t7eokQDBoMBbdq0QXh4OAB5EpVXX30V7dq1AwBcunQJdevWFW0XzgoLvVuczQITBOGcLVu2AMjPPizM1unt7Y1H6tYBoOKh9sDIZuBsvD7zxBNPwOFw4M8//+SrsaDkRqMRV65cQUJCgkyHYLqI1WrlPTTYwJJtYzLriSeeECUqYBMDBEG4F542Vjh06JBiudVqxZ9//gmO47Bu3TrZIJjFoeY4Djt27OCzqI8fP97pMdWSrmzcuFHUH/JQI4iyA9NnPDnbuFsZ1IRoGdSE3x0OB4KCgkSDZGnQ3qpVq2oa1NiML9smbF8YNFzItWvXeO+R1NRUPm4Rx3EiwyBBlCXu3r1b5JlY2GymFGcB/4F8xS4uLk51O3uWhd6p7HlXqsdg8iQgIEAxTqMeWBy0gIAA9O3bF0D+oNjLywu+vr4iWSScBPDz8xNty83NFclHtizM4XDA29ub98glgxpBuEZWVhbWrVunuE1pIs/LbHywTUF1MuY/f5bcbBgMBhgMBgQHByMpKYl/ZoUwA5lW1nChtzzTQ6S6UqVKlUT6j9B47+ycCIIoOTzl+bt16xZiYmJ4b10g39GAxW+VTgJKvzPY+TIjvx5jv1ZGZKWkLdLPBEG4L2r2EfYMG41GXL16VTV2ojvjNgY1qRDV46Hm7e0Nu90Oo9HID0gbNGggUyiDg4ORnZ0tMqixmG0zZszgFVSpolq9enX+WFIj3erVq7Fx40YAwLFjx7Bp0yYA+UaAP/74w8WzJwjP4NChQ9ixY0eJHMtsNmPw4MFO6wkzgKrBPNRCQkL4Z3nGjBmi7Vr9cIbScimhkYxtf++991CvXj1RvcaNG+PNN99UbTsnJwcOh4M30An7HBAQgMzM/IyDtOSTIFzjypUrqgGxAciyc7KEAz4++c82J3ruHxjsvfJ1DBYf7cyZM4ptay3ZZANU9jx3796djy0klTXMeNe6dWu0bNlSs12SDwRRemjpGe7Eli1bsHLlSqSmpvJld+7c4Q1s0rA6anGnheF5AH2Tfmp1hOE91I5DEIR7o2YfYbLRZDJhzZo1mnqZu+I2BjWpEmi1WvkZWTWDGhvomkwmfobWy8tLNgBmcZCEXiosm55w0CvtQ0BAAH8sPz8/0Tah94yzIOIEUZYoSKydqKgoxdiG586d4z8L4xUxhDHOCkJycjIuXbqE6OhoJCUlISUlhd/Gnn0/Pz9No5nWmn7WRtWqVVW3Scuk189oNCouQ2WwwbXUqB8dHQ273c7PGpOHGuEqagGfywtaA1yHw4Fjx46JvhvwYIBoVJAXD2KoMYMaoG0005Kj0m1qciMwMJCPwxgUFAQ/Pz+RzJTKNY7jcPToUVitVpw8eVL1+ARBFD2XLl0q7S7owmq1ymTXkSNHYLfbERsbyy9bZ6h5qDFvFDXvWiWUJgzYvsnJyXxMycuXL/P6HBt3WSwWLF68WNfqBoIg3Aehhxrgmbqp2xrUhN5kaks+hYKWLePiOE6mRLIYaELDl5eXl6xdg8GAmTNn4oMPPgAg9vjQWvZFrsdEeUGa2EMvf/zxh+LSpt9++43/nJiYKNomDeYvRcmIpcSGDRsA5C9jUOKtt96SZdoUIu0Xw2w244033kCNGjXw0ksv4fnnn+e3zZw5U9NI5gpMDkrl2t69e0UzyAX5XYjyzfbt20u7C6UKM6ipZe8WxnF0OBwwGFj8wgdySSCfODx4Tk0PYzsKn8lRo0YBAC9rhLLtlVdeER370Ucfddr3GTNmYOrUqWjSpAl/vKeeegrPPvssX0fJoLZt2zbcvXuX96oXnh9BEMWHp6xesVgsMv3l6tWrsNvt2LFjB5+BnaHmoQYAderUQUREBGbOnKlr0k9N5zMajbhx4wZ+/fVXAMBff/3FJ6Ji8js5ORk3b970yOViBFGeEepidrtdNLbxFNxmBCZUPPfv3w9AOaaZ9HtgYCD8/Px4g1pgYCCfrZPNilSpUoXfxrw8QkJCFI1kwgDiycnJSEhIQHBwMKpXry7rJ+PcuXP8S4CUUqIskz+oLFg2OC1Pr4sXL4oMXiaTic+GqYZSLDQher1Gvby8NM+JZQSVwmRIUFAQvLy8EBgYyMscb29vRQ+1gsAUW+G1UDr3pKQkXL58uUiOSRDlAWGMRbVtaWlp+Pvvv/OXfD7I8ulQECkJCfmGd5PRwIeUED6zzMtdGHaCIX2e9Rjjvb29ednF4guZTCaREU0a/5Wdk5K38IEDB0h/IYhCEh0djbt375Z2N0RIEzY5w2q1KsqgrKws3LlzB3a7XVQunaysXbu26LvBYHCqrwnrCjl79izS0tIUdUHWD6mTBXmoEYT7cP36dT7OPKBsJ2HerEJdLCMjg49V7wm4pUHtr7/+AvBwdlV68X18fPj4R9OmTUODBg14xbFPnz4YOHAggPx4RR988AFCQ0PxwQcfYNCgQejduzcAYNCgQRgwYIBmn5g78bBhw9CzZ0988MEHTo0J5KFGlGWEXp6uoqXkWCwWnD59mv8uHChK6dixIxo1aqSoYCkpbayNDz74gPc+1QOrO3DgQMycOVO2ffTo0QDAe6aFh4fjvffe47cX1kNt6NChAB4uHRXO7qolVvBEN2mCKC2YbqGk4LGy69evY8+ePQ+WfDKD2oP3vDDGGpf/2dvLqOihVr16dbz//vuywZ/0MwA8/vjjLp2HUnyht956C1WqVEHTpk0RFBSEgIAATYPa+fPnaSBKEIVk69at/LJEd2HPnj0u1WfGeSnJyckAIDKomUwmpKWloUmTJnzZ2LFj+c9KE6laephUFqalpSEmJsYlg5raElSCIEqeyMhIHDx4kP+upGew7UI95saNG3ysek/ALQ1qDCbQhQNtAEhPT+cHmWazmc+cx9phbZnNZt4oZzabRdtY3BE9mEwmGI1GmM1m1RlcNiPFtu/atUtX2wThSbhiUMvIyBAtDdi5c6dIkEqfa6HixZ59pWMpeXgwhIYmYVwNQCwP9MDqsiXlLBajdAkm+28wGETtu3IsJQICAhSPCxTeWEcQ5YmdO3dqeqEpbWODNTYYFcZQU17ymS+PvMxG3kNNajTz8fGRxQphn4Wxz4SxXoX/1VDyuPf390dwcDCCg4Ph5+eHatWq8cfeu3evYjtK+s2+ffs0l3QRBFE8XL58WeTZURj0TvZbLBbYbDZF3Yu1ITSoeXt74+rVqyKPfKGuojR41tKN1I4bExMjK2f9YEs/GWzCID09XTTJuHv3bpl3HUEQrnP8+HHcu3dPV12TySTSLbSeQaGcEhrhPAGPMKgBQPPmzTX3lwroDz/80KX6RQW7aTztRiAIPbhiUEtMTBRlBL13755oYCbNFircxhQ6JcHLji/tR6tWrfgyZoQqSqZMmQIA+L//+z8AzmOWGQwGp3JIi/DwcMyYMQNDhgwRHS84OJgMagThAocOHVIs14qhxmQPS5aSbyDLr29nDmrCtrgHE3lGg6KHGkPNQ23atGkAgAkTJvDlw4YNw4cffohmzZppnt+LL76ITp06icqMRiMGDRqEp556ChMnTkTTpk3582WeJmrnLGTv3r18JmGCIEqOgwcPlviSJ6XkUAwmH4RGMibj1OKjuWqMV9Iv1RwZWH/+/PNPUT0WQy0xMVEUJ/TAgQOaoUcIgtDHli1bEB8fr6sum2BkaHnCC+slJCQUvIOlgMcY1Ewmk+YgWSrMnQ12CxrAm5Z8EuUZJYPamTNndAvWxMREfPvtt8jIyJC1IxSkfn5+MBgMmoJX+gwnJiYqen8UFaxN6X89+xTmmNKU8z4+PrrjkRBEeWHbtm2w2+3IyspSXOKk9G7WMqhJB3GiGGoKHmqOB+Y1k5FDXFwcrl+/rvj8syRJUoOa0WiEyWTiPT18fHzg6+vrNDkL218pOx77Y975LEGLGmoD14Iu8ycIwj1QknFbtmzhP+/cuVOUNEH4zLN9mXFMKCeYXqJmqFIrZ3JQD1lZWYrlcXFxWLBgAW9Yk06CkNwiiIKxY8cOpyEg9D5fRqNRNFknbDc9PV0U49GZnHJn3NqgJhXozENEjdmzZ+s6lp56anVYnxo2bCjbxnEcBfUlyjTSQNtAfhKR6OhoWV0lwXjx4kWkpqbi3r17qsamhg0bok2bNgCUZzLUPNSSkpL4YwqVuF69ehWZR6pQLpR0Vk12PC8vL/JQIwgJR48eRV5eHlJTUxWDcGst+dTz3hYu+bQrZCVgSz6NBo73kFCSEb1790aVKlUUt82aNQshISEAgPfffx9hYWFO+6XE7NmzFZXdvLw8TSWYlkMRRNlESf4dP36c/3zo0CFERUXxupNQTjD5yMJnsLaEehXb76OPPuLLnnjiCVVj2NSpU3l9inn9S4/LEGZbZhiNRqSkpPCxroGH8ovVJ4MaQRSMw4cPK8ZaFeKKQU2oYwljHCYmJoomQJ3JKXfGrQ1qQtSWTxQnSsqs1g1ks9mwfv16xW1JSUk4cOBAkfWNIEqaw4cP49atW6rPgNT7QSgYpTHRvL29NZcRMU84Pz8/Pkuv0nalcgCieEJeXl7F4jlaWsqal5eXUw81oSGS4zinnikE4U4cOXJEljlOyOXLl2Vxc5whlAHXr1/HyZMn+QRIHMdh27ZtigM3hjApgZ1f8ymY9HugLxoND4+j5c0q9DwrCc92diwtY7zdbkdmZia/hIogiJInMjISiYmJBd7/xIkTsthrenUAJWOU1KDG9As/Pz9+G/NeE+6nN/SGUD4q7RMbGysr00okc/ToUSQmJpJBjSAKwcGDB3Hnzp1Ct2M0GnHz5k1ebjBDncViwaZNm0R1OY4rtpBcxY3HGNRKg8mTJ8s81by8vNCzZ09FBdhqteLmzZuKbd24cYNX3gnCE2GxdNSUlDNnzoi+axnUtJ53Yfvh4eF4/fXXRc+h0vEHDRrEf+7Zsyc6dOgAID8DJ4tnVFbw8vLik7CoIYxbwnGc7LchCHfm77//xrVr11S3nzt3DidOnJCVaz3nwm2XLl0SxVXjOA5Hjx7lkwspIc7yqWDMf6BOmQSijckqpViKBoOB90ZzVT7p9cYXwjxIWrRooVrHbrcjLS0NkZGRLrdPEETRsGfPnkIlIzh06BAuX74sKjt//ryufZnRjMmu5s2bixI8tW3blq/r6+srWhUglUt6DWomkwmzZ8/G7Nmz0bRpU1HGUEB9yacUu92Ofv36AcifNNGadCUIQpvDhw/jxo0bqttd8VADHo5LmIzJzc2VPdsOhwOtW7cuQG9LH7exYrkSm6w0Zx28vb1htVoVZ0e++uor0ffly5eLAhCTICc8GaVg2lFRUbBYLE7vbakBbfXq1ap1L126pOqBFhwcjMqVK8vKK1eujFq1avHPGfNQCwwMLHPPnh6DmlL8E4LwJApy32ot3dRjbLt48aJqnStXrsgNaqIYag9kHPdw2aTaxAGLbWYymeDn5+f0eS4KmGeaMBuf9HylmUnXrl0LgGQI4f6sWbOmtLtQZKg9b3rPMTU1VbH8jz/+0PTCBR56qAn1vWPHjgHIlw9MjgQEBMDPz48fEIeGhora8fHxQWhoqGqyAi2kcpN5tgQHB2vut3r1apF3C5NlwuvGcRxu3bqlaSggCCKfgtpboqKiEB0djTVr1vDP83/+8x8A+YY0h8OBX3/9Vbbf+vXrYbFYPNK71K0MakoXkM3sCl8wwjX6JY2WQU2I0WhEXFycohGCIDwRpXv5+vXrquvshc8sE6hs37S0NKfHU3pmXnjhBbRs2VK2vU6dOnj55Zd541mDBg0we/Zs1KlTR9aXoqAgHiJFhbe3t2wAXr9+fdF34fnSYJjwNAr6vnQ4HKr3u9ZzwN7nV69e5csCAwNFdW7evMkb1Pj8niLD9YPPDnkGPCVYEoL33ntPdqzigHmmCeOkSTMI2u12UZ+ZwY1iwxLuzqVLl0q7C0WKkgws7DlGRUUhOztbsw4L6G+z2TBixAgYjUbeoAaADzcxZcoU+Pv7o379+pg9ezb69+8vauf9999HhQoVMGvWLJf7KT135l07evRop/syA55wUlZ43TiOQ2pqqij2GkEQymjpYlrbTp8+jejoaFy6dEmmB7HQEkphPdLT01UnBNwdtzGoAXLlMzw8nP/BmEJX2mtr69evj5CQEKfBe5lQj46OxuHDh0uiawRRrLABqTB+l81mEyUA2L59O7Zu3YoVK1bw9b/77jteiTty5IhLx5KiJ9uddN+yYMwODQ1FzZo1AeQvoxB6mQBAtWrVRN+FA2AyqBFlDakH67179wCI7/sVK1bI9hEirLtv3z4A4mC5iomSwLKCymVKYIWKDw5k5z0p1AxqERERqFq1aoG8NwpDUFCQKCbl5cuXRd7Cdrudv647d+7kywu7BI0gPJHly5eXynHVPPTVcKWfJpMJly5dwsKFCwHI5SSb7ExJSYHRaERsbCxvZAMeerp6e3sjLCwMjzzyiO5j60Xt3PWEBmIy9c8//1ScuGXJ4ygBC1Fe2L9/v2YIDS1cNagJ5QnTuaQ2ELvdju+++0613fT0dFe76Ra4rUGtcuXK6NOnD/+DsQH5Bx98UCp9Yzz99NNo3769SPFWWq7BDH8seHJZGNQT5RsmHCtUqMCXSVOiHzlyBMeOHUNsbCxfPzk5WbNdf39/xWMpPTPl1aD22muv8ZlPn3zySQQFBYm2d+zYUfSdDGqEp+PKfctmNB0OB3/vSwNZSz1mrVYrH6vn5MmTAB4Gy/Xz89M0qDkUPNR69uj14EB2vPnmm5g9e7bqALBfv35o27ZtiRvUpk+fjkcffVRUdvnyZV5fsdvt/HUSxpi7cOECLZEiyh1xcXGl3QVduNJPh8OBq1evIikpCYBcTgpjGhmNRtngVhgPt3v37jLdoyhQ09lYed26dUXlvXv35j8LZa5S7DVmUCOvW6K8cPz4ccTHxxdoX1fHT0J5ovaM2e12UZxnRt++fQHkx1oT6mssJra747YGNbZUi1GjRo2S7o4mwmVuSkozM7KxGV92Uy5btqxE+kcQrrJq1SrVzJuLFi3iBRz7v2jRIj62hZLnmd4ZQKWMlUqD6WrVqvGx0dh2qTHbYDDIhHhISIgsyK2nEhAQoLg8XvqdDGqEuxATE4Pdu3e7tI8eJc5gMCAuLg5//vknVq5cCeDhYIkhXfq8evVqXLx4EQcPHoTD4ZDJHqbk5eTkwM/PT35M5qGmpDoZHhjHBDHUDAaDKOOwtP8lbVBTg12zmzdvqmYqF7Jo0aLi7hJBeCx79+4FAPz2229ISEiQbf/ll19kxp6tW7fySc1Y1mGtJCkHDhxQTDTgLM7arl27EBMTIyqLioriPwsHxH5+fggPDxfVNZlMxb5EXSv2JCAfqAvfF0KZKnzvsEkXq9WKjRs3itq4e/cuZUInPJq///5bNQZsYcYAej3UlHSCCxcuKO6nNjZk7UmNbe6YtFIJt+qlUBAKs/YB+Z5h7oSzpSGsjAX5ZTcKLZsg3JUrV66oxtdISEjgFRAmnBMSEmQeakKUZiAAoHbt2vznpk2b8oNa6XMkFeQTJ04UeccBwDvvvCPbR/ryqFOnDoYOHaraT09C6XyF/xlkUCPchZs3b4oGbHrRc9/eunVLFAdMupRHalC7fPky783hLD171apV0bdvX4nRi00qPPgqeu6YQc0GIdJnlsFiqLkDTF7Ex8c79SgGoGgkIAhCLHPOnTunOEl59epVWXKAEydOIDExUVSmlUDg/Pnzip5pzuKsRUdH80vkhf1Rolq1ahg/frxIhhmNRrz99tuaxyguhOMqIWoGNSFs+SfTS4XviZSUFMqETng0Z8+e1fRCK+hKHb376dEJmGep3W5Ho0aNZOM59nxLdT930ZOcUaQGtS+++AIGgwFvvvlmwTqjYoVUyupX2gg91Hx8fERxSYCHN4DD4cDNmzdp9oNwa5QMaX/99ZfiDCjHcXz8DeahpoTQ6CxMgywcxNasWRNNmzYFkO+pVqlSJYSEhKBGjRp8uRZGo1GUXaqsZfR0hlKCBoAMakTxsmDBAt11pfdfbm4ufvrpJ819DAYD9u3bJxtgMs6dO8e3LTTcr1+/HqtWreK/79ixA3fu3AHwcHDKvFodDodmdk2j0QiHwyHSSwxc/nPlHxCktEP+f06fZ67RaJTFPixthMu7pPoYyRGCUObKlSvYvn07AOVYjcePH0dkZKRmGw6HA1u3blVsozBoxSpiKOl5QoRysqi9RZTGd1oeakoDcaH+o2YAYAHQmW5ESz6JsoZWQiZnhrHk5GSsXr0a58+fx19//cWXuxJDjR3/119/5fUuISy8DwstITSUmUwm1WMVxKD23XffISIiAr6+vujQoQOOHj2qa781a9bAYDBg8ODBLh+zyCTjsWPH8OOPP/IDvIKgJkSnTJlS4DZLAh8fH7z22muiMnYuFPiS8ASUMnVeuHBBMQsLx3F8/A01LzTgoUHtkUcewTPPPMMv2xYKx9atW6Nr164A8g1qU6dOxRtvvIHGjRvr8ko1m82iZ6+8GNTYi6dfv36i7wwyqBHFSWEypFksFkW5ooTWrCd71oX3utQAd/bsWd4zwVWDGiBXRA3If593eaKrQm35kk8tjEYjunfvrqtuSSH0OJYmPmGQPCEIMQkJCbyRX6rzOxwOxMTEyIKCaw1U9Txjep9DPR6nADTHbkI56UxmuorS+E7r2rz44osICQlRra+2Lxvgk0GNKG/okRVpaWm4fPkybty4IVo6qvQ8sfbUDGoXLlxQ9K718vLCa6+9xstI4VhQKZQNw1Uj/tq1azF9+nTMnj0bJ0+eRKtWrdC7d29+3KpGXFwc3nnnHXTp0sWl4/H9LNBeEjIzMzFy5EgsWrQIlSpVKnA77KKFhYUVRbdKDKPRKLsRtAxqc+fOLZF+EeULdl8dPXoU+/fvx6VLl7B582YAwJw5czT3ZfdrXFwcNmzYoJlWXCictVKwMyOdNPaaVIgylGKpuUqzZs1QvXr1QrfjKRgMBlSrVk0mfxYuXMjfBzQAJoqbq1ev4o8//lDc5izenxZaBnvAuaLIcZxMGRMuG9Ja8skCV4v2f+ChxsdLE52Lax5qpQnzjGPnVq9ePdSvX1+0FIxl82NYrVbs3buXBqIEoYBafC/mjaG2rNLZeOD06dOylQArV67E3bt3cfz4cdV2Aej2ygC0B61CmV3UBjUl6tati3r16gEA2rZty69WYPJeeo31ePoyL7x169YBIGcHonyhpHfdv3+f92Bl2zmOE8Vu1DKoAWL5pUc3MJlMOH78OC5fviwaC7Zp06bIPNTmzp2LV155BePHj0fTpk3xww8/wN/fH0uWLFHdx263Y+TIkfj4448LnLm4SAxqr7/+Ovr3749evXpp1rNYLEhPT+f/MjIyxJ15INCHDBlSFN0qMZR+bLWXKwDZeRNEUcDuq+vXryMuLg63bt3ilS21ZAMMdr8mJyfjwoULojTpUvQa1Fh/tAxqQiWuKAxqPXr0QPPmzQvdjqdgNBoxceJExW3Xr1/H1atXyaBGFDtJSUmIjo7WVVdvwgE9qN3bLJA2i2EqrCuMBenMoKbmoQaDUlKCB2053H+gxmQGO//HH39clv1TOnDOzc3F3bt3SZ4QhAThMyHV+ZkcUdsnIyPD6UBUmmkzJiaG30fqeSXElViHzgats2fPBlAyBrVmzZqhc+fOAIABAwbghRdeEG2XXs9atWrpbpvpwjQxQJQ11PQmtSWfFotF5sEqfbaceagJ7RnOnimWiImtXhLKnL59+6oa9V3xULNarThx4oTIHmU0GtGrVy/NZfeffPIJwsLC8PLLL+s+lqyfBd7zAWvWrMHJkyfx+eefO637+eefIzg4mP+TxkhigrqgwfNKGnYzOEtKQBBA/qDTWewgAJrPkp7njAnP/fv367bsSwed7H9kZKRiTBA9sOC40jZFMYkK6KHWpEkTNG7cWHf9sgbz4pUmJRAaCBwOB/Ly8vjrvnPnzhLuJeEprF69WpTdrThx5f1++/ZtLF68GEC+h8Gvv/7Kb+M4TtW4I8wW9csvvwB4KLeYZwLHcaLBYXh4OOrXr4+IiAgAQP369dU91DjBObDzYUY2Tj2upLvBzp/FixMiXR6Rl5cHm81GBjXC4yisTqUH6ST6Dz/8AAD4/fff+ayawhU8Ql3wf//7n2bbwkHrxx9/LHrPHz58GADw2WefyZ5hVwaienXFksq4V6lSJbRq1Yr/3rBhQ15H1EqGpRcKi0GUJZTu4aysLMyfP1+27dy5c9i4cWOB73upzGTt6PH6lMZNE6LloSZ1yEpPTxfF6WYkJyfDbrejatWqovKqVavi9u3biu0fOHAAixcvLnT28kJJxhs3bmDatGn45ZdfVONtCJk5cybu37/P/0lTqmrNFrsjbEmElkGN3IoJRmZmpq7YQVrLnJwtgQIgGgQqxUZTQioQhcqG9B7WSkSg1h/hMYTPS0E91B599FFRooPyhppBbfDgwejZsyeA/N9NaFBTyghGEABw+fJlPtZYceOKEpeamoqbN28CyF+eEBMTI5JRrrTF5JBwMCbUOcaPH4+RI0di7NixmD17Nlq0aCHycAOEHmoKg0+DazHU3AGhQU0o54UBeVkdq9UKm81Gk4SEx1EQncoV2SLNJgxAFJSbPVt16tThy4SDQWfevVrjCOZxxZaWMrRiEimh16BWUs9/xYoVRXJo+PDh/JhLaGB8/vnnRfvpPWcamxFlHYvFwut1wufizp07iI2NLbBBTTgpKfyuJRuYriUMJWEymfDcc8/J6ghhcknqkBUcHFwkkyEZGRkYPXo0Fi1aJEsu6SqFMqidOHECSUlJaNOmDcxmM8xmM/bt24dvvvkGZrNZJrB8fHxQoUIF/i8oSJwpy2w2o2XLlggMDCxMt0oMdmMIX0QNGjQA8PDmVRPaH3/8cTH3jnA3inNmTygYL126xN9/FStWxNdff617f+l/IN96L0SPUU/4TCi1ySjqGGrliQYNGsgMamFhYSLZk5yczM+EkwJJuAN6lDh2DzNj2sqVK/ltTP7Ex8fj77//1txfCLv/hW7/ZrMZHTt21OyrWG4/6DvzRvPQGGoMNQ814TmzOnl5ebh58yaf4Zkg3J2cnBx88cUXJXIsPYYcPbJPKd7tH3/8oWi0kyJ8hg8ePIjg4GDRdi2vfmn/H3vsMVmddu3aoWLFiqptlBQNGzYEAHTs2FEUc7tly5aoVKkSH39NC5oYIMo6wpVHJ0+exMaNGwE8DIWhtRSdceDAAdXVLWvWrAGQ74UL5GdV1+qLwWDgjWb+/v4wmUyisaLUoMbsKRzHyRyy7t+/j5kzZ8qOU6VKFZhMJlmW0Tt37ijGWoyJiUFcXBwGDhzI27FWrFiBjRs3wmw2897FeijUCL9nz544e/YsoqKi+L927dph5MiRiIqKcjmQnNlsxrPPPgs/P7/CdKvEYD++0BAwYsQIANoeamwbuRmXL4rToCb1GjMYDKhcuTKCg4Nl8TeUEMYXMhgMImVDGKga0GdQE87ESg1qWVlZ/LaijqFWnmCyBnj44gwNDeXj2rHfkM3mkkGNcAcK8t5jMYOMRiMvf7TakQ6WAgICFO9/k8mkmWlT5qHGG8uUYqh5roeaMFsqU3KfeOIJAA/1HKvVCqvVqhlfkyDcCZvNprgsSA8F9VBz1h8tatSooRjvNj09XTNOm1o//P39Rd+HDBmCadOmKe4rXUbZt29fWZ3+/fvLjHSlQevWrTF79mw8/fTTCA0N5csHDx6M4OBgjBo1Co8//rhmG8Jr6ClhhgiiIHAcJ/L2ZxOFUo9WJW7fvu00FiPbLs1kLEXoNTty5EiZQU1qMxoxYgRfX+qQVaFCBVniJCB/HNm2bVvs3r2bL3M4HNi9e7fi5Gnjxo1ldqxBgwahe/fuiIqKQu3atTXPSUihRvhBQUFo3ry56C8gIAAhISEFCgzuSTGRmjdvzv/4Sj8qMxQopaxmhgMyqJUvitOgJhQeQL5nR2pqqix1sZpnpFLAbsaZM2dEdfUY1Gw2Gx/kmrX5+OOPo169evyMYvPmzUVC3BXBRYiRXsfKlSvLlngqySKCcAWLxSKTIZs3b8bOnTs145lpeVekpKRgzpw5fLuff/65YuKe3bt3w8vLC/PmzXPaT+mA02w2Y+3ataI+Afky2WQyoWXLlortyDzUmLHMKFD82LPHyjwkhlqTJk3QokULAPnymgX2NpvNonNmBrX4+PiS7yRBOCEvL09VrymMzuVMP5ce02AwOF15wjJNMqRGfq2Bq6seagCwZcsW0Xez2SwbtPr6+sLX11fkeVZScdKKkhYtWoj0oHr16mlm62PXfsGCBbyhgXBfbDYbPv74Y0rq5wJqRrLIyEgYDAY+Zti5c+f4bVLZwmLVask2FlpIb4gh4OHEnclk4u1FTNdgeolan5wxffp0LFq0CMuXL0d0dDQmT56MrKwsjB8/HgAwZswY3rvN19dXZseqWLEib99yxdHDraRmu3btSrsLuhkyZAj/0lG64FozHuymIZfj8kVxKilSrwHmBaaVhVOIlkENyE9pzNBjULPb7Rg0aJCoftu2bTFq1CheyZFm823UqJGuvhLaNG7cWNVAQBBq6JmlV/KwYIFe9So90nq5ubkirwyr1ar6blTro7Rcur80M91TTz0FIF8mG41GPPvss4rtSj3UeIOa1pJPD8jyCQBDhw7lvThsNhsfxJcZ1JQSnRCEu6HleV2YSeui9lBTyo7JdCM9gzahZ39B+sG8M6R66OjRozFjxgxRCB6pbuYJCGMxAflJZUaPHq1an12rlJQUXdeWKF2Y7qFn/FFe0cryqVWmZxWTVlvMC1hLFkt1KTaZaTQaeXnDdA3hs6y2NFWLYcOG4auvvsJHH32E1q1bIyoqCtu3b+d1nPj4eCQmJrrUph6KfIS/d+9eXTPIZQE208Osq0KjgxbsplGanfrqq6+KsIeEO1FcHokLFy5UVQikHmpqSA1q0gGpUOHTk4pduL90KUGNGjXQpUsX2T7kdl849Mifjz/+mJZ+EgVGKsOEs5Z65NvHH3+sOZGkldYcyPeKDwoKknlZdOzYUSQ/pPe41CgUHh4OwLnMady4MZ588smHBbxBrWwkJQDyQ3cIM2KpeahJEWZcJYjSwpWl30XVrpD//e9/OHDggFNZomRQ+/e//w1An0FNTyxc1p4SrH9S2ak00VsedDHhO4JWC7k/33//PYCy74iyefNm/PXXXy7vp2U0U9qWkpLicjv79+/H+vXrZdv1/CZ2u10kV4QeagwlXaOgsuiNN97A9evXYbFYcOTIEXTo0IHftnfvXixbtkx132XLlvFx4VzBrTzUPA2TyYQWLVrwS9UGDhyoWlc4+8NuIOlNmJubSzMlZQgl11ml8sJgs9mQlJQEm82mGLNQ6KGmJPSkHmlsFkHqiaKkDAp59913+c+hoaG8YOzVq5dsRsnf3x89evSQtVEelLjiRCh/tO4xUh4JJfQ8f0oyxFW5Jm1DuJ+WQe3RRx+Fr68vatSogSlTpoi2NW/eHB999JHqMYSKWsOGDVGzZk0Azs85PDwcrVq1EnT+wSCME6hOrA0PNag98cQTouVeej3U1BRygnAXSsKgpmeCEXj4HCnFbCxKIwEL2C9FuMxdqby8IbzmpBO5P+UlFnBKSkqhMq6zJZrsM6DgaV+ANoH8rOtJSUku7asUEgt46KGmlMBO7fjuDhnUCkGnTp34OFFStG4AWvJZPvjkk09E39k9cfz48QK1p3RPzZ07FwCQmJioaFATvnz++c9/iralpKTwZVIPNWlwSTaDqmf5T6VKlfi+NGzYEE8//bTTfYDyq9gVB8U1a0+UPVxRVpQGIWxAWVCD2uLFi3XtZzKZcPXqVVEWY4ZUadNa8ilKMqBT5vBJVqRLPoUYPCuGmhrMoMZi2qpNpnhinCWi7FEaHmrCbWzCUEmWCPUl9lkpSQLr52OPPQZfX9+CddgJzjzUateujT59+hTLsd0RMqh5JmVdf+U4rsBjoePHj2P+/Pn82JPd11LvMCkGgwE///yzaB819Fx/pjs88cQTcDgcCAgIQFBQkOKST6E8CgoKQs+ePQE8XKLuSeNC0ogKQYMGDVC3bl3FbdKbUurqCMhvTE+6cQhtlIQS+72VsjgVtE3hkk6j0Qi73Y4KFSo4bctut4u81ziOg6+vLxwOB+x2u8xwxgxqaoMrabYYdo+HhoaqGp2l0P1fdHAch+DgYEUjq9oL0263k2JZhuE4rtCzu1qDEL2KrrAP0v5oyQChAUdaTyjzgoKCND3UtNpRgyl5kGb5VIqh5mEealJY4PLq1avz35VQM6iVdQ8Cwr0ojDe2VtZNrW3CgarUA79Vq1Z8+AXh8dlzpNQn9sw8/fTTfHxHISaTSXfSJqV3PvDweZU+t+x7xYoV+WVR5UEXs1qt/HUnvcdzKA/vF6XnT895C7Nw2+12mR5kt9sV2xEeTxqih7UL5MtEPXoeS3DUrVs32Gw2dOnSRTZRwHEcH0ON4e/vz2cXFzpieMrzSQa1YqBbt26aBjU2o08GtbLLkSNHZGWFFQrOBJmXlxe+/fZbWYBJ4XJj1o9PP/0US5YsEZWZTCbeoCY9FhOG/fv3B5CvNA4ePJhX3oTC8rHHHlNMT0yUHAWZtV+4cKEsCxlRdjh27BiWL18uK2f3ip73j1AZ02tAkwaVFe736aef6moDEA8EhTGHnnrqKZHRp1+/frLBsHCQyQLxu8LDa/Og70oeavDMJZ9S9MZQUzKosXcLQZQUBfVQS0tLw7/+9S/FbXfv3tWMZ8yO+emnn/IDUKn3hfD4/fv358vYvk2bNpX1U7jUWogrgblNJpNiPFXWrsFgQLdu3dC7d29Zv8sT165dI1nlgZQHDzUlvvjiC5cybX/66acyR4fU1FTs2LFDcz9hxk/GnTt3+G1a1z80NFT03WQygeM4VKpUie8DkK+z+fv7o379+ggODtbsz4ABAzwmYR0Z1IqBrl276nr5lQdLe3lFml0zLy+vQC8C4YyC0v7CgaWXl5cs2ycA3uLPUEpUwHEcjEYjf99KB6TMA6RZs2YA8g1qrVq1Qv369dGwYUPRgPWRRx5BvXr1dJ0fUXxIlXD2MpPOqLMBQUpKimjw7HA4NGfpiZJBacawIGRnZxc6NodwuZLS0iU9aO2nlW1KaMBhSzxnzZqFTp06ieo1btyYj7ci3bdmzZp8QgJA/2CSr6e05FMaQw0AOM9V+l0xqEnvzaK6V4nyQV5eXqEnGtWCaEt1Lun7TChrpPetM9mmdJ8L92HygvWtSZMmor5VqVJFtLqF9dNgMLiUqU96PCD/uVSK5yys07VrV35iobwmJRDiKR4whOePm53JPLUlnzabTRZjnck5tTaV9H+LxYJq1arJ+qQXLdnYs2dPxSXrdevWFZ1Xp06d4OXlhXr16jld4t68eXNUqVJFd/9KEzKoFRPSh4J59gDqMdTK20usLCMVbp999lmBXtpbtmzB5s2bASi/SISxg9QyRUmXCgg904T9NZlM/DGkRjfpoIp979y5syhjp1BxdIXevXurLlUgXKdly5bo37+/6P5g958wC7PVasVnn33GfxfW//vvv7F69eri7yyhifD3KSzOlC4tzp07J4p3VtCM1CtXrtQ8bmBgIP+ZeVEAEC1DBPJnLtVkBku7ztpig0Z+6eYDCm5QM7ENwkoPP3uwl5qXlxd/vQYPHozOnTvjueeeQ58+ffDCCy/w9ZKSkmT3JhngCVdYtGgRTp06Vag2lCYar1+/ji+++EKW8GTFihX8PkuXLuW3Se9jZ5OfSp6+wsHusWPHRNuERis2ecn0terVqysOfKXoXdrKZKJQdkr7oFX+7LPP8ku2ygqdO3fW3E4GNc/B0z3UvvnmG0RHRxdo33Xr1om+nz17Ft9//z0+++wz3Lt3T1ZfeK2EE+rSZ96VrKJKDhnMI1You5j86d27N7y8vNCnTx/d4X88FTKoFRMcx4mMCw0aNOA/s5suJycHubm5Jd43onQQxk5T+92lRjOLxcLPpAoFmd1uR15ensi6r5ZNJTAwUOQyqyR4hUs+AfCeLLVr10br1q1lSh4zqNWuXVukfLVt21axD854/PHHKch1ERIaGor69etrXlOO42T3oVCxzMnJkXn6FAckA0sG6TMszQTlzLik1yON/Z65ubmaAxWppyRDmHFSuDzTy8sL7du3579ryZoWLVrAbDaLvHODgoJkMU/1GtT450hqUBM1Jph08GCDmtBDrVWrVggNDUWLFi3QoUMH0TI1pd+WDGqEK9y/f18kV5heo4bSu0LpPszMzITD4RANKLOzs5Gens57dGghbTM3NxdWq5UPiZGcnKy5vxShnHE4HDAajXw82scee0y1rrBMr9GHPbvSpe1qsk5a3rJlSwQEBOg6lqcg9WKW4m46SEH7427nURx4uodaZmamqu4DuGbctVgsiquSGEL9nck8JYOakIiICN3HZzCPWKEsYfKH/a9Xrx7CwsJcbtuToBFsMdG7d2/ZUjsgP2U2GzAsWbIEX375JW/g8HTLO6HN77//zn/+8ssvFevs3r1b9N1sNsNms8FqteLbb7/ly3ft2oVff/1VZFBTc52VenEo3WfSJZ8pKSmi7UJBOWTIEI9xwS3vaBkMzp07J7qnAOUZreJG7VkgipfimpVnv+eXX36JW7duqdZTS87SsmVLxWQ/ZrPZ6T0pXOo0bNgwNG7cGM8++yyCgoJkSuTzzz8vW/qgRnBwMJ555hnAwZR5Bc80oZHN4bmGJemSTzXYe0XomUNLPonC8Pfff2PVqlWq25XeFVI5dvfuXfz2228AxO+zQ4cOgeM4rFu3TqZnOWvzyy+/xNy5c3H48GFs3LjRqeF46NCh6Nq1K+/RyZ4ns9ksM6ixyUmlQSmQv0xdqU9qqHnt6jWolUXY9VcakwHyrPalTUF0opSUlHKhS5X1cbIrWT6dGdqVVpjk5eXpTv6khtB5Y/DgwaL+lGfIoFZM1KpVS1FZ79ixo2xpHns5l4SgyM3N1X0cjuNkscAIfQi9P6SzRlrXlM3Wsjomkwk2m00mNLOysmCxWESzNWoGNbVAt4yUlBTk5ubCZDLJBrnsuELvuObNm6tm+yTcC62Xo9VqlQ0MHA4HrFYr/9IlGVD2UVLI8vLyNGdR9ZKTk6M6o6zm/VitWjWMGjWK/y7MMOxMYRMG42YBb1u2bKkoA5s1a6bq1SvFz88PrVu3FnieKXmoCZ41D/ZQk6ayV4PdH1arFRaLBVartVx4SBDFR1pamixOkBpKsYOys7NFckX67mL6mJIxPysri3/fKb3zLBYLUlNTdWVpb9KkCbp168Z7dDLZ07p1azgcDhgMBl6HYnqb0rIpIF/fAtQzrEtxluVTSnkYBLNzL8uxfcuLd7Ce568wuIO+K30m1fpUkPetMw81PfLA39+f/9yyZUuX9i3LkEGtGDEYDBgyZAg/S/XCCy8ovhSZEaUkDGrfffed7pgVKSkp+M9//lPMPSrbcByHZcuWicqOHz+uWp8JpP/85z+wWq0wm82q6Y/j4+ORkJDA31/Ce0u4NEo4QGJJBYQsWLAAv/zyC0wmk+ogt2bNmhg9erTGmRLuiNYLTi2e1po1a7Bnzx7eoEYyoGyilYl6x44dvJdHYVi1ahX27dunuG358uWoWrWqrLxatWowGo14/vnn+X41aNAATZo0QYMGDUpXDkmTEoier7JhUGvVqpUojp0azBvtwIEDWLp0KdatW4ft27cXd/eIMsyZM2d0e2Jt2rQJmzdvFulG//nPf0QDbqmHBvPEVzI+fPXVV4iPj8fvv/+u6sF24sSJAnkzGQwGDBw4EB07dsQTTzyB/v37IzQ0FEOHDpVNhNarVw+jRo1C9+7dAeTrbCNHjuR1OLYk68UXX5QdR2oMHzFiBMaOHYvBgwfzhjmlvpV1jEYjXnzxRZGTQ1mP51RWSUhIKNb2S1vfVZJ/an1y5mmrhDODmh4PNaHMYvJjxIgRqFWrFoYPH44WLVq43K+yABnUipnmzZvzs1Tsv/SBKSmDWkZGhqrHQE5OToFnODIzM8ttUE8lA5TD4eBnWZl7vxYcxym2w3Ecv+RTeG/k5eWJfkN2XwmX2/Tr14//bDQa+RkFFsuvTp06AMSCUcsrwdfXF4888ojmeRDuh9q9p3bPZWRk4O7du/D29nYpbgvheUhjqAlh3hhCCjMzrHYf1ahRA4A4xiibGGDGf4PBgDp16iAgIAD+/v4FkkNFdy8zOawwCBVl+fRcg1rt2rVVM3sqwWJTZWRkFLv3AFG2ED6TWvIIkMdctNlsSElJkb3HpDFimYwBwHunqS1NTk9PR3Z2tkgX1hsvTSumo9FoRI0aNVC5cmWEhISgevXq8Pf3V0zi5Ofnh3r16vHbgoKCUKdOHX7gypJMCePiMoQJRYB8uRoREYFWrVohKChIsW/lwaBmMBjQqFEjkUey0JuZURIxY4uL8qCr+fr6lolYyxaLRTMmrXAMqYSa/q4HZ9dP693PZI5SArwGDRrAx8cH4eHhqFChQoH65ul4/p3poQhfpExRKG6D2ty5c1WX3yxduhSHDh0SlekVXHPmzEFsbGyh++eJzJ07V1Z2584dREVFAcj/TYVGq/r168tm/5OSkvh2hMqN3W6H0WiUBdfdtWuX4u/YoUMHUQyhESNGAMj/HaWzoCzorDTdupARI0bAarXqXmowduxYWUZRonRRU5avX7+Ov//+m//OlLEtW7YgMzPTaSprwjNRUrrVYiqygWRaWhrsdruqp1lhUMtIJ0QY27GgFEUbAJSTEvAx1AwPPdc82KDmKlarFTk5OUhKStKduIIgGOwdxeSQ2nO6c+dO0XeTyYRbt25hw4YNonKWxEnaPpBvhEtNTVU1qLEwGsJ9vvvuO13nIVwGJUXLaFWtWjW8/PLLsvLKlSsreuN6eXlh5MiRAPJ1tHHjxmHChAn8NleypY8cObLMv+tHjBjB67bC30HpvJX0ecK9KAuGw+3bt2PTpk2K2ziOw507d2SZgoVkZGQU+F4NDg6Gl5eXqrxSMrx37NgRfn5+/HfpcnUiHzKolQIcx4luZqZISDMsFhaHw6GZAUTI3bt3+YETw5WZgLIeKNIVhLObCQkJopnVli1bymLrKc3SAvleg2ztvNBbxGazKf6uYWFhfABb4KHXh8FgkM06sPtP+HtLFbEGDRogIyND92xDRESE4swFUXqoPcN37twRfZc+vywAfFEpL1lZWcjLy0Nubq5LcR8yMjI8PquTKxTmfHNycpCVlaXqJSQcTKSlpck8o4Xb2e+emZmJ+fPn6w4ToIXSe40dMyQkRHW/orgPi+xe1lzyiYeGNq58xLMBxJ7RQoNaUekxhHM88VpL+6xmUEtLSwPHcaJESffv3xdlExaSlJSk2K4QtWQp2dnZBY4DKBxwStEyqJnNZlGmdIbJZBJ54woNj/Xr1weQr6PVqVMHNWvWRGBgoMsGtfr165d5D7UGDRoonmNpGQOys7N1xyfVeq6l25Teb54oF7TwtFUTDodDZuBnqOl5TN7l5uYiKytLFj8tLy+vUHHeDAYDjEYjL0OkeHt7y5JCNWrUCLVr1+avvZeXF8xms2i8SZBBrVRp2LAhALkiMX/+/CJpPy4uDvPmzZOVq71AT506hfPnzxfoWGX9pewKQoPa8uXLRcqbwWCQzeKrvSB27drFD2SlcdiSk5NlnmNGo1HVXVfJWAZoG9QAYPz48WjXrp1im4T7I3wuu3Xrxr9EpbGOpC93Fji5qJSXRYsW4fDhw/j999+xefNmvtxZ+19//XWBZZInMn/+fJw5c6ZA+65btw5fffUVFi5c6LTu1q1b+fgbSood+10WLVoEAIX2TsvJyVF8r+mZiDEYDIWesCmSe5njAO5BPwwKg1aOExjUPNMIPH78eJfrCN93wt+pqPQYwjlbt24t7S64zPz580XGWDWD2vz583Hv3j3RKogNGzbg6tWrivVv3rwp+s5iMephz549SEtLU9Rn1Tw6WN169eph3Lhx6NGjh+7juYLUk0/KmDFj8Pjjj7tkUCuPMPmlNyFNUbNq1Sr89ddfuupqyVA98nX+/Pllxmu4Zs2aGD58eGl3wyUuXbqEr7/+2qV9WAK4I0eO4JdffsHPP/8s2n769Gn8+OOPBe4T89ZX0ofGjBmDjh07yibipXXbtGmDV155pdhknadCBrVSwmAw8MvjpB5qRUVBPB2MRiMyMzNhsVg8aiagJGDXxRla191oNPJtJCcn4/79+yKlUnjN2UypNLZdamoqAgMDZcsWlDzRGFIlKywsjO+PsG9SQkNDya3XgxH+pjVr1uSX+kpJSkoSGWjZshepDMjMzCzQDD6LA6iUYEMLFkfQWdtCj02r1ao6K+jOpKSkwG63O5W70vNNT08XxVUUyp/79+/z8kPo5Sqszzw6cnJykJGRwc+QsmMBxZdZS69BrSiWfBb6/coJ91dRnTzcoBYeHu5yHa1nLTExsdB9IrThOM7jvXjtdjvvgSb0qmHGMaEOlJKSIvquJBuE773KlSu73B8lXUht0ph55VeoUAF16tQp9vhBar91aGgoKlSoUCZiTBUnTH5p6RUZGRmwWCyid6ISaqtFtHA4HLDZbOA4ThantDCU5fFaXl4ebDYbqlSp4lHnKdU5hF62ehOGScd/hXVeYYnulKhbty78/f1V5R/rl5+fH8LCwkTxKQkyqJUK7KZkN3VxGdTUBI8z9/OVK1di7969HiW4SoKff/5Zl6eGVnIHHx8f3iDx3XffYd68eTh79qxiXWYwkwafjIuLU21fqiRMmjQJQP6MwsiRI9G0aVNMmTIFwcHBmDRpksiIQr932YPF0QO0f9/FixcrGtSkrFq1qkCZhQBluaPnnnM2k3zs2DHRjF1kZCR++ukn1ztYyixYsEBXvdOnT+Obb77hvy9cuFA1c/C8efNw7tw5fj9GXl4e/3usXLkSQH78vLlz52L+/PlOA4S/+uqrGD9+PF544QVR3EZWX02mSZG2P3ToUFkdt1nyKVrGqRBDDXhoUHN4toHDGWPHjuU/S4MnC5Xx//73vx5v7HF31LwNPAWDwYCYmBjeG1bI4sWLAYh1qgULFoh0ZSW92WAwaC4jZyglBVBr02q1KsaIZe9NNUPWxIkTnfZDD4MHD8Yrr7yCzp07q9YJDw9H48aNi+R4ZZlJkybBYDDw10r62y1fvhz79+/H5s2b+XeiEtJ3sR6Yh9Ddu3fx7bffFqj/nvy8F4Rz587hzp07HrfkU4peHY+do8lkkp3v3r17Ffd55plndLVtMpkUJ267du3Kfx40aJDI+0wrIzzxEDKolSJSg5pwNldJaNy+fdul9rUMajabjQ86LWzXZDLB4XB4vJJWUFiWJyX0XBO73a75OwUHB8s8fIRGMGH7anE+pPWESD3RqlatCiB/FrV+/frw8vJC5cqVYTAYULVqVdGxKQ5e2UM6W651/wqVSqmHGsdxOHfunC4PKmcInw+te47FedNKimG32xETEyPyvCvrcktJDjGZrYSSfBEa1JTqstT0am1Wr14d4eHhaNq0qWiWkv1m69ev13Uu0t9facZTSal0lSLx2hAa1IwP5KzwGpaBJZ96CQ0NBaDs5WE2m0UxGovSW9RVHUhtP7vdjrt37xZFl0odrWffU5D2/86dO7q9NNQSrQgzB6uhZlBTinGVl5fHe/YLYRM+ajJGGjO3oAQGBqJGjRqasdr8/PzKbYY9V2B6Mfs9K1WqxG9j9540GZgSWtstFoss4yygHMIgOTlZcyJeilK7ni4DtGBeoJ5mUNPqq8FgcPo+MxqNsgkptYQqeuWMyWSCzWaT6X/Vq1fnPwcGBsqSEwivPRnUlCGDWinAcRxv1AIeCuWjR4/yN6rSg+jqumktYX/lyhU+e5GwXaFBxpMEV1Gxdu1aVQ8cPdfj5s2bml5s/v7+MmWtIDEv2G87ZMgQUbmPjw/eeOMN3e2Qh1rZh90Pzn5fZugVvsTZPrm5ufjtt99kAZ8LglAZ1OrTDz/84LSt69evIyYmRuTFxuRreYHNOKohfMaZ3LBarbLlvwaDgW9HKyW7VL4IB5KuvqOkg1AlA8348eM1vTL00L59e95bt8A4VDzUhBge9L+MJyXw9/dHUFAQ//sJZ8d9fX1Fzy6LCVMUFDR2jHS/2NhYfP/990XRpVJHz8DfnVGS1T/88IPovnHF2BAQEKArVADwMI6xFLWwBkpytn379gCKyGhPlArCe+WHH37gjQeF0YmPHz+uKK+UMk5/9913uHTpkqhM69hLly6VlZVl/Z3pd0URT9WdUHufCT3UpDJHLaGFkhx98sknMXnyZDz11FN8GWuzb9++IuO7VH41b94c06ZN4/vj7+/Ph/4oT/q1K9AboBRhD8q1a9d4bzF2U6sJx/j4eN3tszaysrJEa7cB+QMh9fC4ceOGSwLaEx8wdp5SnJ1LQkKC6iyBs9+HzbQIZ8TYb3727Fnd8amYFx3zFGDoXerAEMZHo6U5ZRPp/aB2f7NBC4uxIJyREt4bBVHc1PbR05ZWHaUZs/JmUGPxwdTO2Ww280qY0EPNarXKvB2k11pJHum9n/QgnUxQGgQHBQUVOoC0l5dX4T03HAKZbxT0U2nJZxn3UDMYDKhSpQr/XXhPSO+p1NRUl+MMFQRXdKOyRFlYTaDUf6Fu5opuwu5FPROVakY3VwbtzJOjPL1zyhrS90tmZqau50pL1+A4jtft4+PjkZWVheTkZNn7Wi3OpDS5Rk5Ojq4JTakc9HTZAIiXVRfn+bjadkZGBh8HT+n9o9WeVqZXJu+UDGpqbSoZ9H19fREWFia6v1mbfn5+onG/9D42m82oWLEi/71z587o2bOnYl0iHzKolQLSgM8XL17kM7uxh0Ltha40M+HsOAcOHMDq1av5coPBIHsgqlWrhqZNm/L7JCQklAlBrEVWVhaWLFkiKtN6QbLyRYsW4fr166J9GM6y9zAlTyjI2H2wfv16xMTEuDTTydpjMwmuIoyxJTxuQdsj3Btvb2/F5zo8PByDBg0C4NygVlAKGkNNq46SnCzrBjXp9VBaFiDEbDbz8dPY9WIGNeGgU3jNWrVqpbs/BQn6zTCZTCJZo8erpNTgvc4MgOGBrJTeZ+XEoAaIPRqF947UoLZp0yZs27at2PtTEN2oLFAWl3wCwJo1a/jPahOYWigZ1J588knRd1ffE927d8crr7wiKmvUqBFeeumlMv3OKeu8+OKLCA4O5r+zpGyFea6EesjSpUtx6NAhrF69WpYg57///a/i/tKxycmTJ3kZp9QvViaVg54uGxjt27d3uyWfe/bswW+//QbAtfcPANUsxcDDMaHwHesM6bixQoUKaNeunewYvr6+Iu9fZ7rem2++iYiICFSuXBn169fHm2++qbj0nSCDWqng5eUFs9kMg8HAW95ZYF+mOAgfALvdjmvXrvHf9bq/M6GtJISkL3+TyYSIiAiRoGf73Lp1C0eOHOGzU169ehVxcXEFUnLcCa2XkrP6HMfJgjHrgV134cBR+HtardYCeWMIZxJcwd/fn/eaE/apoO0R7ovBYBB5Rgoxm838NpPJhMzMTCQmJvLyQOgtIHwOrly5UiAF58qVK6K2tLIBSo1mVquVN2irPcNlfXAjPD9h3Eu1ukx5s9lsuHbtGvLy8jQzOdeqVUtWpiaXTCaTaDDiCr6+viJZ49bLpphBzagS068cxVADxMq+8Hfz9/cX1bNarW79PMbGxnq0LqP17CckJBRITylKmOzJy8tTTKh0//59p/HshPqvXpQMalI5pee+FBqIfXx8ZHEeTSaTYrICwv1hv7+fn59sMkePodqZh5pwm9Az7ebNm7JYzXriMxckPExZgOM4VKxYsUgMakL9Uek4rvZLS2cRtsdWoUlxOBy4du0aHw8YeBh39MqVK/y42xnSeyMgIEAxYUrFihVFY05236ude3BwsGj/4OBgt36flyZurL2WXUaNGoXu3bvj6aefxrBhwwDIY9YIB5HJycn4+eef+e96HzDhcig9WTqkMydsn59++gnbt2/H9evXwXEcfvnlFyxfvlw0CC6PD9ipU6cA6BPC06dPF30XvrylyrySK7A03hGjKGdrmPCV9pXwfN5++23Uq1cPHTp0wJgxY/jypk2bAsg3mrPf32w24/z587h48SJ/L7JZOCmrVq0qUEyLVatWAXh4/65du1a1rvQev379OpYtW6a4jZWVJ3kkldtSOI7jlyNwHIeff/4ZHMfBarWKBtvCNpiRXYjWNX311VdlZY8//jj/ecqUKfzn9957D3Xr1kXNmjXRpUsXvnz69Onu/buxJZ8Gc77xTIlyZFATKtnCz0pJRNz5d12xYoUoiYKnoTXw//nnn0XZfUuDX375BUD+O2b58uWy7YcOHcKePXv470r3z4kTJ0Tfmf4kfJcxhMulpOjRg6UIDWrC+/y1117Dm2++6XR/wjOQvkMLq1s7HA7R/cLifxmNRty/f5/Pvq0Xu92uGRKoMGE13B2m0xWFQe3WrVu8/qh0nIL0S89+mzZtUm3j559/RmZmJp9x3RXatm0LQCybpOEtWrVqxcur1q1b47XXXlPsB1E4yKBWCvj6+sLLywve3t78bC4L9sdgN3dcXJxsmzSuTUJCAr+OW6kNqdIgXPJ54cIFvoytT2f7SR8wb29vl4LDujOZmZmIjY2VletZ8smIjo4GkP/izMzMVJx9ZUgzpgiVRuk1tdvtMqWyOGem2D3I/kv7Sng+gYGBMBqNMJlMCAwM5MuFvzWLpyf0MFFyN5fKhcIEiWX7enl5wWq14uzZs06PJ3wWPHnJZ25uLu+9URikcTbYuR87dgwAcP78ecX4YRaLBV27dsVjjz2m2KYUrZlYqVcSIPZyFS4L9fPzg5eXFwICAkQTC24vdxwqHmrCe43FVnOUjfekFr6+vjCZTPDy8hI9b0rP3u3btxUz0xGFh+M4VRnMcRzOnz9fJMdR0zNd5eTJkyJjgtQzSM8ScibvlDxj2QRlUXm7CvsnbDM0NLTAnrmE+6FkUCtqDzWhR5P0HXv58mVNT1mHw8HvU14MajExMcjJyRFdy8KeD/MCU8LVtpmBVM99ovbbCleSOUOpDtObpF7iwj6ZTCZeXhmNRt5Bg9kiWB+JwkEGtVKGPQRSryT2kK1cuZI3ejGkg9yNGzciMjJS1rbakk/h919//ZUvk2ZQkT5gXl5eHr00QsiFCxewYcMGxW1aL0ghCQkJfPm5c+d4rxsAGDt2rGzZ1Lvvvst/Fipp0t/eYDDwa98Z7HcRxu94/fXXRYGhC8rEiRPx9ttvo3fv3qI+EmUTtYEGM6gNHz7cpfYKY1Bjz5SXlxfS0tKwfv161ToMZ5mItQL0uxPXr1/nvTcKg9lsVpTLW7duBZBvWFPy+rBarahXrx569+6NgQMHioxuSrHMXB2gPvroo6Lv7777Li9fpLP3HgG/5FMlIUE5W/I5cOBAvPvuu3jrrbdknhhS7t27h/379xfJcYtD8fcEeaGGs6VpTE8pLJs3b8ahQ4dc2kepX5s2bRJ5PEu9YfXEUWQ6sMPh4GUK04WYLFSSeVqMGjVKsVz4vmH3OelJZQPhcy/VY/Qu+dTaprTkk91DUplz9uxZTYO1syWfZdGgtnLlSly7dk3koVZYDh48qLqtqD3UWLnD4VAdO7sSp9hgMOD9999HRESEqPzdd9/l76u3334bISEhooRzarz44ot8ogFPvk/cBQ/TaMse7CEQeilVqFABHMfh+vXrfHBwIUoDTOHL4NSpU7h7965q3CNA/vAyD7UrV67wGUGVHjChR4UnK6FqCBNGaC2VEP5eZ86cQU5OjszbQugJBIi9OIQvxsuXL4vqCWPrMdjvK4yBVaVKlSL5DXx9fXkPJiVPE6JsoaSUCTMpSoOKO4M9M6dPn9ZUCqT3alxcHL+v0Pv15MmTouXkQjmUl5cn8rjw5CWfBe2j9Jx9fHxEyprdbpfJLqXf1GKxwGg0wmg0wsfHh4/bAbi+5FMJaRv+/v68fPH19XX5Pit1aMmnCC8vL/j4+MDPz0+XcTQ7O5sPk3Dy5MkCH1foQc/aUyIhIUExNmNSUpIsu7fHGXcFaA38md5QmOtdEE6ePImkpCT8/ffffJma/JDGktJjCGPnZbfbeZnC5Al7jykZ6rSMdWoDUCVjMelJZQNhXFBn3vhqz1BeXh6/ZJzjOL6e1HuJOSywlS1KMufGjRvYtWuX4nEKuuTT01FbaVWcx3KlvpqHmvB+OX/+vEi/EsJkmR5P4goVKsDHxwdVq1YV9cHf35+/NwIDA8FxnC5nC29vb5jNZphMJvdOCOUheK4WUUZgN7FwQOTt7Q2Hw8GvuZYKEulMinTJz8aNG7Fv3z5e2ZRmgZN6orE6RqMRUVFRfJlUSHAcJ1oH7gmDVjW0ZhQMBgMyMjLw+++/i7YJz1e47Hbz5s1ISkoSvSBZ0gk1pEojy7AI5CuG0tkMh8OBLl268ArAjBkzVNsmCC3YfTljxgz+8/jx42E2m0Vlaqgt+fz9999VlQal523Dhg0iDzVmUNu0aZPIE0K4b1paGr+UUXhstXMsqwjPz9vbW+TlmpeXJ5Nd0oQCbdq0gdVqVfWQFipsDGdGByWZNGPGDLz//vuy8meeeQb9+vXTbM/tUEtKILzXypFBTYg0YY8SVqsVGzduBJD/jBd0AMj2y8vL49tTYvfu3Thw4ICs/MiRIzJvOU+WF1oGNVauFr+nuNi0aRMOHDiAvXv3uryvnoGdw+HAjBkzRNnmOI5Dq1atMH78eABimTdjxgy88847aNGihWqb0mQDDE++NwhthOEOpIZdqW6h9Ayx54stI7Tb7Xw9NpYQJm5x5s2/ZcsWVQ8qZ0s+tZZ9ezolNUlakCWfah5qwnfchg0bFGNjC4+5e/du1eO0atUKISEhmDBhAgDgqaeewrPPPiuqI9TPBg8ejD59+ug+j3fffRf16tXTXZ9QhgxqpQwzrAg9nry8vMBxHC+IlQxbQL7wPnbsmEhQHz9+HMDDJAdMoEvjsElnYxITE516wjkcjjKb/VFpySzDbrfjyJEjoushjWN38eJFUYBvFqtKmGlFiFRpFMYPqlmzpqIRVegJoMedlyC0EN5DQs8hg8HgUsw+oSInTa6iRXZ2Nh9s2tvbW2REFibhYM/dlStXRM/pnTt3ZNnfDh8+rLrk88iRI4Vanloa6AkoLjWoKSGV90FBQbyHGqA8SSPFmUKrJJN8fX0Vs4M68xZxS1hcNIPKkk+ADGqAqlFdSkGfRSWPBSYXpN6swndvWRhYKuFwOFSvpdY1Tk5O5rMtF5arV68iKSlJVCadFDx8+LCutvR4qHEcx7+rhBiNRt4zTehFxuSQ1qSAmnzzZO9FQhut3/bSpUuqsiwyMhLx8fH8cmp2r0snFoTOC0ajUZRQzhUD0eHDh2G323H79m3ZWO7YsWOw2+1lcsknQ2pQS0lJQXR0dJHEoJUeR0pSUpJq3DWO45CbmyvylL527Rofpkktm6iQmzdvOq3j5+cHg8HA68Umk0nmgSvU2by8vFzS4X18fGjioAigN0Upo2RQM5vNIiVJzaCWmpqKrVu3ijzUtmzZAuBhkoOIiAjFtdtSRSs1NVX2clE6bsuWLfnvzgIRuzPSc/vzzz/5cula/ZycHGzfvl1UXy3Taq9evQDk/4a9e/fGW2+9pRhvQzqYFArHoUOHokePHqLtwtgLM2fO1Dw3gtBC+twqBax3JVaHUJY4M+wIsdls/JIgoYcaIH4eWEDX3bt3ixSXY8eOyTxqd+zYoTqbuX37dt0Zkt0FqaeZElIPZCXYMn6Gt7c3OI7jFTS2P5MtZrMZzz//fAF6DLRv375A+7k9nGDJp+J27uE2ruwnJRAilAm3bt1yWgcovEFN2J7w/c2wWq0iOaIV/NnT9BchWsHTtQbTZ86c4fVFvcdRu07btm3jjf/sOkv1zosXL+o6jl4PNa3+zZw5U+S95grjxo3jPwvP9//+7/8K1B7hOYSEhPChWiwWi+y9CeTfZ3/++Se2b9/OL9NTMqgxnV3ooSZtRy9MrwGA2NhY0b5bt26FxWIpswY1Jt+Ez+L58+exbt06xSRWhT2WlBMnTvDvFykOhwO3b9/Gtm3b+LI///yTj00u1E/V0IotOnjwYADK3o3S+4kM/6VPoX6BhQsXomXLlqhQoQIqVKiAjh07im4swjnMoCZ8WJiHGpsdka7dZy7BzGjGDGrCeBXMW8rX11eWRdJgMCjGFnEm8GNiYhAfHy8abO/bt0+xbmkTFxeHuLg4OBwO0XVR4q+//uI/JycnK2ZFZf+Tk5MBqBsOKlSowGdTZH9KM67sGrJZVKESyfYTUrFiRb6uUnwjgtCL9N7Suj/1IJRdWoNkpaXmjDt37uDSpUv8d6HMY7JQuiTjzJkzouMKlR72zKakpBS50lVUsD4yGSpF2u+TJ08iPT1dcaLDYDDg9u3bqseS/sZMhggNat7e3ny50WiU7aPXOzkoKEgU67HM4BAkJaAYaiKE8oLF5JRSUIParVu3RJ4ISgY1xurVq3m9SOqhxrwMtAxqN27cEHm9qj2b7kJmZiaOHj2qazB94cIFkReZVjBtV2HtWCwWXj8V6kh//PGH7rYKY1BjeHt7u2QkFdYVetpKvT6Iso2/v7/T2J7MiJaRkSEL2SNN6iZd8ilEOi5zBnueDh06pJjEzt3GYEXF/v37ZQY1pqdoTZCycd+NGzc0M3sKUbqGwth1Qm7cuIH09HTRtrS0NJc977U81Nh7NCgoSDbxzeRUScaYI7QplEGtVq1a+OKLL3DixAkcP34cPXr0wDPPPFNkabrLA0oPKvNQU4MFtWQCmRnU9uzZw9cRClxWjw2I2ABVGrRQacln3bp1+e/79+/H1atXRfVYjAx3E+aRkZGIjIyEzWYTXRcl9GYf4ziOF8xKgtzb2xteXl4wGAxOhSoLBP7222/z3wHw3wFg0qRJos/NmzfX1U+C0CIgIACzZs0CoD6oKqiHmrM4HkrHatq0KW7fvi3yPhMun+Y4Dvfv30dOTo5oP6EHhMVi4ZcUCZd8Xrp0ySUvjNJALc6QND7Upk2bEB8fL6vHlE2pwUso36XyiA0O2XUSZhCbNWuW6L6YNWsWZs2ahVGjRuGDDz5wej7NmzfH66+/7rSex+F48E41SpawCt+bxvJpUAsJCeFlypQpU9CoUSNZHaUseno4fvy4yLClJUuuXr3Kyw5pJlmtiTXWlvRYBYkBVpIkJibi5MmTuvSvbdu28bojoD3BURAMBgMyMzP5CUrhBIjQU6Ny5cr85/DwcFk7TDYJPczYvTVx4kQA8t9+1qxZskG3swEma/Opp54Sve+k+7kyuUR4Nt7e3k5/bxYjzWaz8feqniWf0nadeZWHhISIvrPnSThxJgz/U9ZiqLF+3717VxbGg113rbhkbNx39OhRzcyeSscUIoxdJyQuLg53794VvWPi4uKKNJQFO+cOHTrIshCHh4eLQqMQpU+hDGoDBw5Ev3790KBBAzRs2BD/+te/EBgYqDtWAqEM81DTYvPmzbyhLC4uTiachUon8+QQBrRUEhJS496aNWs0vaGkszHuhpZCpZRJ55NPPgGQL8CFg3vhubFYZ0qC3Gq1wsvLizeqOesbSwQh/L2F8YakXms0A0EUFc6URmfbr1+/znuU6fVQA5TlhDOPy5iYGJw8eVJzRldpZlj4mQ2yjx8/LkpNb7FYRIPmv//+W2a4KwmEsYxu3bqFjz/+WHS+aksOgPxzT09Pl/1mzJsW0F5iDogNatJ2hN62epXFMjkIdTyYRDEKrp1QJnNcufVQAyC6f5QmCvUY1JTuc47jRO1xHId79+7h6NGj/Hchu3bt4j02hN74DocDmzdvVnyPsmVcZrNZUc4cPXoU9+7dk5WXNlrGRaW6wnNXyk6nJWfUyM7Oxr1792QGOrXBrlD2qE0oA8qeYsxjQ9pvk8mEypUri7w4nOlLWvJO2IbHxXokCkxISIjI4Ctl+/btIuMZ0xVOnTqFrKws7Nixg6/Lnre7d+/y34U4M6hJM8lK4zYDD8cs0hhqO3fuFPXD3WFy5+TJk7zeIux3dnY2/zxfvnyZ97RVkzFCY/65c+d0j52cGdROnDiB1atX8+VCvYntryduml6Eq6OUdKqwsDBFb3CidCiyRbd2ux1r1qxBVlYWOnbsWFTNlgs++ugjAOCzdjjzUAPyH2zmJZWbm6sYJw2AaHAoHGQqxd2QPrAWi0VzYOTOAb6dKZpaKbKvXr0qms0WtsGUKzVBzgxqrvD++++jevXqAJRTtLP7gyCKg4J4qJ04cYIPBO6KYV1JZjgzvpw6dcqp17O0XfbssJlNptT+9ddfohhP9+/fF3mk7NmzR2RwK06ESt6RI0f4z0oKmTRpinBfjuNQr149zfh1TK707t0bgHz5klQxLCgffvhh2VzuCTz0UDN5O1/y6Sh/BjXg4btKafCnlLlailJyIKl3AsdxiIuL4z0QpO/yq1evwmaz8QNa5pXlcDj4JChSOI5DZGSkqkFt27Ztit6h7oIeXUzJg0sqr7WSM6khNDQKZZCaPBGWKxnUmGzy8fGRZQ5mxjil8x0yZAi6dOkiKlPSnbT0qY8++og35H344Yea50GUPbp3787HDp08ebJsuzQ5mZCUlBRREiH2TmWhe6T7MTmjZuyR3ndKMlV4LOGzp5Yl3V1hcmfr1q2IjY0FIO53Wlqa6DqxZflqOo90BVFhDGrCJZ9btmzB5cuXRXWl7yYtateuLVtpVLt2bQD5XmhSnPV79OjRaNeuHf+dxooP+e677xAREQFfX1906NCBn4BTYtGiRejSpQsqVaqESpUqoVevXpr11Si0Qe3s2bMIDAyEj48PJk2ahA0bNqBp06aKdS0WC9LT0/k/VzLClWXYQ8MeWi8vL10BXIWKn9pa8vv378uOs3HjRly+fFmmiCrNxGkpE8L9OY5DYmKiqsJaHFy9epXPpqJGURj9WBtCjw8mVBns2hbEoCZUKpUMauSZRhQXFStWRGhoqKxca1Y+ISEBZ8+eFWWIZCm/hc+bNM18QZaX6kUo54QD6ZiYGBiNRsX4G7du3RJ5ogr7WdIIY3woBUJmKF0/juP4JANqsOUjwneMkMDAQFSrVk1U5ufnh+DgYOedF1CmA+PadSz5LKdJCRjs2ZF6VwAP9ZWff/4ZgP53s3TppvQ+V4rPxTzU/v77bz6kAzvesWPHZPVZmyaTCcnJyYpLhJTkglTGlTSF8VArqiWfTJ4aDAaR0VRNFigZ1NiEIvDw3WMwGGQZgtk2YUgAhjSZFCtTqidEKPuEbbC+kYda+UEYe8+Z57z0mRNOzB0+fBgJCQlITk6WyR/GuXPnAKjH5mPv3s2bNwPQNqht3LhRtl0pdizz0nVXpKsLGOnp6aLnli11ZGOyAwcOiAz70rEt81RWkv0pKSm8vBd6GEZHR+PQoUM4d+4c4uLicOnSJb5PaWlpvNOFNDyJFkFBQahVq5aojP3+ZrNZds/5+/srZkkXnpcnJwcsLtauXYvp06dj9uzZOHnyJFq1aoXevXvLMlEz9u7di+HDh2PPnj2IjIxE7dq18fTTT6smWFKj0Npvo0aNEBUVhSNHjmDy5MkYO3asqpHj888/R3BwMP+nZngr71SoUEFX7A6h8iK01AsfqtzcXD7zpBSpgNdrUGNCQ2jQ4zgOZ8+eFc2MFDenT5/G8ePHFbcxwVwUszPSuAha6DGozZ49W7WchCNRkrRv3x7Dhw+XlWsZuZgHF6vjcDhw4MAB/jNDmkwFUH5+1BSGyZMn44UXXtDovTLMRf/MmTO4fv267DliSsv/t3fe4VGUaxu/dzedNEJJSAgJkARCDdJRepcuKpYjRcUCeET0eNRPjehR9FiOWNGDgh4LNsSCYqEpgoD03kNPqOl9d74/nryz78zObEkhlOd3XVxsZmen7M6887z307Zs2XLRlSeIjY31+TPifB0Oh+qhTExM1HQXHjJkCADn96833Lp06YKbbrpJs6xJkyaYPn26z8dz2aLWUDNJ+QSu6JRPGXG9TZs2TV0mxgYRXeDts9lIUJM/Kyam+s94s0zepryecBCYrSMwGuNqg8oKatUZvSILagEBAaaCmmwfiXWGDx+uLhNirN4RIpY1btwY7du3r9KxyjZY69atcccdd2jOQ37NgtrljbgW0tPT1esxPT3dZ8e43Mzkp59+QlZWFkpKStR0aLN7zUy4S0pKQkpKihqk4K7kxbFjx1xEZhH1Je+3vLz8ggY9+Io8RsnjdVlZmea+lMsXAcDSpUs1teWMGvHt27fPcF597NgxtQSQnAmxfv16TeqsLMadOHHCND3UHTfccINLJJoYX/z8/Fxs4YYNG+KRRx5xu03GlVdeeQWTJ0/GpEmT0KpVK8yZMwchISF4//33Ddf/+OOPMWXKFKSlpaFly5aYO3euqR3gjioLagEBAUhKSkLHjh0xa9YstG/fHrNnzzZc99FHH0VOTo76z1N00ZVETEyMWp8rOjra5X0RFioj17uQb2TRpcZms2lax+uNE28ENXcGl15Qy8/PV8/hQqBPBZHRFwStLDNnztRcz2bb8zVCrU6dOi4RIQxzsSALamYpfML4/Pjjj9Vl4v746quv1GWLFy9GcXExfvnlF8P7x11UU2VEZRGiL2pL6CdmIqIlLy/PsP5ETQnZBQUFWLJkidt1PEXr7dixA0uWLFHH3m+//RalpaUu0az6ulMCUWOIO9ZVArmGmtFz8QqvoWaEPEHwVEPNaOwQy61WK9566y0AdG17igzLyspyyYBwF/kpd/oG4FOx56+//trrdWsKRVGwY8cONb119+7dLkKjPFk9deoUVq9eXS2C2o8//giAut6JSbyfn5+6L/2YJtdGs1qtLsXXxXdvNhZGRkYa2sPVhX78Dw4OdhslwlyeeIpQ8yWyXi+oyVFKZs9iq9WqiZDxVHNNjtIX+wR8CwgwQz8mV8dnvvrqKyxatMgl08nouO12u+a+NBIX5eZT+vf37duHNWvWuP0O3ZXM0G/TrGuxu66jZoh5d0hICDcZqAZKS0uxYcMGTSCR1WrFgAEDvC5pUFhYiLKyMrf1FI2o9vwMh8NhelEFBgYiPDxc/XchxZeLnbvvvhsJCQlIT09HcnKyy/v62hB6jNp+i85H4sGgH0zltIw77rjDUFATHkcjkU8vqFVXHR5v0Xtczdbxhqrk2ANOUUB0+vREhw4dMH78+Go5JoapbuT7eOzYsYbrCOFeLgArJsXyZE6OIjWrUWGEXjD3Jp1QtKmXC+jrw+KF4Zmbm3tBn0Hnz5/X1Eozur89jZ+iEYRg06ZN6vNWfD9paWma705O20xNTVX3bRYpy5hgFKGmhwU1lfT0dISGhqrXmSdBTdgTeiFICGpmxb0BuGQ7yNEK3qCPmpMn055sCNH0qTYQx+ZwOLBlyxa1htPWrVs1nTXFunJh78LCwmoR1ETNo0OHDqmv5RR0MeYIxHMDoDFr2rRpmshcMamUo4Xk/8eOHYuOHTtW+bjN0I/LYWFhHCVyBeLv748pU6aYvu9LeQOr1aoZ70SdNrEfs8/4kk54+vRpzb1l9LnKBBgoimIYBVzVz2zfvh1btmwxjCYT2xDobUE5O0vYj/J3ZSS4FRcXuxW89M2o9OOAbOe6q91m1OFadCcWyNeVsFO7dOmCuLg40+O70tGXDMvNzTX8Pc+cOQO73e6iWURHR3ttF/zzn/9EbGysaXafGVUS1B599FH89ttvyMjIwLZt2/Doo49ixYoVuPXWW6uy2Sueyky05MFd9gBaLBZ1wNYPyM2bN1c9b2ZdRMQAbXRMsmfWlxTLTz75xOM63qBPBZHxNULN24ejJ0HN39/fZ1XbDBbUmNpCjAURERGGRhpAofJ69PfbM888o/nbF0EtMDBQsz1vxHqbzaYKanLUllF0bkFBgTp5E52bahJvxiJfjHRhsDocDs1YKMZhd9u6rGud1RRqUwJ3NdRYUDNDf/0vWbJErYHz66+/aqIx9FEVspBsNIbI6VaA990q9Z8TEzF5kitqvgHmtouiKFi9ejWee+45r/ZbFb766iu1XpJcQ23fvn3q37t27XLpSioLaiKVpbLjgBgvzcZNOeVTH4kv/+2uKUFtjVHys6Jp06aXb5MVxiPu5jO+XJ/6uZH8WSHe62vZ+nr95+bmaoIiTpw4AcB5DgcPHvTZ0QC4t1sOHz5sWG/SrEmeEfo6kEYpn/v373dJARXITkpFUbBgwQIsWLAAgLY2rRHbt29Xa83pj1lvM3rTrGrVqlVe1QmWbV55vtigQQPUr1/f436uRPQlwyIiIjBr1qxq38/zzz+PBQsW4Ouvv9ZoKd5QpSfWqVOnMH78eLRo0QL9+/fH+vXr8dNPP2HgwIFV2SxjgKc6DkYRamK5WYRa//79Vc+bWa2IuLg4BAcHuwwu+tBUb6LFBPv27fNqPU9UR4RaSkoKAO8fXmYPF/F5m82mqQdSFVhQY2oLYQDcf//9mgf+ww8/rL7Wp+oAzmLg8t/6940+o2fkyJGoW7euptCu1Wr1WDtH1BKTBTWjWjx6z5a+yUhNoB+LjMYmT2kmMnL3QtmDKxwJ+jRQjkirInYp5VMgX1eKAlgrnqGOK7MpgTv0wvmBAwfUIsF//PGHRqB3N4YY3TfuCna7Q190WGxHtoVE1JWow2NEeXk51q1b59NEsrJs377dJZrCqDmBfgJoZC9VNqNAjJdm46a/vz/8/PyQnp6Oq6++Wl3euXNnTXSZ3u5KT09Xj+liEP1vuukmtGzZsrYPg6kl3IlJ8vWpzyrSP8dFhNrtt98OQGvb+/v7o2HDhhg6dKjmM95E57dr1w4A1awsLi52m9J45swZt2nvZribQ2VkZGgELYEv46D4jsX/RhFqgNPZceutt6rbHzlypCYSrrCwEHv27FEjyI4ePep23zt27FBFN3c16gDPKbcCo3N3J6iJOShAnT5F/VG217ToS4bl5OTg0UcfdVmvfv36sNlsyMrK0izPysryWGbppZdewvPPP4+ff/5Zvbd8oUpPrPfeew8ZGRkoKSnBqVOn8Ouvv7KYVkP4UhjVG0FNVsEbNGiAkJAQ+Pn5udQU0nc8EhgZYtu3b0dxcbHqHagM8+fP93rd6ohQEwZhZVM+hVdJRAJWpwjGghpTW9SrV8/wevbUInzJkiWG95wYk+QuO8Lz37BhQ9PjkI0Tm83mUSAXEWo7duzQ1PDRG30ffvghSkpKDI0ks/vus88+04T9m6EoCsrLy1XBC6BxrboFNVGD1G63ayLSYmNjkZCQ4HZCWp3FyK8Y5JRPs++PI9RMMUrP+Pnnn5GRkQFAO8kQY4iiKC5RZEbXbmWflevWrdP8LSZnfn5+LvtZtGgRAEq7fv311zU1gtasWaOmkn/wwQeVOhbAN/tHRkwgFUXB/v37AbjajOXl5S5No2w2m+E+S0tLXaLxMjMzvf6e/fz8DJ8D+mgvdxFqF7J8iIzVajXsUstcebh7Tso1y/TXqj4lsKSkBMuWLTO8f06ePIlTp065zL3ke8PsWS62FxgYCLvdbnjPyWnhRjVcPY057uZQZnOsyghqYvwX56Q/LnEeVqtV3b6oEy6YM2eO5jNy51Ujdu/erb7WF6Cv7DPFqAmWUV1GozJKgHEnUMa1ZFh4eLhhbcuAgAB07NhR83uKBgPdu3c33f6///1vPPPMM1iyZAk6depUqWOsfRcQY0h6err6D/AsqMkTMX2YopGgNnXqVPX1lClTEBkZiYCAADz44IOaz8p1iGTEAN+uXTuMHj1a3XZBQYFLnR9fOHz4sNfreopQ86WLlbfeUP0EfNiwYeq+uNA3c7kwePBgPPbYYy7L5fvEzGgyWt68eXNERkYCAHr37g0A6NGjB9LT03HVVVe5rC/ua3kSrr9H+/fv7/I5f39/1dMo1xATUSaCzMxMFBcX+9QNcPfu3S6dtIw4f/48fvrpJ3z++efqssOHD5tG2sjjhhirhSfbG4QhLb6fjh07YtCgQW7HRhbUKoEqqLlL+ax4TiscoWaEvph8Tk6OKpiZCWp6jJaNHz/epXuanhEjRrgsy8/PN/RaBwQEmN6vO3bswLlz5zSREXK3TyEQVgZf7B/5mOS/hdBuZNPk5uZqzissLMxwnyUlJabReOJ38uSsNBLE9BMaozFKX0PtQuPn56fpkMxcuYhrXJ+OCXgneLn7jEBExZqlfLrr/C3uHzHncyeoiTrXejyNOZ7SXo3er44INVEOQL+eLKLZbDbNvvLz89XXRnalO4QjoqroxzSjKKqoqCjcc889hp9v2LChpusw4zszZszAf//7X3zwwQfYtWsX7r33XhQUFGDSpEkAyF6Qf5cXXngBTzzxBN5//30kJiYiMzMTmZmZmuvJG1hQu0Sw2WxuOw3JgpqcoiULPWJA8jTQhIWFqcW6rVYrUlNTNWq63W5HdnY2AGeYrH5QvBC4qxOkKApOnjxp2o1FT2W6ODVu3FitL2W1WllQYy5bQkNDERYW5lZQE+K7kTFlsVjUrntGxqnR+oA2klbed2BgoOF2/P391f2Ljj6lpaWmKWFymP8777wDoOpiU3l5uTo+HjlyRF0uajF9++23mv3IDpCEhAQAvk0mz58/b9jx2ExQi4mJ4a51lUF0+bSZpHwCznRQR82n/l1KCHvCKAVHjAurVq1Sl73yyiv46KOPvBbUjNK69Zi9byT8WK1WtbaOnh07drgsMyt2/eWXX1Yqzcobtm7d6lK/SFEUNR3UbByTJwnedpV79913AVAdpn/9618A3KdA2e12t07gpk2bIjU11bBbp/gcR2kwtU2dOnXQuHFjtSGKjL+/v8/dZr2JegoICEBiYqKmI7fRvCo5OVktuyGe554i1MzYtGmT4fLVq1erjU6MqM4INeGAtFgs6nhjtJ7ValXtNjlaTY+nWtZG+5CprMCWmJjosiwwMNA0E4MzkaqfcePG4aWXXsKTTz6JtLQ0bN68GUuWLFF1jCNHjuDkyZPq+m+//TZKS0tx/fXXo1GjRuq/l156yaf9sqB2ieDn56eJHtN3A5EnSHI+v5zyabfb0bx5c0NPrcyMGTMwY8YMAGRsjhgxQtOpqVmzZuprkRYmBrULKagZTSJlzp496zGHHiBj35vig1OmTNFMdO+44w51kGRBjbmcefDBBzFjxgzN9a+/98REyKwDkpiAyZ0mPSF375MnvsOHDzfspuTv76/uR/xfUlJiWh9DnhSKgr1VFdTktKt58+a5bE8Yr2K5nOrRpk0b9bW7Ghp6Qcydc0H/Pd99990u6SWMF9g9pHwqCmCteAY4jO+BKxVhTxhhdG+WlJTgwIEDbieIMu4EtRkzZiA9PV1zf9x1110ej1lfX01gZCuYjS87duxwaQ5QHSiKgr1796qFx+Xlno4pLy9PtRG9ncyJyYcsDsrb1489drvdrT00fvx43HjjjejQoYPh++np6Wjbtq1Xx8YwNUVkZKRptFDdunXViBeAovAFZg4rYSO5szFmzJiBCRMmqGKZWcO466+/XnXAie06HA5ce+21AOBSA8rdPvVp9YK9e/e6rXltlgXkbb0xwDlnlLMIZLFDv55c31ef8ikjMiIE06dP1/xttA9PCCeuO7FfrhkpCAkJwb333uvz/pjKM23aNBw+fBglJSVYu3atJoJ9xYoVmpTijIwMKIri8u+pp57yaZ8sqF0i+Pn5aYwWPz8/TYFu2XiRB3MhqCUlJSE4ONinWmypqamqyi8+Fx0drTEoU1JSYLFY1KgLMejl5ub6cnoAfJ/Iukv51L/32muvYebMmYZeB2+82wAN5GZGasuWLX2qf8QwlyL6QrmyYSHGiHnz5nn8XFpammGEmVyT0Ggb+kL7TZs21azj7+/vMo64a5VudD9X1Skg6niI6FWjTngHDhxQazAZdXXyNBbq3/fkXGCqAbmGmhlqhBoLanrq169vONF0d78Zvffmm2+6LLNarS6TztjYWMTGxqrPZTNngP5eql+/PhRFcZmQCYzqKLoraF2V8WTbtm1YtmyZ4TaNIuUOHTqkimxm3Zn37t3r1laZOXOmGtl//PhxzJw503C9N954Q3M8Mo0bN9Y4Xr3BqMkNw1zMyAX05TElJCTE8Hr28/PTjEmA1oaJjo7WjGOyqCYTHR2tmbfIgpoY50Tkr1HDEgB4/fXX8cUXXxhuX3D48GG30WZmKZ/vv/++y74A4LfffsPmzZs17+nFt19//VXztxANxRgjzlXMifWfF9+ffq7rS9S/XK5DRp+WaoQvdli9evVQp04dw6g25tKDBbVLBL2gZrPZMHr0aDWKwWq1om7dukhPT3dpSuDv749bb70VYWFhPglqN954ozrBFYPUPffcg6CgIDRq1Ajp6elo1aqVZgARA45ZrSF3E8XKCGruUj7lB5PwEstF0QXyRN2MhIQEl3x9QXp6OoYOHcqCGnPZI+6Tv/3tb7BarUhJSVEb0YixxV2dsVGjRqn/N2nSxOX9KVOmaPYjU1hYqBaLFvf9+PHjNesEBga6TG7NPJiAsSe1uuqLidQrIw9vbm6uepxGaVdGxyCnDBjVTjIaC428pUwlMaqhZrFo0z455dOUqVOn4qabbnJZLt+DgwYN0kSlOhwOl86+ZhFqrVu3BkDR+2FhYZg8eTImT56sinhmXfP044WoLxsYGIjo6GhTYc1bvBXUjM7r2LFjhjVp3W1TjL8iZUz+PgHq/umpc6XofKfvJirjrkFLjx493BaANmLatGk+rc8wFwpvUztFOZ3w8HBMnjzZ5X2bzaYZk9LT03H99der799zzz2aOdq0adMQEBDgYsPo15Nfi7FNblQCuI4Z586dU+stupv/uHMWeNP4TewLIFtILoMBuKaHys0eAFFzV0Gdos1AUZY6rtlsNsPIPT8/PzRs2NDlPaNzNLJBAWgaSsmYfRdmHSFvvfVWw+WCadOmISwsDBMmTHC7HnNpwILaJYKfn59mQNAPFrKBGBAQoKaE+vv7q++Vl5dXOi1R3l9ycrImBVQ+LmEci0H8/fff16QJVIegduTIEXz++edwOBzIysrCxx9/bLie0QTTarXivffec9mv0WAbERGhvi4vL/fYdUofLVNVRC0FhrnYsFqtsFqtCAgIgJ+fHwICAkwnaUaRZUaI+gYdOnRwiV5r1KgR2rdvj+TkZMTGxqo1mfToBbWgoCCfI9R++OEHTcFxPZ9++imOHTtm+N769etNPycjt/Q2GqeMxkLZcBXvJyUlAaCxRz9xNtsOU0lEDTWzCDVO+fSI0TNUFtT00ZobN270yuNvtVrViVZAQIBhWqYsSMvvG40B+/btw+LFi9Xueb4i1zbz9h40S289d+6cS8SHu22K4xUp7HqbLzs72yV67eWXXzbcpjelMPQ0a9bM8HP6MiUMc6lgVP9KHpeSk5PRokULjQ0kvy/Efln4Es0GPAU5tGnTRk1Xa9Sokea98PBwtGzZUrMNIYKLe9AsQk3GXfSWuwg1Xxq/ma2n7yZsdGytQ7ejfdZUYPkgVYzUz4n1+5LPKSkpqcpNTlq0aOGxrqP4/YX9qu9qzFzesKB2iSAGA9HO1Z2gZrFYcOeddwLQpn96Iwp52j9Ag0bPnj01+xPoBbWjR4+qocfyciO89eSePn0au3btQnl5OfLz8w3TOPURavJ56CfDZsd0zTXXAKDv3G63e/zuevXq5dXxe0tAQAB7LpiLEiGo+fv7w8/PD926dUPHjh1N1xW4mxzffffdAICRI0e6dN676667MGTIEDRt2hSTJ082NWz0E+C7777b7YTYaDJ96tQpjeClZ+/evaZ1kcyENgCaMVNOifdGUEtOTjYU1MQY1a5dO3Tr1s1wv5wKWk04PNRQE+/J6zIajK51MWGzWCya+q8AsGfPHrfOLrmGqYgQ9/f3NxXUgoKC0L9/f1V8A4zHAHF/eSOoyeckxCsR4QV4b9eYrVdeXu5SC9bhcHgVEX/11Ve7TNhzcnLUSF9Bfn5+tUXr3nbbbYbfP9syzKWK1WpFdHS0aUOlDh06oFOnTkhOTlajleRxS8wN5HtRRLB5mle0bdsWQ4YMQXp6ukvtx/DwcIwbN06zDeFA1N/3lU09dxehZpbyaYRZ52YRUSuLT7ITwGKxoHlIxXiavRWWIqp95ufnZ3hO5eXlLvO/W2+9VTNODxgwwPBYACpjZESvXr1UB6b+mSS2M3bsWABQ59+cxn5lwYLaJYL+BtZHI5h5CmSjq23btqaDhSfcqftGtTrmzp2rvpYNNXeDunz8zz77LL766iu8+OKL2LBhg2a977//HgA8trU1OuYff/zRcL+KoiAkJARpaWnqcvFAEg/JyoqRDHM5ER0drXb8DAgIUEU1+X4TgpfFYkH79u2RkpKCrl27aroF6zETfnyJ/NRPgN2NW+3bt1fX1Ue8rVq1Ci+++KLpZytjnMpim5xKpT/G9u3bq2lmIvIsNDRUHR8TEhLU15GRkVWLjC04AhSai4CMhBDJbDohg1M+vcYofVIIakb2y9GjR912+pTfE89nq9Vq2LBEIMYZMTmSJ4ximcAbQU2OShHHc+jQITz77LMux+gOTw0YPvvsM8267sY2cY7x8fEuE+u8vDxVUJPPzaijmbfjXLdu3UwbDAiqGiHiFnsJjWUMUwOI+ZU8BxCp6GbPX6M6jUbRaFartcrZLfJ27XY7mjZtqt5v7733HmbOnKmmXXo6Vj1mn/vhhx+wefPmKqW0y8gimt4JG+onzfNydqBZs2am6aZNmjRBhw4dXMYb+RzFa1/tOPlZ0axZM5dUYLFdm83mcw1J5tKHBbVLFH0HJLMBUY5Q69atW40IavowZD1GkRWe1isvL8f+/ftRWFiI4uJiH47UuR+9AGY2mVcUBfn5+ejUqRM6d+6sLhefT0lJQffu3VlQYxhQ/Y6oqChNyqdeUBO1RPz8/NC1a1e0bt0aQ4YMcSuomaGvk+YOfcqn0bgl7uNrr71WrTNi1PXSXY0gs3HM3fgm12+UxzT9MY4ePVqNwBk/fjxuuOEGBAQEqMc9ZswYKIqCYcOGISIiwu3349aIzd4BfJMAfJcCOMy90Fc8ikKio73iN9PXUJPhlE+3iHqBcm2/srIyjePPLILBCLGunFZus9m8qh0o6tvI44W+5k1gYKDbSVfDhg01Eye9DaNf5g5P6+3evVuzrpHN179/fwDOaLAWLVoYFucWDgR5n0ap8d6KgYMHD8aQIUPcrlOjgtrSfjSWndtUc/tgrliEeCOewYGBgWoaptnz16i2tNE9YLFYfLJxjJDv8dLSUowfP97l3hW2jkg1NTtWbzl8+LBhTWozPI1v+qg0gdVqRaRftnPFnB247bbbALjWwBUF/nv06OExi8vo8+5QFEWz/m233Ybbb78diYmJmucQQM8gcYzMlQMLapcAsgfUTBAzi1Bz56n1hdDQUNPCi55qADgcDnz00UfYu3dvpWqoeSNkGXXS0z8k9GkO8n47d+7s8t1GR0drWu3q014Z5kpGCGpRUVFq1yk98fHxhkX39VTlfmrZsqU6zunToIyOyahIuZGgZoRRgV+5C567eiNdunRRX4v6RnFxcap32l19Drvdrulu5e1ENykpSbNflZJzwA9tKjZeBBSd8Gp7VyR7XgUWxQMnf6K//SOMUz4VhVM+vUS2GcrKymCxWEyf80JQk+uruasLJOoVecM111xjmsYFeI5QKygo0IwxnmofusNsTBHI35lZhJqYlJoVLK9fvz7Cw8O9FrfcCQECuRO8UR1HQY2mnp9ZTf8fdrUDGaaqiNRGb67h1NRUpKWlqes2atQI4eHh1TYXM9qOn5+fWtdaRNnqxyJhm3h774tu5WaYzTnlsev3339Xu3bOmjVL/U7Wrl3r8jmzCFcLgEj/bOeCHGd34wYNGmiCIK6++mq10YC+dqR83nJdcYG/vz9iYmJczklEVSuKYpj+mpaWpn73XF7jyoYFtUuAcePGqa/1NUY8IQtCVSEwMBBjxowxfM+T4GW325GRkYHs7Gy3xqX+PTGw6Q1ao1BafR01eVAUxXCNUlP79+8PRVHQs2dPxMXF0UMTDgAKoqKiNF5XebCUfxOGuRIRNdSio6PRrFkz1UgZPHiweq+0bt3aK0GtKvfTuHHj1O6BenFfNqratGmjHrf8P2DcadMdeqNLGFpyFIkeUe9MHkdlA3Do0KGmny0vL1fPTTgGvDHemjVrZvwMyNV1scpzrUPJVLBxhvbvADeFhjnl0ytkm8GTaCOe/9ddd526TC+oyfeC3DDJE/3791cFISM8Rah5U1u1KkW75WVyuqw+Qk0W2+X/9a/Hjx/vU9dSsX+z9KWePXvivvvuA0C/3w033OD1tqsN+XsLiLzw+2cue0SEmjfP3JYtW2oEtZEjR6JOnTqGHY4rg9F2rFYrbrzxRqSnp6uBAfrxRESgeiv6uKsjK/D0neTk5GjqSQqEQ1FQr149TTdnTYRa2Vn4WyUhK2en+jIqKkpjN3Xv3l1t5CbXydRv0yhCbeDAgYY1d+WoZfGevK327durjgQW1K5sWFC7BDGLFKutjm6eBhFvUz717xlFg7z88suGBcHNxDjAabyLQrlyocj4+HhNeoi9OBt/T5yNcY0WGBr41d3Jk2EuVSwWiyYiTNwvcvrVhUYvqMkTTHFMVqsV7dq109zfvkSoWSwW5OTkqJ5XAJrGK2aI/ckTcG+9xSJCrX379rBYLOjfv79L/Q6f0As+Z9ZUfluXM4oCWHS/kSyoWSwmNdQ4Qs0d8nUvJiny+CHjcDjQpUsXTZ1DucB1p06dVEHczDYS9O7d2+UZ7k4wE+L1hAkTDLftcDg0dVyN7JszZ85g5cqV6t9G0WdGx6EoiqZ+rDxGHT16VP2+0tLSXCLT5DFGLGvQoAFCQkLUSJB27dp5THkSHdrNRENfHbw1QskZ52u/cPP1GKaSiGgs0RSud+/eXn/WW9uiujFL+bRaraZjEEDjk7dldoSg9t133+H33393eX/Dhg0u4pmiKNi8ebPLumaRvpYiXW3EnB0aEd1isRiOzYGBgRrnilivXbt2SEhIgL+/vybiTNhT+ig0MfYpisJ10Ri3sKB2CWIWKXYx8NBDD6mvjQSxqkSoiVpn7tKqBHJHU2FQCkFN9vAkJCSgR48e6t/Bp75BpH8OWoYadxeraq0DhrlckDvrib8BraB2oUV+fZi/OA45KsNqtWLMmDGa+9vbCDXRjr24uFhT28ldJyxAW+xcnpyapaFrKDyOZqXfIMhWgtGjRwOgaDfR4bBSKLrjtftep/KKoDgLUHTPrDomQqaicA01L5EnT2VlZbDZbOp9ob+HHQ4Hhg4dqrlH5Qi1YcOGqWncnmyjbt26udQQMhKVhJNNTIYTExPVupCClJQU2O12t42RAGjqwPrS5Vxv58jOgqKiInVMGTVqlDqWuRPUxowZA5vNpk4+x4wZ4/HYxXdjJKh16NChaqJ+dVF03PlaP64xTDUgBDVx73Tv3t2rzwAXWFA7+Qtw7FsA7gU1Pfp1PTVIAaBxUJ4+fVoV3z1hJuKL7TVv3lwdL1NSUmAtquhwHNUJsNiAshygotOnwGzcF/aS2P6YMWMwZswYREdHo2/fvigpKUF6ejrS09PVzvJ2u13zHcljX+PGjV3qlzOMgAW1y4Bu3bqhXbt26NOnT20fimYgevrppwE4J5s//vgjDh8+bPpZs5x/h8OBvXv34p133gHganDLzJw5E+Xl5RpBTawvBDVhfAtvk4yl1PlQsJhMiow+xzBXGklJSRpBqLYFtU6dOhmK4F26dEFAQID6ntE46cnozcnJAQDMnz8fdrsdZWVlqoEKAG+99Rbee+8908+L76FTp07quNSzZ0+1flPPnj3NU7F+7oE0x4foYFvk9hh9Qh+hpnCKoiEiFdYWAiTeBnT7gCLUzK5rjlDzCjFWdO7cWRXUxDJ9lKmY3In7VwjJsbGxPkWKmNGhQwcMHjwYgwcPVpcNGDAAAJWLGDRoEACqPyb2Fx4ejpiYGDgcDo+1kUpKSrB582bMnz/frfA+e/Zsl8/JWK1WLFu2DAAJarIdJL4j8d3J46DccVmPPrJWP9n++eefAbgKauHh4T6N7TVqM8kRauXmDSwqhV5MZ65IRA01wLdrWa7vVeMUnwKWDwJ+GwWc3wpFUVxEeMA7Qc3o3hbzOUAbZWu1WlXBcdWqVW4P0agOWZ8+fdCrVy91fOrevbsqXHZuGYkGu6fQimEpQFhFROyRL1y2bfS7+Pn5GdeQBdXblW04QXl5ORo3boyBAwciLS1N/b7clQZgGIAFtcuCwYMHIyEhwWPb8guB3mjz8/NDaWmpavCJrjCKopgKaEb1icrKytScfovFgl69ernsW46Ik70M4qFitVphtVpVYW3YsGEG25AMqLIcw3M0+hzDXGm0adPGsNW5Ubv4C8GwYcMM9zd06FDV6AOgqdUhEIKaWWqTMLzEJLe8vBzFxcWa/Z08edLws/pjFPvo16+fOhb169fPPFqtkFIeoq17PW7fa/RdPX2p+VVLpQVqBdGsIaoj0ONDoJmHCGWuoeYVYqy49tpr1Y7cYpkc9dqzZ0+XiIZ27dpBURRMnjwZaWlpVT6WlJQUdOvWDd26dXN5LywsTI1GqVOnDvr06QOLxaIK4IqiICEhwe32S0tLUVxcjKysLJ86luvPW6SaA8C5c+ec39PpNUgN3gjAaevIk1YReWGEPgpOdBcGtB3i9eNiZGSk180WgBq2meR7rdx9xJ1PrL2TmpEUn/G8LnNZk5ycrNZA9eVavvbaa2vqkFzJ3uZ8XXIaiqJoGq2JMcFdOQ55DuWpbId4XzhQFUXB8eMULWrWPE8EO8j07t1bk7LZvHlztUlVUs4bzhVDmwKNKuqlbZwOHHhfsx2j38VqtZrWpjULzCgvL0dcXBx69Oihif71tc4uc+XBghpTLYgUCf0g7O/vjx07dqgDtfCIrly5El9++aW63vHjx/Hmm28CcA03XrNmjWZdT8WL7Xa7WkS0Tp06mol+QECA266k4cHS8Zdmm67HMIwWce83a9ZMLbR/wVM+iw9jWsJrSAvfqBrA8rHpEU1HhKBmZmTpP19eXo6DBw9qulV524Jd34nULce/V18GRZh3AfUZfUSatwJQyVng22bA+mnVdywXM8UVhZmDTNJrXWqoccqnN+if8XLKp5xSY7PZNOsKO+NCjCtybVUjZDukUaNGpusJMd7hcBhGRJhhFM22detWAEB2drZzrPqlB0bW/xwpdc+o4lloaCj69eunfq5fv36GEbBDhgzR1KYzSnWqX78+OnXqpNneNddc45ICW2vIzoG9b1bfdg+8R4L6gXerb5uOcookYi4pGjdufGGjzSqD3KnbXoLu3bpgSOQC3NJW64gzmj9FRESgqKhIjULzptmKvD0hqLlrygRQMwIxrlmtVjRv3lzzvhhz4+LiyDbL3up8s9lEoN1MIIaih7H7Za+Ozwx1HzJZyzE69FWEKc7vMjAwUBPAcdVVVxk+Gzw9L5jLHxbUmGpBpEjoO0/5+/vj9OnT6jLhYc3Ly0N2djbsdjsURcGZM04voKeJqVEkh8Vi0Riu5eXlcDgcGDBggMb4DgoKcut5CbRKHmR7kdvjYBjGiRwaHxERAZvNVvMTX8UBrL4N2DADUBTUPfcl6gWcw6job9G/f391NSGw6xEdMIX3US92mUWUiMgOb5oRAFoBQN99yvxDDmDlCPXPwJBI7z7nDXoBzVtB7cwaoCAD2PcmUOraHOayQ0x+g6K1y42ua0XhlE8vMUo1FOOHEKeSkpJgtVo19+2AAQMuWMMTYdMYIaLqBPJYo0dEtdrtdk0apyyYyeODGFv0gpr8d2FhoYv4P67+HNSpsI2CgoLQs2dP9b2ePXuq0bAyXbt21aS7a7rrVfwe7du3R1xcnGZ7ycnJHiPzLhiyc6A8r3qiQ+X7u7oi1OzFwAJ/YGE0sGF69WyTqR4uh6jrkz87XzuKkZT7DsJOL0Ry8ScAHG4DCQIDAzU10IqLiw2DI2TkjpelpaWaeZs7u0+MYw899BD+9re/ad4TY254eDi6dkoDCo/RG9edBsKSAP8w4JrPqZZazk4g/6DpfjxRr1491w7oS/uhccABdD87SU339vPzQ9++fdVVEhMTDZuxuHteMFcGLKgx1YrFYlEHn5CQEISGhmqMyIMHnQOgoij48MMPsXbtWhQWFqrLt2zZYrr94OBgjadUYLPZ8OKLLwKgWgEFBQVq2PL27dvV/Qmjsm+fnsCaicDuV7UbksOm7doaJgzDmKM3wC7IxDdvP5DxEbDnP0DuHoRFxTnfkwQfUQNFNowE/fr1U41NvdH4wgsvGO5W1CNZv369z4ccGhrqnfGlF/StrhPiSqNP+fS2hpr8ufwM6fOXaa0hNUIt2v16Ak759MigQYPQtm1b3HDDDeqyLl26qLXKBIqiIDU11SWVR0RD1BZjxoxBUlISEhISMHLkSADmE8jmzZurjj673Y5PP/1Ufe/ZZ59VX8uT0eeeew6A69hy4MABACQ4Ckck4NyvFeXAuQ2G9pG3WK1WdYyUGz8IqrLtGkM/lhV5Tr33iEm5jypxdp3z9Z7Z5usxF5ZzG4FFccA2886XFz0OO9lBAnsxcHKJ+ufIXs016dx67Ha7pgbsO++845IOLkeyAs7yPYWFhThx4oRG8BdOEKP6ZWI9jxFw4tlr9QcC6zmXB9QFGlRkH8giYnVz9Kua27Y77CXAqVWu41ptcLnadTUEC2pMtSLXN5PTLfWUlZXBbrfj3LlzKCws1Hih3XWeio+PN/SMypP3/Px8xMfHo6wgS7O8rKwMwcHBAIBerYKAQx8AGx/QhuDLgpqjlgU1xQGsGges/tvl4UFjriguyMS3xBn9iqITCAqRjMZdr2iOxeFwGNZe7Nmzp2rcyRFq7gw+Uc/RU3dPkfoqY7VavUsPKNcJarZA4/UqQ2VTPuVuoCLFpPAE8G0S8PvY6jm2iwlPghqnfPpM9+7dER0djVatWqnLUlJSXOruKIqC+vXra9YDal9Qa9u2LerWrYuwsDCPdWt79eqlcSjqbRvh/BOTVzmKRIwxekStt4KCAvhZdONP/gFNJJk3yN+lxWJRU9uETSbbZkZ15mod/diVs7Pq25QbHez5D1CUWfVt5h9yvg6OM1+P8Y7qmuyvvZNE2G1PVc/2agNZrAXoOS09gzooH7kV1JYuXepxF+6awQFOJ2OLFi3UMc8ocld0R3cXMQdA++zVO2cbVIxx5/7ycNQ+YpOynwwaH1wQ1k8Bfu0JbH2ydvYvOLMW+CIc2PN67R7HJQQLaky1IgtoISEhpumb27Ztw6lTp1QDUzbahCfWCPm94OBgjBgxwmWdDz74AC39/8CDCc8h9ugT6vKysjKnQSh7MYsrJuX5h7SeydoQ1BQFWHcvpbBteQw48jmQ8bFWOLjccNiB7B0Xv2ioKEDmUhIQLiXspUDh8QuyK7kmxdixYz12wKsyJVKr9tJzWiFjx79UUapv377o0aOH6WbEuCWLaPJEUz+BF+OaPv1cX5Pj1ltvNfy8V+gj1BTvarR5RWVTPouk60gUAN/3JlBwCDi68OK/h32l0hFqLKh5y6hRowy77Jp18KxtQc0d+mg60ZTJjEWLFuGll15S15HHH1EeQ49cq9Lfott2Wa7Pxzx48GBVtJSbtxhFqF2odFufUHSiouwUrSwlujTP6hBbyqQ0Z4f3zSkYA9beBXwWDOyvYn27o18D5ze5X0dRgO3/Ag5/VrV91ST6Zhz2Yq3z6/xmdOvWTWOf+FpkXwhqZmPvsWOUnpmbmwubzYahQ4ca1osV6f5mwRYqYl4WaFC/NLzCrqxCyqch8lgSVYOdid1xsKLZws5Zvn0uaznwY0fgxBLX98rygB87ABsf8n57K66lrskb/u7bcVzBsKDGVCuywRUcHGwoqOmL4yqKohHUsrKyDCM79Pj7++Oqq64yNPIaKdT1qn7OtxBpEWVlZU4veLHk/S2vMHRO/6HdSG2kfBYcAvbPIa/orhedy6sjjeFiZdM/gB/aAHteq7l9KAoJS96Ssxso0kUIZP4CLBsALE51v5+Ljc2PAIsak+erKpz4kYrR280nA3JNihYtWqBu3bpV26cn5InPvrddhaEKASg+Ph5xce6jAlJSUlBeXo4edVfhqvAN6vLi4mJNSrpMQUGBpji3OP9Ro0YBoPGwdevWXp+OBrtun9U5Huknod4Iaid/BjZJBpkw4kuliX95QdWP7WLCrCmBxxpqnPLpLWlpaYaTK3c2wMUmqInj6dRJOwnz8/NzWxNWOBRFdIcsqJlF6gt7JyIiAgFW3XVW5l1NR5nExERER5NgLNeyM4pQ8zgJrgqZy4DfrvPdYeXiHKgGMVsvqBVkVH2bsoPWzTOU8YCjDDjwX/qd971dtW2t1tbwwvEfXNc5twHY+gTwx03Aururtr+aQn8P2Iu111u9rkhOTkbXrl3VscpdxJoRQhzzVOM6NzcXTZs2NUz39InSCmepnO4pqFORpVRwuGr7kLGXascOX2poO8prxvb3ZZtrxgPnNwIrDLqa7v4PcH6zayOH0mxg9Xjg1O/a5Ue+JAc14xMsqDHVhqgnAlCdkdDQUMPW70apVPJ65eXlmlbvY8aMUV8bhR3bbDYXQ8/f4TQsr+5OKQyaAV4W1IQRWqSL4rnQEWol54Bvmxu/V3SJRUX5wp7/0P8bp7tfL2sFsLRf5VI6lvYFvkv2rnPr2fUkmi3pSNFzAuH5MYsCyNkFfB0DbH3K9+OrScT3u+9t4JwHb6wZW58kj9W+N6n72cWCPPHJWuY6mfIhVScgIAAhyikMrP8rRkR/h4b+JGK/8MILmD9/vsv6zZo1AwDDYt8i4sZqtaKsrMxzeoMReoOuOsejcp1YV3gEKDNPtQcA/HWf9u9DH1ZsS/pc6VlcNiiKeVMCM2wV14JeDGWcFB4DdjznVnyVbQkNigI/FBk2GKlN4uPjceONN6p/C2HN3X0vR6+JNCgj2+i6664DANUmatyYuv1GRETg2kG6KL5y3wU1AGjXrh2uvfZaDBkyRBXsmjZtCkArqFksFvPfxleOfw8sG+QU5Jf1B459Day/x7ftuDgHfBTUcnYCO1/QXo96YbJ+NXTw00zWWVCrNAfed7728y3KSoOiuI7TK4e5PgePLXK+3v8u8IkFOPlL5fdbE7iUcCjRXmOS7SDuZ6+bI1UgxrJ9+/a5XW/06NFo166d+rc8LgqMivq7IGy7wPqu74nncXE1Zu64RPl5KaiVF9G87buUSjk0XAiOdb72xeZzl4Vy/Dvj5X/9Hcj4H/CrVAqlvBBY5axvCj/X6HEAQO5e4PhirrMmwYIaU2106NBBNcbatWsHq9XqkrZgs9lcil0CcDGQ5VDhNm3aqK9lQU3sy2azuRqu0kDUukUiAGr/DoAm2dufdq4rBBL95Fvf3txhB/64FdjxvMvxe836acCvfYyjTbLc1DG4EILambWeO1oVHqu9KKylfSmseeMM3z5XXgicWkmiwW+jPa+fs4v+LzpOnlBBplQA1ai+QsbHdM1sn1n178hRRhGTvkTVGaGftOa5N4aMj8VOE2DBxSTu6iMJ9JMpH1Kl/fz8EKQ4vXJNQo65XV+kkAYGutY2ExNji8WCvLw8l4K+Lux4jgz1RfHOcUdfQ626ItRKzmkjzQCqwfJFGHnjzaibpv371G+uk5JN//C8/0ul2Ut5vvPcvK2hJiZ4F1ukXnkBsHu26zPtQuIoo2LLywYBW/7PbadDw9pkR74CfmyPqw4MQHTAhUlh95agoCCkpjojl8WE0iyiKzs7G1arVRXURIc9I0Gtbdu26v8BAQFqmrmfnx9SmumibvUTOnsp8OcdwJ433B5/ZGQkOnfujMaNG6t2lag3q0/59FQ3zmtWjqCo7690E+azPjZ60Rfv9kVQc5QBi1tTFLf8TNcLXlZXR67x9uzA/rlAwVHX9+RxT7FfHEXHa4vcvcCJn8zfP7seWNKZJux6ZIHLF2HSYae0TSE8mH02f7/z9anfgR3Puq6zYiiQu8f7fftK8WnfbEhPEWrSPSEiX+vUqQM/Sxk6hG9ElL93nWxLSkpw5MgR0/fr1auHpKQkTWZCamqqGrkvRHqX7pqGO3MToSaex+V5rnZSZdELat5ut+AQzS3y9wM5Ozyv7yin68rIMX/mT6197YtAZyZ8AeYOvvMG9p7+ewiIdF3n5C/A9y2AlcOpDA4DgAU1pgZp3bq1S9Fum82G3FzXgcThcKBhQCaG1P8RIbYCQ+EMcApqHTt2xPXXXw+A6hTJqRZxgccQF+g0aBrUrYObb77ZubOTuhzzVTeQ4akfSNbfq/377Frg8CfAlkeBgxXRGYriWaEvy69o8XyIInxOrTSeuPrpJt3ydmu6blfGp8DP3YCFDYCd/zZe58iXNOHf8ljNHYeZESE/3EQtqTNrjVNhT6+mgvRiW4WScetNiLjsmdr/jvO1/LBc5ep100Q46oUeX9n6JPDLNcDWx71b/+giMkD3z9Uuz9Y94H0JYxcUZ2nrd1kqEW0lU5rtGiFVWfTfs95j6YPB3atXL/Tu0lL9u46/+8+Kya++jpr8nsViwfDhwz0XCheCZeExZxFYvRFUHXV3youAhZI4lHib9v1ND5t/1qiQdnm+9t7U/x72Em1No8ylwOd1aja9u7oQDha/UMBfZ6yapXwKo9ZRWnNpn8VnKD3OF1YMowjgDQ/UyCF5xaZ/UrHl3AqHxYG53nu3C48Bq64HsrfBAjtGNDTxuF8E3HLLLYiJicENN9xg2thk9uzZKC0tVQW1H3/8EYB5OtWNN94Ih8OhSckE4Crc6v8+u5Zq8my4DygwnwjLCHvLbrfD399fbVJA2y+q/ogEfYRZcaZvE8nKNlgBqIyBQJ7I6sdab0W6g/OAdZOBb5oYCH06R0LeflyR2EuBX64GVgwxL/y+900qOL9yuKuwIdt8vtgRB96ltM1FjYEtj5s7PcS1l3eA1gco8vimMiDtecAaSPbQdgOhrTrY+QKwsCGw5jZthoQ7XAS1Qu0yScwVzU6io6PRJ2o5RkZ/izExX7tsMjAwUJ03iY7M69atc5tubxY53KNHD1x33XUYMmQIbrrpJk0GkinuItT8w53lFUqqyUnk4ozw0laWP+dNxNy+tyki7PcbXN878qX2b28jju3F2nXl3+jQR9qsHvmakq8RIWC6ZC8c09YqBoDlUkdufUOMKxgW1JgaIzY2VvN3QECAYXpUQUEB8vPzcW/CHHStuxb96y3VRHTIgpowJsPDw9XUh/j4eDRo0EBdp109rWjihxJtBzEXY00Bjn/jOapAzin/cwKwNR341Ap8HuYaSVSaTcty9wLfJpIX9NtmzveNBBd34b1lOVQY9eceroNbdSDStwBg8z+N19lwP/2/swoRekbInXXyDxqLZLm7na/DWgCZv5IA+JOBp+uXq4FNDwKHF9Df8velT+s1QhYyzm+WJtZuBE9A+yAqdB/d5BHxHe96EciVosryM4DzW7TrOsqA38eQAbpuMgmNAr3HrDKCmv5ciqvQ8aw0G1gYA/zcvfLbkNHfR6dWaP8WgpriAFbf5lYwqlu3LurZnOca4ud+YiYmy/qakADQYPc9+Hviq7CU5yE2NtZwHQ3y2FNYMfHV/1a+RnaVFwDr7nGmyBQer4gqq5jo9fkR6PYekDLN+ZmyHNftABRRJFKHZUpOa49Tb4xt/xfwQzsaKwGaJCh251hyMaPWT/PC+BfIXuKailL7uSulx7mL8NBzaiX9f/gT83VOr3ZG51YnGx+k6Euj6+fgPO+2oUvVjw066VuB5QtIcnIy/P390apVK434ZVTrVe4ACjhrqelJTU2Fw+GAzWbTinT6a0wvBJWed772VHy9ArmGWuPGjZ1jV+4+EiOWDzH/cGXwj3SN9vki3AcxoQoRavJ3Io9dehvL29QrOV1VP9HUH9ep5d5t83Jj9c3O5/ZRVyEHAFCW7Xy9+RHte7Lj25fU+kMfOV/veBawm4zP5fkkjPzczSmyDtkIWP2AVv8EBlSMpUc+8yz8Osp8zzIQ55vxMXDQy/IaekGtVPcc1117devWhdVqRVwQ2cONg1ztYn9/f6SkpCDUlodWyfEAKLLWXYdyM4dAeHg42rZti4YNG6JFixbeNUQQNn+AQYSaxeKsa1qZqGuj362yKZ9ypNn5jZ7XP1Dh9M78Gdj3jvY9fdSat01m9CmdspNijc5pKs7LUa51sojITSO7RdhvRnjr9L8CYEGNqVFuu41u5p49e+L2229X0whkNm3ahL07nMZH/YDTaNWqlUtUR8OGBt1eKmjatKn6vs2ie7i4eHENagXtfxc4/Cm9bv1/9L9NJ/4d0EX/iLRReyGQvdW5vOQcRXL92I68TUYC2F/3uUbPuIumKT5FhVHPrAFO+jCR8ha9wWhkzAZEud9G7l5KafWlgYLi0E4CvkuiWgT6BhErhztf2wudA3zhUXOP+baKdeSHks31+nNBf72UnKF96L1FLiHi0t+FBikflUVcd4oCfNsU+DGNrofcPTQJPvmzdn25mYVsmAKugkfOLtfmCwDVq1s1jsTEIp2gVpWOoce+oWste6vTqFEc2omfjOKgNDGzqAOX4tG6CERxbeXsBDI+ou8m/5Dp4VmPODt5tQzahIiICHUi3DxkP+6MfxcNAuj7slqtuP322106WQVaixB85kfU9c8Gvmvh/r5WFIMJYcW5VrWG2qEPKcJy7R3A5sdoMryiYjIcPxaIHUKpTJ1eB0ZUiLbZW10jAs6s1ab8XvWK8/WB97THqRdf98ym/8VYafEydaq6MZuclxcZp+7Yi4HiinEs2I2gpk/5tAU4IziNnjPecG4DNRAx8naX5Ts7m62oRmGj4DA5Iha3IvGrMnUqjcjdC+x+RbssqqPz9ZbHvBNOjESS3S97L7rUErKgZtTJVER7CNtFH9H/t785i6aLCDWNMKd/VunHGlkg3zDdfJw1wG63a8W738eQUzGzmutHlWW7FsUWy73BJULN4FrJ2Q1secK1hmruXml/0vN96xPa9dw5Mxxl5LQoPAYEN3IuXzOh4n07pYDqfxtPtkh5AdU4utD1unL3ULT73jdrZvtHFzpfmzmHZRvy7J/a9+Rx1ZcItTOrtX8vqYi8DKwH3KIADSvqSJXlAScWk23hFwqMPg5ESI2o6nUBwlLoOjNKSRUoCjkOv2/p/XHqxzNva97q7wG5VAlAx6oowPHF6BC+EQEB/nA4HCiwO4WtEBt9rwMGDABQMfYUn8aDzV4Gvm8Jq9WKvLw8hIaGYtKkSbjppps0u7DZbB4bFvjE+c30f9RVxu8HVlJQ2/M68EWEq5irt++9EdSKTwF/jHP+vfdNz6m68pi9/h7tb663GbyN1NU/r93ZnOK8Tq/S2pTiuIxE6tOrtH+LphDe4igju8Xb6OajXwNLOgGLEsgBv2ygb/urJVhQY2oUUbi7Q4cOiI6ORnBwMIKtBWhRZzcscA4kkX7Z6uuskhgEBga6hAWLbRkREhKiFtm0KjrjJ2cHhZaLgc5oopP5q7QxinxzaT3tbpDKklJw8vbRPnL3OFsgxw7Xrl94hDoGyYOvu+1n/M/5Wo4mqw4cdhJQZOQoNUWh3H5NIwed5+T8Fsqp3/cmderyltJsg0ivfEp3PPMnHVvOTm06RtZyrXFk9uDL20dedU1NglxqLf1De3ND1WiSkvGp63ou3iTp4VfVCDW5boQ4Htn7nbOTDLVfrnYVH49+5fQ8uZyL9F0VHKEJ9NcxwNrJ2vWW9gWOfA583Qj4fSwtEyH2JxYDO19EpZDTrTc/Qg/aT23Al1HGtXN+G01pYmZpxp5Sa8U9JQuc8r0qc24jLPnOaMBQWw5aJYQgJSUFVpTjb3EfIS7oBEZGf6uuEx8f71InqXNraVJVnOkqKAiytwFf1QNW6op8i3MSRrha6N7HlM8cKapT34K9vi5CMLQ5TQYdZa6RFfoJdJB0focXaO/FouNa73iUVG+pLM95DQHVV/vEE/vnAgv8qKGJPu1t5Qi6jw47hVRsfBD4LNiZ1u1LhBrgjFKrbITamomUFvK7wTiq/x0rg5E4cH6r9u/FbjrTFp2sGHt6uT9HRxk9E2QGrweG/AXcVEoOmuJTwKH5XhyzybXvrejijpKzNM4cXVT1bemQxwZ3dRTlIt4yzZs7mxQJQU2DyyRQ9z3Jz/WCDBKVPCAEO83+ik9po52ro0aozLrJruvoRQiH3fja9RCNg8OfU5OhHf9yjXbKlSai5Xl0XvJYYLZNmS2PkdPi9+u1k/v8/SSKL2pMKaD73tJ+ztP4sPVJYO/rlF7la105mfxDJB4ZnZcR6+6haPe/plVvvVx7MbD7Ve0yOfNARraf9LWNNYJaxXeoOIA/bgaWDdZeI44yYPlQYP1Up10vEPVV61TMK0QGQlm20ymZ+jAQos20gcUCNKFyM9g5y7wW3upbyTlScMg8Le7Ae3Tc4pj1YkaxiXN6z2vAt8kUcS7O0x052ymbZuVwjIz+FreEPgFFUeCQHFwt65BjKSYmBvXq1aPOv2cqxMyiE7ChDHl5ebBYLGjSpAlatKCxfWiDxRgZvQitKqJoqw3x29Yx6fZc2Qi1DX8HoLg2P9E3o/BGUNv9qtZJUXKa5ifu0M8/5Tq/egHNW0FNXxs5ZyfZPUKUlDm9isYEfXppyWmau/1k0J1V32xKP+a6Y98cYEEANW7Q18vU4yinjqS/X0f3TuERmnfK8/OLGBbUmAuCMMwGDhyIv8V9hJtiF6BjhDM8to6f07jwt5LxovfWXnPNNdpaaCbY9N6aNeNpciQGOk+GTEgTqpMAOKMBFMVZC0gM5DL5B5yvjbbfaxFNIm4qA7pWhHEf+hA4JAllwhg2KsIpc/InSv2rLgoPA9AZTrtfpn0oCrUW/7m7duCXJ+uKovVm6j2K7tB7PoJinAVHf+5OE2H95E7fwl71rJRSaprM9ylk7Mqc30iROGYijd6QP/4dsEZqry5+H1lQc9iB05KXvfAocPADYyHOCNlLZS+lKEf1ePIoimz/u85lcvF3o0m2EMhcBDU5nVVKHT0w13MH1MRbna+Nvrv8gyQGGLWeB8g4kEPTMz7WPvD19VR2vuhcf9dLxtsU4lO9CiMgOI4M44iKa0bcU7KQIlKB9eiFSQDh9oOwWCyY2Hi+uqxx0HHYLOXqxLNRo0aaNIj+vXRpyGZ1+3a9RMbYyR+1y0VKrTDoAioK/Obupro79hKqo+VpsuMuNbd+N+3fFgvQoCIiWBjpgtMVgllIEyDhJiB+DNCmYmIuxkcZ2fiySakdX4QDViniRe4kpafgCE0ovU15cMeGiu6kWcuplpjAYXc2gvnzdvr73CaDiCqDIuxmNdQAZ701dxFqhSeA38aQcKYnZzv9rx8bAeDgfOdr+TmkOIC/7gcOmKRQykXVd71ExvTaO4FzFc9gs1pGRnwdWxEd+zvVqjSi9DywQnIiDVxFUSD1OjmPJ6EiwmHjg9qxp+QcsHSAdttm0Znr7tL+Ft4IAPZictisGE7rb32SImd/H+P5s+44s5bEGwlr0VFMT3wF/7h6i6bcRWJioma9Tp06YeqYprip8VdoE7oNgOvEtFu3bmrEPwB6JoiJuqXivpIngaXZFJUrIz+j3DBlVGNcH/0Zhg2kxiua+waoepdKbybB+ppAv/QAPgtyXheFJ6gO0a6Kmq/hFeJtQQY5y4SjTo4gEffU6tsoElN+/pTl0vcl6mbJGF1/JWfJKSAiXc6udRXelg8xH4c9RS3JItrywfQc+et+YNfLvqX/b00ngeyPm7QRead+d47tMgVSBPfiVGDb067r2EuApf2Bn6/2LqXxzJ/UZX1jRQ1HYV8XHgHO6FNjy7QRasWZzvtacWhtGnshHcu3SfRcz/xZK2gc+4acePveMndyxl5L/wu7c93dJEj4RwIt7jP+TIvp9FzO3qodkwVFmc6MF8Dp1M3dQ9fdJxYgayWNwYcXUAd1wPWaMOpSrjioZEL+fuCPW2iZkaBmsWnLOUiEW88ioDgDITbn/np3ildfq42WpOdTYD5F7MvcMelWdIlcjw7hm9G9VQRuv/12w/35jL3UmbZo1slVPP98qaEmjzuBunmcrxFqigKc+N51ubh/Mn8FvmmmrXd67BttYAKgdfi7ZMFUUlD7uRs5KX6UbJfAirJIv19HDrF9ugjUtXdSl2VBdF/pOHTXpVm6tBFyLfLS8+bz79NrKLNLNJ2zBgD9VziP+xKgStWlZ82ahYULF2L37t0IDg5Gjx498MILL6jKtbf4H/0IKIrwvCJz8WIvpvSxvD1A88k0+TaofdaoUSMgiB6WbcO24q8cCrsOtTlvsjoVr/WCWp06dbS592fW0qDbgCazIn3CCpMHfO5uIKafMw2iTTpF25z7i/6OaAXEjaCBpF4nmmCf3wKEp5LwVXSCbvJ2z9BDV0ZW7PXGYsPeFRPJCqO3+e1kfO1/F9g/B2g8krybYnIXM5DatJ9aSd4ZowiXU78BoYnG5+krGSZ1dY58AYQ2Na67c/InSt059CGwVvcQDW/pur4Zv41yvr6lwmjK3kbRJN4W9i85Q40UTq+i79Vbzv1F36MI9RfoHxZyR9ieCyl6r+Ss9vj0DzS5ztyp34AuBhNnwek/gF97kyg0YGVFNJU0MSw97yoiimvWjMyfXY1PQHvMeu/V0r60rI9O4BG0e8ZZ80gpp+1brHTvW2zkCc7bCxz6ALjmSyoiDgDjiijK6vQq7fHEDdcazvpOinpBoSyXitEKSnOc3sEen1A6Ur3OZOj8dR9FVBxdCCTdrfVGmk0GpfSTTTkd0CFiE+qVb8MxS1vEB2uN8ZENvwFAhoLNZtNG08piKEBjiL2U0gEFikJjjxFFmVQfSxgiFkmE+k7Xbl58t0a4E6PqGqRRNOxFUYnypFtxUJo5APRa6EzXMyoUHFCXfg+N0KxPJZfG9BOLaeIS0cp1W6turCioPh8Y7V0xdVMCGzrr0gmxCtDeU/ZCihY0qiHXxKABiTvEBEDv8ZZZNxk48QN1rLP4Ac3voHtJRp8SduInrfFdfIq6TrebSc8jEXnZdDw9b/IP0tiSeCtgDXJOuo585nQOHXgP6PCiNgIacKaulWaT8N10POAf5tocx6ir2anfaCwQ4n3qQ+ozWkP7Z6kofMEheha2qqhvuG0mPQuzlgKpFca12T17dCE9v5reStFH+98lI7yuccQXAODEEqd4XpatdYZVltLzNIkBqBtueApQeh7+PyQjwh/Aqa8RZesOIT+3bdsWGRkZ6scDAwIQuOte1A8uRIvgbSg74Y89BdrnaEhIiNQAxQEs6eisCVongX5v+Xs6v8lV4Ils7/48Dv0PKDyOBjsfBQD4100G6r/oOgksLwD8vCifYIY3dXfO/uUcG/L2OcXDTQ8CzSbQ9y1HHte/mgSLrOX0r82TQMvp2m361aExSC80AuTw0dfYtPrTfXPyFxqXRfp30Ukai+s0peeWfA01HU/bVxzkvPMLA7q8SxP0s39S1NqxrymytzQHCDCZ+8giSel5YMcsZ5OkgCig+STjz5XmUJR64i30bJBthd0vA13eoQnyrxV2T7PbqZamQG5AlLuHSmc0m+BM8yovpFImYrz5LJDEp+jeQNun6d479DHVTAxqBLR5HFg2gK4ZWwjQ5AYgbRZFB5/bQMcxdCP91vYSZwSR+j2U0vkHRhmLkLm7tCKgGOvLC4ydNpFtneNf3TSgdUXUoj4SqvWjxh0OASCoAdDqEbIF102mc5J/R30UvIg0EtH+ALC0j3b906u1KcNAxTxhLl1TBYdIEJXPVdyXRoLamJNkE+017vDb/tgtsAQ7f+vw/DUARsFisZD4X3BY4+yLVI7AP1Kbete4odMeq3viTQSlmtTE8xXZ8WszEdTEd+VLNogcwCDbkoDTAWax0T0gIuhLz1MAQQNdVH/GR9qGS4KyPBpjRJrisv7AzQ6aD6+7x3V92QbWR6TtfQNIGAdTCo/T/elNY4DGI+l5DxhH3IqACYsfkHwvOU1Lz1OUeek5il4LiafGHPL1JmcdeMPnocCI/UCYM/oahceBP24k8dwaADTsA3SZQ/PP+Ou0zeEuYqokqK1cuRJTp05F586dUV5ejsceewyDBg3Czp07vSs6WEHwjkcB10ZpzKXKqd9oQGpGNSSmTJnirB8i1cooddCNeOeddyJvcX91eXKd/UDBUZSXl6tC3L336jpunlpFqWAAMPoYEBIHRVGQnJyMlOgmQJYuhUVQlOmMqghpTOKWMDg6vQlE96HXIq1p00PAX1OdE/cm42iCsuN57YOtLIcmzuc2agWmoGigq672GkB12va/SwPhzn87xTQA8I8AWkyjf45yY0FNbu2tKDTxrNcZiGxjfN7uEIN4eAt6cIsor82ScdliOnmrxAR525PA3tnGNTC87bJlFr0V2Ra4LovqU5TlkHdz35s00Bo9CMzSk5KnksDbdiY9fIOiKeIpdijVKMr4mISs2OFAzy8BW4VXzsyDknQPRefsfZ0M/M2PUOSFxeo+JHn/HCD5HqCuyWTm2CK6X86soQnm2ju17+cfNO4KqyflPkrPE2kUhxc4PVyhSXTN7H+X9lN63tUQEQKb8JbKpL0AhMQB3T+kiE+AtuUfDvw5kVIG8yTv919TnK/P/AnU7+EqNGd8TP8E+u9dL26Unqf9KQ66L4S32y8UqJPofEBbLDT5B+je/lqXsnd6FU1qgio8X4pCE4TtMwEAR4saI6uUxL1A5TxCAssBnbOyXfg2OBo6RaWWLVvivvvuo2NbqYvmOLuWIhyvkaJXCg673jsRrUmgKDmtrY8V0sTcaMzday4emEVINRpiPBkWEWpnVtP3a/UDsreT6GALASKl/TQeBRz/HqjXlSYefiFU+7D0vNYo1Atq4ncR5B90FdQKTziF8eqoRagfM3Y8DwTHuoqCZg0ZwpLMt62voQZ4TvnM2U1immDdXTRxbzZRWw9ReMjPrqc0CBHxEFCXrtmybHJ26B0eQij8ruK4HeVaT7d+EiBHuwrCK36TDdNJIM/8haKs9SlIRmLA5kedE6K6HShtyoiASKDtk8Cfk6jWXovpJDrLv7kQMYRQVDfN1RGw5m8k9O58oeJ8HgT6uak7JUcM/drH1VlRGeQom+9bAKOPuhxnpN9ZAHTfydFqf/vb32hSJU0iB3aJxR43mUPTbusH/Ck5ehoNpudzeT6l84Q2BVZWOKvqdweaTaLrzF2KbMYC59guEBEyYSna38VeAMBD+o5M/kFqUNLyQbr2RVRPs9tJqBH1Tls+SKIPQI2fyvOp3qP+N9rymPN44sfSGFKnqbPEBkCTUTnKAqB7Rx9tJ8jU1SLt+Bo5fX68imy9b5oAQzYBka3pXMoLtAJ9yjQg/nqgwTXkVNw3h5yeXeYC4RWOkOaTKO322Nf03F/YABj4B9luMmsmuEb6C8cG4F5EWH8vjRXnNwHtZ2nTKoWzR372HHwf6DibImsdZa7iKUDXclAM1ZaTa7QKyrIp+iZrBTDyAD2bxQRdRPFEtAH6/uRMoez9HQltOTtpm93mUfSsiMQPb0E2Q+l5umcDo4yfafooLnFuZh0XB/4BQCFbK/Zap0Mq4WYS221BJEY2v9P484LEW52lUb6MBEYepLnP7v/QNSCz4zla1+wZA1D5jroG0dDrJlMk3N7XDT6kkHgsBOJGg2msbD6Z7Bth0xogl9wBUNGgYyQsFgsGDx4MnNM6NHuGfImf7LoMIelZH5S1iH6vwAY0dzj5MzlhG+tKWniDeHZabNroapmwikZzRjVQzZDtdNm5XHjCGVgQ2ICut7N/Al83Jhum5Az93j0kIV7OGJEpy3PtYl54hMY5feokQPWhx5XQs088pyPa0NhyehWNI0l307VvsTodduc2klNF4B8OhDZzPnd6LqRx4MgXNG/t8l8g9Z90n+94HthbcYz1uzvHljZPkoNO3WYECWxKuTN6ba/u3BylTntRs7yMotmN+Lkr0G8pOVX3vOYcc0KaANdudmZlAM7f+RKgSoLakiVLNH/Pnz8fDRs2xIYNG9CrVy+TT5kQO1zrwWcuLRQHGXLZFWlku/4NNL0NsFg1HThVrxiApDoH0ClyIxrlL0JcqG5QPP4tGpw/hClNPgT21kPDlCna9+XC/L9cDYwkccvf3x8BjvMw5MjnlP4jUgEC6ztv3PAWzgkl4IwOkCMCACBpMg1oI/bSA7k4ix54J5do60MJxpx0nXAB5A0LSyZhRqTsBUXTwNfyfud6Vj+tkSnY/gy19W71TzI2hYh3YwGp/b+NAFo9CsQMICHEHWIATryNPuMXVuEllGjzBHnkMj6uKCRdYl5QVo5Q2fsWCS1X/Uf7PZxeo43yGyEJhAA9OBqPoNcJN5MIFpZC+f1l2SRS7n3DuEZAvW702dYGaYnC29NxNhl/RcfJ4Ns4gyYd9ToZT4TlB01E24o6bmuo/lXOTqe4Wq8bCYKHPqTvXYi3K4fTw67zO3StFRymazh2iDPtCtBG7AlydpCo647A+kD7fwGdXqN0AoBqeAiaTXRGBcgT6sj29H1q0hIl73BEK6D98zSxAOiePr+ZRF45lFtfC0UWzzJ/AVbf4vTEiUgmPeUFFMmVv59SiEVnQsGRL4BGQ2nSJ3dTSntBm0oIkDGcsxM4/q2xwHvof0DLBygS6qi2lkRGUSIK7XT/Byi5iAyxughqAGA9/D+KLALg5+eHqKgoMlSM0KfUGQmkccNdI346vEjX/6ob6Zz1kTolp2giffgzijzI3U3CV2hT48lH9/+Zezwj21CkQVk2dYYqPuWM5Gk0UGvY1kkA+ukmoKIGTe5uMnIjWrmm0ervV3msUBQaI+T0an1nXTPyM4BtT9Ex9/yS7peD88mbqk+52vKod9sEKNrJCHcpn6qgZiJo6lN8ARKh4q937aD4xy3a1KEGV9P1HhxLkVyHPnDdVsFh8ooL9BHEZgTUJRFhzW1kyJflOrd/7Bu6Z4J1BrL8++XsJi9z9jZyfow67L6hA0DX9pbH6Dl7+FNywskTnYPzgRZ/dwrvAVG03Zyd9PrnivRqOZVc3zSl8Dg9mxuPoXFHrg8qNxMCKKpQpOx6i6K4NohYFO+yWog1H0JQU1OqAMTFxQEl2tTpqJBy57b3vU2T/OYVv2P+QdTbKOrrWYBRh0hU2Pc2jSvfNqOJiZicnfuLbAhA26kcoPszdw+NPasNymmICJ0SnThRet77wtRb051R3vqurl3n0j3f5gkSGwIiSQQQTVD+mkr/Gg3Wfk5MZls8AHSscDiWZtP5+4eTA670HEVdy3jT0CnpLhpLBYP/rLA7ckko6jbftR4aQM8c4TRr/Sj9M9v+2bX0XHSU0X0lC2rFZ4zr5MrXaul5+t3yM4DYwZT+m7eXhBwxXqiCojRWiWtCL+wc+oCirA68R8dk8aNrToxhv402PheAxMPEv5F9UZZDNr7+egGAzm9p65EFNwK6vk+Rhhkfk5N5z6v0XlBDEgDW30PnWnSSnifCNvMLrZjIl7p+V2L8MCqu7h9On7VYXJ+D4SnA4DWunzEjJI7S1kUJiZNSVLmYBwlE1JwgqiPZz3qhWHSdDW+hzYIwut4AcsQu7ef8O6INcJVUHsMvrMIhd4SeZec3Y/P2PUgrNc6YCLXlw2KxUJOU/a72fVzRdwAqxp7S867316Z/AG2fcpbo2P9fY0FNUSj1MHsbcM1n2kY1gLMEgF8d4/kT4Ezxzt1N27MXkWPPHbLtm7eX7LXUB+maFVGmQQ2dNkOR9CzJ+JjsJ3E8ciSnTMlpCjiQWXc3RamaBRtkb6ExQNxvnV5z/q7r7yX7UUQ99lzodOwLghoCvb+nMTnjE4qqD4ml9WSEsN/h31S6xhZMdvDpP+j31P9WtgCaY+541vi4YQGgkF0XdZU2yv7MGuf1awsBrt1KzvUd/6Lf7Mc07aZCGgP9l2nFNIDmyZcIVRLU9OTk0CAdFRXl0+cc/pFA72/Nbxzm0qHkHBVhzdlJIeptKqKdHGWuBWEBDGvwLbDBWeS7yB6EYFsx8Nc0NAUAf5BBpRfUZE9rwWEg/yAURUFDZadhLSQArhP0uu1I5Bi6hQQueVJevz51rNAAADe9SURBVJtrGgzgrM1k9SPR5vwW13UEjQa7v6ZjBjhTBf3DybNnVC+g7ZM0wSk6QV4SkU5oFJ2wLd35MPtzIv0/fK9zINVTfMbpmQ2sT8fb4j4gZSoViweA5CnkHQQoBaflA2TILO2r9ZoKynLoAXd6lVMIqpNAEWkdZ5NRLboQRXUCBq1x9W7IWG1AXIVXedQhqr/hF0y/xYb7Kcqvx8dAzCASGIxSyPQE1qMBfuUIisjZ95a50QJQhJmgwwtkCB3/1jVtr9kEWrdrhcF/4D2KOCs8Rv8Wp5KR401thNjhrjUaRKQZQALjsJ0AFPLkiWtt7BlquiCMAWsApV1FpFLdjrN/0neWcAs9WDc97BTUUh921qSJGwn0/sb1uNrPIu9vxv9ov4H13NfEkTtEAiTc2gvpYXtskXP5rn87923Epn+4RtPEDHQdGwCKVuv9Dd2f8oO7TlMSP4tP0u+hE9PQ/ln8+XU2YgNJSI/BDsSc1UVtCNbeqQpqKnLKcaOhWvEkd4/TCDQSghuPoRppR7+iv1s/Rr8bAAyqGNO2PEGOAUcpXYNy96PvK1LEQpsDI/cbpxzWSTD3+FqsNCE78b1rnblkD4IuQBOS8xspEsYdFj8aa4oznRETGx6gyZQtSCsaluc7U4vVZYXUVS+iVUUKfy51vxV8Lo2hBs8cF6I60nXc9X0yTE/+DJxbT0ZgdG/Pn9cjuiHvnEVj6+nVJIKdWExjuJHjpTiThBB9LUtZTEt7nu5PcZ93n0/fgSwQAa418GQsVuNOW3EjacKr2CvS3EooOk1m4wyg8xztMtFdbM9rNBYLWj3qWUwDSDxpcT/9TtueomgjWVTO3Q383MM5QQ1tSs9qkZ7V6p8UmSZHU+fuIpGi6W0VEaMj6brUR/4ase9toJVBxJ4R9mLqWitHqrshyJKHG2+cis8//1wjqAUGBgJ52Zp1LXtfw/0TfqPfUjxDYwaSSCSPZ30W0z0dHEvChnBsyBP4nguddoWcjr71KTUq11Qc2/sGjQl64XHXy0APA/tIpiiL0trMCs+3SXdeyxaLU7xr/yxFlf9yjXM8NRPCkqSxJiAS6FyR3uYodXVCmlEnoaLGpgJEdaZMBZnwFkDfX0i8zfzFtQOo2EZkWy/3F0/OiKOLqG6f/ty26Mas8FS6pmX2/If+6UnTOXTkFEPAGVGkF9S2zaT7XdStSpoMpP6Dxp/MZdoMiuA4iszXlxwJiaNoU6MOwY2GAg17ui6v35WcBKf/cJYzCG0ODN9NNmFQDG1v2QAgup9z236hdP85SrVjJOB09Bo9/+qmVe88s+t7ZMPYi+m57A2xwyrmu1aymxbFu0ZRhyYB7Z9z/n5m4o0e4fgUWCzAgOUkREa2BaJ7o2VCEYpPDkBQg3ZA7h5kFDZG4vo0AECEXw78C3YD5Q0NnZ4tm0hpklvTXSPID32otRVOfE9pvPpIuaKTTtvv6EKtoKY4nHXw5Kh4PaK0TOFRihAsywWG7XDa//ZiqvN3ZjXQZwldZ7JABpBzLXeXNmU7biT9Nv4RrnPG/e/Ss6r3d85xrdlEKncUHEtRXEaRhCd/Ar6RxtgWD9C6jlKyCU78ABz6yOl0DomnqDSR6iinEO95jYQyEaUa2hwYssEZLa5PczfCFkjzSoE7O6f9v+jaKT1PtZZlZ7B/BM3Ff+pM89l+v1BE/fHF2mfQ0E1kk4c1p+CInzpL9XctJEy3f5ae73rCU5yd0y9yLIpSPW1cHA4HRo4ciezsbKxatcpwnZKSEpSUOFNAjh8/jlatWuHs170QNXql4WeYS5AD71MheIuVUkes/k7PiwkOxYqteW2RUxaJ3vUMroUbC7Teh9V/06aL9fsFi5dtxDBUTC5iBpLIkrWcVPjfKpR3qz8wsKJWgbvILcVBhn1EazI+tj1Ng0e7p3QHXkYtmEV6Trf5FZFXDmDUEfeTimPfOiOSku6mnHFPlOUBWx6nOkfnN8NlAmZE88k0iO38N3D1p05DqLyIOkidXkXf0agMXaFrhSb4oU2NBa+sFU4PVdwISnlYXuFJ7jqXWr4beQkFjQZTGpy+lsGFpPQ8Fe40KhwvIqkaDQX6/uD6/p93OFNMku6mCZy+VtCZtc7aOt4SUJcMVv9IEgpkY6vHpxRBopTThER/PQpKzlEawJEvyThuepvxegB5uTc+QEZq17l0j5Tlmdd2EZTlAbBQRMeZtSTkNria0qQVuzPaSRB/HdWzav0YTSoAus+2PG7evTCiFY0heuFLMHC1a20LPX/cTAJRw140YRcTf73g1eNTIPEmzJw5EwHWYjza/c8Kg8rNPXaLQsXs8/bRxOvTCuGn6/tkJP4opfm2eQJoVxGpsfYuEpXbpFfUugl0Tmp3vUTGYZt018g7wfZ/GU/sBJ3nkOGnT/G6Lsu4qYrg/FbtMQP0vfVf4XkiUniCJj76yR8AtJxB40zqP4CY/iSgGUVXCYzSu0W9uG0zSXwByIjVRxEb0eAabU2+lg9S6oKiANdu8U6El/nzT6B7d6BZM6CgAMjKArZuBdq2JQFZ3xxFjz49QyagLtW4E5PYlg9SpKLR919eQDVJZBqP1grVAD3v2j5F0bBG9Uj6/EjRsgCl/hlFK3lL++comtnbiWt5AfBdC5rsBMWYF3GPHU61nuTrt/g0sNDgeo7uS97u7G3AD24mZUaMOuIcn2RKztHvlb2NRLQ9s7XvhyYBDXq47cJtD4rH2ZxiOAb9iXfmkiCVnp5O0e76VHuLH42Z8kS91SNOh1r7Z7VR2IqDUsC3PemMiG08ilJ1ZcdCva5k25hFUF6fTRM4oy6z8u8zdAs5JY8uoudHx9naCIddr1D6rZ7Uh+h69oSjjOwJuci8LBq2/j+a7BlRmg18KUU6RHUiG63gsPO82z5F117KNFqu2Knerdl1u3YyTSiN6PwW1R3yhdIcqtuo2CnDIqgBie8rhjhFb4sfpQ8vH2Rcr6kytHmChGB9FI0gqjPQd4nTiQrQ82bHcxTF1vV98+fSiSX0fM3bS9dm1nKKkOo4GwiONv6MEBYFfZZQ1B1ApTmMGreEJdO1IEemyu/FDiOhrTiL9h/UgMbcbvO8j6z0loxPtNkAAAl3OTuBtH8Dm/9Bx9R/JTkf9deXopD4EFifRJWM/9G1GTOAfvOfurm3owEqxRHd37UjqRccOnQIQct7oFFQJv483xXd6q6l769OomvhemHD2Eup1EqFc/d4cSzigiqeww17a4Wogatc7eL1U50ObDH3yd5BkaDyubaf5axxZ8SnNq2DqME1wMCKskKnfqOSLoKGvWhZYH2yr/T2RZt0eibHj3GWptj0T/dOXouNIqZD4khsk7NubEHA4HWuz5/QJGBkRSDF1qecTg2BNYDsNP9wcjr/mKats6an/zLX1PaaQnFQ452cHRR0seF+7fNpwO/OUkwC2bYQ5O4hjSA8lSL33NkKioP+uQu6uEioNkHt3nvvxY8//ohVq1ahcePGhus89dRTmDlzpsvyU0unoUE/o/xw5pJl7Z3OAogyoc2AbvMxd94HmJDwJY4XNUTijb/ivY+/xbGTZzBj8kiE7XpY6xEDyGMpK+rLr9VOhpvcCOXEEljKK1JQhu8hZRugm3HNeEpJbTmdjILqpCyfog8a9qKJS1kueWVEjSYzyouAzytEwiF/uYY9e+LseuMWx3rkujMxA52pWvvnUrqqXx1gwG8Ususrhz6msGDxnX5V3zwVVCa6H3l5PIVoXwjsxeT1OfMn1ThQ7PQb9viEPPOx15oXpy0+QxNgMwNTUWjbB+c5oyzCW1bULIkgD+Xx70jkFJPwq16hKECAJkIBkRTVVpBBv1HBYTKIK2E8XRDy9tPELrovGRP5+0lQucokYqC8iDqQHf+WavgJ73On1+jaVBwkBIQlU0Rm4XFKrQlpQh5ubyjNISPp5M/AiqHG69yYD/jVUSOtIyIiSFDNXEYGRJPrkb3uOezeewDd6v5JxtToozR53XA/0PpxCmcHaLIZ2ZZSYw/Op3oYdTuQ8bNnNk0SirOAjq9TrURfcZRTGP6p3+gakmv56LnqFRLv05737vvK2UU1fFIfpPE6sp3bWiwaSnPIUG7Yh0QZIVQP/IPEBsGh/1E0g97zHnstNeeIv67CuJai1cJbAEM2UvpRpkmNrIRbqA7m0YXkJGg6HhR50olEuKxllELY8gEapxxl3kVS6XEnqJWcpciC/IOuHnyLHwnV13xJ+145gmq0Nb3NGU2XdDfQ6Q2qjxLUELjmC/PxB6C6MJlLycsupxbLYuNNZWSQOspI8MlaSc/HrU9QJN7155y/saLQd++u8Un8dfQd67khp3IOkkMfa1N99fReDMRda/zewQ+c0diC8FRg+E76XpYNcP2MxY+8+sVZNA6HJlJUrxCLGg2mFL6jX9N3eGqF53OIHUYRKz93J497w15Axkf4feNh9IxydTJnFCYguuNdCE57lJyDa25zFsH3RIvpQEeDCCWZsnxnylRZPvBdc8/dNVP/QVHLRkJtRCvn+QEUEd77OypKL+jwEj0zy/IoClofEdLkBqD7R96XdykvpIZNJ3+kcxm6hcTioGigqZvrBaAJ+t43yA5Lvte14LuvFGVRfTwR3VUnkYSQxmPIMahvKuINv1xjnFHRZByJdI4yEqLKC0l0DYl3pjm7I6HCSRPYgJ6nYc1dRR899bsDvb6pEH0qcS4CRaEIV7NmOXocdhpvzm90tRPK8pzjdkRb4NhCijJv+QDQ5HoaxwIiaEyLaAtsnG5wXj2cEd41gaJQSvO2mTSm9l5M37e+EVFlObeB5i8WPxIf1k8hASfpbmq60HQ80PntSkfeORwOOH4dAL8zy41X6DaPntUARW+lTKPf5MxqWtZvKV6a+yMebPYyLEbOxyY3UIRW/R60Lb9Q4AuplEP8dUDPr4BVN1HDHEFkO7Ib3KXgrximrUcKkKjT8BrKkDLqRt/qnzR+fi2NB2b2qahV+msf1yhdQOskPfsXRV4BFHXd8kFyzJz8mey9ljPoOR5Ql9IbAapDu7KitI01kMTIlKlax4S8XT11r6IMn9oql+UoJ+fF5odd0/kBGht7GdgJlynVIqhNmzYN33zzDX777Tc0bWoQsleBWYTayb/eRUzHyVU9DOZiwmEHDs0nkUAUHEx9mNLlAMycORPjxo3Djz/+iAceeAAfffQRDhw4QN5agCYlx74lz6cwYIIaAv1+BcJaOI04/3DXbnaD17kWeb1YOfsXeSH0Cr635OwCFrciwSUomsSL0CTyHDfo6YwWkun1LaWr/tqHPEltnwbauol28QWRugWQp7PkFE2qA+uTiATQQyCiZfXs71IiZxdNsGMGGhs/hSdIGDF7/1Kk5BxFLNXvcXGck6OcxqTMn50ic8fXKXXTi651W7ZswZIfF+OfbT4kL5vcMUxgDQBukorxFx6nNHgjun9EHQqrgsMOLIozLiYd1ZHE+trAUUZOlextQKfXXSdp5YU0/mz5PxrHu87VNgA4/DmJkbLjJLqvNmIFoGYhHV/1XvSrDtasAXr00ApqW7YA7QyioQ59RGNi0t0ULSpqxQHa12f/okll0wmVOxejtMuIVlS+wKixgqO8orFEF1dxvugk1e07vYqirjM+chZ0TrwV6PAypftvuA+AhYrLp0xxH/3oiYxPqWZSWS5NEo9+RcJO8r0kLrgjZxdNPMOSnRHBolsbQOKivZiEsm7vk8NLH4GbvQ34qasz2txXBq0F6rs6uOb/exImNp5v/rnIds7JWsJNJMS669pmDQAG/QlEdfDt+ErPU/OEggxt1+PGo2hS2ngM0PW/TkG08BiJUpGt6fsTUQJyZLY3JN1F4lujQa5NSbwl/yA5kYwiBy8ke96ouOZBadxJd9P3Vdln25b/cy2JEDOI6kCafVeijETzO0lIcpRW2FevUf2moGhK/ZPLhygOEujz9lGUuJymHD+WJvjtnq69bIGyfGogVfeqyn+XhceA71tpy2n4h5MI3OT66jlOdxQcpe/+Qoob+nIIlUW222Vih1N66uHPjKOWw5KBwesxc9armD6xHyKOvGaeTeAOfakHwDvnzJl1rgJzq0dJvFpoENBQrysJUBYLOX5/aEfXf7d57iOglnRyrXubfC/ZjrIzPXMZlVbxVry3l1CNNFsQRe6GNjNeryiThMg1f6O5cXR/GpdjBrp3tl0oSs5SMIWeMSeq7sioJG+++SZefPFFZGZmon379nj99dfRpYt5AMoXX3yBJ554AhkZGUhOTsYLL7yAa681ceKZUCVBTVEU3Hffffj666+xYsUKJCf7Vjzu2LFjiI+Px7GDWxDX1MewfObSIWslGbYxzuKZBQUFCA4OxltvvYVp06YhPz8fL7/8slNQExz5Cljl5mHY4gFnLYnAehSZFlivBk7iIiZ7Ow1aAVE0GbAFOR+yx7+ngtcFh52GRkg8hZSLtNxRh50ek+qgLI9qkkRW1JsrPgMEGQy2DFNbOMoosqX0nLO7oBfY7XaUlJQgxJFFkRpGIpa+U5KiUH0YuU6HoDKRqUYUHCbxKW8/1fTI3UOe1YSbzI20SwWHncb4zY9onQM9vwJsdWiifqHFWl8EtQuFvZhqDB78gAS53t97H8HpiXMbKYqq5QPV+6zQU5pNEbuNBtM+z66lCZK3k1RHOdWq0acBx40Een3tefJ58meKzjRLO9WTch+Jj41HmUZMzZw5E+n/uIMm22W5wNdxrt1vBe3+RWmdRceBX3pS+tPg9RS9K8TSLu9oa4dVBnsJsPFBEuWa3e5dQW/BqVWUoic3jzCj7VM0Hl4MzpTqQHGQoFWaTeJ4VSez+pIQUR2pDpG+MLcZhcfpWISt5Q5HOQCF/v8ygp6B/ZZq7PJLnvIiAMrFkflwKWEvJUdVySnKiijLpvTuVg+TqFV6niLDT/1G84vINHJiVTxfZs6cifvuuw9REaFUMiJvH9VeNuxMWkHdq7TNpQRxIylKVtSb9cTx76mJVWA9bRkggVzaY+RB4zpdnji9mprfhSYBV39y6QRsXEiOfecsrdT2aSDpzloT0z777DOMHz8ec+bMQdeuXfHqq6/iiy++wJ49e6jhho7Vq1ejV69emDVrFoYPH45PPvkEL7zwAjZu3Ig2bdp4vd8qCWpTpkzBJ598gm+++QYtWjgv/oiICAQHe/b2C0Ht6NGjpmmizOXNO++8g7vvprzzmTNnugpqgHnaRsfZFH6c8TEp+NVh4FzOnN9M9cJkmtxA6QoMw/jG4c+BP8ZplzWdQBEw+ol73n6KFi08RpNmqz9F83hbyJqh+m5/TSWDP7QZdd/zNq2ouhGCWvPmJKhlZta+oMYQObsoRdsWBPw2hhoYdHzdPGXUjNx9FAFUeJQmUHWaUgR43TRyGuVsp7QeD7VdXOwah51S04587kzBtAZQhFDKfU4xQHGQ6GELdAo5IY2pHurFgFw76uoF5KArL6Doib4/mjc/YbQc/ZrEzRZ/966geHWgOABYLh+hk7kw5O6j8VQXQa0KalFRFG1YdLKiWH1FRFCjIc5mPJHtSPxq9yxdf+c3UWMsUdtwXHHlIrT3zdF2ngco0rnrexR9F92vcmVtGO9RlIpIZpMSOBeIrl27onPnznjjDWpS43A4EB8fj/vuuw+PPOJak2/cuHEoKCjA9987m8B169YNaWlpmDPHi9rmFVRJULOYDMbz5s3DxIkTPX5eCGonvvkGjQxUQ+by5/vvv8fw4dSZZu7cubjzTjeduM6so9QEWyCQdC8QGHlhDvJyYt3dNDEV9PmO0gUYhvENxUERQcVZ1BnOGuBV2ihTReQ0ydpi2zbgrru0gtqHHwI+Rukzlz9u7ZrsHVTDrc4l6lA+vYaiEEITqUHE+S0U8VQdaWgMw1wSzJ07FzfccAPVnZU5vYbma3XTqEmEo4ycjvrnt6Oc6owFRQMJN1buIAqOUokg/wjKwlHKqTEGRyteHnTzrrlbaWkpQkJC8OWXX2L06NHq8gkTJiA7OxvffPONy2eaNGmCGTNmYPr06eqy9PR0LFq0CFu2bPH6EKvUNqGa+hmgzqhR1bId5tJjOAA8+ywA4E4AeM+gkYEh39bMAV1xjKjtA2CYy4BK1kBkLm0sFsBaIR6MH1+7x8JclPhm11wOVFM9VoZhLgl8G+MMukxr8NBsxSPHAeyseL3U3YrMJURJcbGmBj8ABAYGIjBQG8145swZ2O12REdruwpHR0dj9+7dhtvOzMw0XD8z08vSDxVcFH1IHU2aAH4XxaEwFxhFUdRIR/k1wzAMw1y0WCzA1KlAcTEwdy5FzjGMDrZrGIa5nOExjqlpZs2ahZkzZ2qWpaen46mnnqqdAzLgolCx8v/4A5FcQ+2KxGLymmEYhmEuCQzqcjAMwHYNwzCXNzzGMTXNoyUlmDFjhmaZPjoNAOrXrw+bzYasLG3DsKysLMTExBhuOyYmxqf1zeBCBwzDMAzDMAzDMAzDMMxFQ2BgIMLDwzX/jAS1gIAAdOzYEUuXOtN9HQ4Hli5diu7duxtuu3v37pr1AeCXX34xXd+MiyJCjWEYhmEYhmEYhmEYhmF8ZcaMGZgwYQI6deqELl264NVXX0VBQQEmTZoEABg/fjzi4uIwa9YsAMD999+P3r174+WXX8awYcOwYMEC/PXXX3j33Xd92i8LagzDMAzDMAzDMAzDMMwlybhx43D69Gk8+eSTyMzMRFpaGpYsWaI2Hjhy5AisVmeCZo8ePfDJJ5/g8ccfx2OPPYbk5GQsWrQIbdq08Wm/FqW6WnVWgmPHjiE+Ph5Hjx5FY66hxjAMwzAMwzAMwzAMw1wCcA01hmEYhmEYhmEYhmEYhvEBFtQYhmEYhmEYhmEYhmEYxgdYUGMYhmEYhmEYhmEYhmEYH6jVpgQOhwMAcPLkydo8DIZhGIZhGIZhGIZhGOYiIjY2VtNM4GKjVgW1o0ePAgC6dOlSm4fBMAzDMAzDMAzDMAzDXEQcOHAAzZo1q+3DMKVWBbXU1FQAwPbt2xEREVGbh8IwlzR9+vTBihUravswGOaShe8hhqkafA8xTOXJy8tDq1atsHPnToSFhdX24TDMJQs/iy4fxLgYFRVV24filloV1Pz8aPfx8fEIDw+vzUNhmEuagIAANG7cuLYPg2EuWfgeYpiqwfcQw1Se3NxcAEBcXBzPiRimCvCz6PJBjIsXc7onwE0JGOayYOrUqbV9CAxzScP3EMNUDb6HGIZhmNqGn0XMhcaiKIpSWzvPzc1FREQEcnJy2BvDMAzDMAzDMMwVB8+JGIZhtFwq42KtRqgFBgYiPT0dgYGBtXkYDMMwDMMwDMMwtQLPiRiGYbRcKuNirUaoMQzDMAzDMAzDMAzDMMylBtdQYxiGYRiGYRiGYRiGYRgfYEGNYRiGYRiGYRiGYRiGYXyABTWGqWXefPNNJCYmIigoCF27dsW6devU94qLizF16lTUq1cPoaGhGDt2LLKystxub8eOHRg7diwSExNhsVjw6quvuqzz1FNPwWKxaP61bNmyuk+NYS4I1X0PLVy4EJ06dUJkZCTq1KmDtLQ0/O9//9OsoygKnnzySTRq1AjBwcEYMGAA9u3bVyPnxzA1TXXfQzILFiyAxWLB6NGjNcsnTpzo8hwaMmRIdZ0SwzAMc4nh7ln07rvvok+fPggPD4fFYkF2drbH7Z08eRK33HILUlJSYLVaMX36dJd15s+f7/IsCgoKqsazYi53WFBjmFrks88+w4wZM5Ceno6NGzeiffv2GDx4ME6dOgUAeOCBB/Ddd9/hiy++wMqVK3HixAlcd911brdZWFiIZs2a4fnnn0dMTIzpeq1bt8bJkyfVf6tWrarWc2OYC0FN3ENRUVH4v//7P6xZswZbt27FpEmTMGnSJPz000/qOv/+97/x2muvYc6cOVi7di3q1KmDwYMHo7i4uEbPl2Gqm5q4hwQZGRl46KGH0LNnT8P3hwwZonkOffrpp9V2XgzDMMylg6dnUWFhIYYMGYLHHnvM622WlJSgQYMGePzxx9G+fXvT9cLDwzXPosOHD1f5fJgrCKUKvPHGG0pCQoISGBiodOnSRVm7dq36XlFRkTJlyhQlKipKqVOnjnLdddcpmZmZHrf5+eefKy1atFACAwOVNm3aKIsXL9a873A4lCeeeEKJiYlRgoKClP79+yt79+6tymkwTK3RpUsXZerUqerfdrtdiY2NVWbNmqVkZ2cr/v7+yhdffKG+v2vXLgWAsmbNGq+2n5CQoPznP/9xWZ6enq60b9++qofPMLVOTd9Dgg4dOiiPP/64oij0HIqJiVFefPFF9f3s7GwlMDBQ+fTTT6t4RgxzYampe6i8vFzp0aOHMnfuXGXChAnKqFGjNO8bLWOYSxV3c6J33nlH6d27txIWFqYAUM6fP+/VNpcvX6506NBBCQgIUJo3b67MmzfPp/0yzKWEu2eRzPLly326jwS9e/dW7r//fpfl8+bNUyIiIipxxAxDVDpCrSY8mqtXr8bNN9+MO+64A5s2bcLo0aMxevRobN++XV2HowKYy4XS0lJs2LABAwYMUJdZrVYMGDAAa9aswYYNG1BWVqZ5v2XLlmjSpAnWrFmjLktMTMRTTz3l8/737duH2NhYNGvWDLfeeiuOHDlSpfNhmAvNhbiHFEXB0qVLsWfPHvTq1QsAcOjQIWRmZmq2GxERga5du2q2yzAXOzV5Dz399NNo2LAh7rjjDtP9r1ixAg0bNkSLFi1w77334uzZs9V3cgxzgaiJyJpDhw5h2LBh6Nu3LzZv3ozp06fjzjvv1ERKe9ovw1wqeHoWeUufPn0wceJEn/efn5+PhIQExMfHY9SoUdixY4fP22CuXCotqL3yyiuYPHkyJk2ahFatWmHOnDkICQnB+++/j5ycHLz33nt45ZVX0K9fP3Ts2BHz5s3D6tWr8eeff5puc/bs2RgyZAj+8Y9/IDU1Fc888wyuuuoqvPHGGwBoYvPqq6/i8ccfx6hRo9CuXTt8+OGHOHHiBBYtWlTZU2GYWuHMmTOw2+2Ijo7WLI+OjkZmZiYyMzMREBCAyMhIw/cFzZs3R/369X3ad9euXTF//nwsWbIEb7/9Ng4dOoSePXsiLy+v0ufDMBeamryHcnJyEBoaioCAAAwbNgyvv/46Bg4cCADqZ832yzCXCjV1D61atQrvvfce/vvf/5rue8iQIfjwww+xdOlSvPDCC1i5ciWGDh0Ku91ePSfHMBcId3MiAJg+fToeeeQRdOvWzettzpkzB02bNsXLL7+M1NRUTJs2Dddffz3+85//eL1fhrlU8PQs8pYmTZqgUaNGPu27RYsWeP/99/HNN9/go48+gsPhQI8ePXDs2DGftsNcufhV5kNCRX700UfVZbKK3KVLF7ceTfFASUxMxMSJE1Wv5po1azBjxgzNvgYPHqyKZZ6iAm666abKnA7DXNIsXbrU588MHTpUfd2uXTt07doVCQkJ+Pzzz91GEzDM5YjRPRQWFobNmzcjPz8fS5cuxYwZM9CsWTP06dPnwh8gw1zkyPdQXl4ebrvtNvz3v/916+yRbba2bduiXbt2aN68OVasWIH+/fvX6PEyTHXhaU7kLX369EFiYiLmz58PgOZE8nwHoDmRKKpeXftlmMuJDz/80OfPdO/eHd27d1f/7tGjB1JTU/HOO+/gmWeeqc7DYy5TKiWouVORd+/eXWmPZmZmpltlmqMCmMuJ+vXrw2azuXRLy8rKQkxMDGJiYlBaWors7GzNvSTer04iIyORkpKC/fv3V+t2GaYmqcl7yGq1IikpCQCQlpaGXbt2YdasWejTp4/62aysLI0nNCsrC2lpadVzcgxzAaiJe+jAgQPIyMjAiBEj1GUOhwMA4Ofnhz179qB58+Yun2vWrBnq16+P/fv3s6DGXDJ4mhN5iz6yxmxOlJubi6KiIpw/f75a9sswFwOenkUXEn9/f3To0IHnRIzX1GqXz6VLl2LatGm1eQgMU2sEBASgY8eOGs++w+HA0qVL0b17d3Ts2BH+/v6a9/fs2YMjR45oPCnVQX5+Pg4cOOBzmDTD1CYX8h5yOBwoKSkBADRt2hQxMTGa7ebm5mLt2rXVfm8yTE1SE/dQy5YtsW3bNmzevFn9N3LkSLUWVHx8vOHnjh07hrNnz/JziLki+fDDDzFr1qzaPgyGqRU8PYsuJHa7Hdu2beNnEeM1lYpQq6mogJiYGLfKNEcFMJcbM2bMwIQJE9CpUyd06dIFr776KgoKCjBp0iRERETgjjvuwIwZMxAVFYXw8HDcd9996N69u6YOR//+/TFmzBhVnC4tLcXOnTvV18ePH8fmzZsRGhqqRtw89NBDGDFiBBISEnDixAmkp6fDZrPh5ptvvvBfAsNUgZq4h2bNmoVOnTqhefPmKCkpwQ8//ID//e9/ePvttwEAFosF06dPx7/+9S8kJyejadOmeOKJJxAbG4vRo0fXxtfAMJWmuu+hoKAgtGnTRrMPYQuK5fn5+Zg5cybGjh2LmJgYHDhwAA8//DCSkpIwePDgC3buDFNVaiqyxmxOFB4ejuDgYNhstosmoodhqgN3zyIAal1PETm2bds2hIWFoUmTJoiKigIAjB8/HnFxcRpxevPmzQDouXP69Gls3rwZAQEBaNWqFQBqoNOtWzckJSUhOzsbL774Ig4fPow777zzAp49c0lT2fagXbp0UaZNm6b+bbfblbi4OE2b9S+//FJ9f/fu3R7brN94443K8OHDNcu6d++u3H333YqiKIrD4VBiYmKUl156SX0/JydHCQwMVD799NPKngrD1Cqvv/660qRJEyUgIEDp0qWL8ueff6rvFRUVKVOmTFHq1q2rhISEKGPGjFFOnjyp+XxCQoKSnp6u/n3o0CEFgMu/3r17q+uMGzdOadSokRIQEKDExcUp48aNU/bv31/Tp8owNUJ130P/93//pyQlJSlBQUFK3bp1le7duysLFizQfMbhcChPPPGEEh0drQQGBir9+/dX9uzZU6PnyTA1RXXfQ3omTJigjBo1Sv27sLBQGTRokNKgQQPF399fSUhIUCZPnqxkZmZW96kxTI3jbk4ks3z5cgWAcv78eY/bfPjhh5U2bdpolt18883K4MGDfd4vw1wquHsWpaenG85v5s2bp67Tu3dvZcKECZptGn0mISFBfX/69OnqPqOjo5Vrr71W2bhxYw2fKXM5YVEURamMEPfZZ59hwoQJeOedd1QV+fPPP8fu3bsRHR2Ne++9Fz/88APmz5+vejQBYPXq1eo29FEBq1evRu/evfH8889j2LBhWLBgAZ577jls3LhR9Wq+8MILeP755/HBBx+oUQFbt27Fzp07ERQUVJlTYRiGYRiGYRiG8RlPcyIRWfPXX39h8uTJ+O233zxG1hw6dAht2rTB1KlTcfvtt2PZsmX4+9//jsWLF6tRnJ72yzAMw9Q8lUr5BIBx48bh9OnTePLJJ5GZmYm0tDQsWbJEHcD/85//wGq1YuzYsSgpKcHgwYPx1ltvabZx4MABnDlzRv27R48e+OSTT/D444/jscceQ3JyMhYtWqRJHXj44YdRUFCAu+66C9nZ2bjmmmuwZMkSFtMYhmEYhmEYhrmgeJoTzZkzBzNnzlTX79WrFwBg3rx5mDhxIgDgyJEjsFqdpa2bNm2KxYsX44EHHsDs2bPRuHFjzJ07V5MS7Wm/DMMwTM1T6Qg1hmEYhmEYhmEYhmEYhrkSqdUunwzDMAzDMAzDMAzDMAxzqcGCGsMwDMMwDMMwDMMwDMP4AAtqDMMwDMMwDMMwDMMwDOMDLKgxDMMwDMMwDMMwDMMwjA+woMYwDMMwDMMwDMMwDMMwPlApQe3NN99EYmIigoKC0LVrV6xbt059791330WfPn0QHh4Oi8WC7Oxsr7Y5f/58REZGVuZwGIZhGIZhGIZhGIZhGOaC4bOg9tlnn2HGjBlIT0/Hxo0b0b59ewwePBinTp0CABQWFmLIkCF47LHHqv1gGYZhGIZhGIZhGIZhGKa28VlQe+WVVzB58mRMmjQJrVq1wpw5cxASEoL3338fADB9+nQ88sgj6NatW5UO7MCBAxg1ahSio6MRGhqKzp0749dff9Wsk5iYiOeeew633347wsLC0KRJE7z77rtV2i/DMAzDMAzDMAzDMAzDuMMnQa20tBQbNmzAgAEDnBuwWjFgwACsWbOmWg8sPz8f1157LZYuXYpNmzZhyJAhGDFiBI4cOaJZ7+WXX0anTp2wadMmTJkyBffeey/27NlTrcfCMAzDMAzDMAzDMAzDMAKfBLUzZ87AbrcjOjpaszw6OhqZmZnVemDt27fH3XffjTZt2iA5ORnPPPMMmjdvjm+//Vaz3rXXXospU6YgKSkJ//znP1G/fn0sX768Wo+FYRiGYRiGYRiGYRiGYQQXvMvn0KFDERoaitDQULRu3dp0vfz8fDz00ENITU1FZGQkQkNDsWvXLpcItXbt2qmvLRYLYmJi1HpuDMMwDMMwDMMwDMMwDFPd+Pmycv369WGz2ZCVlaVZnpWVhZiYGK+2MXfuXBQVFQEA/P39Tdd76KGH8Msvv+Cll15CUlISgoODcf3116O0tFSznn4bFosFDofDq2NhGIZhGIZhGIZhGIZhGF/xSVALCAhAx44dsXTpUowePRoA4HA4sHTpUkybNs2rbcTFxXm13h9//IGJEydizJgxAChiLSMjw5fDZRiGYRiGYRiGYRiGYZhqxydBDQBmzJiBCRMmoFOnTujSpQteffVVFBQUYNKkSQCAzMxMZGZmYv/+/QCAbdu2qR04o6KivN5PcnIyFi5ciBEjRsBiseCJJ57gyDOGYRiGYRiGYRiGYRim1vFZUBs3bhxOnz6NJ598EpmZmUhLS8OSJUvURgVz5szBzJkz1fV79eoFAJg3bx4mTpxoul2HwwE/P+fhvPLKK7j99tvRo0cP1K9fH//85z+Rm5vr6+EyDMMwDMMwDMMwDMMwTLViURRFqe2DAIDnn38eH330EbZv317bh8IwDMMwDMMwDMMwDMMwpvgcoVbdFBYWYvfu3Zg3bx6GDh1a24fDMAzDMAzDMAzDMAzDMG6x1vYBvPvuuxgwYADat2+PJ598srYPh2EYhmEYhmEYhmEYhmHcctGkfDIMwzAMwzAMwzAMwzDMpUCtR6gxDMMwDMMwDMMwDMMwzKUEC2oMwzAMwzAMwzAMwzAM4wNVFtRmzZqFzp07IywsDA0bNsTo0aOxZ88ezTrFxcWYOnUq6tWrh9DQUIwdOxZZWVnq+1u2bMHNN9+M+Ph4BAcHIzU1FbNnz9ZsY+HChRg4cCAaNGiA8PBwdO/eHT/99FNVD59hGIZhGIZhGIZhGIZhfKLKgtrKlSsxdepU/Pnnn/jll19QVlaGQYMGoaCgQF3ngQcewHfffYcvvvgCK1euxIkTJ3Ddddep72/YsAENGzbERx99hB07duD//u//8Oijj+KNN95Q1/ntt98wcOBA/PDDD9iwYQP69u2LESNGYNOmTVU9BYZhGIZhGIZhGIZhGIbxmmpvSnD69Gk0bNgQK1euRK9evZCTk4MGDRrgk08+wfXXXw8A2L17N1JTU7FmzRp069bNcDtTp07Frl27sGzZMtN9tW7dGuPGjePuoAzDMAzDMAzDMAzDMMwFo9prqOXk5AAAoqKiAFD0WVlZGQYMGKCu07JlSzRp0gRr1qxxux2xDSMcDgfy8vLcrsMwDMMwDMMwDMMwDMMw1Y1fdW7M4XBg+vTpuPrqq9GmTRsAQGZmJgICAhAZGalZNzo6GpmZmYbbWb16NT777DMsXrzYdF8vvfQS8vPzceONN1bb8TMMwzAMwzAMwzAMwzCMJ6pVUJs6dSq2b9+OVatWVXob27dvx6hRo5Ceno5BgwYZrvPJJ59g5syZ+Oabb9CwYcNK74thGIZhGIZhGIZhGIZhfKXaUj6nTZuG77//HsuXL0fjxo3V5TExMSgtLUV2drZm/aysLMTExGiW7dy5E/3798ddd92Fxx9/3HA/CxYswJ133onPP/9ck0bKMAzDMAzDMAzDMAzDMBeCKgtqiqJg2rRp+Prrr7Fs2TI0bdpU837Hjh3h7++PpUuXqsv27NmDI0eOoHv37uqyHTt2oG/fvpgwYQKeffZZw319+umnmDRpEj799FMMGzasqofOMAzDMAzDMAzDMAzDMD5T5S6fU6ZMwSeffIJvvvkGLVq0UJdHREQgODgYAHDvvffihx9+wPz58xEeHo777rsPANVKAyjNs1+/fhg8eDBefPFFdRs2mw0NGjQAQGmeEyZMwOzZs3Hdddep6wQHByMiIqIqp8AwDMMwDMMwDMMwDMMwXlNlQc1isRgunzdvHiZOnAgAKC4uxoMPPohPP/0UJSUlGDx4MN566y015fOpp57CzJkzXbaRkJCAjIwMAECfPn2wcuVKl3UmTJiA+fPnV+UUGIZhGIZhGIZhGIZhGMZrqiyoMQzDMAzDMAzDMAzDMMyVRLU1JWAYhmEYhmEYhmEYhmGYKwEW1BiGYRiGYRiGYRiGYRjGB1hQYxiGYRiGYRiGYRiGYRgfYEGNYRiGYRiGYRiGYRiGYXyABTWGYRiGYRiGYRiGYRiG8QEW1BiGYRiGYRiGYRiGYRjGB1hQYxiGYRiGYRiGYRiGYRgfYEGNYRiGYRiGYRiGYRiGYXyABTWGYRiGYRgAK1asgMViQXZ2dm0fCsMwDMMwDHORw4IawzAMwzBXJH369MH06dPVv3v06IGTJ08iIiKi1o6JRT2GYRiGYZhLA7/aPgCGYRiGYZiLgYCAAMTExNT2YTAMwzAMwzCXAByhxjAMwzDMFcfEiROxcuVKzJ49GxaLBRaLBfPnz9dEh82fPx+RkZH4/vvv0aJFC4SEhOD6669HYWEhPvjgAyQmJqJu3br4+9//Drvdrm67pKQEDz30EOLi4lCnTh107doVK1asUN8/fPgwRowYgbp166JOnTpo3bo1fvjhB2RkZKBv374AgLp168JisWDixIkAgCVLluCaa65BZGQk6tWrh+HDh+PAgQPqNjMyMmCxWPD555+jZ8+eCA4ORufOnbF3716sX78enTp1QmhoKIYOHYrTp09rvofRo0dj5syZaNCgAcLDw3HPPfegtLS05r58hmEYhmGYywCOUGMYhmEY5opj9uzZ2Lt3L9q0aYOnn34aALBjxw6X9QoLC/Haa69hwYIFyMvLw3XXXYcxY8YgMjISP/zwAw4ePIixY8fi6quvxrhx4wAA06ZNw86dO7FgwQLExsbi66+/xpAhQ7Bt2zYkJydj6tSpKC0txW+//YY6depg586dCA0NRXx8PL766iuMHTsWe/bsQXh4OIKDgwEABQUFmDFjBtq1a4f8/Hw8+eSTGDNmDDZv3gyr1ekfTU9Px6uvvoomTZrg9ttvxy233IKwsDDMnj0bISEhuPHGG/Hkk0/i7bffVj+zdOlSBAUFYcWKFcjIyMCkSZNQr149PPvsszX5EzAMwzAMw1zSsKDGMAzDMMwVR0REBAICAhASEqKmee7evdtlvbKyMrz99tto3rw5AOD666/H//73P2RlZSE0NBStWrVC3759sXz5cowbNw5HjhzBvHnzcOTIEcTGxgIAHnroISxZsgTz5s3Dc889hyNHjmDs2LFo27YtAKBZs2bq/qKiogAADRs2RGRkpLp87NixmuN6//330aBBA+zcuRNt2rRRlz/00EMYPHgwAOD+++/HzTffjKVLl+Lqq68GANxxxx2YP3++ZlsBAQF4//33ERISgtatW+Ppp5/GP/7xDzzzzDMasY5hGIZhGIZxwlYSwzAMwzCMCSEhIaqYBgDR0dFITExEaGioZtmpU6cAANu2bYPdbkdKSgpCQ0PVfytXrlRTNP/+97/jX//6F66++mqkp6dj69atHo9j3759uPnmm9GsWTOEh4cjMTERAHDkyBHNeu3atdMcFwBVuNMfq6B9+/YICQlR/+7evTvy8/Nx9OhRj8fFMAzDMAxzpcIRagzDMAzDMCb4+/tr/rZYLIbLHA4HACA/Px82mw0bNmyAzWbTrCdEuDvvvBODBw/G4sWL8fPPP2PWrFl4+eWXcd9995kex4gRI5CQkID//ve/iI2NhcPhQJs2bVxqncnHZrFYDJeJY2UYhmEYhmEqD0eoMQzDMAxzRRIQEKBpJlAddOjQAXa7HadOnUJSUpLmn9xBND4+Hvfccw8WLlyIBx98EP/973/VYwKgOa6zZ89iz549ePzxx9G/f3+kpqbi/Pnz1XbMW7ZsQVFRkfr3n3/+qdZ0YxiGYRiGYYxhQY1hGIZhmCuSxMRErF27FhkZGThz5ky1RG6lpKTg1ltvxfjx47Fw4UIcOnQI69atw6xZs7B48WIAwPTp0/HTTz/h0KFD2LhxI5YvX47U1FQAQEJCAiwWC77//nucPn0a+fn5qFu3LurVq4d3330X+/fvx7JlyzBjxowqH6ugtLQUd9xxB3bu3IkffvgB6enpmDZtGtdPYxiGYRiGcQNbSgzDMAzDXJE89NBDsNlsaNWqFRo0aOBSj6yyzJs3D+PHj8eDDz6IFi1aYPTo0Vi/fj2aNGkCgKLPpk6ditTUVAwZMgQpKSl46623AABxcXGYOXMmHnnkEURHR6vC1oIFC7Bhwwa0adMGDzzwAF588cVqOVYA6N+/P5KTk9GrVy+MGzcOI0eOxFNPPVVt22cYhmEYhrkcsSiKotT2QTAMwzAMwzAXnokTJyI7OxuLFi2q7UNhGIZhGIa5pOAINYZhGIZhGIZhGIZhGIbxARbUGIZhGIZhGIZhGIZhGMYHOOWTYRiGYRiGYRiGYRiGYXyAI9QYhmEYhmEYhmEYhmEYxgdYUGMYhmEYhmEYhmEYhmEYH2BBjWEYhmEYhmEYhmEYhmF8gAU1hmEYhmEYhmEYhmEYhvEBFtQYhmEYhmEYhmEYhmEYxgdYUGMYhmEYhmEYhmEYhmEYH2BBjWEYhmEYhmEYhmEYhmF8gAU1hmEYhmEYhmEYhmEYhvEBFtQYhmEYhmEYhmEYhmEYxgf+H4bWLgqI1NgHAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1500x300 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["fig, ax = plt.subplots(1, 1, figsize=(15, 3))\n", "ax2 = ax.twinx()\n", "result_test.set_index(\"timestamp\").x.plot(ax=ax, color=\"gray\", linewidth=0.5)\n", "result_test.set_index(\"timestamp\").anomaly_score.plot(ax=ax2, color=\"orange\", label=\"Anomaly Score\")\n", "result_test.set_index(\"timestamp\").y.plot(ax=ax2, color=\"red\", label=\"Anomaly Event\")\n", "ax2.legend()\n", "# ax2.set_yticks([])\n", "ax.set_title(\"Anomaly Detection Pipeline\", fontsize=16)\n", "for s in [\"top\", \"right\"]:\n", "    ax.spines[s].set_visible(False)\n", "    ax2.spines[s].set_visible(False)"]}, {"cell_type": "code", "execution_count": null, "id": "07eb236b-ef07-4e21-959d-04666cd6d8f0", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d23f17a0-2b88-4dea-a854-0e3ff913eff2", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 5}