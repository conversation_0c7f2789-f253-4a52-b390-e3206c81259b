{"cells": [{"cell_type": "markdown", "id": "f24ac339", "metadata": {}, "source": ["# Classification dataset and preprocessor usage\n", "\n", "This notebook demonstrates some of the capabilities of the classification dataset and preprocessor components."]}, {"cell_type": "code", "execution_count": 1, "id": "dcffab86", "metadata": {}, "outputs": [], "source": ["import numpy as np\n", "\n", "from tsfm_public.toolkit.dataset import ClassificationDFDataset\n", "from tsfm_public.toolkit.time_series_classification_preprocessor import TimeSeriesClassificationPreprocessor\n", "from tsfm_public.toolkit.util import convert_tsfile_to_dataframe"]}, {"cell_type": "markdown", "id": "646d387a", "metadata": {}, "source": ["## Case 1: Nested dataset\n", "Dataset contains entries which are pandas series"]}, {"cell_type": "code", "execution_count": 2, "id": "15fd0d93", "metadata": {}, "outputs": [], "source": ["path = \"/Users/<USER>/Downloads/BasicMotions/BasicMotions_TRAIN.ts\""]}, {"cell_type": "code", "execution_count": 3, "id": "9ec815c4", "metadata": {}, "outputs": [], "source": ["df = convert_tsfile_to_dataframe(\n", "    path,\n", "    return_separate_X_and_y=False,\n", ")\n", "label_column = \"class_vals\"\n", "input_columns = [f\"dim_{i}\" for i in range(6)]"]}, {"cell_type": "code", "execution_count": 4, "id": "f7f92f5f-55f3-4a12-8a67-fc51de315b3a", "metadata": {}, "outputs": [], "source": ["df[\"id\"] = range(df.shape[0])"]}, {"cell_type": "code", "execution_count": 5, "id": "6dee3f1e", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "      <th>class_vals</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0     0.079106\n", "1     0.079106\n", "2    -0.903497\n", "3...</td>\n", "      <td>0     0.394032\n", "1     0.394032\n", "2    -3.666397\n", "3...</td>\n", "      <td>0     0.551444\n", "1     0.551444\n", "2    -0.282844\n", "3...</td>\n", "      <td>0     0.351565\n", "1     0.351565\n", "2    -0.095881\n", "3...</td>\n", "      <td>0     0.023970\n", "1     0.023970\n", "2    -0.319605\n", "3...</td>\n", "      <td>0     0.633883\n", "1     0.633883\n", "2     0.972131\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0     0.377751\n", "1     0.377751\n", "2     2.952965\n", "3...</td>\n", "      <td>0    -0.610850\n", "1    -0.610850\n", "2     0.970717\n", "3...</td>\n", "      <td>0    -0.147376\n", "1    -0.147376\n", "2    -5.962515\n", "3...</td>\n", "      <td>0    -0.103872\n", "1    -0.103872\n", "2    -7.593275\n", "3...</td>\n", "      <td>0    -0.109198\n", "1    -0.109198\n", "2    -0.697804\n", "3...</td>\n", "      <td>0    -0.037287\n", "1    -0.037287\n", "2    -2.865789\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3...</td>\n", "      <td>0     0.825666\n", "1     0.825666\n", "2    -1.305033\n", "3...</td>\n", "      <td>0     0.032712\n", "1     0.032712\n", "2     0.826170\n", "3...</td>\n", "      <td>0     0.021307\n", "1     0.021307\n", "2    -0.372872\n", "3...</td>\n", "      <td>0     0.122515\n", "1     0.122515\n", "2    -0.045277\n", "3...</td>\n", "      <td>0     0.775041\n", "1     0.775041\n", "2     0.383526\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0     0.289855\n", "1     0.289855\n", "2    -0.669185\n", "3...</td>\n", "      <td>0     0.284130\n", "1     0.284130\n", "2    -0.210466\n", "3...</td>\n", "      <td>0     0.213680\n", "1     0.213680\n", "2     0.252267\n", "3...</td>\n", "      <td>0    -0.314278\n", "1    -0.314278\n", "2     0.018644\n", "3...</td>\n", "      <td>0     0.074574\n", "1     0.074574\n", "2     0.007990\n", "3...</td>\n", "      <td>0    -0.079901\n", "1    -0.079901\n", "2     0.237040\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0    -0.123238\n", "1    -0.123238\n", "2    -0.249547\n", "3...</td>\n", "      <td>0     0.379341\n", "1     0.379341\n", "2     0.541501\n", "3...</td>\n", "      <td>0    -0.286006\n", "1    -0.286006\n", "2     0.208420\n", "3...</td>\n", "      <td>0    -0.098545\n", "1    -0.098545\n", "2    -0.023970\n", "3...</td>\n", "      <td>0     0.058594\n", "1     0.058594\n", "2     0.175783\n", "3...</td>\n", "      <td>0    -0.074574\n", "1    -0.074574\n", "2     0.114525\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               dim_0  \\\n", "0  0     0.079106\n", "1     0.079106\n", "2    -0.903497\n", "3...   \n", "1  0     0.377751\n", "1     0.377751\n", "2     2.952965\n", "3...   \n", "2  0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3...   \n", "3  0     0.289855\n", "1     0.289855\n", "2    -0.669185\n", "3...   \n", "4  0    -0.123238\n", "1    -0.123238\n", "2    -0.249547\n", "3...   \n", "\n", "                                               dim_1  \\\n", "0  0     0.394032\n", "1     0.394032\n", "2    -3.666397\n", "3...   \n", "1  0    -0.610850\n", "1    -0.610850\n", "2     0.970717\n", "3...   \n", "2  0     0.825666\n", "1     0.825666\n", "2    -1.305033\n", "3...   \n", "3  0     0.284130\n", "1     0.284130\n", "2    -0.210466\n", "3...   \n", "4  0     0.379341\n", "1     0.379341\n", "2     0.541501\n", "3...   \n", "\n", "                                               dim_2  \\\n", "0  0     0.551444\n", "1     0.551444\n", "2    -0.282844\n", "3...   \n", "1  0    -0.147376\n", "1    -0.147376\n", "2    -5.962515\n", "3...   \n", "2  0     0.032712\n", "1     0.032712\n", "2     0.826170\n", "3...   \n", "3  0     0.213680\n", "1     0.213680\n", "2     0.252267\n", "3...   \n", "4  0    -0.286006\n", "1    -0.286006\n", "2     0.208420\n", "3...   \n", "\n", "                                               dim_3  \\\n", "0  0     0.351565\n", "1     0.351565\n", "2    -0.095881\n", "3...   \n", "1  0    -0.103872\n", "1    -0.103872\n", "2    -7.593275\n", "3...   \n", "2  0     0.021307\n", "1     0.021307\n", "2    -0.372872\n", "3...   \n", "3  0    -0.314278\n", "1    -0.314278\n", "2     0.018644\n", "3...   \n", "4  0    -0.098545\n", "1    -0.098545\n", "2    -0.023970\n", "3...   \n", "\n", "                                               dim_4  \\\n", "0  0     0.023970\n", "1     0.023970\n", "2    -0.319605\n", "3...   \n", "1  0    -0.109198\n", "1    -0.109198\n", "2    -0.697804\n", "3...   \n", "2  0     0.122515\n", "1     0.122515\n", "2    -0.045277\n", "3...   \n", "3  0     0.074574\n", "1     0.074574\n", "2     0.007990\n", "3...   \n", "4  0     0.058594\n", "1     0.058594\n", "2     0.175783\n", "3...   \n", "\n", "                                               dim_5 class_vals  id  \n", "0  0     0.633883\n", "1     0.633883\n", "2     0.972131\n", "3...   standing   0  \n", "1  0    -0.037287\n", "1    -0.037287\n", "2    -2.865789\n", "3...   standing   1  \n", "2  0     0.775041\n", "1     0.775041\n", "2     0.383526\n", "3...   standing   2  \n", "3  0    -0.079901\n", "1    -0.079901\n", "2     0.237040\n", "3...   standing   3  \n", "4  0    -0.074574\n", "1    -0.074574\n", "2     0.114525\n", "3...   standing   4  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 6, "id": "73c34ec7", "metadata": {}, "outputs": [{"data": {"text/plain": ["TimeSeriesClassificationPreprocessor {\n", "  \"_is_nested\": true,\n", "  \"categorical_encoder\": null,\n", "  \"context_length\": 64,\n", "  \"encode_categorical\": true,\n", "  \"feature_extractor_type\": \"TimeSeriesClassificationPreprocessor\",\n", "  \"freq\": null,\n", "  \"id_columns\": [],\n", "  \"input_columns\": [\n", "    \"dim_0\",\n", "    \"dim_1\",\n", "    \"dim_2\",\n", "    \"dim_3\",\n", "    \"dim_4\",\n", "    \"dim_5\"\n", "  ],\n", "  \"label_column\": \"class_vals\",\n", "  \"label_encoder\": {\n", "    \"classes_\": [\n", "      \"badminton\",\n", "      \"running\",\n", "      \"standing\",\n", "      \"walking\"\n", "    ]\n", "  },\n", "  \"processor_class\": \"TimeSeriesClassificationPreprocessor\",\n", "  \"scale_categorical_columns\": true,\n", "  \"scaler_dict\": {\n", "    \"0\": {\n", "      \"copy\": true,\n", "      \"feature_names_in_\": [\n", "        \"dim_0\",\n", "        \"dim_1\",\n", "        \"dim_2\",\n", "        \"dim_3\",\n", "        \"dim_4\",\n", "        \"dim_5\"\n", "      ],\n", "      \"mean_\": [\n", "        2.552759629,\n", "        -1.3039367667500004,\n", "        -1.02658049575,\n", "        0.01905110399999999,\n", "        -0.023957696500000004,\n", "        -0.055789663749999996\n", "      ],\n", "      \"n_features_in_\": 6,\n", "      \"n_samples_seen_\": 4000,\n", "      \"scale_\": [\n", "        7.072305651552695,\n", "        6.79408755945826,\n", "        3.5463734763579375,\n", "        2.111920179892119,\n", "        1.8207506409016112,\n", "        3.516585934128164\n", "      ],\n", "      \"var_\": [\n", "        50.0175072289842,\n", "        46.1596257655855,\n", "        12.576764833815083,\n", "        4.46020684623556,\n", "        3.315132896343628,\n", "        12.36637663210805\n", "      ],\n", "      \"with_mean\": true,\n", "      \"with_std\": true\n", "    }\n", "  },\n", "  \"scaler_type\": \"standard\",\n", "  \"scaling\": true,\n", "  \"scaling_id_columns\": [],\n", "  \"scaling_id_columns_types\": [],\n", "  \"static_categorical_columns\": [],\n", "  \"time_series_task\": \"classification\",\n", "  \"timestamp_column\": null\n", "}"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["tsp = TimeSeriesClassificationPreprocessor(\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    scaling=True,\n", ")\n", "\n", "tsp.train(df)"]}, {"cell_type": "code", "execution_count": 7, "id": "b640bcde", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "      <th>class_vals</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0    -0.349766\n", "1    -0.349766\n", "2    -0.488703\n", "3...</td>\n", "      <td>0     0.249919\n", "1     0.249919\n", "2    -0.347723\n", "3...</td>\n", "      <td>0     0.444969\n", "1     0.444969\n", "2     0.209717\n", "3...</td>\n", "      <td>0     0.157446\n", "1     0.157446\n", "2    -0.054421\n", "3...</td>\n", "      <td>0     0.026323\n", "1     0.026323\n", "2    -0.162377\n", "3...</td>\n", "      <td>0     0.196120\n", "1     0.196120\n", "2     0.292306\n", "3...</td>\n", "      <td>2</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0    -0.307539\n", "1    -0.307539\n", "2     0.056588\n", "3...</td>\n", "      <td>0     0.102013\n", "1     0.102013\n", "2     0.334799\n", "3...</td>\n", "      <td>0     0.247916\n", "1     0.247916\n", "2    -1.391826\n", "3...</td>\n", "      <td>0    -0.058204\n", "1    -0.058204\n", "2    -3.604457\n", "3...</td>\n", "      <td>0    -0.046816\n", "1    -0.046816\n", "2    -0.370093\n", "3...</td>\n", "      <td>0     0.005262\n", "1     0.005262\n", "2    -0.799070\n", "3...</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0    -0.476035\n", "1    -0.476035\n", "2    -0.420992\n", "3...</td>\n", "      <td>0     0.313449\n", "1     0.313449\n", "2    -0.000161\n", "3...</td>\n", "      <td>0     0.298697\n", "1     0.298697\n", "2     0.522435\n", "3...</td>\n", "      <td>0     0.001068\n", "1     0.001068\n", "2    -0.185577\n", "3...</td>\n", "      <td>0     0.080446\n", "1     0.080446\n", "2    -0.011709\n", "3...</td>\n", "      <td>0     0.236261\n", "1     0.236261\n", "2     0.124927\n", "3...</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0    -0.319967\n", "1    -0.319967\n", "2    -0.455572\n", "3...</td>\n", "      <td>0     0.233742\n", "1     0.233742\n", "2     0.160944\n", "3...</td>\n", "      <td>0     0.349726\n", "1     0.349726\n", "2     0.360607\n", "3...</td>\n", "      <td>0    -0.157832\n", "1    -0.157832\n", "2    -0.000193\n", "3...</td>\n", "      <td>0     0.054116\n", "1     0.054116\n", "2     0.017546\n", "3...</td>\n", "      <td>0    -0.006856\n", "1    -0.006856\n", "2     0.083271\n", "3...</td>\n", "      <td>2</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0    -0.378377\n", "1    -0.378377\n", "2    -0.396237\n", "3...</td>\n", "      <td>0     0.247756\n", "1     0.247756\n", "2     0.271624\n", "3...</td>\n", "      <td>0     0.208826\n", "1     0.208826\n", "2     0.348243\n", "3...</td>\n", "      <td>0    -0.055682\n", "1    -0.055682\n", "2    -0.020371\n", "3...</td>\n", "      <td>0     0.045339\n", "1     0.045339\n", "2     0.109702\n", "3...</td>\n", "      <td>0    -0.005342\n", "1    -0.005342\n", "2     0.048432\n", "3...</td>\n", "      <td>2</td>\n", "      <td>4</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0    -0.411473\n", "1    -0.411473\n", "2    -0.361666\n", "3...</td>\n", "      <td>0     0.105835\n", "1     0.105835\n", "2     0.235348\n", "3...</td>\n", "      <td>0     0.065935\n", "1     0.065935\n", "2     0.349722\n", "3...</td>\n", "      <td>0     0.026290\n", "1     0.026290\n", "2    -0.083426\n", "3...</td>\n", "      <td>0     0.100925\n", "1     0.100925\n", "2    -0.155063\n", "3...</td>\n", "      <td>0     0.022681\n", "1     0.022681\n", "2     0.365772\n", "3...</td>\n", "      <td>2</td>\n", "      <td>5</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0    -0.186176\n", "1    -0.186176\n", "2    -0.344252\n", "3...</td>\n", "      <td>0     0.108095\n", "1     0.108095\n", "2     0.230886\n", "3...</td>\n", "      <td>0     0.722799\n", "1     0.722799\n", "2     0.490423\n", "3...</td>\n", "      <td>0     0.059079\n", "1     0.059079\n", "2    -0.964946\n", "3...</td>\n", "      <td>0     0.046803\n", "1     0.046803\n", "2    -0.048279\n", "3...</td>\n", "      <td>0     0.273372\n", "1     0.273372\n", "2    -0.265879\n", "3...</td>\n", "      <td>2</td>\n", "      <td>6</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0    -0.412760\n", "1    -0.412760\n", "2    -0.343018\n", "3...</td>\n", "      <td>0     0.240684\n", "1     0.240684\n", "2     0.347964\n", "3...</td>\n", "      <td>0     0.058859\n", "1     0.058859\n", "2     0.370038\n", "3...</td>\n", "      <td>0    -0.088471\n", "1    -0.088471\n", "2     0.071691\n", "3...</td>\n", "      <td>0    -0.038039\n", "1    -0.038039\n", "2    -0.051204\n", "3...</td>\n", "      <td>0     0.060550\n", "1     0.060550\n", "2     0.220356\n", "3...</td>\n", "      <td>2</td>\n", "      <td>7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0    -0.409342\n", "1    -0.409342\n", "2    -0.403164\n", "3...</td>\n", "      <td>0     0.240113\n", "1     0.240113\n", "2     0.114332\n", "3...</td>\n", "      <td>0     0.333808\n", "1     0.333808\n", "2     0.359569\n", "3...</td>\n", "      <td>0     0.177624\n", "1     0.177624\n", "2    -0.026676\n", "3...</td>\n", "      <td>0     0.054116\n", "1     0.054116\n", "2    -0.035114\n", "3...</td>\n", "      <td>0     0.005262\n", "1     0.005262\n", "2     0.001475\n", "3...</td>\n", "      <td>2</td>\n", "      <td>8</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0    -0.418559\n", "1    -0.418559\n", "2    -0.027940\n", "3...</td>\n", "      <td>0     0.399952\n", "1     0.399952\n", "2    -0.386232\n", "3...</td>\n", "      <td>0     0.315636\n", "1     0.315636\n", "2     0.229801\n", "3...</td>\n", "      <td>0    -0.040548\n", "1    -0.040548\n", "2    -1.727920\n", "3...</td>\n", "      <td>0     0.136032\n", "1     0.136032\n", "2    -0.001470\n", "3...</td>\n", "      <td>0     0.054491\n", "1     0.054491\n", "2    -0.537776\n", "3...</td>\n", "      <td>2</td>\n", "      <td>9</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>0    -0.318474\n", "1    -0.318474\n", "2    -0.638795\n", "3...</td>\n", "      <td>0     0.299012\n", "1     0.299012\n", "2     0.147685\n", "3...</td>\n", "      <td>0     0.537256\n", "1     0.537256\n", "2    -0.056490\n", "3...</td>\n", "      <td>0    -0.048116\n", "1    -0.048116\n", "2    -0.307905\n", "3...</td>\n", "      <td>0    -0.017561\n", "1    -0.017561\n", "2     0.035100\n", "3...</td>\n", "      <td>0     0.205966\n", "1     0.205966\n", "2     0.052976\n", "3...</td>\n", "      <td>1</td>\n", "      <td>10</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>0    -0.089960\n", "1    -0.089960\n", "2    -0.998225\n", "3...</td>\n", "      <td>0     0.123345\n", "1     0.123345\n", "2    -0.793007\n", "3...</td>\n", "      <td>0     0.183900\n", "1     0.183900\n", "2    -0.085564\n", "3...</td>\n", "      <td>0     0.050252\n", "1     0.050252\n", "2     0.326436\n", "3...</td>\n", "      <td>0    -0.005858\n", "1    -0.005858\n", "2    -0.017561\n", "3...</td>\n", "      <td>0    -0.049269\n", "1    -0.049269\n", "2     0.614190\n", "3...</td>\n", "      <td>1</td>\n", "      <td>11</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>0    -0.046776\n", "1    -0.046776\n", "2    -1.450295\n", "3...</td>\n", "      <td>0     0.076581\n", "1     0.076581\n", "2    -0.480710\n", "3...</td>\n", "      <td>0     0.329627\n", "1     0.329627\n", "2     0.979577\n", "3...</td>\n", "      <td>0     0.017463\n", "1     0.017463\n", "2    -0.218366\n", "3...</td>\n", "      <td>0     0.052653\n", "1     0.052653\n", "2     0.019009\n", "3...</td>\n", "      <td>0     0.080242\n", "1     0.080242\n", "2    -0.546865\n", "3...</td>\n", "      <td>1</td>\n", "      <td>12</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>0    -0.154008\n", "1    -0.154008\n", "2     0.511373\n", "3...</td>\n", "      <td>0     0.454348\n", "1     0.454348\n", "2     1.383984\n", "3...</td>\n", "      <td>0     0.058959\n", "1     0.058959\n", "2    -1.297585\n", "3...</td>\n", "      <td>0     0.030074\n", "1     0.030074\n", "2    -1.273918\n", "3...</td>\n", "      <td>0     0.100925\n", "1     0.100925\n", "2     0.168214\n", "3...</td>\n", "      <td>0     0.043130\n", "1     0.043130\n", "2    -0.411294\n", "3...</td>\n", "      <td>1</td>\n", "      <td>13</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>0    -0.193245\n", "1    -0.193245\n", "2    -0.357349\n", "3...</td>\n", "      <td>0     0.193869\n", "1     0.193869\n", "2     0.475447\n", "3...</td>\n", "      <td>0     0.182922\n", "1     0.182922\n", "2    -0.064091\n", "3...</td>\n", "      <td>0     0.165013\n", "1     0.165013\n", "2     0.095652\n", "3...</td>\n", "      <td>0    -0.125807\n", "1    -0.125807\n", "2    -0.261846\n", "3...</td>\n", "      <td>0     0.048432\n", "1     0.048432\n", "2    -0.033365\n", "3...</td>\n", "      <td>1</td>\n", "      <td>14</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>0    -0.411758\n", "1    -0.411758\n", "2     0.206296\n", "3...</td>\n", "      <td>0     0.214415\n", "1     0.214415\n", "2     0.346293\n", "3...</td>\n", "      <td>0     0.271264\n", "1     0.271264\n", "2    -0.219470\n", "3...</td>\n", "      <td>0     0.009896\n", "1     0.009896\n", "2    -0.647145\n", "3...</td>\n", "      <td>0    -0.010246\n", "1    -0.010246\n", "2     0.327658\n", "3...</td>\n", "      <td>0     0.051461\n", "1     0.051461\n", "2    -0.513540\n", "3...</td>\n", "      <td>1</td>\n", "      <td>15</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>0    -0.167171\n", "1    -0.167171\n", "2     0.909937\n", "3...</td>\n", "      <td>0     0.036744\n", "1     0.036744\n", "2     1.327765\n", "3...</td>\n", "      <td>0     0.162186\n", "1     0.162186\n", "2    -1.423233\n", "3...</td>\n", "      <td>0    -0.154049\n", "1    -0.154049\n", "2     0.207891\n", "3...</td>\n", "      <td>0    -0.219425\n", "1    -0.219425\n", "2     0.431516\n", "3...</td>\n", "      <td>0     0.099176\n", "1     0.099176\n", "2     0.630095\n", "3...</td>\n", "      <td>1</td>\n", "      <td>16</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>0    -0.315076\n", "1    -0.315076\n", "2     0.953248\n", "3...</td>\n", "      <td>0     0.048045\n", "1     0.048045\n", "2    -0.832972\n", "3...</td>\n", "      <td>0    -0.065881\n", "1    -0.065881\n", "2    -0.415047\n", "3...</td>\n", "      <td>0    -0.382310\n", "1    -0.382310\n", "2     1.143638\n", "3...</td>\n", "      <td>0     0.187230\n", "1     0.187230\n", "2    -0.030725\n", "3...</td>\n", "      <td>0     0.183244\n", "1     0.183244\n", "2     1.874462\n", "3...</td>\n", "      <td>1</td>\n", "      <td>17</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>0    -0.226383\n", "1    -0.226383\n", "2     0.519592\n", "3...</td>\n", "      <td>0    -0.000135\n", "1    -0.000135\n", "2     0.011994\n", "3...</td>\n", "      <td>0     0.023022\n", "1     0.023022\n", "2     0.481881\n", "3...</td>\n", "      <td>0    -0.191882\n", "1    -0.191882\n", "2    -0.172965\n", "3...</td>\n", "      <td>0     0.182841\n", "1     0.182841\n", "2     0.176991\n", "3...</td>\n", "      <td>0     0.043888\n", "1     0.043888\n", "2    -0.384786\n", "3...</td>\n", "      <td>1</td>\n", "      <td>18</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>0    -0.006307\n", "1    -0.006307\n", "2    -0.060951\n", "3...</td>\n", "      <td>0     0.082172\n", "1     0.082172\n", "2     0.450232\n", "3...</td>\n", "      <td>0     0.726269\n", "1     0.726269\n", "2    -0.290665\n", "3...</td>\n", "      <td>0     0.171318\n", "1     0.171318\n", "2    -0.528600\n", "3...</td>\n", "      <td>0    -0.226739\n", "1    -0.226739\n", "2     0.367153\n", "3...</td>\n", "      <td>0     0.184002\n", "1     0.184002\n", "2    -0.257548\n", "3...</td>\n", "      <td>1</td>\n", "      <td>19</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                dim_0  \\\n", "0   0    -0.349766\n", "1    -0.349766\n", "2    -0.488703\n", "3...   \n", "1   0    -0.307539\n", "1    -0.307539\n", "2     0.056588\n", "3...   \n", "2   0    -0.476035\n", "1    -0.476035\n", "2    -0.420992\n", "3...   \n", "3   0    -0.319967\n", "1    -0.319967\n", "2    -0.455572\n", "3...   \n", "4   0    -0.378377\n", "1    -0.378377\n", "2    -0.396237\n", "3...   \n", "5   0    -0.411473\n", "1    -0.411473\n", "2    -0.361666\n", "3...   \n", "6   0    -0.186176\n", "1    -0.186176\n", "2    -0.344252\n", "3...   \n", "7   0    -0.412760\n", "1    -0.412760\n", "2    -0.343018\n", "3...   \n", "8   0    -0.409342\n", "1    -0.409342\n", "2    -0.403164\n", "3...   \n", "9   0    -0.418559\n", "1    -0.418559\n", "2    -0.027940\n", "3...   \n", "10  0    -0.318474\n", "1    -0.318474\n", "2    -0.638795\n", "3...   \n", "11  0    -0.089960\n", "1    -0.089960\n", "2    -0.998225\n", "3...   \n", "12  0    -0.046776\n", "1    -0.046776\n", "2    -1.450295\n", "3...   \n", "13  0    -0.154008\n", "1    -0.154008\n", "2     0.511373\n", "3...   \n", "14  0    -0.193245\n", "1    -0.193245\n", "2    -0.357349\n", "3...   \n", "15  0    -0.411758\n", "1    -0.411758\n", "2     0.206296\n", "3...   \n", "16  0    -0.167171\n", "1    -0.167171\n", "2     0.909937\n", "3...   \n", "17  0    -0.315076\n", "1    -0.315076\n", "2     0.953248\n", "3...   \n", "18  0    -0.226383\n", "1    -0.226383\n", "2     0.519592\n", "3...   \n", "19  0    -0.006307\n", "1    -0.006307\n", "2    -0.060951\n", "3...   \n", "\n", "                                                dim_1  \\\n", "0   0     0.249919\n", "1     0.249919\n", "2    -0.347723\n", "3...   \n", "1   0     0.102013\n", "1     0.102013\n", "2     0.334799\n", "3...   \n", "2   0     0.313449\n", "1     0.313449\n", "2    -0.000161\n", "3...   \n", "3   0     0.233742\n", "1     0.233742\n", "2     0.160944\n", "3...   \n", "4   0     0.247756\n", "1     0.247756\n", "2     0.271624\n", "3...   \n", "5   0     0.105835\n", "1     0.105835\n", "2     0.235348\n", "3...   \n", "6   0     0.108095\n", "1     0.108095\n", "2     0.230886\n", "3...   \n", "7   0     0.240684\n", "1     0.240684\n", "2     0.347964\n", "3...   \n", "8   0     0.240113\n", "1     0.240113\n", "2     0.114332\n", "3...   \n", "9   0     0.399952\n", "1     0.399952\n", "2    -0.386232\n", "3...   \n", "10  0     0.299012\n", "1     0.299012\n", "2     0.147685\n", "3...   \n", "11  0     0.123345\n", "1     0.123345\n", "2    -0.793007\n", "3...   \n", "12  0     0.076581\n", "1     0.076581\n", "2    -0.480710\n", "3...   \n", "13  0     0.454348\n", "1     0.454348\n", "2     1.383984\n", "3...   \n", "14  0     0.193869\n", "1     0.193869\n", "2     0.475447\n", "3...   \n", "15  0     0.214415\n", "1     0.214415\n", "2     0.346293\n", "3...   \n", "16  0     0.036744\n", "1     0.036744\n", "2     1.327765\n", "3...   \n", "17  0     0.048045\n", "1     0.048045\n", "2    -0.832972\n", "3...   \n", "18  0    -0.000135\n", "1    -0.000135\n", "2     0.011994\n", "3...   \n", "19  0     0.082172\n", "1     0.082172\n", "2     0.450232\n", "3...   \n", "\n", "                                                dim_2  \\\n", "0   0     0.444969\n", "1     0.444969\n", "2     0.209717\n", "3...   \n", "1   0     0.247916\n", "1     0.247916\n", "2    -1.391826\n", "3...   \n", "2   0     0.298697\n", "1     0.298697\n", "2     0.522435\n", "3...   \n", "3   0     0.349726\n", "1     0.349726\n", "2     0.360607\n", "3...   \n", "4   0     0.208826\n", "1     0.208826\n", "2     0.348243\n", "3...   \n", "5   0     0.065935\n", "1     0.065935\n", "2     0.349722\n", "3...   \n", "6   0     0.722799\n", "1     0.722799\n", "2     0.490423\n", "3...   \n", "7   0     0.058859\n", "1     0.058859\n", "2     0.370038\n", "3...   \n", "8   0     0.333808\n", "1     0.333808\n", "2     0.359569\n", "3...   \n", "9   0     0.315636\n", "1     0.315636\n", "2     0.229801\n", "3...   \n", "10  0     0.537256\n", "1     0.537256\n", "2    -0.056490\n", "3...   \n", "11  0     0.183900\n", "1     0.183900\n", "2    -0.085564\n", "3...   \n", "12  0     0.329627\n", "1     0.329627\n", "2     0.979577\n", "3...   \n", "13  0     0.058959\n", "1     0.058959\n", "2    -1.297585\n", "3...   \n", "14  0     0.182922\n", "1     0.182922\n", "2    -0.064091\n", "3...   \n", "15  0     0.271264\n", "1     0.271264\n", "2    -0.219470\n", "3...   \n", "16  0     0.162186\n", "1     0.162186\n", "2    -1.423233\n", "3...   \n", "17  0    -0.065881\n", "1    -0.065881\n", "2    -0.415047\n", "3...   \n", "18  0     0.023022\n", "1     0.023022\n", "2     0.481881\n", "3...   \n", "19  0     0.726269\n", "1     0.726269\n", "2    -0.290665\n", "3...   \n", "\n", "                                                dim_3  \\\n", "0   0     0.157446\n", "1     0.157446\n", "2    -0.054421\n", "3...   \n", "1   0    -0.058204\n", "1    -0.058204\n", "2    -3.604457\n", "3...   \n", "2   0     0.001068\n", "1     0.001068\n", "2    -0.185577\n", "3...   \n", "3   0    -0.157832\n", "1    -0.157832\n", "2    -0.000193\n", "3...   \n", "4   0    -0.055682\n", "1    -0.055682\n", "2    -0.020371\n", "3...   \n", "5   0     0.026290\n", "1     0.026290\n", "2    -0.083426\n", "3...   \n", "6   0     0.059079\n", "1     0.059079\n", "2    -0.964946\n", "3...   \n", "7   0    -0.088471\n", "1    -0.088471\n", "2     0.071691\n", "3...   \n", "8   0     0.177624\n", "1     0.177624\n", "2    -0.026676\n", "3...   \n", "9   0    -0.040548\n", "1    -0.040548\n", "2    -1.727920\n", "3...   \n", "10  0    -0.048116\n", "1    -0.048116\n", "2    -0.307905\n", "3...   \n", "11  0     0.050252\n", "1     0.050252\n", "2     0.326436\n", "3...   \n", "12  0     0.017463\n", "1     0.017463\n", "2    -0.218366\n", "3...   \n", "13  0     0.030074\n", "1     0.030074\n", "2    -1.273918\n", "3...   \n", "14  0     0.165013\n", "1     0.165013\n", "2     0.095652\n", "3...   \n", "15  0     0.009896\n", "1     0.009896\n", "2    -0.647145\n", "3...   \n", "16  0    -0.154049\n", "1    -0.154049\n", "2     0.207891\n", "3...   \n", "17  0    -0.382310\n", "1    -0.382310\n", "2     1.143638\n", "3...   \n", "18  0    -0.191882\n", "1    -0.191882\n", "2    -0.172965\n", "3...   \n", "19  0     0.171318\n", "1     0.171318\n", "2    -0.528600\n", "3...   \n", "\n", "                                                dim_4  \\\n", "0   0     0.026323\n", "1     0.026323\n", "2    -0.162377\n", "3...   \n", "1   0    -0.046816\n", "1    -0.046816\n", "2    -0.370093\n", "3...   \n", "2   0     0.080446\n", "1     0.080446\n", "2    -0.011709\n", "3...   \n", "3   0     0.054116\n", "1     0.054116\n", "2     0.017546\n", "3...   \n", "4   0     0.045339\n", "1     0.045339\n", "2     0.109702\n", "3...   \n", "5   0     0.100925\n", "1     0.100925\n", "2    -0.155063\n", "3...   \n", "6   0     0.046803\n", "1     0.046803\n", "2    -0.048279\n", "3...   \n", "7   0    -0.038039\n", "1    -0.038039\n", "2    -0.051204\n", "3...   \n", "8   0     0.054116\n", "1     0.054116\n", "2    -0.035114\n", "3...   \n", "9   0     0.136032\n", "1     0.136032\n", "2    -0.001470\n", "3...   \n", "10  0    -0.017561\n", "1    -0.017561\n", "2     0.035100\n", "3...   \n", "11  0    -0.005858\n", "1    -0.005858\n", "2    -0.017561\n", "3...   \n", "12  0     0.052653\n", "1     0.052653\n", "2     0.019009\n", "3...   \n", "13  0     0.100925\n", "1     0.100925\n", "2     0.168214\n", "3...   \n", "14  0    -0.125807\n", "1    -0.125807\n", "2    -0.261846\n", "3...   \n", "15  0    -0.010246\n", "1    -0.010246\n", "2     0.327658\n", "3...   \n", "16  0    -0.219425\n", "1    -0.219425\n", "2     0.431516\n", "3...   \n", "17  0     0.187230\n", "1     0.187230\n", "2    -0.030725\n", "3...   \n", "18  0     0.182841\n", "1     0.182841\n", "2     0.176991\n", "3...   \n", "19  0    -0.226739\n", "1    -0.226739\n", "2     0.367153\n", "3...   \n", "\n", "                                                dim_5  class_vals  id  \n", "0   0     0.196120\n", "1     0.196120\n", "2     0.292306\n", "3...           2   0  \n", "1   0     0.005262\n", "1     0.005262\n", "2    -0.799070\n", "3...           2   1  \n", "2   0     0.236261\n", "1     0.236261\n", "2     0.124927\n", "3...           2   2  \n", "3   0    -0.006856\n", "1    -0.006856\n", "2     0.083271\n", "3...           2   3  \n", "4   0    -0.005342\n", "1    -0.005342\n", "2     0.048432\n", "3...           2   4  \n", "5   0     0.022681\n", "1     0.022681\n", "2     0.365772\n", "3...           2   5  \n", "6   0     0.273372\n", "1     0.273372\n", "2    -0.265879\n", "3...           2   6  \n", "7   0     0.060550\n", "1     0.060550\n", "2     0.220356\n", "3...           2   7  \n", "8   0     0.005262\n", "1     0.005262\n", "2     0.001475\n", "3...           2   8  \n", "9   0     0.054491\n", "1     0.054491\n", "2    -0.537776\n", "3...           2   9  \n", "10  0     0.205966\n", "1     0.205966\n", "2     0.052976\n", "3...           1  10  \n", "11  0    -0.049269\n", "1    -0.049269\n", "2     0.614190\n", "3...           1  11  \n", "12  0     0.080242\n", "1     0.080242\n", "2    -0.546865\n", "3...           1  12  \n", "13  0     0.043130\n", "1     0.043130\n", "2    -0.411294\n", "3...           1  13  \n", "14  0     0.048432\n", "1     0.048432\n", "2    -0.033365\n", "3...           1  14  \n", "15  0     0.051461\n", "1     0.051461\n", "2    -0.513540\n", "3...           1  15  \n", "16  0     0.099176\n", "1     0.099176\n", "2     0.630095\n", "3...           1  16  \n", "17  0     0.183244\n", "1     0.183244\n", "2     1.874462\n", "3...           1  17  \n", "18  0     0.043888\n", "1     0.043888\n", "2    -0.384786\n", "3...           1  18  \n", "19  0     0.184002\n", "1     0.184002\n", "2    -0.257548\n", "3...           1  19  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_prep = tsp.preprocess(df)\n", "df_prep.head(20)"]}, {"cell_type": "code", "execution_count": 8, "id": "4ca285c9", "metadata": {}, "outputs": [], "source": ["from tsfm_public.toolkit.time_series_classification_preprocessor import nest_transform, unnest_transform\n", "\n", "\n", "u = unnest_transform(df, columns=input_columns)\n", "n = nest_transform(u, columns=input_columns)"]}, {"cell_type": "code", "execution_count": 9, "id": "d137c215", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "      <th>class_vals</th>\n", "      <th>id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0     0.079106\n", "1     0.079106\n", "2    -0.903497\n", "3...</td>\n", "      <td>0     0.394032\n", "1     0.394032\n", "2    -3.666397\n", "3...</td>\n", "      <td>0     0.551444\n", "1     0.551444\n", "2    -0.282844\n", "3...</td>\n", "      <td>0     0.351565\n", "1     0.351565\n", "2    -0.095881\n", "3...</td>\n", "      <td>0     0.023970\n", "1     0.023970\n", "2    -0.319605\n", "3...</td>\n", "      <td>0     0.633883\n", "1     0.633883\n", "2     0.972131\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0     0.377751\n", "1     0.377751\n", "2     2.952965\n", "3...</td>\n", "      <td>0    -0.610850\n", "1    -0.610850\n", "2     0.970717\n", "3...</td>\n", "      <td>0    -0.147376\n", "1    -0.147376\n", "2    -5.962515\n", "3...</td>\n", "      <td>0    -0.103872\n", "1    -0.103872\n", "2    -7.593275\n", "3...</td>\n", "      <td>0    -0.109198\n", "1    -0.109198\n", "2    -0.697804\n", "3...</td>\n", "      <td>0    -0.037287\n", "1    -0.037287\n", "2    -2.865789\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3...</td>\n", "      <td>0     0.825666\n", "1     0.825666\n", "2    -1.305033\n", "3...</td>\n", "      <td>0     0.032712\n", "1     0.032712\n", "2     0.826170\n", "3...</td>\n", "      <td>0     0.021307\n", "1     0.021307\n", "2    -0.372872\n", "3...</td>\n", "      <td>0     0.122515\n", "1     0.122515\n", "2    -0.045277\n", "3...</td>\n", "      <td>0     0.775041\n", "1     0.775041\n", "2     0.383526\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0     0.289855\n", "1     0.289855\n", "2    -0.669185\n", "3...</td>\n", "      <td>0     0.284130\n", "1     0.284130\n", "2    -0.210466\n", "3...</td>\n", "      <td>0     0.213680\n", "1     0.213680\n", "2     0.252267\n", "3...</td>\n", "      <td>0    -0.314278\n", "1    -0.314278\n", "2     0.018644\n", "3...</td>\n", "      <td>0     0.074574\n", "1     0.074574\n", "2     0.007990\n", "3...</td>\n", "      <td>0    -0.079901\n", "1    -0.079901\n", "2     0.237040\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0    -0.123238\n", "1    -0.123238\n", "2    -0.249547\n", "3...</td>\n", "      <td>0     0.379341\n", "1     0.379341\n", "2     0.541501\n", "3...</td>\n", "      <td>0    -0.286006\n", "1    -0.286006\n", "2     0.208420\n", "3...</td>\n", "      <td>0    -0.098545\n", "1    -0.098545\n", "2    -0.023970\n", "3...</td>\n", "      <td>0     0.058594\n", "1     0.058594\n", "2     0.175783\n", "3...</td>\n", "      <td>0    -0.074574\n", "1    -0.074574\n", "2     0.114525\n", "3...</td>\n", "      <td>standing</td>\n", "      <td>4</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               dim_0  \\\n", "0  0     0.079106\n", "1     0.079106\n", "2    -0.903497\n", "3...   \n", "1  0     0.377751\n", "1     0.377751\n", "2     2.952965\n", "3...   \n", "2  0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3...   \n", "3  0     0.289855\n", "1     0.289855\n", "2    -0.669185\n", "3...   \n", "4  0    -0.123238\n", "1    -0.123238\n", "2    -0.249547\n", "3...   \n", "\n", "                                               dim_1  \\\n", "0  0     0.394032\n", "1     0.394032\n", "2    -3.666397\n", "3...   \n", "1  0    -0.610850\n", "1    -0.610850\n", "2     0.970717\n", "3...   \n", "2  0     0.825666\n", "1     0.825666\n", "2    -1.305033\n", "3...   \n", "3  0     0.284130\n", "1     0.284130\n", "2    -0.210466\n", "3...   \n", "4  0     0.379341\n", "1     0.379341\n", "2     0.541501\n", "3...   \n", "\n", "                                               dim_2  \\\n", "0  0     0.551444\n", "1     0.551444\n", "2    -0.282844\n", "3...   \n", "1  0    -0.147376\n", "1    -0.147376\n", "2    -5.962515\n", "3...   \n", "2  0     0.032712\n", "1     0.032712\n", "2     0.826170\n", "3...   \n", "3  0     0.213680\n", "1     0.213680\n", "2     0.252267\n", "3...   \n", "4  0    -0.286006\n", "1    -0.286006\n", "2     0.208420\n", "3...   \n", "\n", "                                               dim_3  \\\n", "0  0     0.351565\n", "1     0.351565\n", "2    -0.095881\n", "3...   \n", "1  0    -0.103872\n", "1    -0.103872\n", "2    -7.593275\n", "3...   \n", "2  0     0.021307\n", "1     0.021307\n", "2    -0.372872\n", "3...   \n", "3  0    -0.314278\n", "1    -0.314278\n", "2     0.018644\n", "3...   \n", "4  0    -0.098545\n", "1    -0.098545\n", "2    -0.023970\n", "3...   \n", "\n", "                                               dim_4  \\\n", "0  0     0.023970\n", "1     0.023970\n", "2    -0.319605\n", "3...   \n", "1  0    -0.109198\n", "1    -0.109198\n", "2    -0.697804\n", "3...   \n", "2  0     0.122515\n", "1     0.122515\n", "2    -0.045277\n", "3...   \n", "3  0     0.074574\n", "1     0.074574\n", "2     0.007990\n", "3...   \n", "4  0     0.058594\n", "1     0.058594\n", "2     0.175783\n", "3...   \n", "\n", "                                               dim_5 class_vals  id  \n", "0  0     0.633883\n", "1     0.633883\n", "2     0.972131\n", "3...   standing   0  \n", "1  0    -0.037287\n", "1    -0.037287\n", "2    -2.865789\n", "3...   standing   1  \n", "2  0     0.775041\n", "1     0.775041\n", "2     0.383526\n", "3...   standing   2  \n", "3  0    -0.079901\n", "1    -0.079901\n", "2     0.237040\n", "3...   standing   3  \n", "4  0    -0.074574\n", "1    -0.074574\n", "2     0.114525\n", "3...   standing   4  "]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["df.head()"]}, {"cell_type": "code", "execution_count": 10, "id": "968c3079", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>__nested_series_id</th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.079106</td>\n", "      <td>0.394032</td>\n", "      <td>0.551444</td>\n", "      <td>0.351565</td>\n", "      <td>0.023970</td>\n", "      <td>0.633883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0.079106</td>\n", "      <td>0.394032</td>\n", "      <td>0.551444</td>\n", "      <td>0.351565</td>\n", "      <td>0.023970</td>\n", "      <td>0.633883</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>-0.903497</td>\n", "      <td>-3.666397</td>\n", "      <td>-0.282844</td>\n", "      <td>-0.095881</td>\n", "      <td>-0.319605</td>\n", "      <td>0.972131</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>1.116125</td>\n", "      <td>-0.656101</td>\n", "      <td>0.333118</td>\n", "      <td>1.624657</td>\n", "      <td>-0.569962</td>\n", "      <td>1.209171</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>1.638200</td>\n", "      <td>1.405135</td>\n", "      <td>0.393875</td>\n", "      <td>1.187864</td>\n", "      <td>-0.271664</td>\n", "      <td>1.739182</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   __nested_series_id     dim_0     dim_1     dim_2     dim_3     dim_4  \\\n", "0                   0  0.079106  0.394032  0.551444  0.351565  0.023970   \n", "1                   0  0.079106  0.394032  0.551444  0.351565  0.023970   \n", "2                   0 -0.903497 -3.666397 -0.282844 -0.095881 -0.319605   \n", "3                   0  1.116125 -0.656101  0.333118  1.624657 -0.569962   \n", "4                   0  1.638200  1.405135  0.393875  1.187864 -0.271664   \n", "\n", "      dim_5  \n", "0  0.633883  \n", "1  0.633883  \n", "2  0.972131  \n", "3  1.209171  \n", "4  1.739182  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["u.head()"]}, {"cell_type": "code", "execution_count": 12, "id": "1abfa2b2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>__nested_series_id</th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>2</td>\n", "      <td>-0.813905</td>\n", "      <td>0.825666</td>\n", "      <td>0.032712</td>\n", "      <td>0.021307</td>\n", "      <td>0.122515</td>\n", "      <td>0.775041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>2</td>\n", "      <td>-0.813905</td>\n", "      <td>0.825666</td>\n", "      <td>0.032712</td>\n", "      <td>0.021307</td>\n", "      <td>0.122515</td>\n", "      <td>0.775041</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>2</td>\n", "      <td>-0.424628</td>\n", "      <td>-1.305033</td>\n", "      <td>0.826170</td>\n", "      <td>-0.372872</td>\n", "      <td>-0.045277</td>\n", "      <td>0.383526</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>2</td>\n", "      <td>0.316895</td>\n", "      <td>-0.507693</td>\n", "      <td>0.218569</td>\n", "      <td>0.023970</td>\n", "      <td>-0.130505</td>\n", "      <td>0.588605</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>2</td>\n", "      <td>0.228580</td>\n", "      <td>0.028821</td>\n", "      <td>0.586313</td>\n", "      <td>0.066584</td>\n", "      <td>-0.263674</td>\n", "      <td>0.817655</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     __nested_series_id     dim_0     dim_1     dim_2     dim_3     dim_4  \\\n", "200                   2 -0.813905  0.825666  0.032712  0.021307  0.122515   \n", "201                   2 -0.813905  0.825666  0.032712  0.021307  0.122515   \n", "202                   2 -0.424628 -1.305033  0.826170 -0.372872 -0.045277   \n", "203                   2  0.316895 -0.507693  0.218569  0.023970 -0.130505   \n", "204                   2  0.228580  0.028821  0.586313  0.066584 -0.263674   \n", "\n", "        dim_5  \n", "200  0.775041  \n", "201  0.775041  \n", "202  0.383526  \n", "203  0.588605  \n", "204  0.817655  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["u[u.__nested_series_id == 2].head()"]}, {"cell_type": "code", "execution_count": 13, "id": "69a69f90", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0     0.079106\n", "1     0.079106\n", "2    -0.903497\n", "3...</td>\n", "      <td>0     0.394032\n", "1     0.394032\n", "2    -3.666397\n", "3...</td>\n", "      <td>0     0.551444\n", "1     0.551444\n", "2    -0.282844\n", "3...</td>\n", "      <td>0     0.351565\n", "1     0.351565\n", "2    -0.095881\n", "3...</td>\n", "      <td>0     0.023970\n", "1     0.023970\n", "2    -0.319605\n", "3...</td>\n", "      <td>0     0.633883\n", "1     0.633883\n", "2     0.972131\n", "3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0     0.377751\n", "1     0.377751\n", "2     2.952965\n", "3...</td>\n", "      <td>0    -0.610850\n", "1    -0.610850\n", "2     0.970717\n", "3...</td>\n", "      <td>0    -0.147376\n", "1    -0.147376\n", "2    -5.962515\n", "3...</td>\n", "      <td>0    -0.103872\n", "1    -0.103872\n", "2    -7.593275\n", "3...</td>\n", "      <td>0    -0.109198\n", "1    -0.109198\n", "2    -0.697804\n", "3...</td>\n", "      <td>0    -0.037287\n", "1    -0.037287\n", "2    -2.865789\n", "3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3...</td>\n", "      <td>0     0.825666\n", "1     0.825666\n", "2    -1.305033\n", "3...</td>\n", "      <td>0     0.032712\n", "1     0.032712\n", "2     0.826170\n", "3...</td>\n", "      <td>0     0.021307\n", "1     0.021307\n", "2    -0.372872\n", "3...</td>\n", "      <td>0     0.122515\n", "1     0.122515\n", "2    -0.045277\n", "3...</td>\n", "      <td>0     0.775041\n", "1     0.775041\n", "2     0.383526\n", "3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0     0.289855\n", "1     0.289855\n", "2    -0.669185\n", "3...</td>\n", "      <td>0     0.284130\n", "1     0.284130\n", "2    -0.210466\n", "3...</td>\n", "      <td>0     0.213680\n", "1     0.213680\n", "2     0.252267\n", "3...</td>\n", "      <td>0    -0.314278\n", "1    -0.314278\n", "2     0.018644\n", "3...</td>\n", "      <td>0     0.074574\n", "1     0.074574\n", "2     0.007990\n", "3...</td>\n", "      <td>0    -0.079901\n", "1    -0.079901\n", "2     0.237040\n", "3...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0    -0.123238\n", "1    -0.123238\n", "2    -0.249547\n", "3...</td>\n", "      <td>0     0.379341\n", "1     0.379341\n", "2     0.541501\n", "3...</td>\n", "      <td>0    -0.286006\n", "1    -0.286006\n", "2     0.208420\n", "3...</td>\n", "      <td>0    -0.098545\n", "1    -0.098545\n", "2    -0.023970\n", "3...</td>\n", "      <td>0     0.058594\n", "1     0.058594\n", "2     0.175783\n", "3...</td>\n", "      <td>0    -0.074574\n", "1    -0.074574\n", "2     0.114525\n", "3...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                               dim_0  \\\n", "0  0     0.079106\n", "1     0.079106\n", "2    -0.903497\n", "3...   \n", "1  0     0.377751\n", "1     0.377751\n", "2     2.952965\n", "3...   \n", "2  0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3...   \n", "3  0     0.289855\n", "1     0.289855\n", "2    -0.669185\n", "3...   \n", "4  0    -0.123238\n", "1    -0.123238\n", "2    -0.249547\n", "3...   \n", "\n", "                                               dim_1  \\\n", "0  0     0.394032\n", "1     0.394032\n", "2    -3.666397\n", "3...   \n", "1  0    -0.610850\n", "1    -0.610850\n", "2     0.970717\n", "3...   \n", "2  0     0.825666\n", "1     0.825666\n", "2    -1.305033\n", "3...   \n", "3  0     0.284130\n", "1     0.284130\n", "2    -0.210466\n", "3...   \n", "4  0     0.379341\n", "1     0.379341\n", "2     0.541501\n", "3...   \n", "\n", "                                               dim_2  \\\n", "0  0     0.551444\n", "1     0.551444\n", "2    -0.282844\n", "3...   \n", "1  0    -0.147376\n", "1    -0.147376\n", "2    -5.962515\n", "3...   \n", "2  0     0.032712\n", "1     0.032712\n", "2     0.826170\n", "3...   \n", "3  0     0.213680\n", "1     0.213680\n", "2     0.252267\n", "3...   \n", "4  0    -0.286006\n", "1    -0.286006\n", "2     0.208420\n", "3...   \n", "\n", "                                               dim_3  \\\n", "0  0     0.351565\n", "1     0.351565\n", "2    -0.095881\n", "3...   \n", "1  0    -0.103872\n", "1    -0.103872\n", "2    -7.593275\n", "3...   \n", "2  0     0.021307\n", "1     0.021307\n", "2    -0.372872\n", "3...   \n", "3  0    -0.314278\n", "1    -0.314278\n", "2     0.018644\n", "3...   \n", "4  0    -0.098545\n", "1    -0.098545\n", "2    -0.023970\n", "3...   \n", "\n", "                                               dim_4  \\\n", "0  0     0.023970\n", "1     0.023970\n", "2    -0.319605\n", "3...   \n", "1  0    -0.109198\n", "1    -0.109198\n", "2    -0.697804\n", "3...   \n", "2  0     0.122515\n", "1     0.122515\n", "2    -0.045277\n", "3...   \n", "3  0     0.074574\n", "1     0.074574\n", "2     0.007990\n", "3...   \n", "4  0     0.058594\n", "1     0.058594\n", "2     0.175783\n", "3...   \n", "\n", "                                               dim_5  \n", "0  0     0.633883\n", "1     0.633883\n", "2     0.972131\n", "3...  \n", "1  0    -0.037287\n", "1    -0.037287\n", "2    -2.865789\n", "3...  \n", "2  0     0.775041\n", "1     0.775041\n", "2     0.383526\n", "3...  \n", "3  0    -0.079901\n", "1    -0.079901\n", "2     0.237040\n", "3...  \n", "4  0    -0.074574\n", "1    -0.074574\n", "2     0.114525\n", "3...  "]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["n.head()"]}, {"cell_type": "code", "execution_count": 9, "id": "4b221dc4-b019-4c94-8b64-e195cdd19134", "metadata": {}, "outputs": [{"data": {"text/plain": ["200   -0.813905\n", "201   -0.813905\n", "202   -0.424628\n", "203    0.316895\n", "204    0.228580\n", "         ...   \n", "295   -0.255364\n", "296   -0.066292\n", "297   -0.206440\n", "298   -0.544255\n", "299   -0.544255\n", "Name: dim_0, Length: 100, dtype: float64"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["i = 2\n", "df_prep.iloc[i].dim_0 * tsp.scaler_dict[\"0\"].scale_[0] + tsp.scaler_dict[\"0\"].mean_[0]"]}, {"cell_type": "code", "execution_count": 10, "id": "347e84a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3     0.316895\n", "4     0.228580\n", "        ...   \n", "95   -0.255364\n", "96   -0.066292\n", "97   -0.206440\n", "98   -0.544255\n", "99   -0.544255\n", "Length: 100, dtype: float64"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["df.dim_0.iloc[2]"]}, {"cell_type": "code", "execution_count": 71, "id": "9082cf8f", "metadata": {}, "outputs": [{"data": {"text/plain": ["0    -0.813905\n", "1    -0.813905\n", "2    -0.424628\n", "3     0.316895\n", "4     0.228580\n", "        ...   \n", "95   -0.255364\n", "96   -0.066292\n", "97   -0.206440\n", "98   -0.544255\n", "99   -0.544255\n", "Length: 100, dtype: float64"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["df.iloc[i].dim_0"]}, {"cell_type": "code", "execution_count": 19, "id": "559f9adb-1f07-4ee3-aa04-973ed2e9193c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["DIM 0\n", "DIM 1\n", "DIM 2\n", "DIM 3\n", "DIM 4\n", "DIM 5\n"]}], "source": ["for dim in range(6):\n", "    print(f\"DIM {dim}\")\n", "    for i in range(df_prep.shape[0]):\n", "        untransformed_prep = (\n", "            df_prep[f\"dim_{dim}\"].iloc[i] * tsp.scaler_dict[\"0\"].scale_[dim] + tsp.scaler_dict[\"0\"].mean_[dim]\n", "        )\n", "        assert np.mean(np.abs(df[f\"dim_{dim}\"].iloc[i].values - untransformed_prep.values)) < 1e-6"]}, {"cell_type": "code", "execution_count": 20, "id": "4a03a69e-acd7-4891-8b49-745a450af576", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["df_prep_inv = tsp.inverse_transform_labels(df_prep)\n", "all(df_prep_inv.class_vals == df.class_vals)"]}, {"cell_type": "code", "execution_count": null, "id": "31d97c0c", "metadata": {}, "outputs": [], "source": ["# Full_series mode is enabled in this case because the data is in nested format\n", "# Data is interpolated to fit the desired context_length (4000 interpolated to 128)\n", "\n", "# Comment (<PERSON>): I am thinking of removing the \"full_series\" option and autodetecting if it is a nested series\n", "\n", "dset = ClassificationDFDataset(\n", "    df_prep,\n", "    id_columns=[],\n", "    timestamp_column=None,\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    context_length=512,\n", "    static_categorical_columns=[],\n", "    stride=1,\n", "    enable_padding=False,\n", "    full_series=True,\n", ")"]}, {"cell_type": "code", "execution_count": 8, "id": "baee1316", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'past_values': tensor([[-3.4977e-01,  2.4992e-01,  4.4497e-01,  1.5745e-01,  2.6323e-02,\n", "           1.9612e-01],\n", "         [-3.4977e-01,  2.4992e-01,  4.4497e-01,  1.5745e-01,  2.6323e-02,\n", "           1.9612e-01],\n", "         [-4.1272e-01, -2.0888e-02,  3.3837e-01,  6.1444e-02, -5.9181e-02,\n", "           2.3970e-01],\n", "         [-4.2177e-01, -2.4388e-01,  2.5043e-01,  1.3652e-01, -1.9460e-01,\n", "           3.0810e-01],\n", "         [-2.0198e-01,  1.0009e-01,  3.8367e-01,  7.5703e-01, -2.9732e-01,\n", "           3.6207e-01],\n", "         [-1.4431e-01,  3.3711e-01,  3.9706e-01,  5.9545e-01, -1.6932e-01,\n", "           4.7982e-01],\n", "         [-1.8120e-01,  4.6812e-01,  3.4134e-01,  5.0313e-01, -6.5010e-02,\n", "           4.7102e-01],\n", "         [-2.6859e-01,  5.7314e-01,  2.6326e-01,  3.5357e-01,  2.8357e-02,\n", "           3.3285e-01],\n", "         [-3.5686e-01,  6.6537e-01,  1.5079e-01,  1.0895e-01,  8.8812e-02,\n", "           1.0936e-01],\n", "         [-3.5672e-01,  6.3914e-01, -1.2851e-01, -1.3243e-01,  1.3387e-02,\n", "          -4.8619e-02],\n", "         [-3.7167e-01,  5.2645e-01, -5.0378e-02, -1.6899e-01, -4.4553e-02,\n", "          -1.9329e-01],\n", "         [-3.7799e-01,  4.7998e-01, -5.5974e-03, -1.7423e-01, -6.5832e-02,\n", "          -2.4770e-01],\n", "         [-3.4839e-01,  3.2699e-01,  1.1233e-01, -1.5882e-01, -9.0311e-02,\n", "          -2.7345e-01],\n", "         [-2.7307e-01, -1.0416e-01,  4.4493e-01, -1.1344e-01, -1.5141e-01,\n", "          -3.3659e-01],\n", "         [-3.8136e-01, -2.4014e-01,  5.5441e-01, -6.7130e-02, -4.1697e-02,\n", "          -2.0227e-01],\n", "         [-3.8099e-01, -2.4911e-01,  5.2100e-01, -3.0046e-02,  6.5339e-02,\n", "          -3.2879e-02],\n", "         [-3.3816e-01, -1.4051e-01,  4.0925e-01, -3.9368e-03,  1.2305e-01,\n", "           1.5469e-01],\n", "         [-2.9553e-01,  7.9656e-03,  2.9242e-01,  9.8962e-03,  1.3018e-01,\n", "           3.0745e-01],\n", "         [-2.9553e-01,  7.9656e-03,  2.9242e-01,  9.8962e-03,  1.3018e-01,\n", "           3.0745e-01],\n", "         [-2.9090e-01,  2.4066e-01,  2.2430e-01,  1.1825e-01,  1.2374e-01,\n", "           3.8477e-01],\n", "         [-2.9838e-01,  4.4870e-01,  1.9655e-01,  1.6395e-01,  9.8777e-02,\n", "           3.8422e-01],\n", "         [-3.3026e-01,  5.8310e-01,  1.7162e-01,  1.2813e-01,  5.9716e-02,\n", "           2.8594e-01],\n", "         [-3.8268e-01,  6.2106e-01,  1.2096e-01,  2.6744e-02,  1.0232e-02,\n", "           1.0387e-01],\n", "         [-3.5521e-01,  5.7363e-01,  1.3538e-01,  6.0534e-03, -6.2907e-02,\n", "          -2.3341e-02],\n", "         [-3.4057e-01,  4.2581e-01,  2.4081e-01, -6.3919e-02, -1.0418e-01,\n", "          -1.6414e-01],\n", "         [-3.3163e-01,  2.8804e-01,  3.4050e-01, -1.0002e-01, -1.2242e-01,\n", "          -2.5233e-01],\n", "         [-3.3006e-01,  1.5859e-01,  4.2233e-01, -9.5998e-02, -1.1749e-01,\n", "          -2.7116e-01],\n", "         [-3.4249e-01, -2.4977e-02,  5.1568e-01, -8.6146e-02, -8.5489e-02,\n", "          -2.2737e-01],\n", "         [-3.5728e-01, -7.5259e-02,  5.4699e-01, -5.8914e-02, -8.6924e-03,\n", "          -1.2038e-01],\n", "         [-3.7943e-01, -3.1353e-02,  4.5929e-01, -9.3397e-02,  8.8217e-02,\n", "           2.8077e-02],\n", "         [-3.8600e-01,  7.6630e-02,  3.3458e-01, -1.1062e-01,  1.5592e-01,\n", "           1.5325e-01],\n", "         [-3.6644e-01,  2.1516e-01,  2.3702e-01, -6.7268e-02,  1.6168e-01,\n", "           2.1270e-01],\n", "         [-3.4497e-01,  3.3031e-01,  1.9208e-01, -2.3918e-02,  1.0454e-01,\n", "           2.1211e-01],\n", "         [-3.6605e-01,  4.0404e-01,  1.5983e-01,  8.4181e-03,  5.6242e-02,\n", "           1.6572e-01],\n", "         [-3.7776e-01,  4.2318e-01,  1.7412e-01,  6.3533e-02, -1.1823e-02,\n", "           9.1283e-02],\n", "         [-3.7493e-01,  3.8734e-01,  2.2667e-01,  1.0334e-01, -7.6735e-02,\n", "           6.1963e-03],\n", "         [-3.6614e-01,  3.0439e-01,  2.9388e-01,  6.5306e-02, -9.7351e-02,\n", "          -6.7364e-02],\n", "         [-3.7916e-01,  2.3945e-01,  3.8857e-01, -1.6906e-03, -6.4210e-02,\n", "          -6.3222e-02],\n", "         [-3.9481e-01,  1.9426e-01,  4.1267e-01, -7.1604e-02, -3.6988e-02,\n", "          -4.9885e-02],\n", "         [-4.0150e-01,  1.6305e-01,  4.0611e-01, -8.7703e-02,  5.5014e-03,\n", "          -4.2027e-02],\n", "         [-3.9538e-01,  1.4182e-01,  3.8249e-01, -4.5810e-02,  6.0744e-02,\n", "          -3.5341e-02],\n", "         [-3.8375e-01,  1.2843e-01,  3.1257e-01, -3.3002e-02,  8.1315e-02,\n", "           1.2587e-02],\n", "         [-3.8567e-01,  1.6297e-01,  2.8031e-01, -2.2854e-02,  6.0745e-02,\n", "           5.1994e-02],\n", "         [-3.8539e-01,  2.0421e-01,  2.5101e-01, -2.0942e-02,  4.2688e-02,\n", "           7.2845e-02],\n", "         [-3.8674e-01,  2.3739e-01,  2.4197e-01, -1.5858e-02,  2.1203e-02,\n", "           7.1839e-02],\n", "         [-3.9434e-01,  2.5387e-01,  2.7307e-01,  7.1965e-03, -1.3241e-02,\n", "           4.9461e-02],\n", "         [-3.8158e-01,  2.7922e-01,  2.9446e-01,  6.7296e-02, -1.4383e-02,\n", "           2.8752e-02],\n", "         [-3.7201e-01,  2.5548e-01,  2.8985e-01,  6.0538e-02, -9.2865e-03,\n", "           9.4270e-03],\n", "         [-3.7503e-01,  2.4522e-01,  3.0226e-01,  4.3296e-02, -5.2867e-03,\n", "          -2.7028e-03],\n", "         [-3.8753e-01,  2.4849e-01,  3.3128e-01,  3.4862e-02, -2.3837e-03,\n", "          -1.1022e-02],\n", "         [-3.9225e-01,  2.0281e-01,  3.3282e-01,  2.7965e-02,  6.7587e-03,\n", "          -2.9956e-02],\n", "         [-3.9733e-01,  1.7296e-01,  3.4740e-01,  3.8665e-02,  1.4827e-02,\n", "          -3.3317e-02],\n", "         [-3.8905e-01,  1.6979e-01,  3.2508e-01,  2.9029e-02,  3.4894e-02,\n", "          -4.0519e-03],\n", "         [-3.8449e-01,  1.8297e-01,  2.8455e-01,  8.3392e-03,  4.4677e-02,\n", "           3.5663e-02],\n", "         [-3.9507e-01,  2.0457e-01,  2.5068e-01, -6.4392e-03,  2.7992e-02,\n", "           6.2644e-02],\n", "         [-3.8619e-01,  2.5561e-01,  2.5856e-01,  3.1985e-02,  1.5421e-02,\n", "           6.0869e-02],\n", "         [-3.8999e-01,  2.7303e-01,  2.5886e-01,  1.4665e-02,  9.7580e-04,\n", "           5.3272e-02],\n", "         [-3.9302e-01,  2.7766e-01,  2.5822e-01,  1.0682e-03, -5.8582e-03,\n", "           4.9189e-02],\n", "         [-3.9286e-01,  2.7625e-01,  2.7095e-01,  5.1668e-03, -9.1266e-03,\n", "           3.9497e-02],\n", "         [-3.9224e-01,  2.7083e-01,  3.1991e-01,  2.0931e-02, -2.1697e-02,\n", "           2.2202e-03],\n", "         [-3.8176e-01,  2.3909e-01,  3.1670e-01,  2.1246e-02, -1.7896e-03,\n", "          -1.4762e-02],\n", "         [-3.8558e-01,  2.1254e-01,  3.1280e-01,  9.7800e-02,  7.5812e-03,\n", "          -3.0501e-02],\n", "         [-3.9498e-01,  2.0250e-01,  3.1276e-01,  1.6537e-01,  9.2726e-03,\n", "          -3.8927e-02],\n", "         [-4.0101e-01,  2.0833e-01,  3.1961e-01,  1.6405e-01,  7.3073e-03,\n", "          -3.5589e-02],\n", "         [-3.9187e-01,  1.9242e-01,  3.3749e-01,  9.4095e-02,  7.3073e-03,\n", "          -1.9022e-02],\n", "         [-4.0395e-01,  1.8465e-01,  3.2862e-01, -1.5040e-01,  1.4101e-03,\n", "           2.6042e-02],\n", "         [-4.0164e-01,  1.9064e-01,  2.9500e-01, -2.0045e-01,  1.7089e-02,\n", "           4.9662e-02],\n", "         [-3.8731e-01,  2.0039e-01,  2.5336e-01, -1.2991e-01,  3.1946e-02,\n", "           5.2621e-02],\n", "         [-3.7541e-01,  1.9856e-01,  2.2826e-01, -1.5241e-01,  6.9644e-03,\n", "           5.1296e-02],\n", "         [-3.7802e-01,  2.2747e-01,  2.4001e-01, -7.0638e-02, -1.0178e-02,\n", "           4.3012e-02],\n", "         [-3.8244e-01,  2.5830e-01,  2.7189e-01, -5.1564e-02, -2.5629e-02,\n", "           2.5533e-02],\n", "         [-3.9643e-01,  2.6666e-01,  3.1649e-01, -3.5938e-02, -3.7331e-02,\n", "          -7.9689e-03],\n", "         [-4.1531e-01,  2.4091e-01,  3.6262e-01, -1.5132e-03, -3.5868e-02,\n", "          -5.3506e-02],\n", "         [-4.0879e-01,  1.8362e-01,  3.8543e-01,  1.9177e-02,  8.7012e-03,\n", "          -8.5458e-02],\n", "         [-3.7984e-01,  1.1828e-01,  3.4919e-01, -1.1563e-02,  3.7843e-02,\n", "          -5.0843e-02],\n", "         [-3.7558e-01,  1.1964e-01,  3.0704e-01, -5.1071e-02,  4.8974e-02,\n", "           1.3178e-02],\n", "         [-3.9021e-01,  1.6995e-01,  2.6305e-01, -7.7712e-02,  5.1282e-02,\n", "           7.7011e-02],\n", "         [-4.0796e-01,  2.3584e-01,  2.1787e-01, -7.5328e-02,  5.3453e-02,\n", "           1.1052e-01],\n", "         [-4.0693e-01,  3.1799e-01,  2.2212e-01, -6.6460e-02,  1.8026e-02,\n", "           8.5082e-02],\n", "         [-4.0129e-01,  3.0077e-01,  2.6291e-01, -6.6815e-02, -4.0298e-03,\n", "           7.4862e-03],\n", "         [-4.0474e-01,  2.2798e-01,  3.0591e-01, -1.1149e-02, -2.2315e-02,\n", "          -6.4405e-02],\n", "         [-4.0436e-01,  1.3965e-01,  3.4624e-01,  8.8400e-02, -2.8783e-02,\n", "          -1.0000e-01],\n", "         [-3.5354e-01,  9.3860e-02,  4.0583e-01,  1.3569e-01,  6.6442e-03,\n", "          -7.9292e-02],\n", "         [-3.5680e-01,  8.9224e-02,  3.3575e-01,  5.7956e-02,  2.3809e-02,\n", "           2.9852e-02],\n", "         [-3.6988e-01,  1.4665e-01,  2.9962e-01,  4.7020e-02,  5.0368e-02,\n", "           8.6064e-02],\n", "         [-3.7917e-01,  2.2845e-01,  2.7015e-01,  4.2409e-02,  7.1944e-02,\n", "           1.0580e-01],\n", "         [-3.7755e-01,  3.0091e-01,  2.1777e-01, -2.3701e-02,  7.6172e-02,\n", "           1.1497e-01],\n", "         [-3.8167e-01,  3.8119e-01,  1.8058e-01, -1.7050e-01,  7.7315e-02,\n", "           1.2148e-01],\n", "         [-3.9269e-01,  3.9656e-01,  1.7972e-01, -2.0985e-01,  3.7225e-02,\n", "           5.3757e-02],\n", "         [-3.9847e-01,  3.9708e-01,  1.8300e-01, -2.1710e-01,  1.4621e-02,\n", "           1.5107e-02],\n", "         [-3.9637e-01,  3.3967e-01,  2.1950e-01, -1.7817e-01, -5.3285e-04,\n", "          -2.7507e-02],\n", "         [-3.8833e-01,  1.1889e-01,  3.5989e-01, -2.8410e-02, -5.8816e-02,\n", "          -1.9141e-01],\n", "         [-3.6667e-01,  1.0013e-02,  4.6504e-01,  9.9139e-02, -4.0942e-02,\n", "          -1.7671e-01],\n", "         [-3.6009e-01, -2.1964e-02,  4.9637e-01,  1.3727e-01, -3.5114e-02,\n", "          -1.7121e-01],\n", "         [-3.6300e-01, -2.3803e-02,  4.5270e-01,  5.4922e-02,  1.1444e-02,\n", "          -9.8889e-02],\n", "         [-3.7132e-01, -1.3031e-02,  3.4944e-01, -1.0997e-01,  1.0726e-01,\n", "           6.0704e-02],\n", "         [-3.8820e-01,  9.0811e-02,  2.5051e-01, -8.3367e-02,  1.1068e-01,\n", "           1.4295e-01],\n", "         [-3.8589e-01,  2.1715e-01,  1.8596e-01, -8.4727e-02,  8.0698e-02,\n", "           1.6464e-01],\n", "         [-3.8204e-01,  3.0074e-01,  1.7001e-01, -1.3494e-02,  3.1351e-02,\n", "           1.3118e-01],\n", "         [-3.8493e-01,  3.3725e-01,  1.9841e-01,  7.6656e-02, -1.4018e-02,\n", "           6.4727e-02],\n", "         [-4.0031e-01,  3.4116e-01,  2.5713e-01,  8.0538e-02, -2.6406e-02,\n", "          -1.6377e-03],\n", "         [-3.8943e-01,  2.8651e-01,  2.8850e-01,  8.1523e-02, -2.9834e-02,\n", "          -4.3648e-02],\n", "         [-3.8239e-01,  2.2542e-01,  3.2632e-01,  8.2509e-02, -2.1423e-02,\n", "          -6.2890e-02],\n", "         [-3.7484e-01,  1.6580e-01,  3.4194e-01,  4.3158e-02, -4.6467e-03,\n", "          -5.8074e-02],\n", "         [-3.6953e-01,  1.2497e-01,  3.2696e-01, -2.7051e-02,  1.6861e-02,\n", "          -2.8655e-02],\n", "         [-3.8443e-01,  1.4982e-01,  3.0348e-01, -2.2125e-02,  3.7431e-02,\n", "           2.6965e-02],\n", "         [-3.9399e-01,  1.6333e-01,  2.6578e-01,  2.3094e-03,  3.4346e-02,\n", "           5.2763e-02],\n", "         [-3.9740e-01,  1.6798e-01,  2.5086e-01,  1.2418e-02,  3.2174e-02,\n", "           6.1307e-02],\n", "         [-3.9672e-01,  1.9100e-01,  2.5161e-01,  8.3983e-03,  2.4015e-02,\n", "           6.0100e-02],\n", "         [-3.9469e-01,  2.5545e-01,  2.5510e-01, -2.8925e-03,  1.1816e-03,\n", "           5.5272e-02],\n", "         [-3.9211e-01,  2.6913e-01,  2.7871e-01, -5.8481e-03, -3.3894e-03,\n", "           3.0420e-02],\n", "         [-3.8048e-01,  2.5069e-01,  2.9531e-01,  9.6400e-03,  3.6273e-03,\n", "          -4.3071e-04],\n", "         [-3.8108e-01,  2.2350e-01,  3.1052e-01,  7.7271e-04,  9.9127e-03,\n", "          -1.8477e-02],\n", "         [-3.9105e-01,  1.9891e-01,  3.1924e-01, -3.2016e-02,  1.6472e-02,\n", "          -2.0205e-02],\n", "         [-3.8178e-01,  1.8243e-01,  3.0286e-01, -4.4825e-02,  3.8186e-02,\n", "          -1.5472e-02],\n", "         [-3.9842e-01,  1.7567e-01,  2.6530e-01, -2.5218e-02,  3.4117e-02,\n", "          -7.2658e-04],\n", "         [-4.0179e-01,  1.7808e-01,  2.6082e-01, -1.0045e-02,  2.1615e-02,\n", "           9.9714e-03],\n", "         [-3.9581e-01,  1.8375e-01,  2.7181e-01, -3.0896e-03,  7.7871e-03,\n", "           1.9380e-02],\n", "         [-3.8855e-01,  1.8927e-01,  2.8034e-01, -2.9913e-03, -2.0413e-03,\n", "           3.0030e-02],\n", "         [-3.9443e-01,  2.1056e-01,  2.9983e-01,  6.8613e-03, -7.7554e-03,\n", "           3.5355e-02],\n", "         [-3.9231e-01,  2.3193e-01,  3.0929e-01, -2.6758e-03,  5.8718e-04,\n", "           3.4858e-02],\n", "         [-3.8807e-01,  2.3466e-01,  3.0760e-01, -1.0402e-03,  6.4614e-03,\n", "           2.3817e-02],\n", "         [-3.8641e-01,  2.2174e-01,  2.9336e-01,  3.2359e-03,  9.0900e-03,\n", "           8.5750e-03],\n", "         [-3.9301e-01,  2.0959e-01,  2.6506e-01, -2.2381e-02,  1.5947e-02,\n", "           3.8414e-03],\n", "         [-3.8942e-01,  2.0223e-01,  2.7465e-01, -1.2272e-02,  8.2441e-03,\n", "          -8.9220e-04],\n", "         [-3.8828e-01,  2.0005e-01,  2.7776e-01, -9.0208e-03,  5.8441e-03,\n", "          -2.3123e-03],\n", "         [-3.8883e-01,  1.9722e-01,  2.8021e-01, -1.0262e-02,  6.3242e-03,\n", "           6.6993e-04],\n", "         [-3.8996e-01,  1.9142e-01,  2.8521e-01, -1.2804e-02,  7.3073e-03,\n", "           6.7764e-03]]),\n", " 'target_values': tensor(2),\n", " 'id': (0, 0)}"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["dset[0]"]}, {"cell_type": "markdown", "id": "5fae9696", "metadata": {}, "source": ["## Case 2: Data is in canonical format\n", "We first convert the original data into canonical format."]}, {"cell_type": "code", "execution_count": 9, "id": "5e06db51", "metadata": {}, "outputs": [], "source": ["from tsfm_public.toolkit.time_series_classification_preprocessor import unnest_transform"]}, {"cell_type": "code", "execution_count": 20, "id": "f054bd2a", "metadata": {}, "outputs": [], "source": ["df2 = unnest_transform(df, columns=input_columns)\n", "labels = df[label_column].reset_index()\n", "labels[\"index\"] = labels[\"index\"].astype(str)\n", "df2 = df2.merge(labels, left_on=\"__nested_series_id\", right_on=\"index\")"]}, {"cell_type": "code", "execution_count": 21, "id": "88eb5c34", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>__nested_series_id</th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "      <th>index</th>\n", "      <th>class_vals</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>0.079106</td>\n", "      <td>0.394032</td>\n", "      <td>0.551444</td>\n", "      <td>0.351565</td>\n", "      <td>0.023970</td>\n", "      <td>0.633883</td>\n", "      <td>0</td>\n", "      <td>standing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>0.079106</td>\n", "      <td>0.394032</td>\n", "      <td>0.551444</td>\n", "      <td>0.351565</td>\n", "      <td>0.023970</td>\n", "      <td>0.633883</td>\n", "      <td>0</td>\n", "      <td>standing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>-0.903497</td>\n", "      <td>-3.666397</td>\n", "      <td>-0.282844</td>\n", "      <td>-0.095881</td>\n", "      <td>-0.319605</td>\n", "      <td>0.972131</td>\n", "      <td>0</td>\n", "      <td>standing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>1.116125</td>\n", "      <td>-0.656101</td>\n", "      <td>0.333118</td>\n", "      <td>1.624657</td>\n", "      <td>-0.569962</td>\n", "      <td>1.209171</td>\n", "      <td>0</td>\n", "      <td>standing</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>1.638200</td>\n", "      <td>1.405135</td>\n", "      <td>0.393875</td>\n", "      <td>1.187864</td>\n", "      <td>-0.271664</td>\n", "      <td>1.739182</td>\n", "      <td>0</td>\n", "      <td>standing</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  __nested_series_id     dim_0     dim_1     dim_2     dim_3     dim_4  \\\n", "0                  0  0.079106  0.394032  0.551444  0.351565  0.023970   \n", "1                  0  0.079106  0.394032  0.551444  0.351565  0.023970   \n", "2                  0 -0.903497 -3.666397 -0.282844 -0.095881 -0.319605   \n", "3                  0  1.116125 -0.656101  0.333118  1.624657 -0.569962   \n", "4                  0  1.638200  1.405135  0.393875  1.187864 -0.271664   \n", "\n", "      dim_5 index class_vals  \n", "0  0.633883     0   standing  \n", "1  0.633883     0   standing  \n", "2  0.972131     0   standing  \n", "3  1.209171     0   standing  \n", "4  1.739182     0   standing  "]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["df2.head()"]}, {"cell_type": "code", "execution_count": 22, "id": "613fe8b6", "metadata": {}, "outputs": [{"data": {"text/plain": ["TimeSeriesClassificationPreprocessor {\n", "  \"_is_nested\": false,\n", "  \"categorical_encoder\": null,\n", "  \"context_length\": 64,\n", "  \"encode_categorical\": true,\n", "  \"feature_extractor_type\": \"TimeSeriesClassificationPreprocessor\",\n", "  \"freq\": null,\n", "  \"id_columns\": [],\n", "  \"input_columns\": [\n", "    \"dim_0\",\n", "    \"dim_1\",\n", "    \"dim_2\",\n", "    \"dim_3\",\n", "    \"dim_4\",\n", "    \"dim_5\"\n", "  ],\n", "  \"label_column\": \"class_vals\",\n", "  \"label_encoder\": {\n", "    \"classes_\": [\n", "      \"badminton\",\n", "      \"running\",\n", "      \"standing\",\n", "      \"walking\"\n", "    ]\n", "  },\n", "  \"processor_class\": \"TimeSeriesClassificationPreprocessor\",\n", "  \"scale_categorical_columns\": true,\n", "  \"scaler_dict\": {\n", "    \"0\": {\n", "      \"copy\": true,\n", "      \"feature_names_in_\": [\n", "        \"dim_0\",\n", "        \"dim_1\",\n", "        \"dim_2\",\n", "        \"dim_3\",\n", "        \"dim_4\",\n", "        \"dim_5\"\n", "      ],\n", "      \"mean_\": [\n", "        2.552759629,\n", "        -1.3039367667500004,\n", "        -1.02658049575,\n", "        0.01905110399999999,\n", "        -0.023957696500000004,\n", "        -0.055789663749999996\n", "      ],\n", "      \"n_features_in_\": 6,\n", "      \"n_samples_seen_\": 4000,\n", "      \"scale_\": [\n", "        7.072305651552695,\n", "        6.79408755945826,\n", "        3.5463734763579375,\n", "        2.111920179892119,\n", "        1.8207506409016112,\n", "        3.516585934128164\n", "      ],\n", "      \"var_\": [\n", "        50.0175072289842,\n", "        46.1596257655855,\n", "        12.576764833815083,\n", "        4.46020684623556,\n", "        3.315132896343628,\n", "        12.36637663210805\n", "      ],\n", "      \"with_mean\": true,\n", "      \"with_std\": true\n", "    }\n", "  },\n", "  \"scaler_type\": \"standard\",\n", "  \"scaling\": true,\n", "  \"scaling_id_columns\": [],\n", "  \"scaling_id_columns_types\": [],\n", "  \"static_categorical_columns\": [],\n", "  \"time_series_task\": \"classification\",\n", "  \"timestamp_column\": null\n", "}"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["tsp2 = TimeSeriesClassificationPreprocessor(\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    scaling=True,\n", ")\n", "\n", "tsp2.train(df2)"]}, {"cell_type": "code", "execution_count": 24, "id": "9348ac75", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>__nested_series_id</th>\n", "      <th>dim_0</th>\n", "      <th>dim_1</th>\n", "      <th>dim_2</th>\n", "      <th>dim_3</th>\n", "      <th>dim_4</th>\n", "      <th>dim_5</th>\n", "      <th>index</th>\n", "      <th>class_vals</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0</td>\n", "      <td>-0.349766</td>\n", "      <td>0.249919</td>\n", "      <td>0.444969</td>\n", "      <td>0.157446</td>\n", "      <td>0.026323</td>\n", "      <td>0.196120</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0</td>\n", "      <td>-0.349766</td>\n", "      <td>0.249919</td>\n", "      <td>0.444969</td>\n", "      <td>0.157446</td>\n", "      <td>0.026323</td>\n", "      <td>0.196120</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0</td>\n", "      <td>-0.488703</td>\n", "      <td>-0.347723</td>\n", "      <td>0.209717</td>\n", "      <td>-0.054421</td>\n", "      <td>-0.162377</td>\n", "      <td>0.292306</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0</td>\n", "      <td>-0.203135</td>\n", "      <td>0.095353</td>\n", "      <td>0.383405</td>\n", "      <td>0.760259</td>\n", "      <td>-0.299879</td>\n", "      <td>0.359713</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0</td>\n", "      <td>-0.129316</td>\n", "      <td>0.398740</td>\n", "      <td>0.400537</td>\n", "      <td>0.553436</td>\n", "      <td>-0.136046</td>\n", "      <td>0.510430</td>\n", "      <td>0</td>\n", "      <td>2</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  __nested_series_id     dim_0     dim_1     dim_2     dim_3     dim_4  \\\n", "0                  0 -0.349766  0.249919  0.444969  0.157446  0.026323   \n", "1                  0 -0.349766  0.249919  0.444969  0.157446  0.026323   \n", "2                  0 -0.488703 -0.347723  0.209717 -0.054421 -0.162377   \n", "3                  0 -0.203135  0.095353  0.383405  0.760259 -0.299879   \n", "4                  0 -0.129316  0.398740  0.400537  0.553436 -0.136046   \n", "\n", "      dim_5 index  class_vals  \n", "0  0.196120     0           2  \n", "1  0.196120     0           2  \n", "2  0.292306     0           2  \n", "3  0.359713     0           2  \n", "4  0.510430     0           2  "]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["df2_prep = tsp2.preprocess(df2)\n", "df2_prep.head()"]}, {"cell_type": "code", "execution_count": null, "id": "5ebea5e2", "metadata": {}, "outputs": [], "source": ["# Full_series mode is not available in this case, because the data is in canonical format\n", "# Data is not interpolated to fit the desired context_length\n", "\n", "dset2 = ClassificationDFDataset(\n", "    df2_prep,\n", "    id_columns=[],\n", "    timestamp_column=None,\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    context_length=128,\n", "    static_categorical_columns=[],\n", "    stride=1,\n", "    enable_padding=False,\n", "    full_series=False,\n", ")"]}, {"cell_type": "code", "execution_count": 27, "id": "5e406c1a", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'past_values': tensor([[-3.4977e-01,  2.4992e-01,  4.4497e-01,  1.5745e-01,  2.6323e-02,\n", "           1.9612e-01],\n", "         [-3.4977e-01,  2.4992e-01,  4.4497e-01,  1.5745e-01,  2.6323e-02,\n", "           1.9612e-01],\n", "         [-4.8870e-01, -3.4772e-01,  2.0972e-01, -5.4421e-02, -1.6238e-01,\n", "           2.9231e-01],\n", "         [-2.0314e-01,  9.5353e-02,  3.8341e-01,  7.6026e-01, -2.9988e-01,\n", "           3.5971e-01],\n", "         [-1.2932e-01,  3.9874e-01,  4.0054e-01,  5.5344e-01, -1.3605e-01,\n", "           5.1043e-01],\n", "         [-2.1907e-01,  5.1875e-01,  2.9815e-01,  4.6642e-01, -1.3172e-02,\n", "           4.4227e-01],\n", "         [-3.5688e-01,  6.7009e-01,  2.0106e-01,  1.5240e-01,  1.0239e-01,\n", "           1.3780e-01],\n", "         [-3.5670e-01,  6.3652e-01, -1.5644e-01, -1.5657e-01,  5.8441e-03,\n", "          -6.4417e-02],\n", "         [-3.7799e-01,  4.7998e-01, -5.5974e-03, -1.7423e-01, -6.5832e-02,\n", "          -2.4770e-01],\n", "         [-3.7799e-01,  4.7998e-01, -5.5974e-03, -1.7423e-01, -6.5832e-02,\n", "          -2.4770e-01],\n", "         [-2.6657e-01, -9.6001e-02,  4.3836e-01, -1.1622e-01, -1.5799e-01,\n", "          -3.4465e-01],\n", "         [-4.0518e-01, -2.7005e-01,  5.7850e-01, -5.6943e-02, -1.7561e-02,\n", "          -1.7272e-01],\n", "         [-3.6549e-01, -2.3569e-01,  4.8415e-01, -1.2804e-02,  1.1848e-01,\n", "           5.6763e-02],\n", "         [-2.9553e-01,  7.9656e-03,  2.9242e-01,  9.8962e-03,  1.3018e-01,\n", "           3.0745e-01],\n", "         [-2.9553e-01,  7.9656e-03,  2.9242e-01,  9.8962e-03,  1.3018e-01,\n", "           3.0745e-01],\n", "         [-2.8923e-01,  3.2483e-01,  1.9967e-01,  1.5745e-01,  1.2140e-01,\n", "           4.1273e-01],\n", "         [-3.0697e-01,  5.6507e-01,  1.9362e-01,  1.7006e-01,  7.7521e-02,\n", "           3.5744e-01],\n", "         [-3.8543e-01,  6.2580e-01,  1.1952e-01,  2.8813e-02,  1.7546e-02,\n", "           1.1660e-01],\n", "         [-3.5027e-01,  5.6509e-01,  1.3798e-01,  2.3291e-03, -7.6072e-02,\n", "          -4.6240e-02],\n", "         [-3.3513e-01,  3.4768e-01,  2.9850e-01, -1.0108e-01, -1.1996e-01,\n", "          -2.3028e-01],\n", "         [-3.2683e-01,  2.0632e-01,  3.9805e-01, -9.8560e-02, -1.2581e-01,\n", "          -2.8254e-01],\n", "         [-3.4274e-01, -2.8648e-02,  5.1754e-01, -8.5949e-02, -8.4849e-02,\n", "          -2.2650e-01],\n", "         [-3.6174e-01, -8.9528e-02,  5.5600e-01, -5.0637e-02,  1.4621e-02,\n", "          -8.7896e-02],\n", "         [-3.9409e-01,  1.6849e-02,  3.7915e-01, -1.2883e-01,  1.4920e-01,\n", "           1.2417e-01],\n", "         [-3.6944e-01,  1.9904e-01,  2.4331e-01, -7.3338e-02,  1.6968e-01,\n", "           2.1278e-01],\n", "         [-3.4196e-01,  3.4643e-01,  1.8579e-01, -1.7849e-02,  9.6537e-02,\n", "           2.1202e-01],\n", "         [-3.7781e-01,  4.3218e-01,  1.4715e-01,  2.1246e-02,  3.6563e-02,\n", "           1.4310e-01],\n", "         [-3.7770e-01,  4.1233e-01,  2.0668e-01,  1.1457e-01, -7.0221e-02,\n", "           2.8740e-02],\n", "         [-3.6588e-01,  3.0569e-01,  2.9199e-01,  6.6646e-02, -9.8014e-02,\n", "          -6.7446e-02],\n", "         [-3.8255e-01,  2.2257e-01,  4.1319e-01, -1.9110e-02, -5.5593e-02,\n", "          -6.2145e-02],\n", "         [-4.0376e-01,  1.7361e-01,  4.1230e-01, -1.0991e-01, -2.3411e-02,\n", "          -4.0938e-02],\n", "         [-3.9747e-01,  1.4423e-01,  3.9507e-01, -4.8116e-02,  5.7042e-02,\n", "          -4.3968e-02],\n", "         [-3.8259e-01,  1.2709e-01,  3.0558e-01, -3.1721e-02,  8.3372e-02,\n", "           1.7380e-02],\n", "         [-3.8697e-01,  1.7812e-01,  2.6965e-01, -1.9110e-02,  5.1191e-02,\n", "           6.6609e-02],\n", "         [-3.8371e-01,  2.3198e-01,  2.3118e-01, -2.2893e-02,  3.3637e-02,\n", "           7.9484e-02],\n", "         [-3.9511e-01,  2.5235e-01,  2.7179e-01,  3.5905e-03, -1.3172e-02,\n", "           5.0704e-02],\n", "         [-3.7877e-01,  2.8480e-01,  2.9917e-01,  8.0518e-02, -1.4635e-02,\n", "           2.4196e-02],\n", "         [-3.6768e-01,  2.3668e-01,  2.8388e-01,  4.7729e-02, -5.8582e-03,\n", "          -4.0191e-05],\n", "         [-3.8650e-01,  2.5854e-01,  3.3094e-01,  3.6379e-02, -4.3951e-03,\n", "          -6.8565e-03],\n", "         [-3.9253e-01,  2.0007e-01,  3.3291e-01,  2.7552e-02,  7.3073e-03,\n", "          -3.1092e-02],\n", "         [-3.9906e-01,  1.6316e-01,  3.5264e-01,  4.2685e-02,  1.7546e-02,\n", "          -3.4122e-02],\n", "         [-3.7965e-01,  1.7601e-01,  2.9919e-01,  1.6201e-02,  5.1191e-02,\n", "           2.4196e-02],\n", "         [-3.9596e-01,  1.9946e-01,  2.4989e-01, -1.0282e-02,  2.9249e-02,\n", "           6.2822e-02],\n", "         [-3.8459e-01,  2.6479e-01,  2.5998e-01,  3.8902e-02,  1.3158e-02,\n", "           6.0550e-02],\n", "         [-3.9302e-01,  2.7766e-01,  2.5822e-01,  1.0682e-03, -5.8582e-03,\n", "           4.9189e-02],\n", "         [-3.9302e-01,  2.7766e-01,  2.5822e-01,  1.0682e-03, -5.8582e-03,\n", "           4.9189e-02],\n", "         [-3.9223e-01,  2.7073e-01,  3.2089e-01,  2.1246e-02, -2.1949e-02,\n", "           1.4746e-03],\n", "         [-3.7856e-01,  2.2941e-01,  3.1542e-01,  2.1246e-02,  4.3815e-03,\n", "          -1.9732e-02],\n", "         [-3.9141e-01,  1.9857e-01,  3.1063e-01,  1.6123e-01,  1.0232e-02,\n", "          -3.9424e-02],\n", "         [-4.0229e-01,  2.1056e-01,  3.1711e-01,  1.7384e-01,  7.3073e-03,\n", "          -3.7909e-02],\n", "         [-3.9059e-01,  1.9019e-01,  3.3999e-01,  8.4302e-02,  7.3073e-03,\n", "          -1.6702e-02],\n", "         [-4.1048e-01,  1.8194e-01,  3.2307e-01, -2.6503e-01, -1.4699e-03,\n", "           4.6917e-02],\n", "         [-3.9097e-01,  2.0113e-01,  2.6111e-01, -1.2252e-01,  3.9488e-02,\n", "           5.2976e-02],\n", "         [-3.7536e-01,  1.9799e-01,  2.2802e-01, -1.5405e-01,  7.3073e-03,\n", "           5.1461e-02],\n", "         [-3.7869e-01,  2.3498e-01,  2.4306e-01, -4.9376e-02, -1.4635e-02,\n", "           4.0858e-02],\n", "         [-3.8517e-01,  2.7532e-01,  2.9292e-01, -5.3160e-02, -3.3651e-02,\n", "           1.4350e-02],\n", "         [-4.1648e-01,  2.5122e-01,  3.5851e-01, -5.2375e-03, -4.3891e-02,\n", "          -4.7755e-02],\n", "         [-4.0814e-01,  1.7789e-01,  3.8771e-01,  2.1246e-02,  1.3158e-02,\n", "          -8.8653e-02],\n", "         [-3.6789e-01,  9.3104e-02,  3.3293e-01, -2.5415e-02,  4.8265e-02,\n", "          -3.4879e-02],\n", "         [-3.8377e-01,  1.4790e-01,  2.7948e-01, -7.8382e-02,  4.9728e-02,\n", "           6.4337e-02],\n", "         [-4.0802e-01,  2.3091e-01,  2.1762e-01, -7.5860e-02,  5.5579e-02,\n", "           1.1205e-01],\n", "         [-4.0670e-01,  3.3607e-01,  2.2306e-01, -6.4510e-02,  1.0232e-02,\n", "           7.9484e-02],\n", "         [-3.9782e-01,  2.7814e-01,  2.8846e-01, -6.8293e-02, -1.3172e-02,\n", "          -3.8666e-02],\n", "         [-4.1554e-01,  1.4972e-01,  3.3313e-01,  7.7996e-02, -3.6577e-02,\n", "          -1.0456e-01],\n", "         [-3.5049e-01,  9.1112e-02,  4.0940e-01,  1.3853e-01,  8.7698e-03,\n", "          -7.8050e-02],\n", "         [-3.5908e-01,  8.8541e-02,  3.0911e-01,  2.8813e-02,  2.9249e-02,\n", "           6.8881e-02],\n", "         [-3.8003e-01,  2.0124e-01,  2.9070e-01,  6.4124e-02,  7.0207e-02,\n", "           1.0221e-01],\n", "         [-3.7713e-01,  2.9289e-01,  2.2149e-01, -9.0208e-03,  7.6058e-02,\n", "           1.1432e-01],\n", "         [-3.8241e-01,  3.9564e-01,  1.7388e-01, -1.9693e-01,  7.7521e-02,\n", "           1.2265e-01],\n", "         [-3.9847e-01,  3.9708e-01,  1.8300e-01, -2.1710e-01,  1.4621e-02,\n", "           1.5107e-02],\n", "         [-3.9847e-01,  3.9708e-01,  1.8300e-01, -2.1710e-01,  1.4621e-02,\n", "           1.5107e-02],\n", "         [-3.8817e-01,  1.1447e-01,  3.6270e-01, -2.5415e-02, -5.9981e-02,\n", "          -1.9469e-01],\n", "         [-3.6009e-01, -2.1964e-02,  4.9637e-01,  1.3727e-01, -3.5114e-02,\n", "          -1.7121e-01],\n", "         [-3.6009e-01, -2.1964e-02,  4.9637e-01,  1.3727e-01, -3.5114e-02,\n", "          -1.7121e-01],\n", "         [-3.6896e-01, -2.7568e-02,  3.6329e-01, -1.1369e-01,  1.0678e-01,\n", "           4.9189e-02],\n", "         [-3.9056e-01,  1.0535e-01,  2.3666e-01, -7.9643e-02,  1.1116e-01,\n", "           1.5446e-01],\n", "         [-3.8361e-01,  2.7175e-01,  1.6121e-01, -8.7210e-02,  6.5818e-02,\n", "           1.6961e-01],\n", "         [-3.8016e-01,  3.3572e-01,  1.8063e-01,  7.5474e-02, -1.0246e-02,\n", "           8.4786e-02],\n", "         [-4.0053e-01,  3.4225e-01,  2.5650e-01,  8.0518e-02, -2.6337e-02,\n", "          -7.9746e-04],\n", "         [-3.8661e-01,  2.7230e-01,  2.9665e-01,  8.1780e-02, -3.0725e-02,\n", "          -5.4571e-02],\n", "         [-3.7932e-01,  1.9121e-01,  3.4797e-01,  8.3040e-02, -1.4635e-02,\n", "          -6.8961e-02],\n", "         [-3.6685e-01,  1.2050e-01,  3.3118e-01, -2.7938e-02,  1.3158e-02,\n", "          -3.8666e-02],\n", "         [-3.8591e-01,  1.5231e-01,  3.0114e-01, -2.1632e-02,  3.9488e-02,\n", "           3.2527e-02],\n", "         [-3.9740e-01,  1.6798e-01,  2.5086e-01,  1.2418e-02,  3.2174e-02,\n", "           6.1307e-02],\n", "         [-3.9740e-01,  1.6798e-01,  2.5086e-01,  1.2418e-02,  3.2174e-02,\n", "           6.1307e-02],\n", "         [-3.9485e-01,  2.5463e-01,  2.5369e-01, -2.7151e-03,  1.4558e-03,\n", "           5.6763e-02],\n", "         [-3.9155e-01,  2.7215e-01,  2.8390e-01, -6.4984e-03, -4.3951e-03,\n", "           2.4953e-02],\n", "         [-3.7339e-01,  2.3693e-01,  3.0262e-01,  1.9985e-02,  8.7698e-03,\n", "          -1.6702e-02],\n", "         [-3.9309e-01,  2.0253e-01,  3.2284e-01, -2.9199e-02,  1.1696e-02,\n", "          -2.1247e-02],\n", "         [-3.8122e-01,  1.8144e-01,  3.0188e-01, -4.5593e-02,  3.9488e-02,\n", "          -1.5188e-02],\n", "         [-4.0464e-01,  1.7358e-01,  2.5207e-01, -1.7849e-02,  3.2174e-02,\n", "           4.5040e-03],\n", "         [-3.9912e-01,  1.8232e-01,  2.6904e-01, -2.7151e-03,  1.1696e-02,\n", "           1.5107e-02],\n", "         [-3.8796e-01,  1.8714e-01,  2.7839e-01, -3.9765e-03, -1.4699e-03,\n", "           2.9498e-02],\n", "         [-3.9549e-01,  2.1439e-01,  3.0334e-01,  8.6347e-03, -8.7839e-03,\n", "           3.6314e-02],\n", "         [-3.9053e-01,  2.4177e-01,  3.1263e-01, -9.0208e-03,  5.8441e-03,\n", "           3.4042e-02],\n", "         [-3.8469e-01,  2.2490e-01,  3.0072e-01,  9.8962e-03,  7.3073e-03,\n", "           9.8057e-03],\n", "         [-3.9314e-01,  2.0935e-01,  2.6449e-01, -2.2893e-02,  1.6084e-02,\n", "           3.7467e-03],\n", "         [-3.8828e-01,  2.0005e-01,  2.7776e-01, -9.0208e-03,  5.8441e-03,\n", "          -2.3123e-03],\n", "         [-3.8828e-01,  2.0005e-01,  2.7776e-01, -9.0208e-03,  5.8441e-03,\n", "          -2.3123e-03],\n", "         [-3.8996e-01,  1.9142e-01,  2.8521e-01, -1.2804e-02,  7.3073e-03,\n", "           6.7764e-03],\n", "         [-3.0754e-01,  1.0201e-01,  2.4792e-01, -5.8204e-02, -4.6816e-02,\n", "           5.2615e-03],\n", "         [-3.0754e-01,  1.0201e-01,  2.4792e-01, -5.8204e-02, -4.6816e-02,\n", "           5.2615e-03],\n", "         [ 5.6588e-02,  3.3480e-01, -1.3918e+00, -3.6045e+00, -3.7009e-01,\n", "          -7.9907e-01],\n", "         [ 2.4860e-01, -4.7354e-02, -2.4595e-01, -2.5401e+00,  2.3404e-01,\n", "          -1.1717e+00],\n", "         [ 9.9564e-02, -8.3386e-01, -4.8045e-01, -1.3080e+00,  3.5106e-01,\n", "          -9.5585e-01],\n", "         [-2.4067e-01, -3.6008e-01,  4.3230e-01,  4.6011e-01,  1.3457e-01,\n", "          -3.4389e-01],\n", "         [-4.8952e-01, -3.6194e-01,  2.9799e-01, -3.3691e-01, -6.1444e-02,\n", "          -3.3365e-02],\n", "         [-4.8952e-01, -3.6194e-01,  2.9799e-01, -3.3691e-01, -6.1444e-02,\n", "          -3.3365e-02],\n", "         [-3.3196e-01,  1.0645e-01,  4.5200e-01, -9.0208e-03,  5.7042e-02,\n", "           2.4535e-01],\n", "         [-4.2885e-01,  3.0706e-01,  3.9924e-01, -4.4332e-02, -3.9502e-02,\n", "           3.3018e-01],\n", "         [-4.0833e-01,  3.4589e-01,  3.6342e-01,  9.8962e-03, -7.7535e-02,\n", "           2.5595e-01],\n", "         [-3.7886e-01,  4.6327e-01,  4.0649e-01, -2.6676e-02,  1.4621e-02,\n", "           1.0296e-01],\n", "         [-3.9652e-01,  4.0877e-01,  3.7427e-01, -7.2077e-02,  1.6084e-02,\n", "          -1.6702e-02],\n", "         [-3.9901e-01,  3.0867e-01,  3.7697e-01, -1.9276e-04,  5.4116e-02,\n", "          -1.1516e-01],\n", "         [-3.9901e-01,  3.0867e-01,  3.7697e-01, -1.9276e-04,  5.4116e-02,\n", "          -1.1516e-01],\n", "         [-4.0351e-01,  6.8616e-02,  3.5540e-01, -8.8471e-02,  5.5579e-02,\n", "          -9.3197e-02],\n", "         [-4.0351e-01,  6.8616e-02,  3.5540e-01, -8.8471e-02,  5.5579e-02,\n", "          -9.3197e-02],\n", "         [-4.1346e-01,  4.1666e-02,  3.5264e-01, -6.3249e-02,  2.6323e-02,\n", "          -1.5945e-02],\n", "         [-3.8830e-01,  9.7696e-02,  3.2685e-01, -5.3160e-02,  4.5339e-02,\n", "           5.7520e-02],\n", "         [-4.4541e-01,  2.3904e-01,  3.3841e-01, -3.6592e-01,  3.6563e-02,\n", "           1.2871e-01],\n", "         [-3.4491e-01,  1.3321e-01,  3.3437e-01,  1.2718e-01, -3.8039e-02,\n", "           7.0396e-02],\n", "         [-3.8397e-01,  2.4972e-01,  3.4621e-01,  1.6375e-01, -5.5593e-02,\n", "           7.5697e-02],\n", "         [-3.5972e-01,  3.4706e-01,  3.1417e-01,  9.4391e-02,  2.9184e-03,\n", "           5.9793e-02],\n", "         [-4.1396e-01,  3.2053e-01,  3.0797e-01, -5.8204e-02, -5.9981e-02,\n", "           1.6622e-02],\n", "         [-4.0399e-01,  2.7903e-01,  3.6162e-01,  4.0163e-02, -7.3208e-03,\n", "          -4.5483e-02],\n", "         [-3.6471e-01,  2.0193e-01,  3.5350e-01, -4.1810e-02, -1.4699e-03,\n", "          -4.6997e-02],\n", "         [-4.1160e-01,  1.5033e-01,  3.2567e-01, -5.8204e-02,  2.1935e-02,\n", "          -2.5791e-02],\n", "         [-4.3388e-01,  1.4887e-01,  3.8719e-01, -1.6587e-02,  3.8025e-02,\n", "          -4.5844e-03]]),\n", " 'target_values': tensor(2),\n", " 'past_observed_mask': tensor([[True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True],\n", "         [True, True, True, True, True, True]]),\n", " 'id': (0,)}"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["dset2[0]"]}, {"cell_type": "code", "execution_count": 29, "id": "6f7960ef", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['past_values', 'target_values', 'past_observed_mask', 'id'])"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["dset2[0].keys()"]}, {"cell_type": "code", "execution_count": 30, "id": "141a4294", "metadata": {}, "outputs": [{"data": {"text/plain": ["dict_keys(['past_values', 'target_values', 'id'])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["dset[0].keys()"]}, {"cell_type": "code", "execution_count": null, "id": "537556a9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.10"}}, "nbformat": 4, "nbformat_minor": 5}