{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "**Using TTM-1536-96 model.**\n", "\n", "Pre-trained TTM models will be fetched from the [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "For details, visit the [Hugging Face TTM Model Repository](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "1. IBM Granite TTM-R1 pre-trained models can be found here: [Granite-TTM-R1 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r1)\n", "2. IBM Granite TTM-R2 pre-trained models can be found here: [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2)\n", "3. Research-use (non-commercial use only) TTM-R2 pre-trained models can be found here: [Research-Use-TTM-R2](https://huggingface.co/ibm/ttm-research-r2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-11-05 09:42:15.453567: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-11-05 09:42:16.131037: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-11-05 09:42:19.644826: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import math\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "from transformers.integrations import INTEGRATION_TO_CALLBACK\n", "\n", "from tsfm_public import TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.get_model import get_model\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.visualization import plot_predictions\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "SEED = 42\n", "set_seed(SEED)\n", "\n", "# Specify model parameters\n", "context_length = 1536\n", "forecast_length = 96\n", "freeze_backbone = True\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_datasets() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm-r2_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Set model path"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hf_model_path = \"ibm-granite/granite-timeseries-ttm-r2\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3570095896720886, 'eval_model_preparation_time': 0.0026, 'eval_runtime': 1.8024, 'eval_samples_per_second': 1545.153, 'eval_steps_per_second': 24.412}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.000298364724028334\n", "Using learning rate = 0.000298364724028334\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:19 < 01:12, 2.68 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.612300</td>\n", "      <td>0.655407</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.593900</td>\n", "      <td>0.656050</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.519100</td>\n", "      <td>0.656867</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.480800</td>\n", "      <td>0.658155</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.431600</td>\n", "      <td>0.659995</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.384700</td>\n", "      <td>0.662317</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.355300</td>\n", "      <td>0.668283</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.308800</td>\n", "      <td>0.689046</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.265600</td>\n", "      <td>0.715355</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.248000</td>\n", "      <td>0.734134</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.228800</td>\n", "      <td>0.771885</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.7860906557603315 seconds, Total Train Time = 21.260364055633545\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3571341633796692, 'eval_runtime': 1.0557, 'eval_samples_per_second': 2638.079, 'eval_steps_per_second': 41.679, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.357    0.357\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2743358612060547, 'eval_model_preparation_time': 0.0019, 'eval_runtime': 1.0095, 'eval_samples_per_second': 2758.711, 'eval_steps_per_second': 43.585}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='95' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 95/250 00:35 < 00:58, 2.64 it/s, Epoch 19/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.435300</td>\n", "      <td>0.229630</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.388800</td>\n", "      <td>0.230058</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.323200</td>\n", "      <td>0.231052</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.385600</td>\n", "      <td>0.232311</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.298400</td>\n", "      <td>0.233664</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.243800</td>\n", "      <td>0.234015</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.211400</td>\n", "      <td>0.232407</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.186800</td>\n", "      <td>0.228532</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.180600</td>\n", "      <td>0.228105</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.137400</td>\n", "      <td>0.232864</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.138800</td>\n", "      <td>0.238103</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.124100</td>\n", "      <td>0.240933</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.116900</td>\n", "      <td>0.248530</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.107300</td>\n", "      <td>0.249423</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.113300</td>\n", "      <td>0.250719</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.103300</td>\n", "      <td>0.255713</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.099300</td>\n", "      <td>0.260282</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.097700</td>\n", "      <td>0.261335</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.094600</td>\n", "      <td>0.260480</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.7202145802347284 seconds, Total Train Time = 35.765501976013184\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.27716049551963806, 'eval_runtime': 1.0979, 'eval_samples_per_second': 2536.694, 'eval_steps_per_second': 40.077, 'epoch': 19.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.357    0.357\n", "1   etth2   0.274    0.277\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.32653480768203735, 'eval_model_preparation_time': 0.002, 'eval_runtime': 3.4801, 'eval_samples_per_second': 3282.953, 'eval_steps_per_second': 51.435}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00043287612810830566\n", "Using learning rate = 0.00043287612810830566\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:37 < 02:13, 7.29 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.715300</td>\n", "      <td>0.400856</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.474800</td>\n", "      <td>0.420347</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.359000</td>\n", "      <td>0.452630</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.325600</td>\n", "      <td>0.455598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.297800</td>\n", "      <td>0.474598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.275900</td>\n", "      <td>0.478588</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.262200</td>\n", "      <td>0.467313</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.248000</td>\n", "      <td>0.475465</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.234600</td>\n", "      <td>0.459779</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.225500</td>\n", "      <td>0.477715</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.215400</td>\n", "      <td>0.466766</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.092024196277965 seconds, Total Train Time = 38.078644037246704\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3312471807003021, 'eval_runtime': 2.3232, 'eval_samples_per_second': 4917.742, 'eval_steps_per_second': 77.048, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.357    0.357\n", "1   etth2   0.274    0.277\n", "2   ettm1   0.327    0.331\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.16795998811721802, 'eval_model_preparation_time': 0.002, 'eval_runtime': 3.4881, 'eval_samples_per_second': 3275.398, 'eval_steps_per_second': 51.317}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00011768119524349978\n", "Using learning rate = 0.00011768119524349978\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:38 < 02:15, 7.18 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.471800</td>\n", "      <td>0.123267</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.337700</td>\n", "      <td>0.124431</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.252800</td>\n", "      <td>0.126874</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.176000</td>\n", "      <td>0.131680</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.135800</td>\n", "      <td>0.141091</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.117000</td>\n", "      <td>0.147765</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.108400</td>\n", "      <td>0.156903</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.103900</td>\n", "      <td>0.162671</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.100000</td>\n", "      <td>0.170844</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.097000</td>\n", "      <td>0.176793</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.093700</td>\n", "      <td>0.181367</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.114632953297008 seconds, Total Train Time = 38.65457510948181\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1680709272623062, 'eval_runtime': 2.3584, 'eval_samples_per_second': 4844.357, 'eval_steps_per_second': 75.898, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "  dataset  zs_mse  fs5_mse\n", "0   etth1   0.357    0.357\n", "1   etth2   0.274    0.277\n", "2   ettm1   0.327    0.331\n", "3   ettm2   0.168    0.168\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:06]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14976251125335693, 'eval_model_preparation_time': 0.002, 'eval_runtime': 6.4797, 'eval_samples_per_second': 1611.808, 'eval_steps_per_second': 25.31}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='297' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 297/1350 00:48 < 02:51, 6.13 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.097900</td>\n", "      <td>0.393768</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.095700</td>\n", "      <td>0.397849</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.092900</td>\n", "      <td>0.404240</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.088900</td>\n", "      <td>0.411644</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.084900</td>\n", "      <td>0.410327</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.081700</td>\n", "      <td>0.414159</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.077400</td>\n", "      <td>0.414830</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.073400</td>\n", "      <td>0.416132</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.068600</td>\n", "      <td>0.428362</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.065000</td>\n", "      <td>0.419456</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.062700</td>\n", "      <td>0.418077</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.7186961824243718 seconds, Total Train Time = 48.810704946517944\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14924383163452148, 'eval_runtime': 4.4687, 'eval_samples_per_second': 2337.136, 'eval_steps_per_second': 36.7, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "   dataset  zs_mse  fs5_mse\n", "0    etth1   0.357    0.357\n", "1    etth2   0.274    0.277\n", "2    ettm1   0.327    0.331\n", "3    ettm2   0.168    0.168\n", "4  weather   0.150    0.149\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:33]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15529614686965942, 'eval_model_preparation_time': 0.002, 'eval_runtime': 33.5263, 'eval_samples_per_second': 154.058, 'eval_steps_per_second': 4.832}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1104' max='1200' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1104/1200 15:21 < 01:20, 1.20 it/s, Epoch 46/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.143700</td>\n", "      <td>0.129405</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.140000</td>\n", "      <td>0.127710</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.137600</td>\n", "      <td>0.126163</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.135500</td>\n", "      <td>0.124611</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.133200</td>\n", "      <td>0.123532</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.130900</td>\n", "      <td>0.122066</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.129100</td>\n", "      <td>0.121844</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.127300</td>\n", "      <td>0.120507</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.125600</td>\n", "      <td>0.119225</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.123300</td>\n", "      <td>0.119105</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.121300</td>\n", "      <td>0.117542</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.120600</td>\n", "      <td>0.117430</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.118600</td>\n", "      <td>0.116615</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.118100</td>\n", "      <td>0.117184</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.116800</td>\n", "      <td>0.115890</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.116400</td>\n", "      <td>0.116175</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.115300</td>\n", "      <td>0.115326</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.114800</td>\n", "      <td>0.114901</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.114000</td>\n", "      <td>0.114714</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.112800</td>\n", "      <td>0.114350</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.112600</td>\n", "      <td>0.114116</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.111900</td>\n", "      <td>0.113912</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.111600</td>\n", "      <td>0.113825</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.111300</td>\n", "      <td>0.113824</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.111200</td>\n", "      <td>0.113436</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.110900</td>\n", "      <td>0.113308</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.110400</td>\n", "      <td>0.114188</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.110100</td>\n", "      <td>0.113093</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.109600</td>\n", "      <td>0.113151</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.109400</td>\n", "      <td>0.113164</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.109200</td>\n", "      <td>0.113394</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.109000</td>\n", "      <td>0.113235</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.109100</td>\n", "      <td>0.113087</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.108800</td>\n", "      <td>0.113114</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.108800</td>\n", "      <td>0.112943</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.108400</td>\n", "      <td>0.112619</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.108100</td>\n", "      <td>0.113038</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.107900</td>\n", "      <td>0.113113</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.108100</td>\n", "      <td>0.112789</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.108500</td>\n", "      <td>0.112672</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.108100</td>\n", "      <td>0.112766</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.107800</td>\n", "      <td>0.112637</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.108100</td>\n", "      <td>0.112631</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.107500</td>\n", "      <td>0.112633</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.108000</td>\n", "      <td>0.112636</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.107900</td>\n", "      <td>0.112627</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 6.346862710040549 seconds, Total Train Time = 923.6657681465149\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:24]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.13803862035274506, 'eval_runtime': 26.4286, 'eval_samples_per_second': 195.432, 'eval_steps_per_second': 6.13, 'epoch': 46.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.357    0.357\n", "1        etth2   0.274    0.277\n", "2        ettm1   0.327    0.331\n", "3        ettm2   0.168    0.168\n", "4      weather   0.150    0.149\n", "5  electricity   0.155    0.138\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:58]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4634234607219696, 'eval_model_preparation_time': 0.002, 'eval_runtime': 58.6051, 'eval_samples_per_second': 58.237, 'eval_steps_per_second': 7.286}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 3081120\n", "Number of params after freezing the backbone 1054560\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 5.590810182512223e-05\n", "Using learning rate = 5.590810182512223e-05\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='616' max='2800' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 616/2800 05:51 < 20:48, 1.75 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.298800</td>\n", "      <td>0.391451</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.286600</td>\n", "      <td>0.393831</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.275300</td>\n", "      <td>0.394873</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.264500</td>\n", "      <td>0.396028</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.253800</td>\n", "      <td>0.400728</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.245400</td>\n", "      <td>0.404270</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.237900</td>\n", "      <td>0.408866</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.231600</td>\n", "      <td>0.409725</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.226200</td>\n", "      <td>0.410739</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.221300</td>\n", "      <td>0.412317</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.216800</td>\n", "      <td>0.414294</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 9.234334555539219 seconds, Total Train Time = 352.2789981365204\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:44]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.46613699197769165, 'eval_runtime': 45.3952, 'eval_samples_per_second': 75.184, 'eval_steps_per_second': 9.406, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.357    0.357\n", "1        etth2   0.274    0.277\n", "2        ettm1   0.327    0.331\n", "3        ettm2   0.168    0.168\n", "4      weather   0.150    0.149\n", "5  electricity   0.155    0.138\n", "6      traffic   0.463    0.466\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(DATASET, context_length, forecast_length, dataset_root_path=DATA_ROOT_PATH)\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = get_model(hf_model_path, context_length=context_length, prediction_length=forecast_length)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            seed=SEED,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5]:\n", "        # Set learning rate\n", "        learning_rate = None  # `None` value indicates that the optimal_lr_finder() will be used\n", "\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        if learning_rate is None:\n", "            learning_rate, finetune_forecast_model = optimal_lr_finder(\n", "                finetune_forecast_model,\n", "                dset_train,\n", "                batch_size=BATCH_SIZE,\n", "            )\n", "            print(\"OPTIMAL SUGGESTED LEARNING RATE =\", learning_rate)\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "            seed=SEED,\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "        finetune_forecast_trainer.remove_callback(INTEGRATION_TO_CALLBACK[\"codecarbon\"])\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.357</td>\n", "      <td>0.357</td>\n", "      <td>1.802</td>\n", "      <td>0.786</td>\n", "      <td>21.260</td>\n", "      <td>0.655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.274</td>\n", "      <td>0.277</td>\n", "      <td>1.010</td>\n", "      <td>0.720</td>\n", "      <td>35.766</td>\n", "      <td>0.228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.327</td>\n", "      <td>0.331</td>\n", "      <td>3.480</td>\n", "      <td>1.092</td>\n", "      <td>38.079</td>\n", "      <td>0.401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.168</td>\n", "      <td>0.168</td>\n", "      <td>3.488</td>\n", "      <td>1.115</td>\n", "      <td>38.655</td>\n", "      <td>0.123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.150</td>\n", "      <td>0.149</td>\n", "      <td>6.480</td>\n", "      <td>1.719</td>\n", "      <td>48.811</td>\n", "      <td>0.394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.155</td>\n", "      <td>0.138</td>\n", "      <td>33.526</td>\n", "      <td>6.347</td>\n", "      <td>923.666</td>\n", "      <td>0.113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.463</td>\n", "      <td>0.466</td>\n", "      <td>58.605</td>\n", "      <td>9.234</td>\n", "      <td>352.279</td>\n", "      <td>0.391</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.357    0.357         1.802                0.786   \n", "1        etth2   0.274    0.277         1.010                0.720   \n", "2        ettm1   0.327    0.331         3.480                1.092   \n", "3        ettm2   0.168    0.168         3.488                1.115   \n", "4      weather   0.150    0.149         6.480                1.719   \n", "5  electricity   0.155    0.138        33.526                6.347   \n", "6      traffic   0.463    0.466        58.605                9.234   \n", "\n", "   fs5_total_train_time  fs5_best_val_metric  \n", "0                21.260                0.655  \n", "1                35.766                0.228  \n", "2                38.079                0.401  \n", "3                38.655                0.123  \n", "4                48.811                0.394  \n", "5               923.666                0.113  \n", "6               352.279                0.391  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}