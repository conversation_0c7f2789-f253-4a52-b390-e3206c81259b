{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "**Using TTM-512-96 model.**\n", "\n", "Pre-trained TTM models will be fetched from the [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "For details, visit the [Hugging Face TTM Model Repository](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "1. IBM Granite TTM-R1 pre-trained models can be found here: [Granite-TTM-R1 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r1)\n", "2. IBM Granite TTM-R2 pre-trained models can be found here: [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2)\n", "3. Research-use (non-commercial use only) TTM-R2 pre-trained models can be found here: [Research-Use-TTM-R2](https://huggingface.co/ibm-research/ttm-research-r2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-11-05 09:39:43.950830: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-11-05 09:39:44.021779: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-11-05 09:39:47.880980: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import math\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "from transformers.integrations import INTEGRATION_TO_CALLBACK\n", "\n", "from tsfm_public import TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.get_model import get_model\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.visualization import plot_predictions\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "SEED = 42\n", "set_seed(SEED)\n", "\n", "# Specify model parameters\n", "context_length = 512\n", "forecast_length = 96\n", "freeze_backbone = True\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_datasets() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm-r2_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Set model path"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hf_model_path = \"ibm-granite/granite-timeseries-ttm-r2\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3628121316432953, 'eval_model_preparation_time': 0.0028, 'eval_runtime': 1.5167, 'eval_samples_per_second': 1836.244, 'eval_steps_per_second': 29.011}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00017073526474706903\n", "Using learning rate = 0.00017073526474706903\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='105' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [105/250 00:38 < 00:54, 2.64 it/s, Epoch 21/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.812700</td>\n", "      <td>0.664259</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.832200</td>\n", "      <td>0.664153</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.793800</td>\n", "      <td>0.663970</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.772200</td>\n", "      <td>0.663760</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.811200</td>\n", "      <td>0.663474</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.769200</td>\n", "      <td>0.663127</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.749600</td>\n", "      <td>0.662820</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.719200</td>\n", "      <td>0.662412</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.716600</td>\n", "      <td>0.662103</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.709800</td>\n", "      <td>0.661821</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.688200</td>\n", "      <td>0.661788</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.684000</td>\n", "      <td>0.661836</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.656500</td>\n", "      <td>0.662756</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.644400</td>\n", "      <td>0.665279</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.635400</td>\n", "      <td>0.668289</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.620200</td>\n", "      <td>0.673172</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.621300</td>\n", "      <td>0.674407</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.611000</td>\n", "      <td>0.674804</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.604800</td>\n", "      <td>0.676390</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.611800</td>\n", "      <td>0.678676</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.597600</td>\n", "      <td>0.681201</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.8227878184545607 seconds, Total Train Time = 40.316823959350586\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.36197009682655334, 'eval_runtime': 0.9814, 'eval_samples_per_second': 2837.639, 'eval_steps_per_second': 44.832, 'epoch': 21.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.362\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2757423520088196, 'eval_model_preparation_time': 0.0021, 'eval_runtime': 0.6069, 'eval_samples_per_second': 4588.66, 'eval_steps_per_second': 72.496}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0002477076355991711\n", "Using learning rate = 0.0002477076355991711\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='90' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 90/250 00:33 < 01:01, 2.61 it/s, Epoch 18/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.322400</td>\n", "      <td>0.218503</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.328700</td>\n", "      <td>0.218445</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.307000</td>\n", "      <td>0.218306</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.303400</td>\n", "      <td>0.218164</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.285800</td>\n", "      <td>0.217900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.287000</td>\n", "      <td>0.217582</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.257400</td>\n", "      <td>0.217252</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.242900</td>\n", "      <td>0.216912</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.227400</td>\n", "      <td>0.217226</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.220800</td>\n", "      <td>0.218330</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.202000</td>\n", "      <td>0.221209</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.189800</td>\n", "      <td>0.224448</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.182600</td>\n", "      <td>0.225626</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.176700</td>\n", "      <td>0.226240</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.169000</td>\n", "      <td>0.227703</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.167600</td>\n", "      <td>0.228698</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.162700</td>\n", "      <td>0.229820</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.157300</td>\n", "      <td>0.229787</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.8318122625350952 seconds, Total Train Time = 34.44136691093445\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2727772295475006, 'eval_runtime': 1.3208, 'eval_samples_per_second': 2108.521, 'eval_steps_per_second': 33.312, 'epoch': 18.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.362\n", "1   etth2   0.276    0.273\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3376680314540863, 'eval_model_preparation_time': 0.0018, 'eval_runtime': 2.4056, 'eval_samples_per_second': 4749.271, 'eval_steps_per_second': 74.409}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00035938136638046257\n", "Using learning rate = 0.00035938136638046257\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='286' max='1300' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 286/1300 00:32 < 01:56, 8.67 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.424400</td>\n", "      <td>0.407012</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.373400</td>\n", "      <td>0.413970</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.338900</td>\n", "      <td>0.425914</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.298400</td>\n", "      <td>0.441531</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.280300</td>\n", "      <td>0.451525</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.266300</td>\n", "      <td>0.446058</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.262000</td>\n", "      <td>0.441062</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.250800</td>\n", "      <td>0.432998</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.246800</td>\n", "      <td>0.425966</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.241700</td>\n", "      <td>0.421164</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.236800</td>\n", "      <td>0.418237</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.1163660179484973 seconds, Total Train Time = 33.472071409225464\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3408427834510803, 'eval_runtime': 1.7904, 'eval_samples_per_second': 6381.402, 'eval_steps_per_second': 99.98, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.362\n", "1   etth2   0.276    0.273\n", "2   ettm1   0.338    0.341\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17649634182453156, 'eval_model_preparation_time': 0.0018, 'eval_runtime': 2.771, 'eval_samples_per_second': 4123.041, 'eval_steps_per_second': 64.597}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00035938136638046257\n", "Using learning rate = 0.00035938136638046257\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='286' max='1300' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 286/1300 00:33 < 01:57, 8.59 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.288100</td>\n", "      <td>0.122861</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.256000</td>\n", "      <td>0.123697</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.234200</td>\n", "      <td>0.125028</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.206200</td>\n", "      <td>0.126568</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.188500</td>\n", "      <td>0.128257</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.180000</td>\n", "      <td>0.131432</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.168100</td>\n", "      <td>0.132874</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.162500</td>\n", "      <td>0.135289</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.156900</td>\n", "      <td>0.134445</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.153500</td>\n", "      <td>0.138203</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.151700</td>\n", "      <td>0.136658</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.1548236716877331 seconds, Total Train Time = 33.74325489997864\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17622655630111694, 'eval_runtime': 1.8641, 'eval_samples_per_second': 6128.997, 'eval_steps_per_second': 96.025, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.362\n", "1   etth2   0.276    0.273\n", "2   ettm1   0.338    0.341\n", "3   ettm2   0.176    0.176\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15046171844005585, 'eval_model_preparation_time': 0.002, 'eval_runtime': 3.6313, 'eval_samples_per_second': 2876.072, 'eval_steps_per_second': 45.162}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.0033516026509388406\n", "Using learning rate = 0.0033516026509388406\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='297' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 297/1350 00:32 < 01:55, 9.09 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.160000</td>\n", "      <td>0.405245</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.153700</td>\n", "      <td>0.412479</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.147600</td>\n", "      <td>0.424075</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.140300</td>\n", "      <td>0.476080</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.136000</td>\n", "      <td>0.461959</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.132400</td>\n", "      <td>0.488006</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.130800</td>\n", "      <td>0.474276</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.127400</td>\n", "      <td>0.495313</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.126300</td>\n", "      <td>0.503160</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.129000</td>\n", "      <td>0.461539</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.129500</td>\n", "      <td>0.511235</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.2926849235187878 seconds, Total Train Time = 33.269383668899536\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15043412148952484, 'eval_runtime': 2.4543, 'eval_samples_per_second': 4255.351, 'eval_steps_per_second': 66.821, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "   dataset  zs_mse  fs5_mse\n", "0    etth1   0.363    0.362\n", "1    etth2   0.276    0.273\n", "2    ettm1   0.338    0.341\n", "3    ettm2   0.176    0.176\n", "4  weather   0.150    0.150\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:13]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.18014171719551086, 'eval_model_preparation_time': 0.0019, 'eval_runtime': 14.0186, 'eval_samples_per_second': 368.439, 'eval_steps_per_second': 11.556}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00017073526474706903\n", "Using learning rate = 0.00017073526474706903\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1250' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1250/1250 07:26, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.211400</td>\n", "      <td>0.154705</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.208900</td>\n", "      <td>0.152494</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.206400</td>\n", "      <td>0.149164</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.203000</td>\n", "      <td>0.144169</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.198700</td>\n", "      <td>0.139577</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.194600</td>\n", "      <td>0.137131</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.191300</td>\n", "      <td>0.134782</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.188000</td>\n", "      <td>0.132271</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.185000</td>\n", "      <td>0.130578</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.182200</td>\n", "      <td>0.128892</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.179600</td>\n", "      <td>0.127244</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.177300</td>\n", "      <td>0.126303</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.175100</td>\n", "      <td>0.125187</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.173300</td>\n", "      <td>0.124697</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.171700</td>\n", "      <td>0.124088</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.170100</td>\n", "      <td>0.123624</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.168900</td>\n", "      <td>0.123137</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.167700</td>\n", "      <td>0.122581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.166700</td>\n", "      <td>0.122307</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.165900</td>\n", "      <td>0.122136</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.165100</td>\n", "      <td>0.121934</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.164500</td>\n", "      <td>0.121555</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.163700</td>\n", "      <td>0.121460</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.163000</td>\n", "      <td>0.121408</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.162300</td>\n", "      <td>0.121044</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.162000</td>\n", "      <td>0.120895</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.161600</td>\n", "      <td>0.120860</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.161300</td>\n", "      <td>0.120670</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.160700</td>\n", "      <td>0.120574</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.160300</td>\n", "      <td>0.120577</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.160100</td>\n", "      <td>0.120446</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.159900</td>\n", "      <td>0.120322</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.159500</td>\n", "      <td>0.120252</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.159200</td>\n", "      <td>0.120218</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.159200</td>\n", "      <td>0.120163</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.158800</td>\n", "      <td>0.120148</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.158700</td>\n", "      <td>0.120112</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.158600</td>\n", "      <td>0.120026</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.158500</td>\n", "      <td>0.119978</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.158300</td>\n", "      <td>0.119960</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.158200</td>\n", "      <td>0.119943</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.157900</td>\n", "      <td>0.119933</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.158200</td>\n", "      <td>0.119912</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.158100</td>\n", "      <td>0.119919</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.158000</td>\n", "      <td>0.119904</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.158000</td>\n", "      <td>0.119903</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.157900</td>\n", "      <td>0.119908</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.158000</td>\n", "      <td>0.119906</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.158000</td>\n", "      <td>0.119908</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.157800</td>\n", "      <td>0.119908</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 3.198044848442078 seconds, Total Train Time = 448.517240524292\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:09]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14508052170276642, 'eval_runtime': 10.1986, 'eval_samples_per_second': 506.44, 'eval_steps_per_second': 15.884, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.363    0.362\n", "1        etth2   0.276    0.273\n", "2        ettm1   0.338    0.341\n", "3        ettm2   0.176    0.176\n", "4      weather   0.150    0.150\n", "5  electricity   0.180    0.145\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:23]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.5177494287490845, 'eval_model_preparation_time': 0.0021, 'eval_runtime': 23.5839, 'eval_samples_per_second': 144.717, 'eval_steps_per_second': 18.106}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 805280\n", "Number of params after freezing the backbone 289696\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.0002477076355991711\n", "Using learning rate = 0.0002477076355991711\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='3100' max='3100' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [3100/3100 11:35, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.297500</td>\n", "      <td>0.417052</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.287100</td>\n", "      <td>0.403708</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.279700</td>\n", "      <td>0.395805</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.273600</td>\n", "      <td>0.390183</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.268000</td>\n", "      <td>0.384714</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.261600</td>\n", "      <td>0.376553</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.255300</td>\n", "      <td>0.370888</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.249300</td>\n", "      <td>0.365246</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.243600</td>\n", "      <td>0.358401</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.238600</td>\n", "      <td>0.354540</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.234100</td>\n", "      <td>0.349931</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.230500</td>\n", "      <td>0.347657</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.227200</td>\n", "      <td>0.345784</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.224300</td>\n", "      <td>0.342975</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.221900</td>\n", "      <td>0.342895</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.219800</td>\n", "      <td>0.341435</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.218100</td>\n", "      <td>0.339892</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.216700</td>\n", "      <td>0.339336</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.215300</td>\n", "      <td>0.340271</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.214100</td>\n", "      <td>0.337380</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.212800</td>\n", "      <td>0.336691</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.212100</td>\n", "      <td>0.336662</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.211200</td>\n", "      <td>0.335845</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.210200</td>\n", "      <td>0.334983</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.209800</td>\n", "      <td>0.336293</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.209200</td>\n", "      <td>0.335351</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.208400</td>\n", "      <td>0.335203</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.208000</td>\n", "      <td>0.334956</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.207400</td>\n", "      <td>0.334277</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.207000</td>\n", "      <td>0.333793</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.206700</td>\n", "      <td>0.334447</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.206200</td>\n", "      <td>0.333683</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.205900</td>\n", "      <td>0.333518</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.205600</td>\n", "      <td>0.333938</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.205300</td>\n", "      <td>0.333644</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.205000</td>\n", "      <td>0.333208</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.205000</td>\n", "      <td>0.333282</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.204600</td>\n", "      <td>0.333059</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.204400</td>\n", "      <td>0.332998</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.204400</td>\n", "      <td>0.333071</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.204200</td>\n", "      <td>0.333078</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.204000</td>\n", "      <td>0.332818</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.204000</td>\n", "      <td>0.333064</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.203900</td>\n", "      <td>0.332978</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.204000</td>\n", "      <td>0.332890</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.203700</td>\n", "      <td>0.332903</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.203700</td>\n", "      <td>0.332925</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.203700</td>\n", "      <td>0.332887</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.203700</td>\n", "      <td>0.332878</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.203600</td>\n", "      <td>0.332874</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 4.670924897193909 seconds, Total Train Time = 696.410046339035\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:16]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.40992745757102966, 'eval_runtime': 17.4543, 'eval_samples_per_second': 195.539, 'eval_steps_per_second': 24.464, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.363    0.362\n", "1        etth2   0.276    0.273\n", "2        ettm1   0.338    0.341\n", "3        ettm2   0.176    0.176\n", "4      weather   0.150    0.150\n", "5  electricity   0.180    0.145\n", "6      traffic   0.518    0.410\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(DATASET, context_length, forecast_length, dataset_root_path=DATA_ROOT_PATH)\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = get_model(hf_model_path, context_length=context_length, prediction_length=forecast_length)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            seed=SEED,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5]:\n", "        # Set learning rate\n", "        learning_rate = None  # `None` value indicates that the optimal_lr_finder() will be used\n", "\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        if learning_rate is None:\n", "            learning_rate, finetune_forecast_model = optimal_lr_finder(\n", "                finetune_forecast_model,\n", "                dset_train,\n", "                batch_size=BATCH_SIZE,\n", "            )\n", "            print(\"OPTIMAL SUGGESTED LEARNING RATE =\", learning_rate)\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "            seed=SEED,\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "        finetune_forecast_trainer.remove_callback(INTEGRATION_TO_CALLBACK[\"codecarbon\"])\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.363</td>\n", "      <td>0.362</td>\n", "      <td>1.517</td>\n", "      <td>0.823</td>\n", "      <td>40.317</td>\n", "      <td>0.662</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.276</td>\n", "      <td>0.273</td>\n", "      <td>0.607</td>\n", "      <td>0.832</td>\n", "      <td>34.441</td>\n", "      <td>0.217</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.338</td>\n", "      <td>0.341</td>\n", "      <td>2.406</td>\n", "      <td>1.116</td>\n", "      <td>33.472</td>\n", "      <td>0.407</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.176</td>\n", "      <td>0.176</td>\n", "      <td>2.771</td>\n", "      <td>1.155</td>\n", "      <td>33.743</td>\n", "      <td>0.123</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.150</td>\n", "      <td>0.150</td>\n", "      <td>3.631</td>\n", "      <td>1.293</td>\n", "      <td>33.269</td>\n", "      <td>0.405</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.180</td>\n", "      <td>0.145</td>\n", "      <td>14.019</td>\n", "      <td>3.198</td>\n", "      <td>448.517</td>\n", "      <td>0.120</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.518</td>\n", "      <td>0.410</td>\n", "      <td>23.584</td>\n", "      <td>4.671</td>\n", "      <td>696.410</td>\n", "      <td>0.333</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.363    0.362         1.517                0.823   \n", "1        etth2   0.276    0.273         0.607                0.832   \n", "2        ettm1   0.338    0.341         2.406                1.116   \n", "3        ettm2   0.176    0.176         2.771                1.155   \n", "4      weather   0.150    0.150         3.631                1.293   \n", "5  electricity   0.180    0.145        14.019                3.198   \n", "6      traffic   0.518    0.410        23.584                4.671   \n", "\n", "   fs5_total_train_time  fs5_best_val_metric  \n", "0                40.317                0.662  \n", "1                34.441                0.217  \n", "2                33.472                0.407  \n", "3                33.743                0.123  \n", "4                33.269                0.405  \n", "5               448.517                0.120  \n", "6               696.410                0.333  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}