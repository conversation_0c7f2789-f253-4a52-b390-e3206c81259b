{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": [" # TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "  **Using TTM-512-96 model with Frequency Tuning.**"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-10-04 09:07:43.310254: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-10-04 09:07:44.089127: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-10-04 09:07:47.466446: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import logging\n", "import math\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "\n", "from tsfm_public import TinyTimeMixerForPrediction, TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.visualization import plot_predictions\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "logging.basicConfig(level=logging.ERROR)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "SEED = 42\n", "set_seed(SEED)\n", "\n", "# Specify model parameters\n", "context_length = 512\n", "forecast_length = 96\n", "freeze_backbone = True\n", "enable_prefix_tuning = True\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_dataset() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm_v2_freq_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Get model path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TTM models for Only Research and Academic (Non-Commercial) Use are here: https://huggingface.co/ibm-research/ttm-research-r2\n", "# Please provide the branch name properly based on context_len and forecast_len\n", "\n", "hf_model_path = \"ibm-research/ttm-research-r2\"\n", "if context_length == 512:\n", "    hf_model_branch = \"main\"\n", "elif context_length == 1024 or context_length == 1536:\n", "    hf_model_branch = f\"{context_length}_{forecast_length}_ft_r2\"\n", "else:\n", "    raise ValueError(\"Valid context lengths are: 512, 1024, and 1536 for now. Stay tuned for more TTM models.\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: etth1, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 8033, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "a7c75b8186484a0bb2613087e9263f36", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.51k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "68391ef558864bceb3b151f58d42c775", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/3.44M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "2d09a76631f14e649aa97380cd5486a8", "version_major": 2, "version_minor": 0}, "text/plain": ["generation_config.json:   0%|          | 0.00/69.0 [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3641158640384674, 'eval_model_preparation_time': 0.0029, 'eval_runtime': 3.4329, 'eval_samples_per_second': 811.279, 'eval_steps_per_second': 12.817}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: etth1, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 311, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0006280291441834253\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0006280291441834253\n", "Using learning rate = 0.0006280291441834253\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='70' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 70/250 00:28 < 01:14, 2.43 it/s, Epoch 14/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.772100</td>\n", "      <td>0.677957</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.735000</td>\n", "      <td>0.677009</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.712900</td>\n", "      <td>0.676227</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.708100</td>\n", "      <td>0.675994</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.667200</td>\n", "      <td>0.677063</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.660600</td>\n", "      <td>0.679934</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.642500</td>\n", "      <td>0.685532</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.626100</td>\n", "      <td>0.695030</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.607000</td>\n", "      <td>0.696889</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.591100</td>\n", "      <td>0.710723</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.571500</td>\n", "      <td>0.725537</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.552800</td>\n", "      <td>0.723338</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.536200</td>\n", "      <td>0.747540</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.515100</td>\n", "      <td>0.759912</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23069104002816:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:08:21 EDT)\" (scheduled at 2024-10-04 09:08:21.237899-04:00)\n", "INFO:p-849332:t-23069104002816:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:08:36 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.8126076459884644 seconds, Total Train Time = 29.085506439208984\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.36364585161209106, 'eval_runtime': 1.0267, 'eval_samples_per_second': 2712.446, 'eval_steps_per_second': 42.854, 'epoch': 14.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: etth2, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 8033, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.364    0.364\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.27660802006721497, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 0.7318, 'eval_samples_per_second': 3805.928, 'eval_steps_per_second': 60.13}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: etth2, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 311, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0002477076355991711\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0002477076355991711\n", "Using learning rate = 0.0002477076355991711\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='70' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 70/250 00:28 < 01:15, 2.39 it/s, Epoch 14/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.295200</td>\n", "      <td>0.212050</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.274500</td>\n", "      <td>0.211893</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.245700</td>\n", "      <td>0.211690</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.236100</td>\n", "      <td>0.211553</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.232500</td>\n", "      <td>0.211572</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.223500</td>\n", "      <td>0.211775</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.205000</td>\n", "      <td>0.212336</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.198200</td>\n", "      <td>0.213646</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.193500</td>\n", "      <td>0.215267</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.185800</td>\n", "      <td>0.216570</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.181800</td>\n", "      <td>0.215838</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.174100</td>\n", "      <td>0.214250</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.170900</td>\n", "      <td>0.213147</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.168300</td>\n", "      <td>0.213569</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23070958139136:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:08:57 EDT)\" (scheduled at 2024-10-04 09:08:57.116463-04:00)\n", "INFO:p-849332:t-23070958139136:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:09:12 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.7992050477436611 seconds, Total Train Time = 29.094688653945923\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.27722853422164917, 'eval_runtime': 1.0587, 'eval_samples_per_second': 2630.492, 'eval_steps_per_second': 41.559, 'epoch': 14.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: ettm1, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.364    0.364\n", "1   etth2   0.277    0.277\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 33953, val = 11425, test = 11425\n", "WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3216254413127899, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 3.0306, 'eval_samples_per_second': 3769.85, 'eval_steps_per_second': 59.064}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: ettm1, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 1607, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0010974987654930567\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0010974987654930567\n", "Using learning rate = 0.0010974987654930567\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='286' max='1300' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 286/1300 00:35 < 02:05, 8.05 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.288200</td>\n", "      <td>0.383428</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.268400</td>\n", "      <td>0.392796</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.256200</td>\n", "      <td>0.402290</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.248000</td>\n", "      <td>0.402057</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.243600</td>\n", "      <td>0.400347</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.233100</td>\n", "      <td>0.401490</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.224400</td>\n", "      <td>0.399640</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.218200</td>\n", "      <td>0.406397</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.214000</td>\n", "      <td>0.416378</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.208100</td>\n", "      <td>0.420510</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.204500</td>\n", "      <td>0.431284</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23070958139136:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:09:35 EDT)\" (scheduled at 2024-10-04 09:09:35.591012-04:00)\n", "INFO:p-849332:t-23070958139136:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:09:50 EDT)\" executed successfully\n", "INFO:p-849332:t-23070958139136:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:05 EDT)\" (scheduled at 2024-10-04 09:09:50.591012-04:00)\n", "INFO:p-849332:t-23070958139136:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:05 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.1328961849212646 seconds, Total Train Time = 35.978880167007446\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3108648955821991, 'eval_runtime': 1.9303, 'eval_samples_per_second': 5918.836, 'eval_steps_per_second': 92.733, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: ettm2, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.364    0.364\n", "1   etth2   0.277    0.277\n", "2   ettm1   0.322    0.311\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 33953, val = 11425, test = 11425\n", "WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17139409482479095, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 3.0318, 'eval_samples_per_second': 3768.374, 'eval_steps_per_second': 59.041}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: ettm2, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 1607, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0013219411484660286\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0013219411484660286\n", "Using learning rate = 0.0013219411484660286\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='286' max='1300' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 286/1300 00:39 < 02:19, 7.24 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.183500</td>\n", "      <td>0.121340</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.163500</td>\n", "      <td>0.128155</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.155700</td>\n", "      <td>0.128910</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.151000</td>\n", "      <td>0.127212</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.145700</td>\n", "      <td>0.126899</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.146000</td>\n", "      <td>0.126764</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.138500</td>\n", "      <td>0.135505</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.134200</td>\n", "      <td>0.136716</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.130400</td>\n", "      <td>0.138214</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.128600</td>\n", "      <td>0.133596</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.126300</td>\n", "      <td>0.137331</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23069102954240:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:21 EDT)\" (scheduled at 2024-10-04 09:10:21.934245-04:00)\n", "INFO:p-849332:t-23069102954240:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:36 EDT)\" executed successfully\n", "INFO:p-849332:t-23069102954240:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:51 EDT)\" (scheduled at 2024-10-04 09:10:36.934245-04:00)\n", "INFO:p-849332:t-23069102954240:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:51 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.2798414880579168 seconds, Total Train Time = 40.0526020526886\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17053768038749695, 'eval_runtime': 2.0308, 'eval_samples_per_second': 5625.762, 'eval_steps_per_second': 88.141, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: weather, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.364    0.364\n", "1   etth2   0.277    0.277\n", "2   ettm1   0.322    0.311\n", "3   ettm2   0.171    0.171\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 36280, val = 5175, test = 10444\n", "WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1578841358423233, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 4.7155, 'eval_samples_per_second': 2214.823, 'eval_steps_per_second': 34.779}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: weather, context length: 512, prediction length 96\n", "INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 1723, val = 5175, test = 10444\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0013219411484660286\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0013219411484660286\n", "Using learning rate = 0.0013219411484660286\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='297' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 297/1350 00:39 < 02:20, 7.50 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.164400</td>\n", "      <td>0.400965</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.156600</td>\n", "      <td>0.411972</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.151400</td>\n", "      <td>0.424562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.146700</td>\n", "      <td>0.434680</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.141100</td>\n", "      <td>0.443567</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.135800</td>\n", "      <td>0.462677</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.132800</td>\n", "      <td>0.453966</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.130600</td>\n", "      <td>0.484906</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.129300</td>\n", "      <td>0.488840</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.124200</td>\n", "      <td>0.493310</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.119500</td>\n", "      <td>0.528248</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23071093761792:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:14 EDT)\" (scheduled at 2024-10-04 09:11:14.654216-04:00)\n", "INFO:p-849332:t-23071093761792:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:29 EDT)\" executed successfully\n", "INFO:p-849332:t-23071093761792:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:44 EDT)\" (scheduled at 2024-10-04 09:11:29.654216-04:00)\n", "INFO:p-849332:t-23071093761792:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:44 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.4791956381364302 seconds, Total Train Time = 40.24339771270752\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1534864753484726, 'eval_runtime': 2.9993, 'eval_samples_per_second': 3482.165, 'eval_steps_per_second': 54.68, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: electricity, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   dataset  zs_mse  fs5_mse\n", "0    etth1   0.364    0.364\n", "1    etth2   0.277    0.277\n", "2    ettm1   0.322    0.311\n", "3    ettm2   0.171    0.171\n", "4  weather   0.158    0.153\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 17805, val = 2537, test = 5165\n", "WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:17]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.16642886400222778, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 17.5817, 'eval_samples_per_second': 293.771, 'eval_steps_per_second': 9.214}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: electricity, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 800, val = 2537, test = 5165\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00014174741629268049\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00014174741629268049\n", "Using learning rate = 0.00014174741629268049\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1250' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1250/1250 08:34, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.200000</td>\n", "      <td>0.143117</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.198000</td>\n", "      <td>0.141135</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.195900</td>\n", "      <td>0.138918</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.193700</td>\n", "      <td>0.136844</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.190800</td>\n", "      <td>0.134736</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.188100</td>\n", "      <td>0.132839</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.185200</td>\n", "      <td>0.131205</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.182900</td>\n", "      <td>0.129960</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.180600</td>\n", "      <td>0.129008</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.178600</td>\n", "      <td>0.128143</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.176600</td>\n", "      <td>0.127417</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.175200</td>\n", "      <td>0.127078</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.173600</td>\n", "      <td>0.126531</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.172100</td>\n", "      <td>0.125496</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.170800</td>\n", "      <td>0.125140</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.169900</td>\n", "      <td>0.124904</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.168800</td>\n", "      <td>0.124422</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.168000</td>\n", "      <td>0.124320</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.166900</td>\n", "      <td>0.124035</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.166300</td>\n", "      <td>0.124053</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.165700</td>\n", "      <td>0.123601</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.164900</td>\n", "      <td>0.123492</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.164400</td>\n", "      <td>0.123469</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.163800</td>\n", "      <td>0.122978</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.163300</td>\n", "      <td>0.122880</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.162800</td>\n", "      <td>0.123042</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.162500</td>\n", "      <td>0.123237</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.161900</td>\n", "      <td>0.122722</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.161600</td>\n", "      <td>0.122502</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.161100</td>\n", "      <td>0.122304</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.161000</td>\n", "      <td>0.122278</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.160600</td>\n", "      <td>0.122204</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.160300</td>\n", "      <td>0.122023</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.160200</td>\n", "      <td>0.122520</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.160000</td>\n", "      <td>0.121933</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.159700</td>\n", "      <td>0.121884</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.159400</td>\n", "      <td>0.121899</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.159200</td>\n", "      <td>0.121840</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.159100</td>\n", "      <td>0.121886</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.159100</td>\n", "      <td>0.121761</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.158900</td>\n", "      <td>0.121728</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.159000</td>\n", "      <td>0.121783</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.158700</td>\n", "      <td>0.121812</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.158700</td>\n", "      <td>0.121707</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.158800</td>\n", "      <td>0.121747</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.158600</td>\n", "      <td>0.121713</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.158500</td>\n", "      <td>0.121719</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.158700</td>\n", "      <td>0.121724</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.158600</td>\n", "      <td>0.121715</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.158800</td>\n", "      <td>0.121715</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:30 EDT)\" (scheduled at 2024-10-04 09:12:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:00 EDT)\" (scheduled at 2024-10-04 09:12:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:15 EDT)\" (scheduled at 2024-10-04 09:13:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:30 EDT)\" (scheduled at 2024-10-04 09:13:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:45 EDT)\" (scheduled at 2024-10-04 09:13:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:00 EDT)\" (scheduled at 2024-10-04 09:13:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:15 EDT)\" (scheduled at 2024-10-04 09:14:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:30 EDT)\" (scheduled at 2024-10-04 09:14:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:45 EDT)\" (scheduled at 2024-10-04 09:14:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:00 EDT)\" (scheduled at 2024-10-04 09:14:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:15 EDT)\" (scheduled at 2024-10-04 09:15:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:30 EDT)\" (scheduled at 2024-10-04 09:15:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:45 EDT)\" (scheduled at 2024-10-04 09:15:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:00 EDT)\" (scheduled at 2024-10-04 09:15:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:15 EDT)\" (scheduled at 2024-10-04 09:16:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:30 EDT)\" (scheduled at 2024-10-04 09:16:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:45 EDT)\" (scheduled at 2024-10-04 09:16:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:00 EDT)\" (scheduled at 2024-10-04 09:16:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:15 EDT)\" (scheduled at 2024-10-04 09:17:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:30 EDT)\" (scheduled at 2024-10-04 09:17:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:45 EDT)\" (scheduled at 2024-10-04 09:17:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:00 EDT)\" (scheduled at 2024-10-04 09:17:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:15 EDT)\" (scheduled at 2024-10-04 09:18:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:30 EDT)\" (scheduled at 2024-10-04 09:18:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:45 EDT)\" (scheduled at 2024-10-04 09:18:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:00 EDT)\" (scheduled at 2024-10-04 09:18:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:15 EDT)\" (scheduled at 2024-10-04 09:19:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:30 EDT)\" (scheduled at 2024-10-04 09:19:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:45 EDT)\" (scheduled at 2024-10-04 09:19:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:00 EDT)\" (scheduled at 2024-10-04 09:19:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:15 EDT)\" (scheduled at 2024-10-04 09:20:00.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:15 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:30 EDT)\" (scheduled at 2024-10-04 09:20:15.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:30 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:45 EDT)\" (scheduled at 2024-10-04 09:20:30.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:45 EDT)\" executed successfully\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:00 EDT)\" (scheduled at 2024-10-04 09:20:45.751854-04:00)\n", "INFO:p-849332:t-23070964442880:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:00 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 3.542292618751526 seconds, Total Train Time = 516.1418724060059\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:11]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1458154022693634, 'eval_runtime': 12.4771, 'eval_samples_per_second': 413.958, 'eval_steps_per_second': 12.984, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: traffic, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["       dataset  zs_mse  fs5_mse\n", "0        etth1   0.364    0.364\n", "1        etth2   0.277    0.277\n", "2        ettm1   0.322    0.311\n", "3        ettm2   0.171    0.171\n", "4      weather   0.158    0.153\n", "5  electricity   0.166    0.146\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-512 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/main\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 11673, val = 1661, test = 3413\n", "WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:31]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.5142123699188232, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 31.6561, 'eval_samples_per_second': 107.815, 'eval_steps_per_second': 13.489}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Dataset name: traffic, context length: 512, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:data_handling.py:load_dataset:Data lengths: train = 493, val = 1661, test = 3413\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 854972\n", "Number of params after freezing the backbone 302162\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-849332:t-23083607622400:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-849332:t-23083607622400:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00011768119524349978\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00011768119524349978\n", "Using learning rate = 0.00011768119524349978\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23083607622400:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-849332:t-23083607622400:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='3100' max='3100' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [3100/3100 13:56, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.300400</td>\n", "      <td>0.419105</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.293100</td>\n", "      <td>0.412652</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.288100</td>\n", "      <td>0.407203</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.283400</td>\n", "      <td>0.403235</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.279000</td>\n", "      <td>0.400708</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.274600</td>\n", "      <td>0.394619</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.270100</td>\n", "      <td>0.390383</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.265900</td>\n", "      <td>0.388255</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.261400</td>\n", "      <td>0.382568</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.257300</td>\n", "      <td>0.376852</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.252700</td>\n", "      <td>0.370713</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.248900</td>\n", "      <td>0.369995</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.245000</td>\n", "      <td>0.365612</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.241800</td>\n", "      <td>0.360373</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.238700</td>\n", "      <td>0.359754</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.235900</td>\n", "      <td>0.357963</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.233700</td>\n", "      <td>0.354857</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.231700</td>\n", "      <td>0.353336</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.229900</td>\n", "      <td>0.353722</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.228100</td>\n", "      <td>0.348975</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.226400</td>\n", "      <td>0.348115</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.225300</td>\n", "      <td>0.347397</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.224200</td>\n", "      <td>0.346221</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.222900</td>\n", "      <td>0.345252</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.221900</td>\n", "      <td>0.346918</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.220900</td>\n", "      <td>0.344589</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.220100</td>\n", "      <td>0.344082</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.219600</td>\n", "      <td>0.344436</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.218500</td>\n", "      <td>0.343222</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.217700</td>\n", "      <td>0.343030</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.217300</td>\n", "      <td>0.343605</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.216700</td>\n", "      <td>0.341811</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.216200</td>\n", "      <td>0.341363</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.215800</td>\n", "      <td>0.341179</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.215400</td>\n", "      <td>0.341348</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.215000</td>\n", "      <td>0.340437</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.214800</td>\n", "      <td>0.340590</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.214300</td>\n", "      <td>0.340316</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.214200</td>\n", "      <td>0.340021</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.214000</td>\n", "      <td>0.340593</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.213800</td>\n", "      <td>0.340408</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.213600</td>\n", "      <td>0.339833</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.213500</td>\n", "      <td>0.340246</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.213400</td>\n", "      <td>0.339990</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.213400</td>\n", "      <td>0.339910</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.213200</td>\n", "      <td>0.340009</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.213100</td>\n", "      <td>0.339965</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.213200</td>\n", "      <td>0.339881</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.213200</td>\n", "      <td>0.339873</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.213100</td>\n", "      <td>0.339869</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:07 EDT)\" (scheduled at 2024-10-04 09:22:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:37 EDT)\" (scheduled at 2024-10-04 09:22:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:52 EDT)\" (scheduled at 2024-10-04 09:22:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:07 EDT)\" (scheduled at 2024-10-04 09:22:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:22 EDT)\" (scheduled at 2024-10-04 09:23:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:37 EDT)\" (scheduled at 2024-10-04 09:23:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:52 EDT)\" (scheduled at 2024-10-04 09:23:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:07 EDT)\" (scheduled at 2024-10-04 09:23:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:22 EDT)\" (scheduled at 2024-10-04 09:24:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:37 EDT)\" (scheduled at 2024-10-04 09:24:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:52 EDT)\" (scheduled at 2024-10-04 09:24:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:07 EDT)\" (scheduled at 2024-10-04 09:24:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:22 EDT)\" (scheduled at 2024-10-04 09:25:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:37 EDT)\" (scheduled at 2024-10-04 09:25:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:52 EDT)\" (scheduled at 2024-10-04 09:25:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:07 EDT)\" (scheduled at 2024-10-04 09:25:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:22 EDT)\" (scheduled at 2024-10-04 09:26:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:37 EDT)\" (scheduled at 2024-10-04 09:26:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:52 EDT)\" (scheduled at 2024-10-04 09:26:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:07 EDT)\" (scheduled at 2024-10-04 09:26:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:22 EDT)\" (scheduled at 2024-10-04 09:27:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:37 EDT)\" (scheduled at 2024-10-04 09:27:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:52 EDT)\" (scheduled at 2024-10-04 09:27:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:07 EDT)\" (scheduled at 2024-10-04 09:27:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:22 EDT)\" (scheduled at 2024-10-04 09:28:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:37 EDT)\" (scheduled at 2024-10-04 09:28:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:52 EDT)\" (scheduled at 2024-10-04 09:28:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:07 EDT)\" (scheduled at 2024-10-04 09:28:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:22 EDT)\" (scheduled at 2024-10-04 09:29:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:37 EDT)\" (scheduled at 2024-10-04 09:29:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:52 EDT)\" (scheduled at 2024-10-04 09:29:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:07 EDT)\" (scheduled at 2024-10-04 09:29:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:22 EDT)\" (scheduled at 2024-10-04 09:30:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:37 EDT)\" (scheduled at 2024-10-04 09:30:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:52 EDT)\" (scheduled at 2024-10-04 09:30:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:07 EDT)\" (scheduled at 2024-10-04 09:30:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:22 EDT)\" (scheduled at 2024-10-04 09:31:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:37 EDT)\" (scheduled at 2024-10-04 09:31:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:52 EDT)\" (scheduled at 2024-10-04 09:31:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:07 EDT)\" (scheduled at 2024-10-04 09:31:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:22 EDT)\" (scheduled at 2024-10-04 09:32:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:37 EDT)\" (scheduled at 2024-10-04 09:32:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:52 EDT)\" (scheduled at 2024-10-04 09:32:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:07 EDT)\" (scheduled at 2024-10-04 09:32:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:22 EDT)\" (scheduled at 2024-10-04 09:33:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:37 EDT)\" (scheduled at 2024-10-04 09:33:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:52 EDT)\" (scheduled at 2024-10-04 09:33:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:07 EDT)\" (scheduled at 2024-10-04 09:33:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:22 EDT)\" (scheduled at 2024-10-04 09:34:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:37 EDT)\" (scheduled at 2024-10-04 09:34:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:52 EDT)\" (scheduled at 2024-10-04 09:34:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:07 EDT)\" (scheduled at 2024-10-04 09:34:52.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:07 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:22 EDT)\" (scheduled at 2024-10-04 09:35:07.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:22 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:37 EDT)\" (scheduled at 2024-10-04 09:35:22.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:37 EDT)\" executed successfully\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:52 EDT)\" (scheduled at 2024-10-04 09:35:37.068300-04:00)\n", "INFO:p-849332:t-23068871223040:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:52 EDT)\" executed successfully\n", "INFO:p-849332:t-23083607622400:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-849332:t-23083607622400:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 5.444428825378418 seconds, Total Train Time = 837.8258655071259\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:20]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.41814321279525757, 'eval_runtime': 21.6958, 'eval_samples_per_second': 157.311, 'eval_steps_per_second': 19.681, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.364    0.364\n", "1        etth2   0.277    0.277\n", "2        ettm1   0.322    0.311\n", "3        ettm2   0.171    0.171\n", "4      weather   0.158    0.153\n", "5  electricity   0.166    0.146\n", "6      traffic   0.514    0.418\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}/{hf_model_branch}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(\n", "        DATASET,\n", "        context_length,\n", "        forecast_length,\n", "        dataset_root_path=DATA_ROOT_PATH,\n", "        use_frequency_token=enable_prefix_tuning,\n", "    )\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = TinyTimeMixerForPrediction.from_pretrained(hf_model_path, revision=hf_model_branch)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            seed=SEED,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5]:\n", "        # Set learning rate\n", "        learning_rate = None  # `None` value indicates that the optimal_lr_finder() will be used\n", "\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "            use_frequency_token=enable_prefix_tuning,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        if learning_rate is None:\n", "            learning_rate, finetune_forecast_model = optimal_lr_finder(\n", "                finetune_forecast_model,\n", "                dset_train,\n", "                batch_size=BATCH_SIZE,\n", "                enable_prefix_tuning=enable_prefix_tuning,\n", "            )\n", "            print(\"OPTIMAL SUGGESTED LEARNING RATE =\", learning_rate)\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "            seed=SEED,\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.364</td>\n", "      <td>0.364</td>\n", "      <td>3.433</td>\n", "      <td>0.813</td>\n", "      <td>29.086</td>\n", "      <td>0.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.277</td>\n", "      <td>0.277</td>\n", "      <td>0.732</td>\n", "      <td>0.799</td>\n", "      <td>29.095</td>\n", "      <td>0.212</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.322</td>\n", "      <td>0.311</td>\n", "      <td>3.031</td>\n", "      <td>1.133</td>\n", "      <td>35.979</td>\n", "      <td>0.383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.171</td>\n", "      <td>0.171</td>\n", "      <td>3.032</td>\n", "      <td>1.280</td>\n", "      <td>40.053</td>\n", "      <td>0.121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.158</td>\n", "      <td>0.153</td>\n", "      <td>4.716</td>\n", "      <td>1.479</td>\n", "      <td>40.243</td>\n", "      <td>0.401</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.166</td>\n", "      <td>0.146</td>\n", "      <td>17.582</td>\n", "      <td>3.542</td>\n", "      <td>516.142</td>\n", "      <td>0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.514</td>\n", "      <td>0.418</td>\n", "      <td>31.656</td>\n", "      <td>5.444</td>\n", "      <td>837.826</td>\n", "      <td>0.340</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.364    0.364         3.433                0.813   \n", "1        etth2   0.277    0.277         0.732                0.799   \n", "2        ettm1   0.322    0.311         3.031                1.133   \n", "3        ettm2   0.171    0.171         3.032                1.280   \n", "4      weather   0.158    0.153         4.716                1.479   \n", "5  electricity   0.166    0.146        17.582                3.542   \n", "6      traffic   0.514    0.418        31.656                5.444   \n", "\n", "   fs5_total_train_time  fs5_best_val_metric  \n", "0                29.086                0.676  \n", "1                29.095                0.212  \n", "2                35.979                0.383  \n", "3                40.053                0.121  \n", "4                40.243                0.401  \n", "5               516.142                0.122  \n", "6               837.826                0.340  "]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}