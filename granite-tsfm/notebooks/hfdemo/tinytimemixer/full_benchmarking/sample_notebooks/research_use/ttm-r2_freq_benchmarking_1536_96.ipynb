{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": [" # TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "  **Using TTM-1536-96 model with Frequency Tuning.**"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-10-04 09:11:04.868399: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-10-04 09:11:04.917034: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-10-04 09:11:05.640376: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import logging\n", "import math\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "\n", "from tsfm_public import TinyTimeMixerForPrediction, TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.visualization import plot_predictions\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "logging.basicConfig(level=logging.ERROR)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "SEED = 42\n", "set_seed(SEED)\n", "\n", "# Specify model parameters\n", "context_length = 1536\n", "forecast_length = 96\n", "freeze_backbone = True\n", "enable_prefix_tuning = True\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_dataset() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm_v2_freq_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Get model path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TTM models for Only Research and Academic (Non-Commercial) Use are here: https://huggingface.co/ibm-research/ttm-research-r2\n", "# Please provide the branch name properly based on context_len and forecast_len\n", "\n", "hf_model_path = \"ibm-research/ttm-research-r2\"\n", "if context_length == 512:\n", "    hf_model_branch = \"main\"\n", "elif context_length == 1024 or context_length == 1536:\n", "    hf_model_branch = f\"{context_length}_{forecast_length}_ft_r2\"\n", "else:\n", "    raise ValueError(\"Valid context lengths are: 512, 1024, and 1536 for now. Stay tuned for more TTM models.\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: etth1, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 7009, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "5dd422436cfd43dd8cf226ddf999c210", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.51k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "0d8b6eaa0b024d6297b46248a189418a", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/13.0M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3588193356990814, 'eval_model_preparation_time': 0.0029, 'eval_runtime': 2.5439, 'eval_samples_per_second': 1094.782, 'eval_steps_per_second': 17.296}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: etth1, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 260, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0005214008287999684\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0005214008287999684\n", "Using learning rate = 0.0005214008287999684\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:28 < 01:43, 1.88 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.706600</td>\n", "      <td>0.676132</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.590100</td>\n", "      <td>0.676201</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.489000</td>\n", "      <td>0.676943</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.389600</td>\n", "      <td>0.680147</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.344500</td>\n", "      <td>0.689842</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.310300</td>\n", "      <td>0.714360</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.294400</td>\n", "      <td>0.751174</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.258300</td>\n", "      <td>0.766458</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.248700</td>\n", "      <td>0.809178</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.222200</td>\n", "      <td>0.848143</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.215800</td>\n", "      <td>0.868455</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23215233554176:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:31 EDT)\" (scheduled at 2024-10-04 09:11:31.009870-04:00)\n", "INFO:p-861228:t-23215233554176:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:46 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.0010949481617322 seconds, Total Train Time = 29.304269552230835\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.35881510376930237, 'eval_runtime': 1.3471, 'eval_samples_per_second': 2067.478, 'eval_steps_per_second': 32.664, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: etth2, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 7009, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2640553414821625, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 1.2594, 'eval_samples_per_second': 2211.296, 'eval_steps_per_second': 34.936}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: etth2, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 260, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00020565123083486514\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='90' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 90/250 00:46 < 01:25, 1.88 it/s, Epoch 18/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.436100</td>\n", "      <td>0.226110</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.402200</td>\n", "      <td>0.226250</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.344900</td>\n", "      <td>0.226580</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.311700</td>\n", "      <td>0.226758</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.302500</td>\n", "      <td>0.226712</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.235200</td>\n", "      <td>0.226074</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.199100</td>\n", "      <td>0.224492</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.176200</td>\n", "      <td>0.224491</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.148300</td>\n", "      <td>0.229641</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.133200</td>\n", "      <td>0.238513</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.126300</td>\n", "      <td>0.249538</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.127900</td>\n", "      <td>0.257025</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.114400</td>\n", "      <td>0.268801</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.103800</td>\n", "      <td>0.276413</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.108000</td>\n", "      <td>0.277767</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.101900</td>\n", "      <td>0.283584</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.097500</td>\n", "      <td>0.285750</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.097900</td>\n", "      <td>0.283838</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23221903103744:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:08 EDT)\" (scheduled at 2024-10-04 09:12:08.603368-04:00)\n", "INFO:p-861228:t-23221903103744:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:23 EDT)\" executed successfully\n", "INFO:p-861228:t-23221903103744:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:38 EDT)\" (scheduled at 2024-10-04 09:12:23.603368-04:00)\n", "INFO:p-861228:t-23221903103744:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:38 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.9902433421876695 seconds, Total Train Time = 47.71788811683655\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2693004012107849, 'eval_runtime': 1.6154, 'eval_samples_per_second': 1724.008, 'eval_steps_per_second': 27.237, 'epoch': 18.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: ettm1, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 32929, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "1   etth2   0.264    0.269\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3177188038825989, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 4.7801, 'eval_samples_per_second': 2390.124, 'eval_steps_per_second': 37.447}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: ettm1, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 1556, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 9.770099572992256e-05\n", "OPTIMAL SUGGESTED LEARNING RATE = 9.770099572992256e-05\n", "Using learning rate = 9.770099572992256e-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:49 < 02:55, 5.55 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.946500</td>\n", "      <td>0.383166</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.807700</td>\n", "      <td>0.385413</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.642300</td>\n", "      <td>0.389591</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.530900</td>\n", "      <td>0.397725</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.428000</td>\n", "      <td>0.410418</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.365300</td>\n", "      <td>0.428604</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.329900</td>\n", "      <td>0.452348</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.303900</td>\n", "      <td>0.461395</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.286200</td>\n", "      <td>0.454643</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.272300</td>\n", "      <td>0.454069</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.264200</td>\n", "      <td>0.455587</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23217620121344:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:08 EDT)\" (scheduled at 2024-10-04 09:13:08.581729-04:00)\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:23 EDT)\" executed successfully\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:38 EDT)\" (scheduled at 2024-10-04 09:13:23.581729-04:00)\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:38 EDT)\" executed successfully\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:53 EDT)\" (scheduled at 2024-10-04 09:13:38.581729-04:00)\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:53 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.433471766385165 seconds, Total Train Time = 50.059786319732666\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.31683409214019775, 'eval_runtime': 2.8235, 'eval_samples_per_second': 4046.381, 'eval_steps_per_second': 63.396, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: ettm2, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 32929, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "1   etth2   0.264    0.269\n", "2   ettm1   0.318    0.317\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.16930672526359558, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 4.8089, 'eval_samples_per_second': 2375.807, 'eval_steps_per_second': 37.223}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: ettm2, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 1556, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 9.770099572992256e-05\n", "OPTIMAL SUGGESTED LEARNING RATE = 9.770099572992256e-05\n", "Using learning rate = 9.770099572992256e-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:48 < 02:52, 5.65 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.408900</td>\n", "      <td>0.121544</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.352800</td>\n", "      <td>0.121782</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.276300</td>\n", "      <td>0.122294</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.195100</td>\n", "      <td>0.123225</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.152900</td>\n", "      <td>0.125274</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.123600</td>\n", "      <td>0.132202</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.107600</td>\n", "      <td>0.143401</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.100600</td>\n", "      <td>0.152680</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.096500</td>\n", "      <td>0.161098</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.093800</td>\n", "      <td>0.165741</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.091600</td>\n", "      <td>0.170623</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23217620121344:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:12 EDT)\" (scheduled at 2024-10-04 09:14:12.055758-04:00)\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:27 EDT)\" executed successfully\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:42 EDT)\" (scheduled at 2024-10-04 09:14:27.055758-04:00)\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:42 EDT)\" executed successfully\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:42 EDT)\" executed successfully\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:57 EDT)\" (scheduled at 2024-10-04 09:14:42.055758-04:00)\n", "INFO:p-861228:t-23217620121344:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:57 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.3992452404715798 seconds, Total Train Time = 49.18882489204407\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1694120466709137, 'eval_runtime': 2.8815, 'eval_samples_per_second': 3964.902, 'eval_steps_per_second': 62.12, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: weather, context length: 1536, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "1   etth2   0.264    0.269\n", "2   ettm1   0.318    0.317\n", "3   ettm2   0.169    0.169\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 35256, val = 5175, test = 10444\n", "WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:09]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15942735970020294, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 9.6351, 'eval_samples_per_second': 1083.958, 'eval_steps_per_second': 17.021}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: weather, context length: 1536, prediction length 96\n", "INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 1672, val = 5175, test = 10444\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.000298364724028334\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.000298364724028334\n", "Using learning rate = 0.000298364724028334\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='297' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 297/1350 00:57 < 03:24, 5.15 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.106200</td>\n", "      <td>0.392984</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.100900</td>\n", "      <td>0.393534</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.095400</td>\n", "      <td>0.394598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.088200</td>\n", "      <td>0.397990</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.080900</td>\n", "      <td>0.400138</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.074700</td>\n", "      <td>0.409341</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.068600</td>\n", "      <td>0.411072</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.064000</td>\n", "      <td>0.413273</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.059300</td>\n", "      <td>0.425014</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.055800</td>\n", "      <td>0.413878</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.053400</td>\n", "      <td>0.417557</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23215315343104:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:21 EDT)\" (scheduled at 2024-10-04 09:15:21.383105-04:00)\n", "INFO:p-861228:t-23215315343104:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:36 EDT)\" executed successfully\n", "INFO:p-861228:t-23215315343104:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:51 EDT)\" (scheduled at 2024-10-04 09:15:36.383105-04:00)\n", "INFO:p-861228:t-23215315343104:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:15:51 EDT)\" executed successfully\n", "INFO:p-861228:t-23215315343104:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:06 EDT)\" (scheduled at 2024-10-04 09:15:51.383105-04:00)\n", "INFO:p-861228:t-23215315343104:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:06 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.972560167312622 seconds, Total Train Time = 58.1689395904541\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15510673820972443, 'eval_runtime': 5.2632, 'eval_samples_per_second': 1984.335, 'eval_steps_per_second': 31.16, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: electricity, context length: 1536, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   dataset  zs_mse  fs5_mse\n", "0    etth1   0.359    0.359\n", "1    etth2   0.264    0.269\n", "2    ettm1   0.318    0.317\n", "3    ettm2   0.169    0.169\n", "4  weather   0.159    0.155\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 16781, val = 2537, test = 5165\n", "WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:39]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15161459147930145, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 39.4884, 'eval_samples_per_second': 130.798, 'eval_steps_per_second': 4.102}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: electricity, context length: 1536, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 748, val = 2537, test = 5165\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0002477076355991711\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0002477076355991711\n", "Using learning rate = 0.0002477076355991711\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1200' max='1200' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1200/1200 17:23, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.140300</td>\n", "      <td>0.126572</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.136400</td>\n", "      <td>0.124738</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.134500</td>\n", "      <td>0.123417</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.132500</td>\n", "      <td>0.121974</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.130200</td>\n", "      <td>0.120977</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.128100</td>\n", "      <td>0.119957</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.126300</td>\n", "      <td>0.119561</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.124500</td>\n", "      <td>0.118178</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.123200</td>\n", "      <td>0.117826</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.121200</td>\n", "      <td>0.117279</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.119100</td>\n", "      <td>0.116886</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.118700</td>\n", "      <td>0.116810</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.116900</td>\n", "      <td>0.116884</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.116500</td>\n", "      <td>0.116189</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.115200</td>\n", "      <td>0.116396</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.114900</td>\n", "      <td>0.116871</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.113600</td>\n", "      <td>0.115763</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.113100</td>\n", "      <td>0.115339</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.112200</td>\n", "      <td>0.115565</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.111100</td>\n", "      <td>0.115062</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.110700</td>\n", "      <td>0.114427</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.110100</td>\n", "      <td>0.114737</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.109700</td>\n", "      <td>0.114358</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.109300</td>\n", "      <td>0.114306</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.109100</td>\n", "      <td>0.114210</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.108600</td>\n", "      <td>0.114578</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.108100</td>\n", "      <td>0.114918</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.107800</td>\n", "      <td>0.114290</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.107400</td>\n", "      <td>0.113889</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.106800</td>\n", "      <td>0.114318</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.106600</td>\n", "      <td>0.114354</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.106400</td>\n", "      <td>0.113839</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.106500</td>\n", "      <td>0.113933</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.106100</td>\n", "      <td>0.113754</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.106100</td>\n", "      <td>0.113705</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.105500</td>\n", "      <td>0.113696</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.105200</td>\n", "      <td>0.113619</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.104900</td>\n", "      <td>0.113821</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.105100</td>\n", "      <td>0.113573</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.105400</td>\n", "      <td>0.113611</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.105100</td>\n", "      <td>0.113580</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.104700</td>\n", "      <td>0.113523</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.105000</td>\n", "      <td>0.113410</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.104500</td>\n", "      <td>0.113375</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.105000</td>\n", "      <td>0.113353</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.104700</td>\n", "      <td>0.113399</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.104700</td>\n", "      <td>0.113472</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.104600</td>\n", "      <td>0.113407</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.104700</td>\n", "      <td>0.113441</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.104700</td>\n", "      <td>0.113444</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:30 EDT)\" (scheduled at 2024-10-04 09:17:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:00 EDT)\" (scheduled at 2024-10-04 09:17:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:15 EDT)\" (scheduled at 2024-10-04 09:18:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:30 EDT)\" (scheduled at 2024-10-04 09:18:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:45 EDT)\" (scheduled at 2024-10-04 09:18:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:00 EDT)\" (scheduled at 2024-10-04 09:18:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:15 EDT)\" (scheduled at 2024-10-04 09:19:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:30 EDT)\" (scheduled at 2024-10-04 09:19:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:45 EDT)\" (scheduled at 2024-10-04 09:19:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:00 EDT)\" (scheduled at 2024-10-04 09:19:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:15 EDT)\" (scheduled at 2024-10-04 09:20:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:30 EDT)\" (scheduled at 2024-10-04 09:20:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:45 EDT)\" (scheduled at 2024-10-04 09:20:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:00 EDT)\" (scheduled at 2024-10-04 09:20:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:15 EDT)\" (scheduled at 2024-10-04 09:21:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:30 EDT)\" (scheduled at 2024-10-04 09:21:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:45 EDT)\" (scheduled at 2024-10-04 09:21:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:00 EDT)\" (scheduled at 2024-10-04 09:21:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:15 EDT)\" (scheduled at 2024-10-04 09:22:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:30 EDT)\" (scheduled at 2024-10-04 09:22:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:45 EDT)\" (scheduled at 2024-10-04 09:22:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:00 EDT)\" (scheduled at 2024-10-04 09:22:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:15 EDT)\" (scheduled at 2024-10-04 09:23:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:30 EDT)\" (scheduled at 2024-10-04 09:23:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:45 EDT)\" (scheduled at 2024-10-04 09:23:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:00 EDT)\" (scheduled at 2024-10-04 09:23:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:15 EDT)\" (scheduled at 2024-10-04 09:24:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:30 EDT)\" (scheduled at 2024-10-04 09:24:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:45 EDT)\" (scheduled at 2024-10-04 09:24:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:00 EDT)\" (scheduled at 2024-10-04 09:24:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:15 EDT)\" (scheduled at 2024-10-04 09:25:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:30 EDT)\" (scheduled at 2024-10-04 09:25:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:45 EDT)\" (scheduled at 2024-10-04 09:25:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:00 EDT)\" (scheduled at 2024-10-04 09:25:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:15 EDT)\" (scheduled at 2024-10-04 09:26:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:30 EDT)\" (scheduled at 2024-10-04 09:26:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:45 EDT)\" (scheduled at 2024-10-04 09:26:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:00 EDT)\" (scheduled at 2024-10-04 09:26:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:15 EDT)\" (scheduled at 2024-10-04 09:27:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:30 EDT)\" (scheduled at 2024-10-04 09:27:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:45 EDT)\" (scheduled at 2024-10-04 09:27:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:00 EDT)\" (scheduled at 2024-10-04 09:27:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:15 EDT)\" (scheduled at 2024-10-04 09:28:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:30 EDT)\" (scheduled at 2024-10-04 09:28:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:45 EDT)\" (scheduled at 2024-10-04 09:28:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:00 EDT)\" (scheduled at 2024-10-04 09:28:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:15 EDT)\" (scheduled at 2024-10-04 09:29:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:30 EDT)\" (scheduled at 2024-10-04 09:29:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:45 EDT)\" (scheduled at 2024-10-04 09:29:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:00 EDT)\" (scheduled at 2024-10-04 09:29:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:15 EDT)\" (scheduled at 2024-10-04 09:30:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:30 EDT)\" (scheduled at 2024-10-04 09:30:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:45 EDT)\" (scheduled at 2024-10-04 09:30:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:30:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:00 EDT)\" (scheduled at 2024-10-04 09:30:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:15 EDT)\" (scheduled at 2024-10-04 09:31:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:30 EDT)\" (scheduled at 2024-10-04 09:31:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:45 EDT)\" (scheduled at 2024-10-04 09:31:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:00 EDT)\" (scheduled at 2024-10-04 09:31:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:15 EDT)\" (scheduled at 2024-10-04 09:32:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:30 EDT)\" (scheduled at 2024-10-04 09:32:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:45 EDT)\" (scheduled at 2024-10-04 09:32:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:00 EDT)\" (scheduled at 2024-10-04 09:32:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:15 EDT)\" (scheduled at 2024-10-04 09:33:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:30 EDT)\" (scheduled at 2024-10-04 09:33:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:45 EDT)\" (scheduled at 2024-10-04 09:33:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:00 EDT)\" (scheduled at 2024-10-04 09:33:45.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:00 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:15 EDT)\" (scheduled at 2024-10-04 09:34:00.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:15 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:30 EDT)\" (scheduled at 2024-10-04 09:34:15.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:30 EDT)\" executed successfully\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:45 EDT)\" (scheduled at 2024-10-04 09:34:30.668008-04:00)\n", "INFO:p-861228:t-23215225161472:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:45 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 6.7941923379898075 seconds, Total Train Time = 1045.3023135662079\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:25]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.13990454375743866, 'eval_runtime': 26.7672, 'eval_samples_per_second': 192.96, 'eval_steps_per_second': 6.052, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: traffic, context length: 1536, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["       dataset  zs_mse  fs5_mse\n", "0        etth1   0.359    0.359\n", "1        etth2   0.264    0.269\n", "2        ettm1   0.318    0.317\n", "3        ettm2   0.169    0.169\n", "4      weather   0.159    0.155\n", "5  electricity   0.152    0.140\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1536 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1536_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 10649, val = 1661, test = 3413\n", "WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 01:18]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4620630145072937, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 78.9449, 'eval_samples_per_second': 43.233, 'eval_steps_per_second': 5.409}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Dataset name: traffic, context length: 1536, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:data_handling.py:load_dataset:Data lengths: train = 442, val = 1661, test = 3413\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 3243996\n", "Number of params after freezing the backbone 1079394\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-861228:t-23229240083200:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-861228:t-23229240083200:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 8.111308307896872e-05\n", "OPTIMAL SUGGESTED LEARNING RATE = 8.111308307896872e-05\n", "Using learning rate = 8.111308307896872e-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23229240083200:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-861228:t-23229240083200:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='616' max='2800' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 616/2800 06:27 < 22:57, 1.59 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.295900</td>\n", "      <td>0.390884</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.282800</td>\n", "      <td>0.397275</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.272100</td>\n", "      <td>0.398559</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.261400</td>\n", "      <td>0.400883</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.251000</td>\n", "      <td>0.404521</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.242200</td>\n", "      <td>0.408792</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.233900</td>\n", "      <td>0.412845</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.227200</td>\n", "      <td>0.413990</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.221400</td>\n", "      <td>0.415893</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.217000</td>\n", "      <td>0.420565</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.213100</td>\n", "      <td>0.419890</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:05 EDT)\" (scheduled at 2024-10-04 09:37:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:35 EDT)\" (scheduled at 2024-10-04 09:37:20.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:35 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:50 EDT)\" (scheduled at 2024-10-04 09:37:35.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:50 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:05 EDT)\" (scheduled at 2024-10-04 09:37:50.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:05 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:20 EDT)\" (scheduled at 2024-10-04 09:38:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:35 EDT)\" (scheduled at 2024-10-04 09:38:20.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:35 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:50 EDT)\" (scheduled at 2024-10-04 09:38:35.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:50 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:05 EDT)\" (scheduled at 2024-10-04 09:38:50.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:05 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:20 EDT)\" (scheduled at 2024-10-04 09:39:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:35 EDT)\" (scheduled at 2024-10-04 09:39:20.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:35 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:50 EDT)\" (scheduled at 2024-10-04 09:39:35.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:50 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:05 EDT)\" (scheduled at 2024-10-04 09:39:50.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:05 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:20 EDT)\" (scheduled at 2024-10-04 09:40:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:35 EDT)\" (scheduled at 2024-10-04 09:40:20.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:35 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:50 EDT)\" (scheduled at 2024-10-04 09:40:35.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:50 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:05 EDT)\" (scheduled at 2024-10-04 09:40:50.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:05 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:20 EDT)\" (scheduled at 2024-10-04 09:41:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:35 EDT)\" (scheduled at 2024-10-04 09:41:20.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:35 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:50 EDT)\" (scheduled at 2024-10-04 09:41:35.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:50 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:05 EDT)\" (scheduled at 2024-10-04 09:41:50.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:05 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:20 EDT)\" (scheduled at 2024-10-04 09:42:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:35 EDT)\" (scheduled at 2024-10-04 09:42:20.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:35 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:50 EDT)\" (scheduled at 2024-10-04 09:42:35.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:50 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:05 EDT)\" (scheduled at 2024-10-04 09:42:50.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:05 EDT)\" executed successfully\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:20 EDT)\" (scheduled at 2024-10-04 09:43:05.631304-04:00)\n", "INFO:p-861228:t-23215218599680:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:20 EDT)\" executed successfully\n", "INFO:p-861228:t-23229240083200:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-861228:t-23229240083200:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 10.461192239414562 seconds, Total Train Time = 388.9561378955841\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:47]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4685199558734894, 'eval_runtime': 48.9684, 'eval_samples_per_second': 69.698, 'eval_steps_per_second': 8.72, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.359    0.359\n", "1        etth2   0.264    0.269\n", "2        ettm1   0.318    0.317\n", "3        ettm2   0.169    0.169\n", "4      weather   0.159    0.155\n", "5  electricity   0.152    0.140\n", "6      traffic   0.462    0.469\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}/{hf_model_branch}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(\n", "        DATASET,\n", "        context_length,\n", "        forecast_length,\n", "        dataset_root_path=DATA_ROOT_PATH,\n", "        use_frequency_token=enable_prefix_tuning,\n", "    )\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = TinyTimeMixerForPrediction.from_pretrained(hf_model_path, revision=hf_model_branch)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            seed=SEED,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5]:\n", "        # Set learning rate\n", "        learning_rate = None  # `None` value indicates that the optimal_lr_finder() will be used\n", "\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "            use_frequency_token=enable_prefix_tuning,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        if learning_rate is None:\n", "            learning_rate, finetune_forecast_model = optimal_lr_finder(\n", "                finetune_forecast_model,\n", "                dset_train,\n", "                batch_size=BATCH_SIZE,\n", "                enable_prefix_tuning=enable_prefix_tuning,\n", "            )\n", "            print(\"OPTIMAL SUGGESTED LEARNING RATE =\", learning_rate)\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "            seed=SEED,\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.359</td>\n", "      <td>0.359</td>\n", "      <td>2.544</td>\n", "      <td>1.001</td>\n", "      <td>29.304</td>\n", "      <td>0.676</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.264</td>\n", "      <td>0.269</td>\n", "      <td>1.259</td>\n", "      <td>0.990</td>\n", "      <td>47.718</td>\n", "      <td>0.224</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.318</td>\n", "      <td>0.317</td>\n", "      <td>4.780</td>\n", "      <td>1.433</td>\n", "      <td>50.060</td>\n", "      <td>0.383</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.169</td>\n", "      <td>0.169</td>\n", "      <td>4.809</td>\n", "      <td>1.399</td>\n", "      <td>49.189</td>\n", "      <td>0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.159</td>\n", "      <td>0.155</td>\n", "      <td>9.635</td>\n", "      <td>1.973</td>\n", "      <td>58.169</td>\n", "      <td>0.393</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.152</td>\n", "      <td>0.140</td>\n", "      <td>39.488</td>\n", "      <td>6.794</td>\n", "      <td>1045.302</td>\n", "      <td>0.113</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.462</td>\n", "      <td>0.469</td>\n", "      <td>78.945</td>\n", "      <td>10.461</td>\n", "      <td>388.956</td>\n", "      <td>0.391</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.359    0.359         2.544                1.001   \n", "1        etth2   0.264    0.269         1.259                0.990   \n", "2        ettm1   0.318    0.317         4.780                1.433   \n", "3        ettm2   0.169    0.169         4.809                1.399   \n", "4      weather   0.159    0.155         9.635                1.973   \n", "5  electricity   0.152    0.140        39.488                6.794   \n", "6      traffic   0.462    0.469        78.945               10.461   \n", "\n", "   fs5_total_train_time  fs5_best_val_metric  \n", "0                29.304                0.676  \n", "1                47.718                0.224  \n", "2                50.060                0.383  \n", "3                49.189                0.122  \n", "4                58.169                0.393  \n", "5              1045.302                0.113  \n", "6               388.956                0.391  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}