{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": [" # TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "  **Using TTM-1024-96 model with Frequency Tuning.**"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-10-04 09:09:31.338304: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-10-04 09:09:31.388332: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-10-04 09:09:33.367705: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import logging\n", "import math\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "\n", "from tsfm_public import TinyTimeMixerForPrediction, TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.visualization import plot_predictions\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")\n", "\n", "\n", "logging.basicConfig(level=logging.ERROR)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "SEED = 42\n", "set_seed(SEED)\n", "\n", "# Specify model parameters\n", "context_length = 1024\n", "forecast_length = 96\n", "freeze_backbone = True\n", "enable_prefix_tuning = True\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_dataset() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm_v2_freq_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Get model path"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# TTM models for Only Research and Academic (Non-Commercial) Use are here: https://huggingface.co/ibm/ttm-research-r2\n", "# Please provide the branch name properly based on context_len and forecast_len\n", "\n", "hf_model_path = \"ibm-research/ttm-research-r2\"\n", "if context_length == 512:\n", "    hf_model_branch = \"main\"\n", "elif context_length == 1024 or context_length == 1536:\n", "    hf_model_branch = f\"{context_length}_{forecast_length}_ft_r2\"\n", "else:\n", "    raise ValueError(\"Valid context lengths are: 512, 1024, and 1536 for now. Stay tuned for more TTM models.\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: etth1, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 7521, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "05912e0244824f5082e37db1d808b62b", "version_major": 2, "version_minor": 0}, "text/plain": ["config.json:   0%|          | 0.00/1.51k [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "811d9832d129491d8cf0f88d75b1499a", "version_major": 2, "version_minor": 0}, "text/plain": ["model.safetensors:   0%|          | 0.00/12.5M [00:00<?, ?B/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.36299267411231995, 'eval_model_preparation_time': 0.0029, 'eval_runtime': 4.4622, 'eval_samples_per_second': 624.135, 'eval_steps_per_second': 9.861}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: etth1, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 285, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00020565123083486514\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='60' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 60/250 00:33 < 01:48, 1.75 it/s, Epoch 12/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.452500</td>\n", "      <td>0.679759</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.376100</td>\n", "      <td>0.679738</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.264900</td>\n", "      <td>0.679831</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.193600</td>\n", "      <td>0.680443</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.955400</td>\n", "      <td>0.682166</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.765700</td>\n", "      <td>0.685723</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.667100</td>\n", "      <td>0.690811</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.505500</td>\n", "      <td>0.694354</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.423700</td>\n", "      <td>0.695601</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.365500</td>\n", "      <td>0.694273</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.331300</td>\n", "      <td>0.691512</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.307800</td>\n", "      <td>0.688851</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23177196345088:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:04 EDT)\" (scheduled at 2024-10-04 09:10:04.201164-04:00)\n", "INFO:p-854016:t-23177196345088:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:19 EDT)\" executed successfully\n", "INFO:p-854016:t-23177196345088:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:34 EDT)\" (scheduled at 2024-10-04 09:10:19.201164-04:00)\n", "INFO:p-854016:t-23177196345088:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:34 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.0863359371821086 seconds, Total Train Time = 34.31828022003174\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.36278820037841797, 'eval_runtime': 1.4201, 'eval_samples_per_second': 1961.099, 'eval_steps_per_second': 30.983, 'epoch': 12.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: etth2, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 7521, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.363\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2709115445613861, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 1.4158, 'eval_samples_per_second': 1967.019, 'eval_steps_per_second': 31.077}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: etth2, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 285, val = 2785, test = 2785\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.000298364724028334\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.000298364724028334\n", "Using learning rate = 0.000298364724028334\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:31 < 01:56, 1.68 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.075600</td>\n", "      <td>0.228444</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.039400</td>\n", "      <td>0.229198</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.916700</td>\n", "      <td>0.230436</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.834900</td>\n", "      <td>0.232362</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.668400</td>\n", "      <td>0.235177</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.608800</td>\n", "      <td>0.238780</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.487000</td>\n", "      <td>0.243153</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.423200</td>\n", "      <td>0.249117</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.374200</td>\n", "      <td>0.259001</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.344200</td>\n", "      <td>0.276216</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.317600</td>\n", "      <td>0.295603</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23185549661952:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:10:46 EDT)\" (scheduled at 2024-10-04 09:10:46.693758-04:00)\n", "INFO:p-854016:t-23185549661952:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.1077192696658047 seconds, Total Train Time = 32.47017168998718\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2712791860103607, 'eval_runtime': 1.5242, 'eval_samples_per_second': 1827.234, 'eval_steps_per_second': 28.868, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: ettm1, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 33441, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.363\n", "1   etth2   0.271    0.271\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.32694563269615173, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 4.6754, 'eval_samples_per_second': 2443.632, 'eval_steps_per_second': 38.285}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: ettm1, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 1581, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00043287612810830566\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00043287612810830566\n", "Using learning rate = 0.00043287612810830566\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:47 < 02:48, 5.78 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.840300</td>\n", "      <td>0.408443</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.622600</td>\n", "      <td>0.413902</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.449800</td>\n", "      <td>0.420073</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.327900</td>\n", "      <td>0.420382</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.286800</td>\n", "      <td>0.412459</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.261300</td>\n", "      <td>0.427272</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.243400</td>\n", "      <td>0.439357</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.228300</td>\n", "      <td>0.436092</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.214300</td>\n", "      <td>0.454617</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.201800</td>\n", "      <td>0.466312</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.195100</td>\n", "      <td>0.472506</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23177226843904:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:30 EDT)\" (scheduled at 2024-10-04 09:11:30.859561-04:00)\n", "INFO:p-854016:t-23177226843904:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:11:45 EDT)\" executed successfully\n", "INFO:p-854016:t-23177226843904:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:00 EDT)\" (scheduled at 2024-10-04 09:11:45.859561-04:00)\n", "INFO:p-854016:t-23177226843904:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:00 EDT)\" executed successfully\n", "INFO:p-854016:t-23177226843904:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:15 EDT)\" (scheduled at 2024-10-04 09:12:00.859561-04:00)\n", "INFO:p-854016:t-23177226843904:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:15 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.4592871015722102 seconds, Total Train Time = 48.18804407119751\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3278079628944397, 'eval_runtime': 2.4712, 'eval_samples_per_second': 4623.251, 'eval_steps_per_second': 72.434, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: ettm2, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 33441, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.363\n", "1   etth2   0.271    0.271\n", "2   ettm1   0.327    0.328\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1779371052980423, 'eval_model_preparation_time': 0.0026, 'eval_runtime': 4.4828, 'eval_samples_per_second': 2548.632, 'eval_steps_per_second': 39.93}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: ettm2, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 1581, val = 11425, test = 11425\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.0002477076355991711\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.0002477076355991711\n", "Using learning rate = 0.0002477076355991711\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:46 < 02:47, 5.82 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.524500</td>\n", "      <td>0.122229</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.414800</td>\n", "      <td>0.123283</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.310200</td>\n", "      <td>0.125309</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.212000</td>\n", "      <td>0.128540</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.159700</td>\n", "      <td>0.133482</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.142000</td>\n", "      <td>0.138576</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.133700</td>\n", "      <td>0.137888</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.128100</td>\n", "      <td>0.140013</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.121600</td>\n", "      <td>0.141608</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.116800</td>\n", "      <td>0.148989</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.111300</td>\n", "      <td>0.153411</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23185409140480:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:31 EDT)\" (scheduled at 2024-10-04 09:12:31.605868-04:00)\n", "INFO:p-854016:t-23185409140480:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:12:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23185409140480:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:01 EDT)\" (scheduled at 2024-10-04 09:12:46.605868-04:00)\n", "INFO:p-854016:t-23185409140480:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23185409140480:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:16 EDT)\" (scheduled at 2024-10-04 09:13:01.605868-04:00)\n", "INFO:p-854016:t-23185409140480:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.4911205118352717 seconds, Total Train Time = 47.828335762023926\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1781630963087082, 'eval_runtime': 2.6238, 'eval_samples_per_second': 4354.33, 'eval_steps_per_second': 68.221, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: weather, context length: 1024, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.363    0.363\n", "1   etth2   0.271    0.271\n", "2   ettm1   0.327    0.328\n", "3   ettm2   0.178    0.178\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 35768, val = 5175, test = 10444\n", "WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:07]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.16557331383228302, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 7.5397, 'eval_samples_per_second': 1385.208, 'eval_steps_per_second': 21.752}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: weather, context length: 1024, prediction length 96\n", "INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 1698, val = 5175, test = 10444\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00020565123083486514\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='459' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 459/1350 01:25 < 02:47, 5.33 it/s, Epoch 17/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.161200</td>\n", "      <td>0.385808</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.153800</td>\n", "      <td>0.383190</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.145900</td>\n", "      <td>0.382595</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.135000</td>\n", "      <td>0.382253</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.123100</td>\n", "      <td>0.385421</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.110500</td>\n", "      <td>0.384698</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.101900</td>\n", "      <td>0.380126</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.095600</td>\n", "      <td>0.385159</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.090800</td>\n", "      <td>0.389009</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.086400</td>\n", "      <td>0.386302</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.083900</td>\n", "      <td>0.386835</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.079600</td>\n", "      <td>0.387808</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.076700</td>\n", "      <td>0.390683</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.075000</td>\n", "      <td>0.390224</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.072600</td>\n", "      <td>0.390617</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.070900</td>\n", "      <td>0.391976</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.068500</td>\n", "      <td>0.394016</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23177466210048:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:36 EDT)\" (scheduled at 2024-10-04 09:13:36.934582-04:00)\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:51 EDT)\" executed successfully\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:13:51 EDT)\" executed successfully\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:06 EDT)\" (scheduled at 2024-10-04 09:13:51.934582-04:00)\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:06 EDT)\" executed successfully\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:21 EDT)\" (scheduled at 2024-10-04 09:14:06.934582-04:00)\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:21 EDT)\" executed successfully\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:36 EDT)\" (scheduled at 2024-10-04 09:14:21.934582-04:00)\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:36 EDT)\" executed successfully\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:51 EDT)\" (scheduled at 2024-10-04 09:14:36.934582-04:00)\n", "INFO:p-854016:t-23177466210048:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:14:51 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 2.039622292799108 seconds, Total Train Time = 86.71302151679993\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.16545218229293823, 'eval_runtime': 4.3782, 'eval_samples_per_second': 2385.457, 'eval_steps_per_second': 37.458, 'epoch': 17.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: electricity, context length: 1024, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["   dataset  zs_mse  fs5_mse\n", "0    etth1   0.363    0.363\n", "1    etth2   0.271    0.271\n", "2    ettm1   0.327    0.328\n", "3    ettm2   0.178    0.178\n", "4  weather   0.166    0.165\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 17293, val = 2537, test = 5165\n", "WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:30]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15667933225631714, 'eval_model_preparation_time': 0.0026, 'eval_runtime': 32.0456, 'eval_samples_per_second': 161.177, 'eval_steps_per_second': 5.055}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: electricity, context length: 1024, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 774, val = 2537, test = 5165\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 5.590810182512223e-05\n", "OPTIMAL SUGGESTED LEARNING RATE = 5.590810182512223e-05\n", "Using learning rate = 5.590810182512223e-05\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1250' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1250/1250 14:00, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.154000</td>\n", "      <td>0.132241</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.150400</td>\n", "      <td>0.132691</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.148000</td>\n", "      <td>0.132196</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.146900</td>\n", "      <td>0.130720</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.144700</td>\n", "      <td>0.130376</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.143600</td>\n", "      <td>0.129266</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.141100</td>\n", "      <td>0.128518</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.140600</td>\n", "      <td>0.127543</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.138700</td>\n", "      <td>0.126815</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.136300</td>\n", "      <td>0.125934</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.135100</td>\n", "      <td>0.125684</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.134500</td>\n", "      <td>0.124737</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.132800</td>\n", "      <td>0.123635</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.131600</td>\n", "      <td>0.123778</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.130500</td>\n", "      <td>0.122154</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.130000</td>\n", "      <td>0.122326</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.128400</td>\n", "      <td>0.122099</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.127500</td>\n", "      <td>0.121983</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.126400</td>\n", "      <td>0.121733</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.125600</td>\n", "      <td>0.120991</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.125600</td>\n", "      <td>0.120885</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.124900</td>\n", "      <td>0.120421</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.123700</td>\n", "      <td>0.119788</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.123200</td>\n", "      <td>0.120046</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.122400</td>\n", "      <td>0.119741</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.122700</td>\n", "      <td>0.119550</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.121800</td>\n", "      <td>0.120000</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.121700</td>\n", "      <td>0.119300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.121000</td>\n", "      <td>0.119297</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.120900</td>\n", "      <td>0.119253</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.120700</td>\n", "      <td>0.118943</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.120700</td>\n", "      <td>0.119013</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.120000</td>\n", "      <td>0.119013</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.119900</td>\n", "      <td>0.118858</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.119900</td>\n", "      <td>0.118655</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.119500</td>\n", "      <td>0.118519</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.119300</td>\n", "      <td>0.118727</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.119700</td>\n", "      <td>0.118654</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.119000</td>\n", "      <td>0.118608</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.119400</td>\n", "      <td>0.118434</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.119100</td>\n", "      <td>0.118522</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.119600</td>\n", "      <td>0.118490</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.119000</td>\n", "      <td>0.118394</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.119200</td>\n", "      <td>0.118425</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.118600</td>\n", "      <td>0.118437</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.119100</td>\n", "      <td>0.118353</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.119000</td>\n", "      <td>0.118378</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.119100</td>\n", "      <td>0.118386</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.119000</td>\n", "      <td>0.118385</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.118500</td>\n", "      <td>0.118385</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:01 EDT)\" (scheduled at 2024-10-04 09:16:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:31 EDT)\" (scheduled at 2024-10-04 09:16:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:46 EDT)\" (scheduled at 2024-10-04 09:16:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:16:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:01 EDT)\" (scheduled at 2024-10-04 09:16:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:16 EDT)\" (scheduled at 2024-10-04 09:17:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:31 EDT)\" (scheduled at 2024-10-04 09:17:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:46 EDT)\" (scheduled at 2024-10-04 09:17:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:17:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:01 EDT)\" (scheduled at 2024-10-04 09:17:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:16 EDT)\" (scheduled at 2024-10-04 09:18:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:31 EDT)\" (scheduled at 2024-10-04 09:18:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:46 EDT)\" (scheduled at 2024-10-04 09:18:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:18:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:01 EDT)\" (scheduled at 2024-10-04 09:18:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:16 EDT)\" (scheduled at 2024-10-04 09:19:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:31 EDT)\" (scheduled at 2024-10-04 09:19:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:46 EDT)\" (scheduled at 2024-10-04 09:19:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:19:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:01 EDT)\" (scheduled at 2024-10-04 09:19:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:16 EDT)\" (scheduled at 2024-10-04 09:20:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:31 EDT)\" (scheduled at 2024-10-04 09:20:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:46 EDT)\" (scheduled at 2024-10-04 09:20:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:20:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:01 EDT)\" (scheduled at 2024-10-04 09:20:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:16 EDT)\" (scheduled at 2024-10-04 09:21:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:31 EDT)\" (scheduled at 2024-10-04 09:21:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:46 EDT)\" (scheduled at 2024-10-04 09:21:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:21:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:01 EDT)\" (scheduled at 2024-10-04 09:21:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:16 EDT)\" (scheduled at 2024-10-04 09:22:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:31 EDT)\" (scheduled at 2024-10-04 09:22:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:46 EDT)\" (scheduled at 2024-10-04 09:22:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:22:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:01 EDT)\" (scheduled at 2024-10-04 09:22:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:01 EDT)\" (scheduled at 2024-10-04 09:22:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:16 EDT)\" (scheduled at 2024-10-04 09:23:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:31 EDT)\" (scheduled at 2024-10-04 09:23:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:46 EDT)\" (scheduled at 2024-10-04 09:23:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:23:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:01 EDT)\" (scheduled at 2024-10-04 09:23:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:16 EDT)\" (scheduled at 2024-10-04 09:24:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:31 EDT)\" (scheduled at 2024-10-04 09:24:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:46 EDT)\" (scheduled at 2024-10-04 09:24:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:24:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:01 EDT)\" (scheduled at 2024-10-04 09:24:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:16 EDT)\" (scheduled at 2024-10-04 09:25:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:31 EDT)\" (scheduled at 2024-10-04 09:25:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:46 EDT)\" (scheduled at 2024-10-04 09:25:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:25:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:01 EDT)\" (scheduled at 2024-10-04 09:25:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:16 EDT)\" (scheduled at 2024-10-04 09:26:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:31 EDT)\" (scheduled at 2024-10-04 09:26:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:46 EDT)\" (scheduled at 2024-10-04 09:26:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:26:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:01 EDT)\" (scheduled at 2024-10-04 09:26:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:16 EDT)\" (scheduled at 2024-10-04 09:27:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:31 EDT)\" (scheduled at 2024-10-04 09:27:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:46 EDT)\" (scheduled at 2024-10-04 09:27:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:27:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:01 EDT)\" (scheduled at 2024-10-04 09:27:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:16 EDT)\" (scheduled at 2024-10-04 09:28:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:31 EDT)\" (scheduled at 2024-10-04 09:28:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:46 EDT)\" (scheduled at 2024-10-04 09:28:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:28:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:01 EDT)\" (scheduled at 2024-10-04 09:28:46.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:01 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:16 EDT)\" (scheduled at 2024-10-04 09:29:01.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:16 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:31 EDT)\" (scheduled at 2024-10-04 09:29:16.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:31 EDT)\" executed successfully\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:46 EDT)\" (scheduled at 2024-10-04 09:29:31.931082-04:00)\n", "INFO:p-854016:t-23177213200128:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:29:46 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 5.812682814598084 seconds, Total Train Time = 842.4421577453613\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:18]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14788587391376495, 'eval_runtime': 20.3053, 'eval_samples_per_second': 254.367, 'eval_steps_per_second': 7.978, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: traffic, context length: 1024, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["       dataset  zs_mse  fs5_mse\n", "0        etth1   0.363    0.363\n", "1        etth2   0.271    0.271\n", "2        ettm1   0.327    0.328\n", "3        ettm2   0.178    0.178\n", "4      weather   0.166    0.165\n", "5  electricity   0.157    0.148\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm/ttm-research-r2/1024_96_ft_r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 11161, val = 1661, test = 3413\n", "WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 01:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4755541682243347, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 64.835, 'eval_samples_per_second': 52.641, 'eval_steps_per_second': 6.586}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Dataset name: traffic, context length: 1024, prediction length 96\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:data_handling.py:load_dataset:Data lengths: train = 467, val = 1661, test = 3413\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 3126076\n", "Number of params after freezing the backbone 980178\n", "LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "LR Finder: Using GPU:0.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:p-854016:t-23192246899456:other.py:check_os_kernel:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "INFO:p-854016:t-23192246899456:base.py:add_job:Adding job tentatively -- it will be properly scheduled when the scheduler starts\n"]}, {"name": "stdout", "output_type": "stream", "text": ["LR Finder: Suggested learning rate = 0.00017073526474706903\n", "OPTIMAL SUGGESTED LEARNING RATE = 0.00017073526474706903\n", "Using learning rate = 0.00017073526474706903\n"]}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23192246899456:base.py:_real_add_job:Added job \"EmissionsTracker._measure_power\" to job store \"default\"\n", "INFO:p-854016:t-23192246899456:base.py:start:Scheduler started\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='2891' max='2950' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [2891/2950 22:51 < 00:28, 2.11 it/s, Epoch 49/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.311200</td>\n", "      <td>0.385815</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.296600</td>\n", "      <td>0.384570</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.289100</td>\n", "      <td>0.383350</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.279400</td>\n", "      <td>0.380428</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.270800</td>\n", "      <td>0.378043</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.264300</td>\n", "      <td>0.376897</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.259800</td>\n", "      <td>0.371791</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.255100</td>\n", "      <td>0.367791</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.249100</td>\n", "      <td>0.366351</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.247200</td>\n", "      <td>0.360846</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.243400</td>\n", "      <td>0.364740</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.240400</td>\n", "      <td>0.361001</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.237600</td>\n", "      <td>0.359136</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.235600</td>\n", "      <td>0.359346</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.231600</td>\n", "      <td>0.358251</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.229000</td>\n", "      <td>0.353927</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.225800</td>\n", "      <td>0.360125</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.224300</td>\n", "      <td>0.353490</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.224400</td>\n", "      <td>0.355529</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.220100</td>\n", "      <td>0.357273</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.219300</td>\n", "      <td>0.356624</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.216300</td>\n", "      <td>0.352336</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.215600</td>\n", "      <td>0.356624</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.214500</td>\n", "      <td>0.350067</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.213500</td>\n", "      <td>0.348890</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.210700</td>\n", "      <td>0.356339</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.209100</td>\n", "      <td>0.348824</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.209500</td>\n", "      <td>0.350834</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.207700</td>\n", "      <td>0.350435</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.207400</td>\n", "      <td>0.348997</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.206500</td>\n", "      <td>0.351628</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.205000</td>\n", "      <td>0.349470</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.204500</td>\n", "      <td>0.348043</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.202500</td>\n", "      <td>0.345874</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.202700</td>\n", "      <td>0.350634</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.201500</td>\n", "      <td>0.347463</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.201000</td>\n", "      <td>0.348373</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.200000</td>\n", "      <td>0.349413</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.199800</td>\n", "      <td>0.345183</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.199500</td>\n", "      <td>0.348336</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.199100</td>\n", "      <td>0.348488</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.198500</td>\n", "      <td>0.346594</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.197900</td>\n", "      <td>0.347064</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.197900</td>\n", "      <td>0.346063</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.197500</td>\n", "      <td>0.347178</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.197100</td>\n", "      <td>0.346488</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.197100</td>\n", "      <td>0.347005</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.196900</td>\n", "      <td>0.346596</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.197000</td>\n", "      <td>0.346689</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:31:50 EDT)\" (scheduled at 2024-10-04 09:31:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:20 EDT)\" (scheduled at 2024-10-04 09:32:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:35 EDT)\" (scheduled at 2024-10-04 09:32:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:50 EDT)\" (scheduled at 2024-10-04 09:32:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:32:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:05 EDT)\" (scheduled at 2024-10-04 09:32:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:20 EDT)\" (scheduled at 2024-10-04 09:33:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:35 EDT)\" (scheduled at 2024-10-04 09:33:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:50 EDT)\" (scheduled at 2024-10-04 09:33:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:33:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:05 EDT)\" (scheduled at 2024-10-04 09:33:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:20 EDT)\" (scheduled at 2024-10-04 09:34:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:35 EDT)\" (scheduled at 2024-10-04 09:34:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:50 EDT)\" (scheduled at 2024-10-04 09:34:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:34:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:05 EDT)\" (scheduled at 2024-10-04 09:34:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:20 EDT)\" (scheduled at 2024-10-04 09:35:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:35 EDT)\" (scheduled at 2024-10-04 09:35:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:50 EDT)\" (scheduled at 2024-10-04 09:35:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:35:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:05 EDT)\" (scheduled at 2024-10-04 09:35:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:20 EDT)\" (scheduled at 2024-10-04 09:36:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:35 EDT)\" (scheduled at 2024-10-04 09:36:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:50 EDT)\" (scheduled at 2024-10-04 09:36:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:36:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:05 EDT)\" (scheduled at 2024-10-04 09:36:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:20 EDT)\" (scheduled at 2024-10-04 09:37:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:35 EDT)\" (scheduled at 2024-10-04 09:37:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:50 EDT)\" (scheduled at 2024-10-04 09:37:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:37:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:05 EDT)\" (scheduled at 2024-10-04 09:37:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:20 EDT)\" (scheduled at 2024-10-04 09:38:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:35 EDT)\" (scheduled at 2024-10-04 09:38:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:50 EDT)\" (scheduled at 2024-10-04 09:38:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:38:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:05 EDT)\" (scheduled at 2024-10-04 09:38:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:20 EDT)\" (scheduled at 2024-10-04 09:39:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:35 EDT)\" (scheduled at 2024-10-04 09:39:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:50 EDT)\" (scheduled at 2024-10-04 09:39:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:39:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:05 EDT)\" (scheduled at 2024-10-04 09:39:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:20 EDT)\" (scheduled at 2024-10-04 09:40:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:35 EDT)\" (scheduled at 2024-10-04 09:40:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:50 EDT)\" (scheduled at 2024-10-04 09:40:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:40:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:05 EDT)\" (scheduled at 2024-10-04 09:40:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:20 EDT)\" (scheduled at 2024-10-04 09:41:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:35 EDT)\" (scheduled at 2024-10-04 09:41:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:50 EDT)\" (scheduled at 2024-10-04 09:41:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:41:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:05 EDT)\" (scheduled at 2024-10-04 09:41:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:20 EDT)\" (scheduled at 2024-10-04 09:42:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:35 EDT)\" (scheduled at 2024-10-04 09:42:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:50 EDT)\" (scheduled at 2024-10-04 09:42:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:42:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:05 EDT)\" (scheduled at 2024-10-04 09:42:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:20 EDT)\" (scheduled at 2024-10-04 09:43:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:35 EDT)\" (scheduled at 2024-10-04 09:43:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:50 EDT)\" (scheduled at 2024-10-04 09:43:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:43:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:05 EDT)\" (scheduled at 2024-10-04 09:43:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:20 EDT)\" (scheduled at 2024-10-04 09:44:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:35 EDT)\" (scheduled at 2024-10-04 09:44:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:50 EDT)\" (scheduled at 2024-10-04 09:44:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:44:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:05 EDT)\" (scheduled at 2024-10-04 09:44:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:20 EDT)\" (scheduled at 2024-10-04 09:45:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:35 EDT)\" (scheduled at 2024-10-04 09:45:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:50 EDT)\" (scheduled at 2024-10-04 09:45:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:45:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:05 EDT)\" (scheduled at 2024-10-04 09:45:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:20 EDT)\" (scheduled at 2024-10-04 09:46:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:35 EDT)\" (scheduled at 2024-10-04 09:46:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:50 EDT)\" (scheduled at 2024-10-04 09:46:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:46:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:05 EDT)\" (scheduled at 2024-10-04 09:46:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:20 EDT)\" (scheduled at 2024-10-04 09:47:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:35 EDT)\" (scheduled at 2024-10-04 09:47:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:50 EDT)\" (scheduled at 2024-10-04 09:47:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:47:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:05 EDT)\" (scheduled at 2024-10-04 09:47:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:20 EDT)\" (scheduled at 2024-10-04 09:48:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:35 EDT)\" (scheduled at 2024-10-04 09:48:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:50 EDT)\" (scheduled at 2024-10-04 09:48:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:48:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:05 EDT)\" (scheduled at 2024-10-04 09:48:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:20 EDT)\" (scheduled at 2024-10-04 09:49:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:35 EDT)\" (scheduled at 2024-10-04 09:49:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:50 EDT)\" (scheduled at 2024-10-04 09:49:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:49:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:05 EDT)\" (scheduled at 2024-10-04 09:49:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:20 EDT)\" (scheduled at 2024-10-04 09:50:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:35 EDT)\" (scheduled at 2024-10-04 09:50:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:50 EDT)\" (scheduled at 2024-10-04 09:50:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:50:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:05 EDT)\" (scheduled at 2024-10-04 09:50:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:20 EDT)\" (scheduled at 2024-10-04 09:51:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:35 EDT)\" (scheduled at 2024-10-04 09:51:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:50 EDT)\" (scheduled at 2024-10-04 09:51:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:51:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:05 EDT)\" (scheduled at 2024-10-04 09:51:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:20 EDT)\" (scheduled at 2024-10-04 09:52:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:35 EDT)\" (scheduled at 2024-10-04 09:52:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:50 EDT)\" (scheduled at 2024-10-04 09:52:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:52:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:05 EDT)\" (scheduled at 2024-10-04 09:52:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:20 EDT)\" (scheduled at 2024-10-04 09:53:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:35 EDT)\" (scheduled at 2024-10-04 09:53:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:50 EDT)\" (scheduled at 2024-10-04 09:53:35.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:53:50 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:54:05 EDT)\" (scheduled at 2024-10-04 09:53:50.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:54:05 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:54:20 EDT)\" (scheduled at 2024-10-04 09:54:05.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:54:20 EDT)\" executed successfully\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Running job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:54:35 EDT)\" (scheduled at 2024-10-04 09:54:20.993797-04:00)\n", "INFO:p-854016:t-23177200080640:base.py:run_job:Job \"EmissionsTracker._measure_power (trigger: interval[0:00:15], next run at: 2024-10-04 09:54:35 EDT)\" executed successfully\n", "INFO:p-854016:t-23192246899456:base.py:shutdown:Scheduler has been shut down\n", "ERROR:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:Region:  not found for Country with ISO CODE : USA\n", "WARNING:p-854016:t-23192246899456:emissions.py:get_private_infra_emissions:CODECARBON : Regional emissions retrieval failed. Falling back on country emissions.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 8.869987984092868 seconds, Total Train Time = 1373.7280249595642\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:35]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.41427138447761536, 'eval_runtime': 37.1621, 'eval_samples_per_second': 91.841, 'eval_steps_per_second': 11.49, 'epoch': 49.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.363    0.363\n", "1        etth2   0.271    0.271\n", "2        ettm1   0.327    0.328\n", "3        ettm2   0.178    0.178\n", "4      weather   0.166    0.165\n", "5  electricity   0.157    0.148\n", "6      traffic   0.476    0.414\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}/{hf_model_branch}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(\n", "        DATASET,\n", "        context_length,\n", "        forecast_length,\n", "        dataset_root_path=DATA_ROOT_PATH,\n", "        use_frequency_token=enable_prefix_tuning,\n", "    )\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = TinyTimeMixerForPrediction.from_pretrained(hf_model_path, revision=hf_model_branch)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            seed=SEED,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5]:\n", "        # Set learning rate\n", "        learning_rate = None  # `None` value indicates that the optimal_lr_finder() will be used\n", "\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "            use_frequency_token=enable_prefix_tuning,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = TinyTimeMixerForPrediction.from_pretrained(\n", "                hf_model_path, revision=hf_model_branch\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        if learning_rate is None:\n", "            learning_rate, finetune_forecast_model = optimal_lr_finder(\n", "                finetune_forecast_model,\n", "                dset_train,\n", "                batch_size=BATCH_SIZE,\n", "                enable_prefix_tuning=enable_prefix_tuning,\n", "            )\n", "            print(\"OPTIMAL SUGGESTED LEARNING RATE =\", learning_rate)\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "            seed=SEED,\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.363</td>\n", "      <td>0.363</td>\n", "      <td>4.462</td>\n", "      <td>1.086</td>\n", "      <td>34.318</td>\n", "      <td>0.680</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.271</td>\n", "      <td>0.271</td>\n", "      <td>1.416</td>\n", "      <td>1.108</td>\n", "      <td>32.470</td>\n", "      <td>0.228</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.327</td>\n", "      <td>0.328</td>\n", "      <td>4.675</td>\n", "      <td>1.459</td>\n", "      <td>48.188</td>\n", "      <td>0.408</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.178</td>\n", "      <td>0.178</td>\n", "      <td>4.483</td>\n", "      <td>1.491</td>\n", "      <td>47.828</td>\n", "      <td>0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.166</td>\n", "      <td>0.165</td>\n", "      <td>7.540</td>\n", "      <td>2.040</td>\n", "      <td>86.713</td>\n", "      <td>0.380</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.157</td>\n", "      <td>0.148</td>\n", "      <td>32.046</td>\n", "      <td>5.813</td>\n", "      <td>842.442</td>\n", "      <td>0.118</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.476</td>\n", "      <td>0.414</td>\n", "      <td>64.835</td>\n", "      <td>8.870</td>\n", "      <td>1373.728</td>\n", "      <td>0.345</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.363    0.363         4.462                1.086   \n", "1        etth2   0.271    0.271         1.416                1.108   \n", "2        ettm1   0.327    0.328         4.675                1.459   \n", "3        ettm2   0.178    0.178         4.483                1.491   \n", "4      weather   0.166    0.165         7.540                2.040   \n", "5  electricity   0.157    0.148        32.046                5.813   \n", "6      traffic   0.476    0.414        64.835                8.870   \n", "\n", "   fs5_total_train_time  fs5_best_val_metric  \n", "0                34.318                0.680  \n", "1                32.470                0.228  \n", "2                48.188                0.408  \n", "3                47.828                0.122  \n", "4                86.713                0.380  \n", "5               842.442                0.118  \n", "6              1373.728                0.345  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}