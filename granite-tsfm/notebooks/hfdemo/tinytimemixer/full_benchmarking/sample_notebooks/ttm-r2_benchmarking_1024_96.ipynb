{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "**Using TTM-1024-96 model.**\n", "\n", "Pre-trained TTM models will be fetched from the [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "For details, visit the [Hugging Face TTM Model Repository](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "1. IBM Granite TTM-R1 pre-trained models can be found here: [Granite-TTM-R1 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r1)\n", "2. IBM Granite TTM-R2 pre-trained models can be found here: [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2)\n", "3. Research-use (non-commercial use only) TTM-R2 pre-trained models can be found here: [Research-Use-TTM-R2](https://huggingface.co/ibm-research/ttm-research-r2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-11-05 09:36:47.873779: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-11-05 09:36:58.939912: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-11-05 09:37:10.025472: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import math\n", "import warnings\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "from transformers.integrations import INTEGRATION_TO_CALLBACK\n", "\n", "from tsfm_public import TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.get_model import get_model\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.visualization import plot_predictions\n", "\n", "\n", "warnings.filterwarnings(\"ignore\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "SEED = 42\n", "set_seed(SEED)\n", "\n", "# Specify model parameters\n", "context_length = 1024\n", "forecast_length = 96\n", "freeze_backbone = True\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_datasets() function\n", "# in notebooks/hfdemo/tinytimemixer/utils/ttm_utils.py\n", "# to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm-r2_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Set model path"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hf_model_path = \"ibm-granite/granite-timeseries-ttm-r2\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.35859495401382446, 'eval_model_preparation_time': 0.0028, 'eval_runtime': 8.9505, 'eval_samples_per_second': 311.157, 'eval_steps_per_second': 4.916}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.000298364724028334\n", "Using learning rate = 0.000298364724028334\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:17 < 01:03, 3.05 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.916600</td>\n", "      <td>0.665669</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.888700</td>\n", "      <td>0.665982</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.824300</td>\n", "      <td>0.666453</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.886300</td>\n", "      <td>0.667170</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.773700</td>\n", "      <td>0.668418</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.695100</td>\n", "      <td>0.669920</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.525000</td>\n", "      <td>0.671401</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.475700</td>\n", "      <td>0.673846</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.404700</td>\n", "      <td>0.675814</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.374400</td>\n", "      <td>0.677924</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.335900</td>\n", "      <td>0.681410</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.9498170722614635 seconds, Total Train Time = 21.376437425613403\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.35856103897094727, 'eval_runtime': 0.8932, 'eval_samples_per_second': 3117.893, 'eval_steps_per_second': 49.259, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.269417405128479, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 0.8529, 'eval_samples_per_second': 3265.37, 'eval_steps_per_second': 51.589}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.000298364724028334\n", "Using learning rate = 0.000298364724028334\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:17 < 01:05, 2.98 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.945200</td>\n", "      <td>0.239151</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.861000</td>\n", "      <td>0.239945</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.805900</td>\n", "      <td>0.241062</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.724700</td>\n", "      <td>0.242527</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.654900</td>\n", "      <td>0.244388</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.576800</td>\n", "      <td>0.246938</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.495700</td>\n", "      <td>0.250335</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.457700</td>\n", "      <td>0.256598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.392900</td>\n", "      <td>0.267042</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.357100</td>\n", "      <td>0.283817</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.323500</td>\n", "      <td>0.308233</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.6733335581692782 seconds, Total Train Time = 18.43419575691223\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.26942315697669983, 'eval_runtime': 0.9311, 'eval_samples_per_second': 2991.213, 'eval_steps_per_second': 47.258, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "1   etth2   0.269    0.269\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3369019627571106, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 3.5784, 'eval_samples_per_second': 3192.741, 'eval_steps_per_second': 50.022}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.0005214008287999684\n", "Using learning rate = 0.0005214008287999684\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:31 < 01:52, 8.64 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.814100</td>\n", "      <td>0.394550</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.607200</td>\n", "      <td>0.395544</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.477900</td>\n", "      <td>0.397824</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.380700</td>\n", "      <td>0.397300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.311600</td>\n", "      <td>0.408491</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.268400</td>\n", "      <td>0.428093</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.242600</td>\n", "      <td>0.437327</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.223300</td>\n", "      <td>0.456643</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.207600</td>\n", "      <td>0.463043</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.197200</td>\n", "      <td>0.468228</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.188800</td>\n", "      <td>0.478250</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.9686962907964533 seconds, Total Train Time = 32.147533893585205\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.33640581369400024, 'eval_runtime': 1.9157, 'eval_samples_per_second': 5963.974, 'eval_steps_per_second': 93.44, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "1   etth2   0.269    0.269\n", "2   ettm1   0.337    0.336\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:03]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1764754354953766, 'eval_model_preparation_time': 0.0025, 'eval_runtime': 3.4544, 'eval_samples_per_second': 3307.416, 'eval_steps_per_second': 51.819}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.000298364724028334\n", "Using learning rate = 0.000298364724028334\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:32 < 01:55, 8.47 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.495700</td>\n", "      <td>0.122071</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.399600</td>\n", "      <td>0.122304</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.328300</td>\n", "      <td>0.122963</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.242100</td>\n", "      <td>0.124153</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.188300</td>\n", "      <td>0.127375</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.150100</td>\n", "      <td>0.135246</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.133100</td>\n", "      <td>0.143912</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.122500</td>\n", "      <td>0.151637</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.117400</td>\n", "      <td>0.158312</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.111200</td>\n", "      <td>0.164967</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.106800</td>\n", "      <td>0.169490</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.0002062320709229 seconds, Total Train Time = 32.82567358016968\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17645052075386047, 'eval_runtime': 1.9039, 'eval_samples_per_second': 6000.805, 'eval_steps_per_second': 94.017, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "  dataset  zs_mse  fs5_mse\n", "0   etth1   0.359    0.359\n", "1   etth2   0.269    0.269\n", "2   ettm1   0.337    0.336\n", "3   ettm2   0.176    0.176\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:06]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15011762082576752, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 6.3602, 'eval_samples_per_second': 1642.084, 'eval_steps_per_second': 25.785}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00035938136638046257\n", "Using learning rate = 0.00035938136638046257\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='297' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 297/1350 00:37 < 02:15, 7.80 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.153500</td>\n", "      <td>0.393854</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.147700</td>\n", "      <td>0.399079</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.140500</td>\n", "      <td>0.407770</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.130300</td>\n", "      <td>0.410832</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.115500</td>\n", "      <td>0.407429</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.102600</td>\n", "      <td>0.411830</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.092700</td>\n", "      <td>0.409271</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.085700</td>\n", "      <td>0.415379</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.080700</td>\n", "      <td>0.414570</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.076900</td>\n", "      <td>0.414594</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.073200</td>\n", "      <td>0.414909</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.4071654189716687 seconds, Total Train Time = 38.4444363117218\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1500033736228943, 'eval_runtime': 3.4099, 'eval_samples_per_second': 3062.849, 'eval_steps_per_second': 48.095, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "   dataset  zs_mse  fs5_mse\n", "0    etth1   0.359    0.359\n", "1    etth2   0.269    0.269\n", "2    ettm1   0.337    0.336\n", "3    ettm2   0.176    0.176\n", "4  weather   0.150    0.150\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:26]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15828542411327362, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 26.7977, 'eval_samples_per_second': 192.74, 'eval_steps_per_second': 6.045}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 8.111308307896872e-05\n", "Using learning rate = 8.111308307896872e-05\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1250' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1250/1250 12:26, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.154100</td>\n", "      <td>0.133550</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.150400</td>\n", "      <td>0.133363</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.148100</td>\n", "      <td>0.131550</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.147100</td>\n", "      <td>0.129834</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.144600</td>\n", "      <td>0.128791</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.143500</td>\n", "      <td>0.127429</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.140500</td>\n", "      <td>0.126259</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.139600</td>\n", "      <td>0.125177</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.137400</td>\n", "      <td>0.124556</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.134800</td>\n", "      <td>0.123992</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.133200</td>\n", "      <td>0.123508</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.132400</td>\n", "      <td>0.122755</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.130200</td>\n", "      <td>0.121776</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.129000</td>\n", "      <td>0.121530</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.127400</td>\n", "      <td>0.120715</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.127100</td>\n", "      <td>0.120709</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.124800</td>\n", "      <td>0.120068</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.124400</td>\n", "      <td>0.119745</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.123300</td>\n", "      <td>0.119724</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.122700</td>\n", "      <td>0.119251</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.122700</td>\n", "      <td>0.119369</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.122000</td>\n", "      <td>0.118602</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.121000</td>\n", "      <td>0.118696</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.120500</td>\n", "      <td>0.118636</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.120000</td>\n", "      <td>0.118489</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.120100</td>\n", "      <td>0.118362</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.119200</td>\n", "      <td>0.118232</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.119200</td>\n", "      <td>0.117863</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.118600</td>\n", "      <td>0.117886</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.118500</td>\n", "      <td>0.118188</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.118100</td>\n", "      <td>0.117668</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.118200</td>\n", "      <td>0.117762</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.117600</td>\n", "      <td>0.117765</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.117500</td>\n", "      <td>0.117664</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.117400</td>\n", "      <td>0.117472</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.117000</td>\n", "      <td>0.117431</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.117000</td>\n", "      <td>0.117465</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.117400</td>\n", "      <td>0.117517</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.116500</td>\n", "      <td>0.117558</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.116800</td>\n", "      <td>0.117487</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.116600</td>\n", "      <td>0.117426</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.117200</td>\n", "      <td>0.117347</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.116500</td>\n", "      <td>0.117497</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.116800</td>\n", "      <td>0.117334</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.116200</td>\n", "      <td>0.117415</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.116500</td>\n", "      <td>0.117320</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.116600</td>\n", "      <td>0.117332</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.116700</td>\n", "      <td>0.117344</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.116400</td>\n", "      <td>0.117354</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.116100</td>\n", "      <td>0.117354</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 4.880673928260803 seconds, Total Train Time = 747.7492995262146\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:18]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14718736708164215, 'eval_runtime': 19.2227, 'eval_samples_per_second': 268.692, 'eval_steps_per_second': 8.428, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.359    0.359\n", "1        etth2   0.269    0.269\n", "2        ettm1   0.337    0.336\n", "3        ettm2   0.176    0.176\n", "4      weather   0.150    0.150\n", "5  electricity   0.158    0.147\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r2\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:46]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4737617075443268, 'eval_model_preparation_time': 0.0024, 'eval_runtime': 46.8323, 'eval_samples_per_second': 72.877, 'eval_steps_per_second': 9.118}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 2964960\n", "Number of params after freezing the backbone 955424\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["OPTIMAL SUGGESTED LEARNING RATE = 0.00020565123083486514\n", "Using learning rate = 0.00020565123083486514\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1652' max='2950' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1652/2950 11:14 < 08:50, 2.45 it/s, Epoch 28/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.306300</td>\n", "      <td>0.384197</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.290600</td>\n", "      <td>0.380115</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.283100</td>\n", "      <td>0.377606</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.275400</td>\n", "      <td>0.375396</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.267800</td>\n", "      <td>0.371779</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.262100</td>\n", "      <td>0.370619</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.257600</td>\n", "      <td>0.364189</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.253500</td>\n", "      <td>0.361611</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.247700</td>\n", "      <td>0.357288</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.244900</td>\n", "      <td>0.354975</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.240500</td>\n", "      <td>0.355310</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.236600</td>\n", "      <td>0.355367</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.234100</td>\n", "      <td>0.349950</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.232100</td>\n", "      <td>0.353106</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.229900</td>\n", "      <td>0.352745</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.226000</td>\n", "      <td>0.347146</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.223000</td>\n", "      <td>0.356564</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.221000</td>\n", "      <td>0.345382</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.220800</td>\n", "      <td>0.349640</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.217800</td>\n", "      <td>0.355157</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.217000</td>\n", "      <td>0.356424</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.213900</td>\n", "      <td>0.349901</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.212700</td>\n", "      <td>0.355637</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.212000</td>\n", "      <td>0.349804</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.210700</td>\n", "      <td>0.348401</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.208500</td>\n", "      <td>0.356707</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.207000</td>\n", "      <td>0.348334</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.207000</td>\n", "      <td>0.350621</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 7.419389545917511 seconds, Total Train Time = 675.6236307621002\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:31]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4179241955280304, 'eval_runtime': 32.9888, 'eval_samples_per_second': 103.459, 'eval_steps_per_second': 12.944, 'epoch': 28.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse\n", "0        etth1   0.359    0.359\n", "1        etth2   0.269    0.269\n", "2        ettm1   0.337    0.336\n", "3        ettm2   0.176    0.176\n", "4      weather   0.150    0.150\n", "5  electricity   0.158    0.147\n", "6      traffic   0.474    0.418\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(DATASET, context_length, forecast_length, dataset_root_path=DATA_ROOT_PATH)\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = get_model(hf_model_path, context_length=context_length, prediction_length=forecast_length)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            seed=SEED,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5]:\n", "        # Set learning rate\n", "        learning_rate = None  # `None` value indicates that the optimal_lr_finder() will be used\n", "\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        if learning_rate is None:\n", "            learning_rate, finetune_forecast_model = optimal_lr_finder(\n", "                finetune_forecast_model,\n", "                dset_train,\n", "                batch_size=BATCH_SIZE,\n", "            )\n", "            print(\"OPTIMAL SUGGESTED LEARNING RATE =\", learning_rate)\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "            seed=SEED,\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "        finetune_forecast_trainer.remove_callback(INTEGRATION_TO_CALLBACK[\"codecarbon\"])\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.359</td>\n", "      <td>0.359</td>\n", "      <td>8.950</td>\n", "      <td>0.950</td>\n", "      <td>21.376</td>\n", "      <td>0.666</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.269</td>\n", "      <td>0.269</td>\n", "      <td>0.853</td>\n", "      <td>0.673</td>\n", "      <td>18.434</td>\n", "      <td>0.239</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.337</td>\n", "      <td>0.336</td>\n", "      <td>3.578</td>\n", "      <td>0.969</td>\n", "      <td>32.148</td>\n", "      <td>0.395</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.176</td>\n", "      <td>0.176</td>\n", "      <td>3.454</td>\n", "      <td>1.000</td>\n", "      <td>32.826</td>\n", "      <td>0.122</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.150</td>\n", "      <td>0.150</td>\n", "      <td>6.360</td>\n", "      <td>1.407</td>\n", "      <td>38.444</td>\n", "      <td>0.394</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.158</td>\n", "      <td>0.147</td>\n", "      <td>26.798</td>\n", "      <td>4.881</td>\n", "      <td>747.749</td>\n", "      <td>0.117</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.474</td>\n", "      <td>0.418</td>\n", "      <td>46.832</td>\n", "      <td>7.419</td>\n", "      <td>675.624</td>\n", "      <td>0.345</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.359    0.359         8.950                0.950   \n", "1        etth2   0.269    0.269         0.853                0.673   \n", "2        ettm1   0.337    0.336         3.578                0.969   \n", "3        ettm2   0.176    0.176         3.454                1.000   \n", "4      weather   0.150    0.150         6.360                1.407   \n", "5  electricity   0.158    0.147        26.798                4.881   \n", "6      traffic   0.474    0.418        46.832                7.419   \n", "\n", "   fs5_total_train_time  fs5_best_val_metric  \n", "0                21.376                0.666  \n", "1                18.434                0.239  \n", "2                32.148                0.395  \n", "3                32.826                0.122  \n", "4                38.444                0.394  \n", "5               747.749                0.117  \n", "6               675.624                0.345  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}