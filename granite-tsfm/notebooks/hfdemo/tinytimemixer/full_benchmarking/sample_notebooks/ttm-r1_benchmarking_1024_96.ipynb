{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# TTM zero-shot and few-shot benchmarking on multiple datasets\n", "\n", "**Using TTM-1024-96 model.**\n", "\n", "Pre-trained TTM models will be fetched from the [Granite-TTM-R1 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r1).\n", "\n", "For details, visit the [Hugging Face TTM Model Repository](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2).\n", "\n", "1. IBM Granite TTM-R1 pre-trained models can be found here: [Granite-TTM-R1 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r1)\n", "2. IBM Granite TTM-R2 pre-trained models can be found here: [Granite-TTM-R2 Model Card](https://huggingface.co/ibm-granite/granite-timeseries-ttm-r2)\n", "3. Research-use (non-commercial use only) TTM-R2 pre-trained models can be found here: [Research-Use-TTM-R2](https://huggingface.co/ibm-research/ttm-research-r2)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["2024-11-05 01:50:13.619224: I tensorflow/tsl/cuda/cudart_stub.cc:28] Could not find cuda drivers on your machine, GPU will not be used.\n", "2024-11-05 01:50:13.658544: I tensorflow/core/platform/cpu_feature_guard.cc:182] This TensorFlow binary is optimized to use available CPU instructions in performance-critical operations.\n", "To enable the following instructions: AVX2 FMA, in other operations, rebuild TensorFlow with the appropriate compiler flags.\n", "2024-11-05 01:50:14.553147: W tensorflow/compiler/tf2tensorrt/utils/py_utils.cc:38] TF-TRT Warning: Could not find TensorRT\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: libtorch_cuda_cu.so: cannot open shared object file: No such file or directory\n", "  warn(f\"Failed to load image Python extension: {e}\")\n"]}], "source": ["import math\n", "\n", "import matplotlib.pyplot as plt\n", "import pandas as pd\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "from transformers.integrations import INTEGRATION_TO_CALLBACK\n", "\n", "from tsfm_public import TrackingCallback, count_parameters, load_dataset\n", "from tsfm_public.toolkit.get_model import get_model\n", "from tsfm_public.toolkit.visualization import plot_predictions"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Important arguments"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Set seed\n", "set_seed(42)\n", "\n", "# Specify model parameters\n", "context_length = 1024\n", "forecast_length = 96\n", "freeze_backbone = True\n", "learning_rate = 0.001\n", "\n", "# Other args\n", "EPOCHS = 50\n", "NUM_WORKERS = 16\n", "\n", "# Make sure all the datasets in the following `list_datasets` are\n", "# saved in the `DATA_ROOT_PATH` folder. Or, change it accordingly.\n", "# Refer to the load_datasets() function to see how it is used.\n", "DATA_ROOT_PATH = \"/dccstor/tsfm23/datasets/\"\n", "\n", "# This is where results will be saved\n", "OUT_DIR = f\"ttm-r1_results_benchmark_{context_length}_{forecast_length}/\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## List of benchmark datasets (TTM was not pre-trained on any of these)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["list_datasets = [\n", "    \"etth1\",\n", "    \"etth2\",\n", "    \"ettm1\",\n", "    \"ettm2\",\n", "    \"weather\",\n", "    \"electricity\",\n", "    \"traffic\",\n", "]"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Set model path"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["hf_model_path = \"ibm-granite/granite-timeseries-ttm-r1\""]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Main benchmarking loop"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = etth1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3621068000793457, 'eval_model_preparation_time': 0.0026, 'eval_runtime': 1.02, 'eval_samples_per_second': 2730.292, 'eval_steps_per_second': 43.136}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='85' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 85/250 00:36 < 01:13, 2.25 it/s, Epoch 17/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.584400</td>\n", "      <td>0.663804</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.601700</td>\n", "      <td>0.663111</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.585400</td>\n", "      <td>0.662300</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.564100</td>\n", "      <td>0.661117</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.527600</td>\n", "      <td>0.659781</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.497200</td>\n", "      <td>0.658587</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.455000</td>\n", "      <td>0.658336</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.427900</td>\n", "      <td>0.660301</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.399900</td>\n", "      <td>0.663791</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.353700</td>\n", "      <td>0.670050</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.312400</td>\n", "      <td>0.682261</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.301000</td>\n", "      <td>0.700288</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.266300</td>\n", "      <td>0.724918</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.249500</td>\n", "      <td>0.753728</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.233800</td>\n", "      <td>0.775392</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.216400</td>\n", "      <td>0.788408</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.214900</td>\n", "      <td>0.795417</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.9482389057383818 seconds, Total Train Time = 39.05620765686035\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3614313006401062, 'eval_runtime': 1.1398, 'eval_samples_per_second': 2443.306, 'eval_steps_per_second': 38.602, 'epoch': 17.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n", "Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='143' max='550' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [143/550 00:28 < 01:23, 4.89 it/s, Epoch 13/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.642100</td>\n", "      <td>0.663602</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.594700</td>\n", "      <td>0.662923</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.545200</td>\n", "      <td>0.662525</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.518200</td>\n", "      <td>0.663749</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.464700</td>\n", "      <td>0.667679</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.414400</td>\n", "      <td>0.674993</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.371700</td>\n", "      <td>0.687040</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.334700</td>\n", "      <td>0.708806</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.308600</td>\n", "      <td>0.737532</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.286700</td>\n", "      <td>0.751357</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.268600</td>\n", "      <td>0.776843</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.256200</td>\n", "      <td>0.794736</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.245500</td>\n", "      <td>0.800167</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.0440415602463942 seconds, Total Train Time = 29.59992265701294\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.36271077394485474, 'eval_runtime': 1.1134, 'eval_samples_per_second': 2501.438, 'eval_steps_per_second': 39.52, 'epoch': 13.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.362    0.361     0.363\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = etth2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.280693918466568, 'eval_model_preparation_time': 0.0019, 'eval_runtime': 0.9381, 'eval_samples_per_second': 2968.845, 'eval_steps_per_second': 46.905}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n", "Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='55' max='250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 55/250 00:23 < 01:24, 2.30 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.565900</td>\n", "      <td>0.224047</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.524400</td>\n", "      <td>0.224874</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.560600</td>\n", "      <td>0.226318</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.518900</td>\n", "      <td>0.229082</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.532800</td>\n", "      <td>0.234455</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.487500</td>\n", "      <td>0.244063</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.437800</td>\n", "      <td>0.257846</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.404400</td>\n", "      <td>0.275406</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.368500</td>\n", "      <td>0.299849</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.353900</td>\n", "      <td>0.332489</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.320900</td>\n", "      <td>0.374024</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.8974325223402544 seconds, Total Train Time = 23.770314931869507\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.2801705598831177, 'eval_runtime': 1.1153, 'eval_samples_per_second': 2497.161, 'eval_steps_per_second': 39.452, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n", "Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='132' max='550' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [132/550 00:25 < 01:23, 5.00 it/s, Epoch 12/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.414400</td>\n", "      <td>0.223319</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.420600</td>\n", "      <td>0.223185</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.378100</td>\n", "      <td>0.223428</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.359700</td>\n", "      <td>0.224102</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.330100</td>\n", "      <td>0.225113</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.298500</td>\n", "      <td>0.227125</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.264500</td>\n", "      <td>0.229628</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.242900</td>\n", "      <td>0.238282</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.219100</td>\n", "      <td>0.251995</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.201800</td>\n", "      <td>0.277940</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.180300</td>\n", "      <td>0.317722</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.171200</td>\n", "      <td>0.340438</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 0.9633900125821432 seconds, Total Train Time = 26.702669858932495\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='44' max='44' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [44/44 00:00]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.28046759963035583, 'eval_runtime': 1.1216, 'eval_samples_per_second': 2483.164, 'eval_steps_per_second': 39.231, 'epoch': 12.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.362    0.361     0.363\n", "1   etth2   0.281    0.280     0.280\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = ettm1, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.38726314902305603, 'eval_model_preparation_time': 0.002, 'eval_runtime': 3.0815, 'eval_samples_per_second': 3707.636, 'eval_steps_per_second': 58.089}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='375' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 375/1250 00:50 < 01:58, 7.40 it/s, Epoch 15/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.504500</td>\n", "      <td>0.422623</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.471700</td>\n", "      <td>0.417156</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.424700</td>\n", "      <td>0.412834</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.385000</td>\n", "      <td>0.409597</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.340900</td>\n", "      <td>0.409013</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.300900</td>\n", "      <td>0.417046</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.273300</td>\n", "      <td>0.429183</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.251200</td>\n", "      <td>0.439041</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.232500</td>\n", "      <td>0.448727</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.223100</td>\n", "      <td>0.456104</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.214200</td>\n", "      <td>0.460536</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.207600</td>\n", "      <td>0.466538</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.203800</td>\n", "      <td>0.476997</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.198100</td>\n", "      <td>0.480505</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.194100</td>\n", "      <td>0.488817</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.2302387396494547 seconds, Total Train Time = 51.18072175979614\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.3715095520019531, 'eval_runtime': 2.2124, 'eval_samples_per_second': 5163.992, 'eval_steps_per_second': 80.906, 'epoch': 15.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='714' max='2550' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 714/2550 00:51 < 02:13, 13.78 it/s, Epoch 14/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.542500</td>\n", "      <td>0.419067</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.470700</td>\n", "      <td>0.414835</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.418600</td>\n", "      <td>0.413820</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.366600</td>\n", "      <td>0.407678</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.327400</td>\n", "      <td>0.407747</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.301200</td>\n", "      <td>0.413063</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.282900</td>\n", "      <td>0.419441</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.271800</td>\n", "      <td>0.437654</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.262200</td>\n", "      <td>0.438877</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.254400</td>\n", "      <td>0.450964</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.250900</td>\n", "      <td>0.463638</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.246800</td>\n", "      <td>0.465234</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.241700</td>\n", "      <td>0.471252</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.239800</td>\n", "      <td>0.479022</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.6246584824153356 seconds, Total Train Time = 52.44022512435913\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.37059730291366577, 'eval_runtime': 2.1998, 'eval_samples_per_second': 5193.564, 'eval_steps_per_second': 81.37, 'epoch': 14.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.362    0.361     0.363\n", "1   etth2   0.281    0.280     0.280\n", "2   ettm1   0.387    0.372     0.371\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = ettm2, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17503736913204193, 'eval_model_preparation_time': 0.002, 'eval_runtime': 3.0285, 'eval_samples_per_second': 3772.489, 'eval_steps_per_second': 59.105}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='275' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 275/1250 00:36 < 02:09, 7.52 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.280700</td>\n", "      <td>0.121009</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.264600</td>\n", "      <td>0.121268</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.226500</td>\n", "      <td>0.123216</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.199600</td>\n", "      <td>0.129200</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.169000</td>\n", "      <td>0.141758</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.152200</td>\n", "      <td>0.155555</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.138100</td>\n", "      <td>0.163517</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.129300</td>\n", "      <td>0.172492</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.122000</td>\n", "      <td>0.183950</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.116400</td>\n", "      <td>0.191413</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.112600</td>\n", "      <td>0.197757</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.2246154004877263 seconds, Total Train Time = 37.09647488594055\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.17288224399089813, 'eval_runtime': 2.0563, 'eval_samples_per_second': 5555.966, 'eval_steps_per_second': 87.048, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='561' max='2550' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 561/2550 00:40 < 02:25, 13.70 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.314100</td>\n", "      <td>0.121323</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.274900</td>\n", "      <td>0.122920</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.243900</td>\n", "      <td>0.126737</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.213000</td>\n", "      <td>0.131092</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.194700</td>\n", "      <td>0.134649</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.184000</td>\n", "      <td>0.137388</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.175500</td>\n", "      <td>0.139926</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.169700</td>\n", "      <td>0.142911</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.164200</td>\n", "      <td>0.151129</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.160800</td>\n", "      <td>0.147594</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.157500</td>\n", "      <td>0.151603</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.6438330086794766 seconds, Total Train Time = 41.61775827407837\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='179' max='179' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [179/179 00:01]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1721040904521942, 'eval_runtime': 2.1992, 'eval_samples_per_second': 5195.151, 'eval_steps_per_second': 81.394, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["  dataset  zs_mse  fs5_mse  fs10_mse\n", "0   etth1   0.362    0.361     0.363\n", "1   etth2   0.281    0.280     0.280\n", "2   ettm1   0.387    0.372     0.371\n", "3   ettm2   0.175    0.173     0.172\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = weather, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:04]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15184031426906586, 'eval_model_preparation_time': 0.0019, 'eval_runtime': 4.9387, 'eval_samples_per_second': 2114.708, 'eval_steps_per_second': 33.207}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='297' max='1350' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 297/1350 00:41 < 02:28, 7.07 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.152400</td>\n", "      <td>0.419179</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.147600</td>\n", "      <td>0.424661</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.142100</td>\n", "      <td>0.439751</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.136300</td>\n", "      <td>0.458828</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.127800</td>\n", "      <td>0.483952</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.119200</td>\n", "      <td>0.519423</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.110600</td>\n", "      <td>0.522068</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.103600</td>\n", "      <td>0.505524</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.097000</td>\n", "      <td>0.515911</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.091300</td>\n", "      <td>0.517793</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.086200</td>\n", "      <td>0.485350</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 1.5865312923084607 seconds, Total Train Time = 42.57614731788635\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1506919413805008, 'eval_runtime': 3.4423, 'eval_samples_per_second': 3034.011, 'eval_steps_per_second': 47.642, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='605' max='2750' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [ 605/2750 00:50 < 02:58, 11.99 it/s, Epoch 11/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.137700</td>\n", "      <td>0.424454</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.133600</td>\n", "      <td>0.436503</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.128500</td>\n", "      <td>0.445798</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.123400</td>\n", "      <td>0.456645</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.116700</td>\n", "      <td>0.477022</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.111500</td>\n", "      <td>0.483446</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.105500</td>\n", "      <td>0.470728</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.099900</td>\n", "      <td>0.470292</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.095400</td>\n", "      <td>0.476556</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.090700</td>\n", "      <td>0.458923</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.087200</td>\n", "      <td>0.471447</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 2.335432356054133 seconds, Total Train Time = 51.203959941864014\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='164' max='164' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [164/164 00:02]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.15016287565231323, 'eval_runtime': 3.5628, 'eval_samples_per_second': 2931.377, 'eval_steps_per_second': 46.031, 'epoch': 11.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "   dataset  zs_mse  fs5_mse  fs10_mse\n", "0    etth1   0.362    0.361     0.363\n", "1    etth2   0.281    0.280     0.280\n", "2    ettm1   0.387    0.372     0.371\n", "3    ettm2   0.175    0.173     0.172\n", "4  weather   0.152    0.151     0.150\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = electricity, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:23]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.1555725336074829, 'eval_model_preparation_time': 0.0019, 'eval_runtime': 24.0761, 'eval_samples_per_second': 214.529, 'eval_steps_per_second': 6.729}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1250' max='1250' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1250/1250 12:48, Epoch 50/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.145900</td>\n", "      <td>0.127765</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.140000</td>\n", "      <td>0.124079</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.135400</td>\n", "      <td>0.121057</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.131600</td>\n", "      <td>0.118233</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.127300</td>\n", "      <td>0.116960</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.124700</td>\n", "      <td>0.115788</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.121300</td>\n", "      <td>0.114265</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.119800</td>\n", "      <td>0.113604</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.117900</td>\n", "      <td>0.113588</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.115900</td>\n", "      <td>0.114102</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.114200</td>\n", "      <td>0.114330</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.114100</td>\n", "      <td>0.114430</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.112600</td>\n", "      <td>0.113078</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.111900</td>\n", "      <td>0.114254</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.110900</td>\n", "      <td>0.113203</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.110300</td>\n", "      <td>0.116154</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.108800</td>\n", "      <td>0.114116</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.108300</td>\n", "      <td>0.114400</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.107600</td>\n", "      <td>0.113790</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.107100</td>\n", "      <td>0.112894</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.107000</td>\n", "      <td>0.114230</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.107100</td>\n", "      <td>0.113750</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.106600</td>\n", "      <td>0.112724</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.105200</td>\n", "      <td>0.112615</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.104600</td>\n", "      <td>0.112540</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.104400</td>\n", "      <td>0.114088</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.104300</td>\n", "      <td>0.113155</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.104000</td>\n", "      <td>0.113183</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.103300</td>\n", "      <td>0.113108</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.103300</td>\n", "      <td>0.112891</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.103000</td>\n", "      <td>0.112966</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.102800</td>\n", "      <td>0.112305</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.102100</td>\n", "      <td>0.112232</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.101800</td>\n", "      <td>0.112428</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.101800</td>\n", "      <td>0.112281</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.101500</td>\n", "      <td>0.112245</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.101300</td>\n", "      <td>0.112165</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.101600</td>\n", "      <td>0.112430</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.100800</td>\n", "      <td>0.112168</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.101100</td>\n", "      <td>0.112292</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.100700</td>\n", "      <td>0.112243</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.101100</td>\n", "      <td>0.112085</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.100500</td>\n", "      <td>0.112192</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.100500</td>\n", "      <td>0.112366</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.100200</td>\n", "      <td>0.112117</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.100500</td>\n", "      <td>0.112201</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.100400</td>\n", "      <td>0.112215</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.100400</td>\n", "      <td>0.112120</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.100400</td>\n", "      <td>0.112149</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.100000</td>\n", "      <td>0.112153</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 5.149650845527649 seconds, Total Train Time = 771.8066599369049\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:16]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.14543357491493225, 'eval_runtime': 18.8494, 'eval_samples_per_second': 274.014, 'eval_steps_per_second': 8.594, 'epoch': 50.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='2288' max='2600' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [2288/2600 13:59 < 01:54, 2.72 it/s, Epoch 44/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.142600</td>\n", "      <td>0.123726</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.136800</td>\n", "      <td>0.119504</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.132600</td>\n", "      <td>0.116580</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.129200</td>\n", "      <td>0.114476</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.126500</td>\n", "      <td>0.112773</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.124100</td>\n", "      <td>0.111989</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.122400</td>\n", "      <td>0.111565</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.121000</td>\n", "      <td>0.111765</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.119900</td>\n", "      <td>0.112019</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.119100</td>\n", "      <td>0.110442</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.118400</td>\n", "      <td>0.111159</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.117700</td>\n", "      <td>0.111936</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.116800</td>\n", "      <td>0.110754</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.116500</td>\n", "      <td>0.111346</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.115700</td>\n", "      <td>0.111159</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.115600</td>\n", "      <td>0.112541</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.115400</td>\n", "      <td>0.110576</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.114600</td>\n", "      <td>0.110490</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.114300</td>\n", "      <td>0.110820</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.113700</td>\n", "      <td>0.110099</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.113600</td>\n", "      <td>0.111032</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.113100</td>\n", "      <td>0.110572</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.112500</td>\n", "      <td>0.110152</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.112200</td>\n", "      <td>0.110462</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.112200</td>\n", "      <td>0.110486</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.111500</td>\n", "      <td>0.109890</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.111200</td>\n", "      <td>0.109659</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.110900</td>\n", "      <td>0.110145</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.111000</td>\n", "      <td>0.110042</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.110500</td>\n", "      <td>0.109693</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.110400</td>\n", "      <td>0.109685</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.110300</td>\n", "      <td>0.109534</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.109900</td>\n", "      <td>0.109661</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.109800</td>\n", "      <td>0.109107</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.109500</td>\n", "      <td>0.109508</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.109200</td>\n", "      <td>0.109286</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.109000</td>\n", "      <td>0.109707</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.108600</td>\n", "      <td>0.109372</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.108600</td>\n", "      <td>0.109286</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.108500</td>\n", "      <td>0.109232</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.108400</td>\n", "      <td>0.109145</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.108300</td>\n", "      <td>0.109114</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.108300</td>\n", "      <td>0.109157</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.108300</td>\n", "      <td>0.109180</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 8.964020311832428 seconds, Total Train Time = 841.2196242809296\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='162' max='162' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [162/162 00:17]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.13808377087116241, 'eval_runtime': 18.7227, 'eval_samples_per_second': 275.868, 'eval_steps_per_second': 8.653, 'epoch': 44.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse  fs10_mse\n", "0        etth1   0.362    0.361     0.363\n", "1        etth2   0.281    0.280     0.280\n", "2        ettm1   0.387    0.372     0.371\n", "3        ettm2   0.175    0.173     0.172\n", "4      weather   0.152    0.151     0.150\n", "5  electricity   0.156    0.145     0.138\n", "\n", "====================================================================================================\n", "Running zero-shot/few-shot for TTM-1024 on dataset = traffic, forecast_len = 96\n", "Model will be loaded from ibm-granite/granite-timeseries-ttm-r1\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["++++++++++++++++++++ Test MSE zero-shot ++++++++++++++++++++\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:41]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4576044976711273, 'eval_model_preparation_time': 0.0021, 'eval_runtime': 41.4647, 'eval_samples_per_second': 82.311, 'eval_steps_per_second': 10.298}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 5% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='1652' max='2950' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [1652/2950 11:17 < 08:53, 2.43 it/s, Epoch 28/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.286700</td>\n", "      <td>0.364595</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.261100</td>\n", "      <td>0.354531</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.248300</td>\n", "      <td>0.348675</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.237800</td>\n", "      <td>0.347864</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.229100</td>\n", "      <td>0.346862</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.223000</td>\n", "      <td>0.355750</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.217900</td>\n", "      <td>0.347379</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.215000</td>\n", "      <td>0.351959</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.210800</td>\n", "      <td>0.345832</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.208000</td>\n", "      <td>0.353231</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.207100</td>\n", "      <td>0.352474</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.204400</td>\n", "      <td>0.359140</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.204100</td>\n", "      <td>0.350371</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.202100</td>\n", "      <td>0.366590</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.201200</td>\n", "      <td>0.361391</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.198500</td>\n", "      <td>0.349136</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.196100</td>\n", "      <td>0.369769</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.195400</td>\n", "      <td>0.345229</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.195100</td>\n", "      <td>0.357952</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.192400</td>\n", "      <td>0.357397</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.192000</td>\n", "      <td>0.363344</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.190500</td>\n", "      <td>0.355350</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.189100</td>\n", "      <td>0.363749</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.188000</td>\n", "      <td>0.354421</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.188700</td>\n", "      <td>0.353886</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.186300</td>\n", "      <td>0.361907</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.184700</td>\n", "      <td>0.352133</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.184500</td>\n", "      <td>0.356455</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 7.461020086492811 seconds, Total Train Time = 679.5133337974548\n", "++++++++++++++++++++ Test MSE after few-shot 5% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:30]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.4156947731971741, 'eval_runtime': 32.2478, 'eval_samples_per_second': 105.837, 'eval_steps_per_second': 13.241, 'epoch': 28.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "-------------------- Running few-shot 10% --------------------\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/dataset.py:199: PerformanceWarning: DataFrame is highly fragmented.  This is usually the result of calling `frame.insert` many times, which has poor performance.  Consider joining all columns at once using pd.concat(axis=1) instead. To get a de-fragmented frame, use `newframe = frame.copy()`\n", "  data_df[\"group\"] = 0  # create a artificial group\n", "WARNING:/dccstor/dnn_forecasting/arindam/FM/HF/public_tsfm/tsfm/tsfm_public/toolkit/get_model.py:Requested `prediction_length` is not exactly equal to any of the available TTM prediction lengths. Hence, TTM will forecast using the `prediction_filter_length` argument to provide the requested prediction length.\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/site-packages/transformers/training_args.py:1545: FutureWarning: `evaluation_strategy` is deprecated and will be removed in version 4.46 of 🤗 Transformers. Use `eval_strategy` instead\n", "  warnings.warn(\n", "WARNING:accelerate.utils.other:Detected kernel version 4.18.0, which is below the recommended minimum of 5.5.0; this can cause the process to hang. It is recommended to upgrade the kernel to the minimum version or higher.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Number of params before freezing backbone 946336\n", "Number of params after freezing the backbone 389984\n", "Using learning rate = 0.001\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='2064' max='6450' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [2064/6450 08:15 < 17:32, 4.17 it/s, Epoch 16/50]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>0.270300</td>\n", "      <td>0.362664</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>0.250300</td>\n", "      <td>0.351346</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>0.241200</td>\n", "      <td>0.348280</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>0.235000</td>\n", "      <td>0.348637</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>0.230200</td>\n", "      <td>0.346396</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>0.226200</td>\n", "      <td>0.343058</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>0.223100</td>\n", "      <td>0.351453</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>0.220700</td>\n", "      <td>0.347172</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>0.218700</td>\n", "      <td>0.349513</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>0.216700</td>\n", "      <td>0.351570</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>0.215000</td>\n", "      <td>0.353760</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>0.214200</td>\n", "      <td>0.348613</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>0.213000</td>\n", "      <td>0.353903</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>0.211300</td>\n", "      <td>0.347297</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>0.210300</td>\n", "      <td>0.354026</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>0.208300</td>\n", "      <td>0.350533</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n", "/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["[TrackingCallback] Mean Epoch Time = 14.446707516908646 seconds, Total Train Time = 496.55380868911743\n", "++++++++++++++++++++ Test MSE after few-shot 10% fine-tuning ++++++++++++++++++++\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='427' max='427' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [427/427 00:30]\n", "    </div>\n", "    "], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stderr", "output_type": "stream", "text": ["/dccstor/dnn_forecasting/conda_envs/envs/fm/lib/python3.9/multiprocessing/popen_fork.py:66: RuntimeWarning: os.fork() was called. os.fork() is incompatible with multithreaded code, and JAX is multithreaded, so this will likely lead to a deadlock.\n", "  self.pid = os.fork()\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'eval_loss': 0.41844481229782104, 'eval_runtime': 32.4527, 'eval_samples_per_second': 105.168, 'eval_steps_per_second': 13.158, 'epoch': 16.0}\n", "++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++\n", "       dataset  zs_mse  fs5_mse  fs10_mse\n", "0        etth1   0.362    0.361     0.363\n", "1        etth2   0.281    0.280     0.280\n", "2        ettm1   0.387    0.372     0.371\n", "3        ettm2   0.175    0.173     0.172\n", "4      weather   0.152    0.151     0.150\n", "5  electricity   0.156    0.145     0.138\n", "6      traffic   0.458    0.416     0.418\n"]}], "source": ["all_results = {\n", "    \"dataset\": [],\n", "    \"zs_mse\": [],\n", "    \"fs5_mse\": [],\n", "    \"fs10_mse\": [],\n", "    \"zs_eval_time\": [],\n", "    \"fs5_mean_epoch_time\": [],\n", "    \"fs5_total_train_time\": [],\n", "    \"fs10_mean_epoch_time\": [],\n", "    \"fs10_total_train_time\": [],\n", "    \"fs5_best_val_metric\": [],\n", "    \"fs10_best_val_metric\": [],\n", "}\n", "# Loop over data\n", "for DATASET in list_datasets:\n", "    print()\n", "    print(\"=\" * 100)\n", "    print(\n", "        f\"Running zero-shot/few-shot for TTM-{context_length} on dataset = {DATASET}, forecast_len = {forecast_length}\"\n", "    )\n", "    print(f\"Model will be loaded from {hf_model_path}\")\n", "    SUBDIR = f\"{OUT_DIR}/{DATASET}\"\n", "\n", "    # Set batch size\n", "    if DATASET == \"traffic\":\n", "        BATCH_SIZE = 8\n", "    elif <PERSON> == \"electricity\":\n", "        BATCH_SIZE = 32\n", "    else:\n", "        BATCH_SIZE = 64\n", "\n", "    # Data prep: Get dataset\n", "    _, _, dset_test = load_dataset(DATASET, context_length, forecast_length, dataset_root_path=DATA_ROOT_PATH)\n", "\n", "    #############################################################\n", "    ##### Use the pretrained model in zero-shot forecasting #####\n", "    #############################################################\n", "    # Load model\n", "    zeroshot_model = get_model(hf_model_path, context_length=context_length, prediction_length=forecast_length)\n", "\n", "    # zeroshot_trainer\n", "    zeroshot_trainer = Trainer(\n", "        model=zeroshot_model,\n", "        args=TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/zeroshot\",\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "        ),\n", "        eval_dataset=dset_test,\n", "    )\n", "\n", "    # evaluate = zero-shot performance\n", "    print(\"+\" * 20, \"Test MSE zero-shot\", \"+\" * 20)\n", "    zeroshot_output = zeroshot_trainer.evaluate(dset_test)\n", "    print(zeroshot_output)\n", "    print(\"+\" * 60)\n", "    all_results[\"zs_eval_time\"].append(zeroshot_output[\"eval_runtime\"])\n", "\n", "    # Plot\n", "    plot_predictions(\n", "        model=zeroshot_trainer.model,\n", "        dset=dset_test,\n", "        plot_dir=SUBDIR,\n", "        num_plots=10,\n", "        plot_prefix=\"test_zeroshot\",\n", "        channel=0,\n", "    )\n", "    plt.close()\n", "\n", "    # write results\n", "    all_results[\"dataset\"].append(DATASET)\n", "    all_results[\"zs_mse\"].append(zeroshot_output[\"eval_loss\"])\n", "\n", "    ################################################################\n", "    ## Use the pretrained model in few-shot 5% and 10% forecasting #\n", "    ################################################################\n", "    for fewshot_percent in [5, 10]:\n", "        print(\"-\" * 20, f\"Running few-shot {fewshot_percent}%\", \"-\" * 20)\n", "        # Data prep: Get dataset\n", "        dset_train, dset_val, dset_test = load_dataset(\n", "            DATASET,\n", "            context_length,\n", "            forecast_length,\n", "            fewshot_fraction=fewshot_percent / 100,\n", "            dataset_root_path=DATA_ROOT_PATH,\n", "        )\n", "\n", "        # change head dropout to 0.7 for ett datasets\n", "        if \"ett\" in DATASET:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length, head_dropout=0.7\n", "            )\n", "        else:\n", "            finetune_forecast_model = get_model(\n", "                hf_model_path, context_length=context_length, prediction_length=forecast_length\n", "            )\n", "\n", "        if freeze_backbone:\n", "            print(\n", "                \"Number of params before freezing backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "            # Freeze the backbone of the model\n", "            for param in finetune_forecast_model.backbone.parameters():\n", "                param.requires_grad = False\n", "\n", "            # Count params\n", "            print(\n", "                \"Number of params after freezing the backbone\",\n", "                count_parameters(finetune_forecast_model),\n", "            )\n", "\n", "        print(f\"Using learning rate = {learning_rate}\")\n", "        finetune_forecast_args = TrainingArguments(\n", "            output_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",\n", "            overwrite_output_dir=True,\n", "            learning_rate=learning_rate,\n", "            num_train_epochs=EPOCHS,\n", "            do_eval=True,\n", "            evaluation_strategy=\"epoch\",\n", "            per_device_train_batch_size=BATCH_SIZE,\n", "            per_device_eval_batch_size=BATCH_SIZE,\n", "            dataloader_num_workers=NUM_WORKERS,\n", "            report_to=None,\n", "            save_strategy=\"epoch\",\n", "            logging_strategy=\"epoch\",\n", "            save_total_limit=1,\n", "            logging_dir=f\"{SUBDIR}/fewshot_{fewshot_percent}\",  # Make sure to specify a logging directory\n", "            load_best_model_at_end=True,  # Load the best model when training ends\n", "            metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "            greater_is_better=False,  # For loss\n", "        )\n", "\n", "        # Create the early stopping callback\n", "        early_stopping_callback = EarlyStoppingCallback(\n", "            early_stopping_patience=10,  # Number of epochs with no improvement after which to stop\n", "            early_stopping_threshold=0.0,  # Minimum improvement required to consider as improvement\n", "        )\n", "        tracking_callback = TrackingCallback()\n", "\n", "        # Optimizer and scheduler\n", "        optimizer = AdamW(finetune_forecast_model.parameters(), lr=learning_rate)\n", "        scheduler = OneCycleLR(\n", "            optimizer,\n", "            learning_rate,\n", "            epochs=EPOCHS,\n", "            steps_per_epoch=math.ceil(len(dset_train) / (BATCH_SIZE)),\n", "        )\n", "\n", "        finetune_forecast_trainer = Trainer(\n", "            model=finetune_forecast_model,\n", "            args=finetune_forecast_args,\n", "            train_dataset=dset_train,\n", "            eval_dataset=dset_val,\n", "            callbacks=[early_stopping_callback, tracking_callback],\n", "            optimizers=(optimizer, scheduler),\n", "        )\n", "        finetune_forecast_trainer.remove_callback(INTEGRATION_TO_CALLBACK[\"codecarbon\"])\n", "\n", "        # Fine tune\n", "        finetune_forecast_trainer.train()\n", "\n", "        # Evaluation\n", "        print(\n", "            \"+\" * 20,\n", "            f\"Test MSE after few-shot {fewshot_percent}% fine-tuning\",\n", "            \"+\" * 20,\n", "        )\n", "        fewshot_output = finetune_forecast_trainer.evaluate(dset_test)\n", "        print(fewshot_output)\n", "        print(\"+\" * 60)\n", "\n", "        # Plot\n", "        plot_predictions(\n", "            model=finetune_forecast_trainer.model,\n", "            dset=dset_test,\n", "            plot_dir=SUBDIR,\n", "            num_plots=10,\n", "            plot_prefix=f\"test_fewshot_{fewshot_percent}\",\n", "            channel=0,\n", "        )\n", "        plt.close()\n", "\n", "        # write results\n", "        all_results[f\"fs{fewshot_percent}_mse\"].append(fewshot_output[\"eval_loss\"])\n", "        all_results[f\"fs{fewshot_percent}_mean_epoch_time\"].append(tracking_callback.mean_epoch_time)\n", "        all_results[f\"fs{fewshot_percent}_total_train_time\"].append(tracking_callback.total_train_time)\n", "        all_results[f\"fs{fewshot_percent}_best_val_metric\"].append(tracking_callback.best_eval_metric)\n", "\n", "    df_out = pd.DataFrame(all_results).round(3)\n", "    print(df_out[[\"dataset\", \"zs_mse\", \"fs5_mse\", \"fs10_mse\"]])\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")\n", "    df_out.to_csv(f\"{OUT_DIR}/results_zero_few.csv\")"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Benchmarking results*\n", "\n", "*Some slight differences in the results as compared to the TTM paper results is possible due to different training environments."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>dataset</th>\n", "      <th>zs_mse</th>\n", "      <th>fs5_mse</th>\n", "      <th>fs10_mse</th>\n", "      <th>zs_eval_time</th>\n", "      <th>fs5_mean_epoch_time</th>\n", "      <th>fs5_total_train_time</th>\n", "      <th>fs10_mean_epoch_time</th>\n", "      <th>fs10_total_train_time</th>\n", "      <th>fs5_best_val_metric</th>\n", "      <th>fs10_best_val_metric</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>etth1</td>\n", "      <td>0.362</td>\n", "      <td>0.361</td>\n", "      <td>0.363</td>\n", "      <td>1.020</td>\n", "      <td>0.948</td>\n", "      <td>39.056</td>\n", "      <td>1.044</td>\n", "      <td>29.600</td>\n", "      <td>0.658</td>\n", "      <td>0.663</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>etth2</td>\n", "      <td>0.281</td>\n", "      <td>0.280</td>\n", "      <td>0.280</td>\n", "      <td>0.938</td>\n", "      <td>0.897</td>\n", "      <td>23.770</td>\n", "      <td>0.963</td>\n", "      <td>26.703</td>\n", "      <td>0.224</td>\n", "      <td>0.223</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ettm1</td>\n", "      <td>0.387</td>\n", "      <td>0.372</td>\n", "      <td>0.371</td>\n", "      <td>3.082</td>\n", "      <td>1.230</td>\n", "      <td>51.181</td>\n", "      <td>1.625</td>\n", "      <td>52.440</td>\n", "      <td>0.409</td>\n", "      <td>0.408</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ettm2</td>\n", "      <td>0.175</td>\n", "      <td>0.173</td>\n", "      <td>0.172</td>\n", "      <td>3.028</td>\n", "      <td>1.225</td>\n", "      <td>37.096</td>\n", "      <td>1.644</td>\n", "      <td>41.618</td>\n", "      <td>0.121</td>\n", "      <td>0.121</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>weather</td>\n", "      <td>0.152</td>\n", "      <td>0.151</td>\n", "      <td>0.150</td>\n", "      <td>4.939</td>\n", "      <td>1.587</td>\n", "      <td>42.576</td>\n", "      <td>2.335</td>\n", "      <td>51.204</td>\n", "      <td>0.419</td>\n", "      <td>0.424</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>electricity</td>\n", "      <td>0.156</td>\n", "      <td>0.145</td>\n", "      <td>0.138</td>\n", "      <td>24.076</td>\n", "      <td>5.150</td>\n", "      <td>771.807</td>\n", "      <td>8.964</td>\n", "      <td>841.220</td>\n", "      <td>0.112</td>\n", "      <td>0.109</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>traffic</td>\n", "      <td>0.458</td>\n", "      <td>0.416</td>\n", "      <td>0.418</td>\n", "      <td>41.465</td>\n", "      <td>7.461</td>\n", "      <td>679.513</td>\n", "      <td>14.447</td>\n", "      <td>496.554</td>\n", "      <td>0.345</td>\n", "      <td>0.343</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       dataset  zs_mse  fs5_mse  fs10_mse  zs_eval_time  fs5_mean_epoch_time  \\\n", "0        etth1   0.362    0.361     0.363         1.020                0.948   \n", "1        etth2   0.281    0.280     0.280         0.938                0.897   \n", "2        ettm1   0.387    0.372     0.371         3.082                1.230   \n", "3        ettm2   0.175    0.173     0.172         3.028                1.225   \n", "4      weather   0.152    0.151     0.150         4.939                1.587   \n", "5  electricity   0.156    0.145     0.138        24.076                5.150   \n", "6      traffic   0.458    0.416     0.418        41.465                7.461   \n", "\n", "   fs5_total_train_time  fs10_mean_epoch_time  fs10_total_train_time  \\\n", "0                39.056                 1.044                 29.600   \n", "1                23.770                 0.963                 26.703   \n", "2                51.181                 1.625                 52.440   \n", "3                37.096                 1.644                 41.618   \n", "4                42.576                 2.335                 51.204   \n", "5               771.807                 8.964                841.220   \n", "6               679.513                14.447                496.554   \n", "\n", "   fs5_best_val_metric  fs10_best_val_metric  \n", "0                0.658                 0.663  \n", "1                0.224                 0.223  \n", "2                0.409                 0.408  \n", "3                0.121                 0.121  \n", "4                0.419                 0.424  \n", "5                0.112                 0.109  \n", "6                0.345                 0.343  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_out"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.12"}}, "nbformat": 4, "nbformat_minor": 4}