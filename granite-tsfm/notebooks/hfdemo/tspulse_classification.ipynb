{"cells": [{"cell_type": "markdown", "id": "19d56d72", "metadata": {}, "source": ["# Getting Started with TSPulse Classification\n", "\n", "This notebook demonstrates the usage of a pre-trained TSPulse model for time-series classification task. Refer to [TSPulse](https://arxiv.org/abs/2505.13033) paper for architecture and other details.\n", "\n", "Backbone of the pre-trained model is freezed and the classifier head along with the input patch embedding layer is finetuned on the classification dataset.\n"]}, {"cell_type": "markdown", "id": "bf2d0025-e9dd-465f-a45a-2ecc22073328", "metadata": {}, "source": ["## Imports"]}, {"cell_type": "code", "execution_count": null, "id": "8ccc6220-6a78-4aa1-b3ac-737223dbe987", "metadata": {}, "outputs": [], "source": ["import math\n", "import os\n", "import tempfile\n", "\n", "import numpy as np\n", "import torch\n", "from torch.optim import AdamW\n", "from torch.optim.lr_scheduler import OneCycleLR\n", "from torch.utils.data import DataLoader, random_split\n", "from transformers import EarlyStoppingCallback, Trainer, TrainingArguments, set_seed\n", "from transformers.data.data_collator import default_data_collator\n", "from transformers.trainer_utils import RemoveColumnsCollator"]}, {"cell_type": "code", "execution_count": 2, "id": "ee950055-4527-40eb-8fe2-ce023003f2b8", "metadata": {}, "outputs": [], "source": ["from tsfm_public.models.tspulse import TSPulseForClassification\n", "from tsfm_public.toolkit.dataset import ClassificationDFDataset\n", "from tsfm_public.toolkit.lr_finder import optimal_lr_finder\n", "from tsfm_public.toolkit.time_series_classification_preprocessor import TimeSeriesClassificationPreprocessor\n", "from tsfm_public.toolkit.util import convert_tsfile_to_dataframe"]}, {"cell_type": "markdown", "id": "07aba832-beab-442f-a6e7-a70abbfdfbc1", "metadata": {}, "source": ["## Data Preprocessing"]}, {"cell_type": "code", "execution_count": 3, "id": "dc3eedad-946d-41fa-b2c2-838307091d7a", "metadata": {}, "outputs": [], "source": ["seed = 42\n", "set_seed(seed)"]}, {"cell_type": "code", "execution_count": 4, "id": "938db5cc-a761-4eb7-9042-e42aa8075a5d", "metadata": {}, "outputs": [], "source": ["dataset_name = \"BasicMotions\""]}, {"cell_type": "code", "execution_count": 5, "id": "503186b9-99aa-4335-a966-e3dc9a24414f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["40\n"]}], "source": ["path = f\"/datasets/{dataset_name}/{dataset_name}_TRAIN.ts\"\n", "\n", "df_base = convert_tsfile_to_dataframe(\n", "    path,\n", "    return_separate_X_and_y=False,\n", ")\n", "label_column = \"class_vals\"\n", "input_columns = [f\"dim_{i}\" for i in range(df_base.shape[1] - 1)]\n", "\n", "tsp = TimeSeriesClassificationPreprocessor(\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    scaling=True,\n", ")\n", "\n", "tsp.train(df_base)\n", "df_prep = tsp.preprocess(df_base)\n", "base_dataset = ClassificationDFDataset(\n", "    df_prep,\n", "    id_columns=[],\n", "    timestamp_column=None,\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    context_length=512,\n", "    static_categorical_columns=[],\n", "    stride=1,\n", "    enable_padding=False,\n", "    full_series=True,\n", ")\n", "\n", "path = f\"/datasets/{dataset_name}/{dataset_name}_TRAIN.ts\"\n", "\n", "df_test = convert_tsfile_to_dataframe(\n", "    path,\n", "    return_separate_X_and_y=False,\n", ")\n", "label_column = \"class_vals\"\n", "input_columns = [f\"dim_{i}\" for i in range(df_test.shape[1] - 1)]\n", "\n", "tsp = TimeSeriesClassificationPreprocessor(\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    scaling=True,\n", ")\n", "\n", "tsp.train(df_test)\n", "df_prep = tsp.preprocess(df_test)\n", "\n", "test_dataset = ClassificationDFDataset(\n", "    df_prep,\n", "    id_columns=[],\n", "    timestamp_column=None,\n", "    input_columns=input_columns,\n", "    label_column=label_column,\n", "    context_length=512,\n", "    static_categorical_columns=[],\n", "    stride=1,\n", "    enable_padding=False,\n", "    full_series=True,\n", ")\n", "\n", "dataset_size = len(base_dataset)\n", "print(dataset_size)\n", "split_valid_ratio = 0.1\n", "val_size = int(split_valid_ratio * dataset_size)  # 10% valid split\n", "train_size = dataset_size - val_size\n", "train_dataset, valid_dataset = random_split(base_dataset, [train_size, val_size])"]}, {"cell_type": "markdown", "id": "b0976700-08fc-42c6-8dab-c1134c0fcc21", "metadata": {}, "source": ["## Configs for the TSPulse model"]}, {"cell_type": "markdown", "id": "50be9ebe", "metadata": {}, "source": ["### Hyperparameters to Optimize and suggested values :\n", "#### head_reduce_d_model = 1, 2\n", "#### decoder_mode = mix_channel, common_channel\n", "#### head_gated_attention_activation = softmax, sigmoid\n", "#### mask_ratio = 0, 0.3\n", "#### channel_virtual_expand_scale = 1, 2\n"]}, {"cell_type": "code", "execution_count": 6, "id": "07694897-1fa7-46d5-8b01-71af52a5a64d", "metadata": {}, "outputs": [], "source": ["config_dict = {\n", "    \"head_gated_attention_activation\": \"softmax\",\n", "    \"channel_virtual_expand_scale\": 2,\n", "    \"mask_ratio\": 0.3,\n", "    \"head_reduce_d_model\": 1,\n", "    \"disable_mask_in_classification_eval\": True,\n", "    \"fft_time_consistent_masking\": True,\n", "    \"decoder_mode\": \"mix_channel\",\n", "    \"head_aggregation_dim\": \"patch\",\n", "    \"head_aggregation\": None,\n", "    \"loss\": \"cross_entropy\",\n", "    \"ignore_mismatched_sizes\": True,\n", "}\n", "\n", "config_dict[\"num_input_channels\"] = tsp.num_input_channels\n", "config_dict[\"num_targets\"] = df_base[\"class_vals\"].nunique()"]}, {"cell_type": "markdown", "id": "0f48a3aa-10bb-476d-a0bf-c20b47f286d7", "metadata": {}, "source": ["## Getting the Pretrained Model with above configs"]}, {"cell_type": "code", "execution_count": 7, "id": "e2f7a221-91b7-4bfb-b397-ffc441746786", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Some weights of TSPulseForClassification were not initialized from the model checkpoint at /dccstor/tsfm23/vj_share/models/tspulse_neurips/models/vela/apr_20_block_mask/fft_mix_learn_mask_with_registers_v20_scaled_consistent/tspulse_consistent_masking_var_hybrid_e20_scaled_p16_sign_w20-20250418-0024-1/models/tspulse_model and are newly initialized: ['decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.gating_block.attn_layer.bias', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.gating_block.attn_layer.weight', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.mlp.fc1.bias', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.mlp.fc1.weight', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.mlp.fc2.bias', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.mlp.fc2.weight', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.norm.norm.bias', 'decoder_with_head.decoder.decoder_block.mixers.0.channel_feature_mixer.norm.norm.weight', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.gating_block.attn_layer.bias', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.gating_block.attn_layer.weight', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.mlp.fc1.bias', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.mlp.fc1.weight', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.mlp.fc2.bias', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.mlp.fc2.weight', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.norm.norm.bias', 'decoder_with_head.decoder.decoder_block.mixers.1.channel_feature_mixer.norm.norm.weight', 'decoder_with_head.head.head_norm.norm.bias', 'decoder_with_head.head.head_norm.norm.weight', 'decoder_with_head.head.loc_scale_norm.bias', 'decoder_with_head.head.loc_scale_norm.weight', 'decoder_with_head.head.projection.bias', 'decoder_with_head.head.projection.weight', 'decoder_with_head.head.reduce_proj.bias', 'decoder_with_head.head.reduce_proj.weight']\n", "You should probably TRAIN this model on a down-stream task to be able to use it for predictions and inference.\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Identity Init in Module:  TSPulseChannelFeatureMixerBlock\n", "Init identity weights for channel mixing\n", "Try identity init in Gated Attention.\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Identity Init in Module:  TSPulseChannelFeatureMixerBlock\n", "Init identity weights for channel mixing\n", "Try identity init in Gated Attention.\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n", "Initializing Linear layers with method: pytorch\n"]}], "source": ["model_path = \"/tspulse/tspulse_classification/tspulse_model\"\n", "model = TSPulseForClassification.from_pretrained(model_path, **config_dict)"]}, {"cell_type": "code", "execution_count": 8, "id": "17dcd1b1-6e88-4359-9214-882e6e7ffea7", "metadata": {}, "outputs": [], "source": ["device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n", "model = model.to(device).float()"]}, {"cell_type": "code", "execution_count": 9, "id": "15e12599-c519-49b4-9e5b-d32dfec912e2", "metadata": {}, "outputs": [], "source": ["# Freezing Backbone except patch embedding layer....\n", "\n", "for param in model.backbone.parameters():\n", "    param.requires_grad = False\n", "\n", "for param in model.backbone.time_encoding.parameters():\n", "    param.requires_grad = True\n", "for param in model.backbone.fft_encoding.parameters():\n", "    param.requires_grad = True"]}, {"cell_type": "markdown", "id": "03211294-ff0d-4358-aa43-dd024387f2e1", "metadata": {}, "source": ["## Finetuning the classifier head and patch embedding layer"]}, {"cell_type": "code", "execution_count": 10, "id": "873aecaf-b329-4bc8-a72b-40989bd9a6d2", "metadata": {}, "outputs": [], "source": ["OUT_DIR = \"tspulse_finetuned_models/\""]}, {"cell_type": "code", "execution_count": 11, "id": "98b4ab6a-ee7b-4602-b207-b0cb9dc3ae54", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["INFO:p-2776908:t-23180912518144:lr_finder.py:optimal_lr_finder:LR Finder: Running learning rate (LR) finder algorithm. If the suggested LR is very low, we suggest setting the LR manually.\n", "INFO:p-2776908:t-23180912518144:lr_finder.py:optimal_lr_finder:LR Finder: Using cuda:0.\n", "INFO:p-2776908:t-23180912518144:lr_finder.py:optimal_lr_finder:LR Finder: Suggested learning rate = 0.007054802310718645\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Suggested LR :  0.007054802310718645\n"]}, {"data": {"text/html": ["\n", "    <div>\n", "      \n", "      <progress value='400' max='400' style='width:300px; height:20px; vertical-align: middle;'></progress>\n", "      [400/400 01:04, Epoch 200/200]\n", "    </div>\n", "    <table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", " <tr style=\"text-align: left;\">\n", "      <th>Epoch</th>\n", "      <th>Training Loss</th>\n", "      <th>Validation Loss</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <td>1</td>\n", "      <td>1.556900</td>\n", "      <td>1.442582</td>\n", "    </tr>\n", "    <tr>\n", "      <td>2</td>\n", "      <td>1.427000</td>\n", "      <td>1.427761</td>\n", "    </tr>\n", "    <tr>\n", "      <td>3</td>\n", "      <td>1.256800</td>\n", "      <td>1.412310</td>\n", "    </tr>\n", "    <tr>\n", "      <td>4</td>\n", "      <td>1.381200</td>\n", "      <td>1.396338</td>\n", "    </tr>\n", "    <tr>\n", "      <td>5</td>\n", "      <td>1.327500</td>\n", "      <td>1.383629</td>\n", "    </tr>\n", "    <tr>\n", "      <td>6</td>\n", "      <td>1.336300</td>\n", "      <td>1.375756</td>\n", "    </tr>\n", "    <tr>\n", "      <td>7</td>\n", "      <td>1.353700</td>\n", "      <td>1.373112</td>\n", "    </tr>\n", "    <tr>\n", "      <td>8</td>\n", "      <td>1.386300</td>\n", "      <td>1.377008</td>\n", "    </tr>\n", "    <tr>\n", "      <td>9</td>\n", "      <td>1.312800</td>\n", "      <td>1.382742</td>\n", "    </tr>\n", "    <tr>\n", "      <td>10</td>\n", "      <td>1.280200</td>\n", "      <td>1.387058</td>\n", "    </tr>\n", "    <tr>\n", "      <td>11</td>\n", "      <td>1.370600</td>\n", "      <td>1.387202</td>\n", "    </tr>\n", "    <tr>\n", "      <td>12</td>\n", "      <td>1.246700</td>\n", "      <td>1.375691</td>\n", "    </tr>\n", "    <tr>\n", "      <td>13</td>\n", "      <td>1.114000</td>\n", "      <td>1.352145</td>\n", "    </tr>\n", "    <tr>\n", "      <td>14</td>\n", "      <td>1.139700</td>\n", "      <td>1.309101</td>\n", "    </tr>\n", "    <tr>\n", "      <td>15</td>\n", "      <td>1.122700</td>\n", "      <td>1.251598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>16</td>\n", "      <td>1.071100</td>\n", "      <td>1.198390</td>\n", "    </tr>\n", "    <tr>\n", "      <td>17</td>\n", "      <td>0.919900</td>\n", "      <td>1.145507</td>\n", "    </tr>\n", "    <tr>\n", "      <td>18</td>\n", "      <td>0.823800</td>\n", "      <td>1.099716</td>\n", "    </tr>\n", "    <tr>\n", "      <td>19</td>\n", "      <td>0.892200</td>\n", "      <td>1.063917</td>\n", "    </tr>\n", "    <tr>\n", "      <td>20</td>\n", "      <td>0.705800</td>\n", "      <td>1.029633</td>\n", "    </tr>\n", "    <tr>\n", "      <td>21</td>\n", "      <td>0.684700</td>\n", "      <td>0.974129</td>\n", "    </tr>\n", "    <tr>\n", "      <td>22</td>\n", "      <td>0.646600</td>\n", "      <td>0.868906</td>\n", "    </tr>\n", "    <tr>\n", "      <td>23</td>\n", "      <td>0.575000</td>\n", "      <td>0.724108</td>\n", "    </tr>\n", "    <tr>\n", "      <td>24</td>\n", "      <td>0.460600</td>\n", "      <td>0.598435</td>\n", "    </tr>\n", "    <tr>\n", "      <td>25</td>\n", "      <td>0.314100</td>\n", "      <td>0.510377</td>\n", "    </tr>\n", "    <tr>\n", "      <td>26</td>\n", "      <td>0.325700</td>\n", "      <td>0.392394</td>\n", "    </tr>\n", "    <tr>\n", "      <td>27</td>\n", "      <td>0.386300</td>\n", "      <td>0.316696</td>\n", "    </tr>\n", "    <tr>\n", "      <td>28</td>\n", "      <td>0.145400</td>\n", "      <td>0.307985</td>\n", "    </tr>\n", "    <tr>\n", "      <td>29</td>\n", "      <td>0.141900</td>\n", "      <td>0.298277</td>\n", "    </tr>\n", "    <tr>\n", "      <td>30</td>\n", "      <td>0.102000</td>\n", "      <td>0.267511</td>\n", "    </tr>\n", "    <tr>\n", "      <td>31</td>\n", "      <td>0.054600</td>\n", "      <td>0.199648</td>\n", "    </tr>\n", "    <tr>\n", "      <td>32</td>\n", "      <td>0.035000</td>\n", "      <td>0.146565</td>\n", "    </tr>\n", "    <tr>\n", "      <td>33</td>\n", "      <td>0.047900</td>\n", "      <td>0.111432</td>\n", "    </tr>\n", "    <tr>\n", "      <td>34</td>\n", "      <td>0.032300</td>\n", "      <td>0.107581</td>\n", "    </tr>\n", "    <tr>\n", "      <td>35</td>\n", "      <td>0.016400</td>\n", "      <td>0.106593</td>\n", "    </tr>\n", "    <tr>\n", "      <td>36</td>\n", "      <td>0.018300</td>\n", "      <td>0.141175</td>\n", "    </tr>\n", "    <tr>\n", "      <td>37</td>\n", "      <td>0.008600</td>\n", "      <td>0.215018</td>\n", "    </tr>\n", "    <tr>\n", "      <td>38</td>\n", "      <td>0.029900</td>\n", "      <td>0.237048</td>\n", "    </tr>\n", "    <tr>\n", "      <td>39</td>\n", "      <td>0.007100</td>\n", "      <td>0.124698</td>\n", "    </tr>\n", "    <tr>\n", "      <td>40</td>\n", "      <td>0.016400</td>\n", "      <td>0.084904</td>\n", "    </tr>\n", "    <tr>\n", "      <td>41</td>\n", "      <td>0.010600</td>\n", "      <td>0.082203</td>\n", "    </tr>\n", "    <tr>\n", "      <td>42</td>\n", "      <td>0.005700</td>\n", "      <td>0.066973</td>\n", "    </tr>\n", "    <tr>\n", "      <td>43</td>\n", "      <td>0.003900</td>\n", "      <td>0.060933</td>\n", "    </tr>\n", "    <tr>\n", "      <td>44</td>\n", "      <td>0.016800</td>\n", "      <td>0.053735</td>\n", "    </tr>\n", "    <tr>\n", "      <td>45</td>\n", "      <td>0.001800</td>\n", "      <td>0.081874</td>\n", "    </tr>\n", "    <tr>\n", "      <td>46</td>\n", "      <td>0.013500</td>\n", "      <td>0.026365</td>\n", "    </tr>\n", "    <tr>\n", "      <td>47</td>\n", "      <td>0.013000</td>\n", "      <td>0.017800</td>\n", "    </tr>\n", "    <tr>\n", "      <td>48</td>\n", "      <td>0.007000</td>\n", "      <td>0.043858</td>\n", "    </tr>\n", "    <tr>\n", "      <td>49</td>\n", "      <td>0.002100</td>\n", "      <td>0.077474</td>\n", "    </tr>\n", "    <tr>\n", "      <td>50</td>\n", "      <td>0.004200</td>\n", "      <td>0.067162</td>\n", "    </tr>\n", "    <tr>\n", "      <td>51</td>\n", "      <td>0.002600</td>\n", "      <td>0.051630</td>\n", "    </tr>\n", "    <tr>\n", "      <td>52</td>\n", "      <td>0.004300</td>\n", "      <td>0.028582</td>\n", "    </tr>\n", "    <tr>\n", "      <td>53</td>\n", "      <td>0.007900</td>\n", "      <td>0.030996</td>\n", "    </tr>\n", "    <tr>\n", "      <td>54</td>\n", "      <td>0.003900</td>\n", "      <td>0.050074</td>\n", "    </tr>\n", "    <tr>\n", "      <td>55</td>\n", "      <td>0.005400</td>\n", "      <td>0.044961</td>\n", "    </tr>\n", "    <tr>\n", "      <td>56</td>\n", "      <td>0.005600</td>\n", "      <td>0.017551</td>\n", "    </tr>\n", "    <tr>\n", "      <td>57</td>\n", "      <td>0.003300</td>\n", "      <td>0.010754</td>\n", "    </tr>\n", "    <tr>\n", "      <td>58</td>\n", "      <td>0.004500</td>\n", "      <td>0.008677</td>\n", "    </tr>\n", "    <tr>\n", "      <td>59</td>\n", "      <td>0.001500</td>\n", "      <td>0.016226</td>\n", "    </tr>\n", "    <tr>\n", "      <td>60</td>\n", "      <td>0.002100</td>\n", "      <td>0.031967</td>\n", "    </tr>\n", "    <tr>\n", "      <td>61</td>\n", "      <td>0.001800</td>\n", "      <td>0.045598</td>\n", "    </tr>\n", "    <tr>\n", "      <td>62</td>\n", "      <td>0.001100</td>\n", "      <td>0.047409</td>\n", "    </tr>\n", "    <tr>\n", "      <td>63</td>\n", "      <td>0.002500</td>\n", "      <td>0.031313</td>\n", "    </tr>\n", "    <tr>\n", "      <td>64</td>\n", "      <td>0.009600</td>\n", "      <td>0.028413</td>\n", "    </tr>\n", "    <tr>\n", "      <td>65</td>\n", "      <td>0.001600</td>\n", "      <td>0.041419</td>\n", "    </tr>\n", "    <tr>\n", "      <td>66</td>\n", "      <td>0.061500</td>\n", "      <td>0.022579</td>\n", "    </tr>\n", "    <tr>\n", "      <td>67</td>\n", "      <td>0.010800</td>\n", "      <td>0.028224</td>\n", "    </tr>\n", "    <tr>\n", "      <td>68</td>\n", "      <td>0.011100</td>\n", "      <td>0.029553</td>\n", "    </tr>\n", "    <tr>\n", "      <td>69</td>\n", "      <td>0.033200</td>\n", "      <td>0.003526</td>\n", "    </tr>\n", "    <tr>\n", "      <td>70</td>\n", "      <td>0.006600</td>\n", "      <td>0.002119</td>\n", "    </tr>\n", "    <tr>\n", "      <td>71</td>\n", "      <td>0.042600</td>\n", "      <td>0.003035</td>\n", "    </tr>\n", "    <tr>\n", "      <td>72</td>\n", "      <td>0.003100</td>\n", "      <td>0.020235</td>\n", "    </tr>\n", "    <tr>\n", "      <td>73</td>\n", "      <td>0.008300</td>\n", "      <td>0.025564</td>\n", "    </tr>\n", "    <tr>\n", "      <td>74</td>\n", "      <td>0.046900</td>\n", "      <td>0.014743</td>\n", "    </tr>\n", "    <tr>\n", "      <td>75</td>\n", "      <td>0.001600</td>\n", "      <td>0.014206</td>\n", "    </tr>\n", "    <tr>\n", "      <td>76</td>\n", "      <td>0.027800</td>\n", "      <td>0.013937</td>\n", "    </tr>\n", "    <tr>\n", "      <td>77</td>\n", "      <td>0.004000</td>\n", "      <td>0.028331</td>\n", "    </tr>\n", "    <tr>\n", "      <td>78</td>\n", "      <td>0.004000</td>\n", "      <td>0.108031</td>\n", "    </tr>\n", "    <tr>\n", "      <td>79</td>\n", "      <td>0.013300</td>\n", "      <td>0.046315</td>\n", "    </tr>\n", "    <tr>\n", "      <td>80</td>\n", "      <td>0.000900</td>\n", "      <td>0.036679</td>\n", "    </tr>\n", "    <tr>\n", "      <td>81</td>\n", "      <td>0.001100</td>\n", "      <td>0.036374</td>\n", "    </tr>\n", "    <tr>\n", "      <td>82</td>\n", "      <td>0.003100</td>\n", "      <td>0.033996</td>\n", "    </tr>\n", "    <tr>\n", "      <td>83</td>\n", "      <td>0.001500</td>\n", "      <td>0.031235</td>\n", "    </tr>\n", "    <tr>\n", "      <td>84</td>\n", "      <td>0.002500</td>\n", "      <td>0.028050</td>\n", "    </tr>\n", "    <tr>\n", "      <td>85</td>\n", "      <td>0.003000</td>\n", "      <td>0.027626</td>\n", "    </tr>\n", "    <tr>\n", "      <td>86</td>\n", "      <td>0.001200</td>\n", "      <td>0.031695</td>\n", "    </tr>\n", "    <tr>\n", "      <td>87</td>\n", "      <td>0.001200</td>\n", "      <td>0.038426</td>\n", "    </tr>\n", "    <tr>\n", "      <td>88</td>\n", "      <td>0.001700</td>\n", "      <td>0.045399</td>\n", "    </tr>\n", "    <tr>\n", "      <td>89</td>\n", "      <td>0.001200</td>\n", "      <td>0.052298</td>\n", "    </tr>\n", "    <tr>\n", "      <td>90</td>\n", "      <td>0.001500</td>\n", "      <td>0.060567</td>\n", "    </tr>\n", "    <tr>\n", "      <td>91</td>\n", "      <td>0.004100</td>\n", "      <td>0.045593</td>\n", "    </tr>\n", "    <tr>\n", "      <td>92</td>\n", "      <td>0.002700</td>\n", "      <td>0.035192</td>\n", "    </tr>\n", "    <tr>\n", "      <td>93</td>\n", "      <td>0.000700</td>\n", "      <td>0.032774</td>\n", "    </tr>\n", "    <tr>\n", "      <td>94</td>\n", "      <td>0.000200</td>\n", "      <td>0.032850</td>\n", "    </tr>\n", "    <tr>\n", "      <td>95</td>\n", "      <td>0.000400</td>\n", "      <td>0.034212</td>\n", "    </tr>\n", "    <tr>\n", "      <td>96</td>\n", "      <td>0.000600</td>\n", "      <td>0.037284</td>\n", "    </tr>\n", "    <tr>\n", "      <td>97</td>\n", "      <td>0.001000</td>\n", "      <td>0.040954</td>\n", "    </tr>\n", "    <tr>\n", "      <td>98</td>\n", "      <td>0.004200</td>\n", "      <td>0.041900</td>\n", "    </tr>\n", "    <tr>\n", "      <td>99</td>\n", "      <td>0.000900</td>\n", "      <td>0.042048</td>\n", "    </tr>\n", "    <tr>\n", "      <td>100</td>\n", "      <td>0.000200</td>\n", "      <td>0.038322</td>\n", "    </tr>\n", "    <tr>\n", "      <td>101</td>\n", "      <td>0.000300</td>\n", "      <td>0.035206</td>\n", "    </tr>\n", "    <tr>\n", "      <td>102</td>\n", "      <td>0.000200</td>\n", "      <td>0.033522</td>\n", "    </tr>\n", "    <tr>\n", "      <td>103</td>\n", "      <td>0.019600</td>\n", "      <td>0.025545</td>\n", "    </tr>\n", "    <tr>\n", "      <td>104</td>\n", "      <td>0.000200</td>\n", "      <td>0.025719</td>\n", "    </tr>\n", "    <tr>\n", "      <td>105</td>\n", "      <td>0.000800</td>\n", "      <td>0.043249</td>\n", "    </tr>\n", "    <tr>\n", "      <td>106</td>\n", "      <td>0.002000</td>\n", "      <td>0.058912</td>\n", "    </tr>\n", "    <tr>\n", "      <td>107</td>\n", "      <td>0.000900</td>\n", "      <td>0.049574</td>\n", "    </tr>\n", "    <tr>\n", "      <td>108</td>\n", "      <td>0.003900</td>\n", "      <td>0.051759</td>\n", "    </tr>\n", "    <tr>\n", "      <td>109</td>\n", "      <td>0.001300</td>\n", "      <td>0.036032</td>\n", "    </tr>\n", "    <tr>\n", "      <td>110</td>\n", "      <td>0.008100</td>\n", "      <td>0.005646</td>\n", "    </tr>\n", "    <tr>\n", "      <td>111</td>\n", "      <td>0.001900</td>\n", "      <td>0.001350</td>\n", "    </tr>\n", "    <tr>\n", "      <td>112</td>\n", "      <td>0.004000</td>\n", "      <td>0.000962</td>\n", "    </tr>\n", "    <tr>\n", "      <td>113</td>\n", "      <td>0.002200</td>\n", "      <td>0.001144</td>\n", "    </tr>\n", "    <tr>\n", "      <td>114</td>\n", "      <td>0.012100</td>\n", "      <td>0.008203</td>\n", "    </tr>\n", "    <tr>\n", "      <td>115</td>\n", "      <td>0.000300</td>\n", "      <td>0.035714</td>\n", "    </tr>\n", "    <tr>\n", "      <td>116</td>\n", "      <td>0.001400</td>\n", "      <td>0.089100</td>\n", "    </tr>\n", "    <tr>\n", "      <td>117</td>\n", "      <td>0.002800</td>\n", "      <td>0.111580</td>\n", "    </tr>\n", "    <tr>\n", "      <td>118</td>\n", "      <td>0.000600</td>\n", "      <td>0.126313</td>\n", "    </tr>\n", "    <tr>\n", "      <td>119</td>\n", "      <td>0.001400</td>\n", "      <td>0.113221</td>\n", "    </tr>\n", "    <tr>\n", "      <td>120</td>\n", "      <td>0.000500</td>\n", "      <td>0.101193</td>\n", "    </tr>\n", "    <tr>\n", "      <td>121</td>\n", "      <td>0.000600</td>\n", "      <td>0.090234</td>\n", "    </tr>\n", "    <tr>\n", "      <td>122</td>\n", "      <td>0.001300</td>\n", "      <td>0.065415</td>\n", "    </tr>\n", "    <tr>\n", "      <td>123</td>\n", "      <td>0.001900</td>\n", "      <td>0.042484</td>\n", "    </tr>\n", "    <tr>\n", "      <td>124</td>\n", "      <td>0.001400</td>\n", "      <td>0.027321</td>\n", "    </tr>\n", "    <tr>\n", "      <td>125</td>\n", "      <td>0.001000</td>\n", "      <td>0.017642</td>\n", "    </tr>\n", "    <tr>\n", "      <td>126</td>\n", "      <td>0.000100</td>\n", "      <td>0.012565</td>\n", "    </tr>\n", "    <tr>\n", "      <td>127</td>\n", "      <td>0.000500</td>\n", "      <td>0.009333</td>\n", "    </tr>\n", "    <tr>\n", "      <td>128</td>\n", "      <td>0.000200</td>\n", "      <td>0.007195</td>\n", "    </tr>\n", "    <tr>\n", "      <td>129</td>\n", "      <td>0.002800</td>\n", "      <td>0.007897</td>\n", "    </tr>\n", "    <tr>\n", "      <td>130</td>\n", "      <td>0.000100</td>\n", "      <td>0.008526</td>\n", "    </tr>\n", "    <tr>\n", "      <td>131</td>\n", "      <td>0.000100</td>\n", "      <td>0.009026</td>\n", "    </tr>\n", "    <tr>\n", "      <td>132</td>\n", "      <td>0.000900</td>\n", "      <td>0.009294</td>\n", "    </tr>\n", "    <tr>\n", "      <td>133</td>\n", "      <td>0.001600</td>\n", "      <td>0.009325</td>\n", "    </tr>\n", "    <tr>\n", "      <td>134</td>\n", "      <td>0.000300</td>\n", "      <td>0.009227</td>\n", "    </tr>\n", "    <tr>\n", "      <td>135</td>\n", "      <td>0.000800</td>\n", "      <td>0.008764</td>\n", "    </tr>\n", "    <tr>\n", "      <td>136</td>\n", "      <td>0.000200</td>\n", "      <td>0.008340</td>\n", "    </tr>\n", "    <tr>\n", "      <td>137</td>\n", "      <td>0.000400</td>\n", "      <td>0.008072</td>\n", "    </tr>\n", "    <tr>\n", "      <td>138</td>\n", "      <td>0.000100</td>\n", "      <td>0.007823</td>\n", "    </tr>\n", "    <tr>\n", "      <td>139</td>\n", "      <td>0.000600</td>\n", "      <td>0.007452</td>\n", "    </tr>\n", "    <tr>\n", "      <td>140</td>\n", "      <td>0.000500</td>\n", "      <td>0.006920</td>\n", "    </tr>\n", "    <tr>\n", "      <td>141</td>\n", "      <td>0.000300</td>\n", "      <td>0.006467</td>\n", "    </tr>\n", "    <tr>\n", "      <td>142</td>\n", "      <td>0.000100</td>\n", "      <td>0.006084</td>\n", "    </tr>\n", "    <tr>\n", "      <td>143</td>\n", "      <td>0.000100</td>\n", "      <td>0.005777</td>\n", "    </tr>\n", "    <tr>\n", "      <td>144</td>\n", "      <td>0.000200</td>\n", "      <td>0.005506</td>\n", "    </tr>\n", "    <tr>\n", "      <td>145</td>\n", "      <td>0.000100</td>\n", "      <td>0.005287</td>\n", "    </tr>\n", "    <tr>\n", "      <td>146</td>\n", "      <td>0.000100</td>\n", "      <td>0.005108</td>\n", "    </tr>\n", "    <tr>\n", "      <td>147</td>\n", "      <td>0.000100</td>\n", "      <td>0.004958</td>\n", "    </tr>\n", "    <tr>\n", "      <td>148</td>\n", "      <td>0.000100</td>\n", "      <td>0.004837</td>\n", "    </tr>\n", "    <tr>\n", "      <td>149</td>\n", "      <td>0.000400</td>\n", "      <td>0.004767</td>\n", "    </tr>\n", "    <tr>\n", "      <td>150</td>\n", "      <td>0.000100</td>\n", "      <td>0.004706</td>\n", "    </tr>\n", "    <tr>\n", "      <td>151</td>\n", "      <td>0.000200</td>\n", "      <td>0.004641</td>\n", "    </tr>\n", "    <tr>\n", "      <td>152</td>\n", "      <td>0.000200</td>\n", "      <td>0.004573</td>\n", "    </tr>\n", "    <tr>\n", "      <td>153</td>\n", "      <td>0.000400</td>\n", "      <td>0.004498</td>\n", "    </tr>\n", "    <tr>\n", "      <td>154</td>\n", "      <td>0.000400</td>\n", "      <td>0.004437</td>\n", "    </tr>\n", "    <tr>\n", "      <td>155</td>\n", "      <td>0.000100</td>\n", "      <td>0.004379</td>\n", "    </tr>\n", "    <tr>\n", "      <td>156</td>\n", "      <td>0.000200</td>\n", "      <td>0.004332</td>\n", "    </tr>\n", "    <tr>\n", "      <td>157</td>\n", "      <td>0.000100</td>\n", "      <td>0.004285</td>\n", "    </tr>\n", "    <tr>\n", "      <td>158</td>\n", "      <td>0.000900</td>\n", "      <td>0.004133</td>\n", "    </tr>\n", "    <tr>\n", "      <td>159</td>\n", "      <td>0.000100</td>\n", "      <td>0.003926</td>\n", "    </tr>\n", "    <tr>\n", "      <td>160</td>\n", "      <td>0.000100</td>\n", "      <td>0.003762</td>\n", "    </tr>\n", "    <tr>\n", "      <td>161</td>\n", "      <td>0.000100</td>\n", "      <td>0.003625</td>\n", "    </tr>\n", "    <tr>\n", "      <td>162</td>\n", "      <td>0.000100</td>\n", "      <td>0.003511</td>\n", "    </tr>\n", "    <tr>\n", "      <td>163</td>\n", "      <td>0.001200</td>\n", "      <td>0.003473</td>\n", "    </tr>\n", "    <tr>\n", "      <td>164</td>\n", "      <td>0.000600</td>\n", "      <td>0.003420</td>\n", "    </tr>\n", "    <tr>\n", "      <td>165</td>\n", "      <td>0.000200</td>\n", "      <td>0.003359</td>\n", "    </tr>\n", "    <tr>\n", "      <td>166</td>\n", "      <td>0.000700</td>\n", "      <td>0.003286</td>\n", "    </tr>\n", "    <tr>\n", "      <td>167</td>\n", "      <td>0.000300</td>\n", "      <td>0.003201</td>\n", "    </tr>\n", "    <tr>\n", "      <td>168</td>\n", "      <td>0.000200</td>\n", "      <td>0.003135</td>\n", "    </tr>\n", "    <tr>\n", "      <td>169</td>\n", "      <td>0.000200</td>\n", "      <td>0.003079</td>\n", "    </tr>\n", "    <tr>\n", "      <td>170</td>\n", "      <td>0.000600</td>\n", "      <td>0.003039</td>\n", "    </tr>\n", "    <tr>\n", "      <td>171</td>\n", "      <td>0.000200</td>\n", "      <td>0.003006</td>\n", "    </tr>\n", "    <tr>\n", "      <td>172</td>\n", "      <td>0.000200</td>\n", "      <td>0.002979</td>\n", "    </tr>\n", "    <tr>\n", "      <td>173</td>\n", "      <td>0.000200</td>\n", "      <td>0.002959</td>\n", "    </tr>\n", "    <tr>\n", "      <td>174</td>\n", "      <td>0.000100</td>\n", "      <td>0.002943</td>\n", "    </tr>\n", "    <tr>\n", "      <td>175</td>\n", "      <td>0.000100</td>\n", "      <td>0.002930</td>\n", "    </tr>\n", "    <tr>\n", "      <td>176</td>\n", "      <td>0.000100</td>\n", "      <td>0.002920</td>\n", "    </tr>\n", "    <tr>\n", "      <td>177</td>\n", "      <td>0.004500</td>\n", "      <td>0.002855</td>\n", "    </tr>\n", "    <tr>\n", "      <td>178</td>\n", "      <td>0.001100</td>\n", "      <td>0.002802</td>\n", "    </tr>\n", "    <tr>\n", "      <td>179</td>\n", "      <td>0.000500</td>\n", "      <td>0.002755</td>\n", "    </tr>\n", "    <tr>\n", "      <td>180</td>\n", "      <td>0.000100</td>\n", "      <td>0.002717</td>\n", "    </tr>\n", "    <tr>\n", "      <td>181</td>\n", "      <td>0.000200</td>\n", "      <td>0.002686</td>\n", "    </tr>\n", "    <tr>\n", "      <td>182</td>\n", "      <td>0.000100</td>\n", "      <td>0.002659</td>\n", "    </tr>\n", "    <tr>\n", "      <td>183</td>\n", "      <td>0.000200</td>\n", "      <td>0.002638</td>\n", "    </tr>\n", "    <tr>\n", "      <td>184</td>\n", "      <td>0.000100</td>\n", "      <td>0.002621</td>\n", "    </tr>\n", "    <tr>\n", "      <td>185</td>\n", "      <td>0.000100</td>\n", "      <td>0.002609</td>\n", "    </tr>\n", "    <tr>\n", "      <td>186</td>\n", "      <td>0.000200</td>\n", "      <td>0.002599</td>\n", "    </tr>\n", "    <tr>\n", "      <td>187</td>\n", "      <td>0.000100</td>\n", "      <td>0.002590</td>\n", "    </tr>\n", "    <tr>\n", "      <td>188</td>\n", "      <td>0.000000</td>\n", "      <td>0.002584</td>\n", "    </tr>\n", "    <tr>\n", "      <td>189</td>\n", "      <td>0.000200</td>\n", "      <td>0.002578</td>\n", "    </tr>\n", "    <tr>\n", "      <td>190</td>\n", "      <td>0.000100</td>\n", "      <td>0.002574</td>\n", "    </tr>\n", "    <tr>\n", "      <td>191</td>\n", "      <td>0.000100</td>\n", "      <td>0.002570</td>\n", "    </tr>\n", "    <tr>\n", "      <td>192</td>\n", "      <td>0.000500</td>\n", "      <td>0.002568</td>\n", "    </tr>\n", "    <tr>\n", "      <td>193</td>\n", "      <td>0.000100</td>\n", "      <td>0.002566</td>\n", "    </tr>\n", "    <tr>\n", "      <td>194</td>\n", "      <td>0.000400</td>\n", "      <td>0.002564</td>\n", "    </tr>\n", "    <tr>\n", "      <td>195</td>\n", "      <td>0.002200</td>\n", "      <td>0.002563</td>\n", "    </tr>\n", "    <tr>\n", "      <td>196</td>\n", "      <td>0.001300</td>\n", "      <td>0.002563</td>\n", "    </tr>\n", "    <tr>\n", "      <td>197</td>\n", "      <td>0.000100</td>\n", "      <td>0.002562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>198</td>\n", "      <td>0.000200</td>\n", "      <td>0.002562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>199</td>\n", "      <td>0.000200</td>\n", "      <td>0.002562</td>\n", "    </tr>\n", "    <tr>\n", "      <td>200</td>\n", "      <td>0.000100</td>\n", "      <td>0.002562</td>\n", "    </tr>\n", "  </tbody>\n", "</table><p>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["TrainOutput(global_step=400, training_loss=0.14310260522610407, metrics={'train_runtime': 64.902, 'train_samples_per_second': 110.937, 'train_steps_per_second': 6.163, 'total_flos': 49402375372800.0, 'train_loss': 0.14310260522610407, 'epoch': 200.0})"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["temp_dir = tempfile.mkdtemp()\n", "\n", "suggested_lr = None\n", "\n", "train_dict = {\"per_device_train_batch_size\": 32, \"num_train_epochs\": 200, \"eval_accumulation_steps\": None}\n", "\n", "EPOCHS = train_dict[\"num_train_epochs\"]\n", "BATCH_SIZE = train_dict[\"per_device_train_batch_size\"]\n", "eval_accumulation_steps = train_dict[\"eval_accumulation_steps\"]\n", "NUM_WORKERS = 1\n", "NUM_GPUS = 1\n", "\n", "set_seed(42)\n", "if suggested_lr is None:\n", "    lr, model = optimal_lr_finder(\n", "        model,\n", "        train_dataset,\n", "        batch_size=BATCH_SIZE,\n", "    )\n", "    suggested_lr = lr\n", "print(\"Suggested LR : \", suggested_lr)\n", "finetune_args = TrainingArguments(\n", "    output_dir=temp_dir,\n", "    overwrite_output_dir=True,\n", "    learning_rate=suggested_lr,\n", "    num_train_epochs=EPOCHS,\n", "    do_eval=True,\n", "    eval_strategy=\"epoch\",\n", "    per_device_train_batch_size=BATCH_SIZE,\n", "    per_device_eval_batch_size=BATCH_SIZE,\n", "    eval_accumulation_steps=eval_accumulation_steps,\n", "    dataloader_num_workers=NUM_WORKERS,\n", "    report_to=\"tensorboard\",\n", "    save_strategy=\"epoch\",\n", "    logging_strategy=\"epoch\",\n", "    save_total_limit=1,\n", "    logging_dir=os.path.join(OUT_DIR, \"output\"),  # Make sure to specify a logging directory\n", "    load_best_model_at_end=True,  # Load the best model when training ends\n", "    metric_for_best_model=\"eval_loss\",  # Metric to monitor for early stopping\n", "    greater_is_better=False,  # For loss\n", ")\n", "\n", "# Create the early stopping callback\n", "early_stopping_callback = EarlyStoppingCallback(\n", "    early_stopping_patience=100,  # Number of epochs with no improvement after which to stop\n", "    early_stopping_threshold=0.0001,  # Minimum improvement required to consider as improvement\n", ")\n", "\n", "# Optimizer and scheduler\n", "optimizer = AdamW(model.parameters(), lr=suggested_lr)\n", "scheduler = OneCycleLR(\n", "    optimizer,\n", "    suggested_lr,\n", "    epochs=EPOCHS,\n", "    steps_per_epoch=math.ceil(len(train_dataset) / (BATCH_SIZE * NUM_GPUS)),\n", ")\n", "\n", "finetune_trainer = Trainer(\n", "    model=model,\n", "    args=finetune_args,\n", "    train_dataset=train_dataset,\n", "    eval_dataset=valid_dataset,\n", "    callbacks=[early_stopping_callback],\n", "    optimizers=(optimizer, scheduler),\n", ")\n", "\n", "# Fine tune\n", "finetune_trainer.train()"]}, {"cell_type": "markdown", "id": "f91d63f9-1bcf-457b-a582-7453f9ab2716", "metadata": {}, "source": ["## Classification Scores"]}, {"cell_type": "code", "execution_count": 12, "id": "98dce5c3-33ad-4859-99d2-0ef7e54e76aa", "metadata": {}, "outputs": [{"data": {"text/html": [], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["test_accuracy :  1.0\n"]}], "source": ["predictions_dict = finetune_trainer.predict(test_dataset)\n", "preds_np = predictions_dict.predictions[0]\n", "\n", "remove_columns_collator = RemoveColumnsCollator(\n", "    data_collator=default_data_collator,\n", "    signature_columns=[\"target_values\"],\n", "    logger=None,\n", "    description=None,\n", "    model_name=\"temp\",\n", ")\n", "\n", "test_dataloader = DataLoader(test_dataset, batch_size=32, shuffle=False, collate_fn=remove_columns_collator)\n", "target_list = []\n", "for batch in test_dataloader:\n", "    batch_labels = batch[\"target_values\"].numpy()\n", "    target_list.append(batch_labels)\n", "targets_np = np.concatenate(target_list, axis=0)\n", "test_accuracy = np.mean(targets_np == np.argmax(preds_np, axis=1))\n", "print(\"test_accuracy : \", test_accuracy)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.12"}}, "nbformat": 4, "nbformat_minor": 5}