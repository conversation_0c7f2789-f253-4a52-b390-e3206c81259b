#!/usr/bin/env python3
"""
Markov-based odd/even periodicity analysis v2 with histogram preprocessing
Uses global histogram binning with fixed samples per bin across all series
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import chi2_contingency, ks_2samp
from joblib import Parallel, delayed
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

class TemporalBinner:
    """Divides time series into consecutive temporal bins and returns bin means"""
    
    def __init__(self, samples_per_bin=50):
        self.samples_per_bin = samples_per_bin
    
    def transform(self, ts):
        """Transform time series to consecutive bin means"""
        ts_clean = np.array(ts)[~np.isnan(np.array(ts))]
        if len(ts_clean) < 10:
            return None
        
        n_samples = len(ts_clean)
        n_bins = n_samples // self.samples_per_bin
        
        if n_bins < 2:
            return None
        
        # Truncate to fit exact bins (discard residual samples)
        truncated_length = n_bins * self.samples_per_bin
        ts_truncated = ts_clean[:truncated_length]
        
        # Reshape into bins and take means
        bin_means = ts_truncated.reshape(n_bins, self.samples_per_bin).mean(axis=1)
        
        return bin_means

def build_transition_matrix_v2(bin_means, period_length=2):
    """Build transition matrix using discretized bin means"""
    if len(bin_means) < period_length * 2:
        return None
    
    # Discretize continuous bin means into states (high/low)
    median_val = np.median(bin_means)
    discrete_states = (bin_means > median_val).astype(int)  # 0=low, 1=high
    
    n_states = 2  # Binary states: low/high
    transitions = np.zeros((n_states, n_states))
    
    # Count transitions between consecutive time points
    for i in range(len(discrete_states) - 1):
        current_pos = i % period_length
        next_pos = (i + 1) % period_length
        
        current_state = discrete_states[i]
        next_state = discrete_states[i + 1]
        
        # Weight transitions by position in period
        weight = 2.0 if current_pos != next_pos else 1.0
        transitions[current_state, next_state] += weight
    
    # Normalize to probabilities
    row_sums = transitions.sum(axis=1)
    transitions = np.divide(transitions, row_sums[:, np.newaxis], 
                          out=np.zeros_like(transitions), where=row_sums[:, np.newaxis]!=0)
    
    return transitions

def analyze_multi_period_patterns_v2(ts, binner, periods_to_test=[2, 3, 4, 5, 6, 7, 8, 10, 12]):
    """Analyze multiple periodic patterns using histogram binning"""
    # Transform to bin indices
    bin_indices = binner.transform(ts)
    if bin_indices is None:
        return None
    
    results = {}
    
    for period in periods_to_test:
        if len(bin_indices) < period * 4:
            continue
            
        try:
            # Split into period-based groups
            period_groups = []
            for phase in range(period):
                phase_values = bin_indices[phase::period]
                if len(phase_values) >= 3:
                    period_groups.append(phase_values)
            
            if len(period_groups) < 2:
                continue
            
            # Statistical tests between phases
            max_ks_stat = 0
            min_ks_p = 1.0
            
            for i in range(len(period_groups)):
                for j in range(i + 1, len(period_groups)):
                    ks_stat, ks_p = ks_2samp(period_groups[i], period_groups[j])
                    max_ks_stat = max(max_ks_stat, ks_stat)
                    min_ks_p = min(min_ks_p, ks_p)
            
            # Build transition matrix
            transition_matrix = build_transition_matrix_v2(bin_indices, period)
            chi2_stat = 0
            chi2_p = 1.0
            
            if transition_matrix is not None and transition_matrix.size > 1:
                try:
                    chi2_stat, chi2_p = chi2_contingency(transition_matrix + 1e-10)[:2]
                except:
                    pass
            
            # Autocorrelation at period lag
            autocorr_lag = 0
            if len(bin_indices) > period:
                try:
                    autocorr_lag = np.corrcoef(bin_indices[:-period], bin_indices[period:])[0, 1]
                    autocorr_lag = abs(autocorr_lag) if not np.isnan(autocorr_lag) else 0
                except:
                    autocorr_lag = 0
            
            # Phase distribution analysis
            phase_means = [np.mean(group) for group in period_groups]
            phase_vars = [np.var(group) for group in period_groups]
            mean_variance = np.var(phase_means)
            max_var_ratio = max(phase_vars) / (min(phase_vars) + 1e-10)
            
            results[f'period_{period}'] = {
                'period': period,
                'chi2_stat': chi2_stat,
                'chi2_p_value': chi2_p,
                'max_ks_stat': max_ks_stat,
                'min_ks_p_value': min_ks_p,
                'autocorr_lag': autocorr_lag,
                'phase_mean_variance': mean_variance,
                'max_variance_ratio': max_var_ratio,
                'n_phases': len(period_groups),
                'min_phase_size': min(len(group) for group in period_groups),
                'series_length': len(bin_indices),
                'n_bins_used': len(np.unique(bin_indices))
            }
            
        except Exception as e:
            continue
    
    return results

def analyze_single_series_v2(idx_ts_binner):
    """Analyze single time series with global binner"""
    idx, ts, binner = idx_ts_binner
    
    try:
        result = analyze_multi_period_patterns_v2(ts, binner)
        if result is None:
            return {'index': idx, 'valid': False}
        
        result['index'] = idx
        result['valid'] = True
        return result
        
    except Exception as e:
        return {'index': idx, 'valid': False, 'error': str(e)}

def analyze_dataset_periodicity_v2(series_list, samples_per_bin=50, n_jobs=-1):
    """Analyze dataset with per-series histogram binning"""
    print(f"🔍 Analyzing {len(series_list)} series with histogram binning (v2)...")
    print(f"📊 Target: {samples_per_bin} samples per bin per series")
    
    # Create temporal binner
    binner = TemporalBinner(samples_per_bin=samples_per_bin)
    
    # Process in parallel with binner
    indexed_series = [(i, ts, binner) for i, ts in enumerate(series_list)]
    results = Parallel(n_jobs=n_jobs)(
        delayed(analyze_single_series_v2)(idx_ts_binner) 
        for idx_ts_binner in tqdm(indexed_series, desc="Processing series")
    )
    
    # Filter valid results
    valid_results = [r for r in results if r.get('valid', False)]
    invalid_count = len(results) - len(valid_results)
    
    print(f"📊 Analysis completed: {len(valid_results)} valid, {invalid_count} invalid series")
    
    if len(valid_results) == 0:
        return None
    
    # Aggregate statistics (same as v1)
    periods_tested = [2, 3, 4, 5, 6, 7, 8, 10, 12]
    period_summaries = {}
    
    for period in periods_tested:
        period_key = f'period_{period}'
        
        chi2_p_values = []
        ks_p_values = []
        autocorrs = []
        
        for result in valid_results:
            if period_key in result:
                period_data = result[period_key]
                chi2_p_values.append(period_data['chi2_p_value'])
                ks_p_values.append(period_data['min_ks_p_value'])
                autocorrs.append(period_data['autocorr_lag'])
        
        if len(chi2_p_values) == 0:
            continue
            
        # Significance counts
        alpha = 0.05
        significant_chi2 = sum(1 for p in chi2_p_values if p < alpha)
        significant_ks = sum(1 for p in ks_p_values if p < alpha)
        strong_autocorr = sum(1 for a in autocorrs if a > 0.3)
        
        period_summaries[period] = {
            'period': period,
            'n_series': len(chi2_p_values),
            'significant_chi2': significant_chi2,
            'significant_ks': significant_ks,
            'strong_autocorr': strong_autocorr,
            'mean_autocorr': np.mean(autocorrs),
            'significance_rate': (significant_chi2 + significant_ks) / (2 * len(chi2_p_values)),
            'strong_evidence_rate': strong_autocorr / len(chi2_p_values)
        }
    
    summary = {
        'total_series': len(series_list),
        'valid_series': len(valid_results),
        'invalid_series': invalid_count,
        'periods_tested': periods_tested,
        'period_summaries': period_summaries,
        'samples_per_bin': samples_per_bin
    }
    
    return summary, valid_results

def extract_series_boundary_label(X_train: pd.DataFrame, y_train: pd.Series):
    """Extract series, boundaries, and labels from training data"""
    all_series = []
    all_tstars = []
    all_labels = []
    
    grouped = X_train.groupby(level='id')
    for id_, group in grouped:
        group = group.sort_index(level='time')
        values = group['value'].values
        periods = group['period'].values
        
        try:
            tstar = np.where(periods == 1)[0][0]
        except IndexError:
            tstar = len(values)
        
        label = int(y_train.loc[id_])
        
        all_series.append(values)
        all_tstars.append(tstar)
        all_labels.append(label)
    
    return all_series, all_tstars, all_labels

if __name__ == "__main__":
    # Load dataset
    X_train = pd.read_parquet('X_train.parquet')
    y_train = pd.read_parquet('y_train.parquet').squeeze()
    print(f"📁 Loaded dataset: X_train {X_train.shape}, y_train {len(y_train)}")
    
    series_list, tstars_list, labels_list = extract_series_boundary_label(X_train, y_train)
    print(f"📊 Extracted {len(series_list)} time series")
    
    # Test different bin sizes
    for samples_per_bin in [10, 20, 30]:
        print(f"\n{'='*60}")
        print(f"🧪 TESTING: {samples_per_bin} samples per bin")
        print(f"{'='*60}")
        
        # Analyze full series
        summary, results = analyze_dataset_periodicity_v2(
            series_list, samples_per_bin=samples_per_bin, n_jobs=-1
        )
        
        if summary:
            # Find top period
            if summary['period_summaries']:
                top_period = max(summary['period_summaries'].items(), 
                               key=lambda x: x[1]['significance_rate'])
                sig_rate = top_period[1]['significance_rate'] * 100
                print(f"🎯 Top period: {top_period[0]} ({sig_rate:.1f}% significance)")