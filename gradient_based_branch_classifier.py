"""
Gradient-Based Sequential Branch Classification
Adds neural networks and gradient flow to the branch classification problem
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from collections import deque, namedtuple
from typing import Dict, List, Tuple, Optional
import random

# Experience tuple for replay buffer
Experience = namedtuple('Experience', ['state', 'action', 'reward', 'next_state', 'done'])

class BranchStateEncoder(nn.Module):
    """Neural network to encode branch states and new data into feature vectors"""
    
    def __init__(self, input_dim: int = 100, hidden_dim: int = 64, output_dim: int = 32):
        super().__init__()
        self.encoder = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, output_dim),
            nn.Tanh()
        )
        
    def forward(self, x):
        return self.encoder(x)

class SimilarityNetwork(nn.Module):
    """Neural network to learn similarity between time series segments"""
    
    def __init__(self, feature_dim: int = 32):
        super().__init__()
        self.similarity_net = nn.Sequential(
            nn.Linear(feature_dim * 2, 64),  # Concatenated features
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()  # Output similarity score [0, 1]
        )
        
    def forward(self, branch_features, new_features):
        combined = torch.cat([branch_features, new_features], dim=-1)
        return self.similarity_net(combined)

class BreakDetectionNetwork(nn.Module):
    """Neural network to detect structural breaks"""
    
    def __init__(self, feature_dim: int = 32):
        super().__init__()
        self.break_net = nn.Sequential(
            nn.Linear(feature_dim * 2, 64),
            nn.ReLU(),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Linear(32, 1),
            nn.Sigmoid()  # Output break probability [0, 1]
        )
        
    def forward(self, branch_features, new_features):
        combined = torch.cat([branch_features, new_features], dim=-1)
        return self.break_net(combined)

class DecisionNetwork(nn.Module):
    """Neural network for final decision making (3-option classification)"""
    
    def __init__(self, input_dim: int = 47):  # Calculated: 32 + 5 + 5 + 5 = 47
        super().__init__()
        self.decision_net = nn.Sequential(
            nn.Linear(input_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, 3)  # 3 actions: NEW_BRANCH, CONTINUE_BRANCH, STRUCTURAL_BREAK
        )
        
    def forward(self, decision_features):
        return self.decision_net(decision_features)

class GradientBranchClassifier:
    """
    Gradient-based branch classifier with neural networks and reinforcement learning
    """
    
    def __init__(self, 
                 data_dim: int = 100,
                 feature_dim: int = 32,
                 learning_rate: float = 0.001,
                 buffer_size: int = 10000,
                 batch_size: int = 32,
                 gamma: float = 0.95):
        
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Neural networks
        self.state_encoder = BranchStateEncoder(data_dim, 64, feature_dim).to(self.device)
        self.similarity_net = SimilarityNetwork(feature_dim).to(self.device)
        self.break_net = BreakDetectionNetwork(feature_dim).to(self.device)
        self.decision_net = DecisionNetwork(feature_dim + 5 + 5 + 5).to(self.device)  # 32 + 5 + 5 + 5 = 47
        
        # Optimizers - GRADIENT FLOW HERE!
        self.encoder_optimizer = optim.Adam(self.state_encoder.parameters(), lr=learning_rate)
        self.similarity_optimizer = optim.Adam(self.similarity_net.parameters(), lr=learning_rate)
        self.break_optimizer = optim.Adam(self.break_net.parameters(), lr=learning_rate)
        self.decision_optimizer = optim.Adam(self.decision_net.parameters(), lr=learning_rate)
        
        # Loss functions
        self.similarity_criterion = nn.MSELoss()
        self.break_criterion = nn.BCELoss()
        self.decision_criterion = nn.CrossEntropyLoss()
        
        # RL components
        self.experience_buffer = deque(maxlen=buffer_size)
        self.batch_size = batch_size
        self.gamma = gamma
        
        # Branch management
        self.active_branches = {}
        self.branch_features = {}  # Learned feature representations
        
        # Training state
        self.training_mode = True
        self.epsilon = 0.1  # Exploration rate
        self.step_count = 0
        
    def process_new_part(self, new_data: np.ndarray, ground_truth: Optional[Dict] = None) -> Dict:
        """
        Process new part with gradient-based learning
        
        Args:
            new_data: New time series segment
            ground_truth: Optional ground truth for supervised learning
                         {'action': 0/1/2, 'similarity_scores': {...}, 'break_detected': bool}
        """
        
        # Convert to tensor
        new_tensor = torch.FloatTensor(new_data).unsqueeze(0).to(self.device)
        
        # Step 1: Encode new data into feature space
        if self.training_mode:
            new_features = self.state_encoder(new_tensor)
        else:
            with torch.no_grad():
                new_features = self.state_encoder(new_tensor)
        
        # Step 2: Compute similarities using neural network
        similarities = self._compute_neural_similarities(new_features)
        
        # Step 3: Detect breaks using neural network
        break_scores = self._compute_neural_breaks(new_features)
        
        # Step 4: Make decision using neural network
        decision_info = self._make_neural_decision(new_features, similarities, break_scores)
        
        # Step 5: Learn from experience if ground truth provided
        if ground_truth is not None and self.training_mode:
            self._learn_from_experience(new_features, similarities, break_scores, 
                                      decision_info, ground_truth)
        
        # Step 6: Update internal state
        self._update_branch_state(decision_info, new_features, new_data)
        
        self.step_count += 1
        
        return {
            'decision': decision_info['action_name'],
            'branch_id': decision_info['branch_id'],
            'confidence': decision_info['confidence'],
            'similarities': similarities,
            'break_scores': break_scores,
            'neural_features': new_features.detach().cpu().numpy(),
            'step': self.step_count
        }
    
    def _compute_neural_similarities(self, new_features: torch.Tensor) -> Dict[str, float]:
        """Compute similarities using neural network"""
        similarities = {}
        
        for branch_id, branch_feature in self.branch_features.items():
            with torch.no_grad() if not self.training_mode else torch.enable_grad():
                similarity = self.similarity_net(branch_feature, new_features)
                similarities[branch_id] = similarity.item()
        
        return similarities
    
    def _compute_neural_breaks(self, new_features: torch.Tensor) -> Dict[str, float]:
        """Compute break probabilities using neural network"""
        break_scores = {}
        
        for branch_id, branch_feature in self.branch_features.items():
            with torch.no_grad() if not self.training_mode else torch.enable_grad():
                break_prob = self.break_net(branch_feature, new_features)
                break_scores[branch_id] = break_prob.item()
        
        return break_scores
    
    def _make_neural_decision(self, new_features: torch.Tensor, 
                            similarities: Dict, break_scores: Dict) -> Dict:
        """Make decision using neural network"""
        
        # Prepare decision features
        decision_features = self._prepare_decision_features(new_features, similarities, break_scores)
        
        with torch.no_grad() if not self.training_mode else torch.enable_grad():
            decision_logits = self.decision_net(decision_features)
            decision_probs = torch.softmax(decision_logits, dim=-1)
        
        # Epsilon-greedy exploration during training
        if self.training_mode and random.random() < self.epsilon:
            action = random.randint(0, 2)
        else:
            action = torch.argmax(decision_probs).item()
        
        action_names = ['NEW_BRANCH', 'CONTINUE_BRANCH', 'STRUCTURAL_BREAK']
        
        # Determine branch ID based on action
        if action == 0:  # NEW_BRANCH
            branch_id = f'branch_{len(self.active_branches)}'
        elif action == 1 and similarities:  # CONTINUE_BRANCH
            branch_id = max(similarities.keys(), key=lambda k: similarities[k])
        else:  # STRUCTURAL_BREAK or fallback
            branch_id = f'branch_{len(self.active_branches)}'
        
        return {
            'action': action,
            'action_name': action_names[action],
            'branch_id': branch_id,
            'confidence': decision_probs[0, action].item(),
            'logits': decision_logits if self.training_mode else decision_logits.detach(),
            'decision_features': decision_features.detach()
        }
    
    def _prepare_decision_features(self, new_features: torch.Tensor, 
                                 similarities: Dict, break_scores: Dict) -> torch.Tensor:
        """Prepare features for decision network"""
        
        # Convert similarities and break scores to tensors
        max_branches = 5  # Pad/truncate to fixed size
        
        sim_tensor = torch.zeros(max_branches).to(self.device)
        break_tensor = torch.zeros(max_branches).to(self.device)
        
        for i, (branch_id, sim) in enumerate(list(similarities.items())[:max_branches]):
            sim_tensor[i] = sim
            break_tensor[i] = break_scores.get(branch_id, 0.5)
        
        # Additional features
        novelty_score = 1.0 - max(similarities.values()) if similarities else 1.0
        branch_count = len(self.active_branches)
        
        extra_features = torch.tensor([
            novelty_score,
            branch_count / 10.0,  # Normalized
            len(similarities),
            np.mean(list(similarities.values())) if similarities else 0.0,
            np.mean(list(break_scores.values())) if break_scores else 0.5
        ]).float().to(self.device)
        
        # Concatenate all features
        decision_features = torch.cat([
            new_features.squeeze(0),  # New data features
            sim_tensor,               # Similarity scores
            break_tensor,             # Break scores  
            extra_features            # Additional features
        ]).unsqueeze(0)
        
        return decision_features
    
    def _learn_from_experience(self, new_features: torch.Tensor, similarities: Dict, 
                             break_scores: Dict, decision_info: Dict, ground_truth: Dict):
        """
        Learn from experience using gradient descent - THIS IS WHERE GRADIENTS FLOW!
        """
        
        # 1. Learn similarity network
        if 'similarity_scores' in ground_truth:
            self._train_similarity_network(new_features, ground_truth['similarity_scores'])
        
        # 2. Learn break detection network  
        if 'break_detected' in ground_truth:
            self._train_break_network(new_features, ground_truth['break_detected'])
        
        # 3. Learn decision network
        if 'action' in ground_truth:
            self._train_decision_network(decision_info, ground_truth['action'])
        
        # 4. Store experience for replay learning
        self._store_experience(new_features, decision_info, ground_truth)
        
        # 5. Replay learning from buffer
        if len(self.experience_buffer) > self.batch_size:
            self._replay_learning()
    
    def _train_similarity_network(self, new_features: torch.Tensor, target_similarities: Dict):
        """Train similarity network with gradient descent"""
        
        if not target_similarities:
            return
            
        self.similarity_optimizer.zero_grad()
        total_loss = 0
        
        for branch_id, target_sim in target_similarities.items():
            if branch_id in self.branch_features:
                branch_feature = self.branch_features[branch_id].detach()  # Detach to avoid double backward
                predicted_sim = self.similarity_net(branch_feature, new_features.detach())
                target_tensor = torch.tensor([target_sim]).float().to(self.device)
                
                loss = self.similarity_criterion(predicted_sim, target_tensor)
                total_loss += loss
        
        if total_loss > 0:
            total_loss.backward(retain_graph=True)
            self.similarity_optimizer.step()
    
    def _train_break_network(self, new_features: torch.Tensor, break_info: Dict):
        """Train break detection network with gradient descent"""
        
        if not break_info:
            return
            
        self.break_optimizer.zero_grad()
        total_loss = 0
        
        for branch_id, break_detected in break_info.items():
            if branch_id in self.branch_features:
                branch_feature = self.branch_features[branch_id].detach()  # Detach to avoid double backward
                predicted_break = self.break_net(branch_feature, new_features.detach())
                target_tensor = torch.tensor([[float(break_detected)]]).to(self.device)
                
                loss = self.break_criterion(predicted_break, target_tensor)
                total_loss += loss
        
        if total_loss > 0:
            total_loss.backward(retain_graph=True)
            self.break_optimizer.step()
    
    def _train_decision_network(self, decision_info: Dict, target_action: int):
        """Train decision network with gradient descent"""
        
        self.decision_optimizer.zero_grad()
        
        logits = decision_info['logits']
        target_tensor = torch.tensor([target_action]).long().to(self.device)
        
        loss = self.decision_criterion(logits, target_tensor)
        loss.backward()
        self.decision_optimizer.step()
    
    def _store_experience(self, new_features: torch.Tensor, decision_info: Dict, ground_truth: Dict):
        """Store experience for replay learning"""
        
        # Create state representation
        state = decision_info['decision_features'].cpu().numpy()
        action = decision_info['action']
        
        # Compute reward based on ground truth
        reward = self._compute_reward(decision_info, ground_truth)
        
        # Store in buffer (simplified - in practice would include next_state)
        experience = Experience(
            state=state,
            action=action,
            reward=reward,
            next_state=state,  # Simplified
            done=False
        )
        
        self.experience_buffer.append(experience)
    
    def _compute_reward(self, decision_info: Dict, ground_truth: Dict) -> float:
        """Compute reward for reinforcement learning"""
        
        reward = 0.0
        
        # Reward for correct action
        if decision_info['action'] == ground_truth.get('action', -1):
            reward += 1.0
        else:
            reward -= 0.5
        
        # Reward for high confidence when correct
        if decision_info['action'] == ground_truth.get('action', -1):
            reward += decision_info['confidence'] * 0.5
        
        # Penalty for overconfident wrong decisions
        if decision_info['action'] != ground_truth.get('action', -1):
            reward -= decision_info['confidence'] * 0.3
        
        return reward
    
    def _replay_learning(self):
        """Experience replay learning - REINFORCEMENT LEARNING WITH GRADIENTS"""
        
        # Sample batch from experience buffer
        batch = random.sample(self.experience_buffer, self.batch_size)
        
        states = torch.FloatTensor(np.array([e.state.flatten() for e in batch])).to(self.device)
        actions = torch.LongTensor([e.action for e in batch]).to(self.device)
        rewards = torch.FloatTensor([e.reward for e in batch]).to(self.device)
        
        # Compute Q-values
        self.decision_optimizer.zero_grad()
        
        q_values = self.decision_net(states)
        q_values_for_actions = q_values.gather(1, actions.unsqueeze(1)).squeeze(1)
        
        # Simple Q-learning update (can be enhanced with target networks)
        target_q_values = rewards  # Simplified - no next state consideration
        
        loss = nn.MSELoss()(q_values_for_actions, target_q_values.detach())
        loss.backward()
        self.decision_optimizer.step()
    
    def _update_branch_state(self, decision_info: Dict, new_features: torch.Tensor, new_data: np.ndarray):
        """Update branch state with new features"""
        
        branch_id = decision_info['branch_id']
        
        if decision_info['action_name'] in ['NEW_BRANCH', 'STRUCTURAL_BREAK']:
            # Create new branch
            self.active_branches[branch_id] = {
                'created_step': self.step_count,
                'part_count': 1,
                'total_length': len(new_data)
            }
            self.branch_features[branch_id] = new_features.detach()
            
        elif decision_info['action_name'] == 'CONTINUE_BRANCH':
            # Update existing branch
            if branch_id in self.active_branches:
                self.active_branches[branch_id]['part_count'] += 1
                self.active_branches[branch_id]['total_length'] += len(new_data)
                
                # Update branch features (exponential moving average)
                alpha = 0.1
                old_features = self.branch_features[branch_id]
                self.branch_features[branch_id] = (1 - alpha) * old_features + alpha * new_features.detach()
    
    def set_training_mode(self, training: bool):
        """Set training mode"""
        self.training_mode = training
        self.state_encoder.train(training)
        self.similarity_net.train(training)
        self.break_net.train(training)
        self.decision_net.train(training)
    
    def save_model(self, path: str):
        """Save all neural networks"""
        torch.save({
            'state_encoder': self.state_encoder.state_dict(),
            'similarity_net': self.similarity_net.state_dict(),
            'break_net': self.break_net.state_dict(),
            'decision_net': self.decision_net.state_dict(),
            'step_count': self.step_count
        }, path)
    
    def load_model(self, path: str):
        """Load all neural networks"""
        checkpoint = torch.load(path, map_location=self.device)
        self.state_encoder.load_state_dict(checkpoint['state_encoder'])
        self.similarity_net.load_state_dict(checkpoint['similarity_net'])
        self.break_net.load_state_dict(checkpoint['break_net'])
        self.decision_net.load_state_dict(checkpoint['decision_net'])
        self.step_count = checkpoint['step_count']

# Example usage with gradient flow
if __name__ == "__main__":
    classifier = GradientBranchClassifier(data_dim=100, learning_rate=0.001)
    
    print("🧠 Testing Gradient-Based Branch Classifier")
    print("=" * 50)
    
    # Simulate training with ground truth
    for i in range(50):
        # Generate data
        data = np.random.randn(100) + i * 0.05
        
        # Simulate ground truth (in practice this would come from labels)
        if i < 10:
            ground_truth = {'action': 0}  # NEW_BRANCH
        elif i < 30:
            ground_truth = {'action': 1}  # CONTINUE_BRANCH
        else:
            ground_truth = {'action': 2}  # STRUCTURAL_BREAK
        
        # Process with learning
        result = classifier.process_new_part(data, ground_truth)
        
        if i % 10 == 9:
            print(f"Step {i+1:2d}: {result['decision']:15s} -> {result['branch_id']:10s} "
                  f"(conf: {result['confidence']:.3f})")
    
    print("\n🎯 Switching to inference mode...")
    classifier.set_training_mode(False)
    
    # Test inference
    for i in range(5):
        data = np.random.randn(100) + i * 0.1
        result = classifier.process_new_part(data)
        print(f"Inference {i}: {result['decision']:15s} -> {result['branch_id']:10s} "
              f"(conf: {result['confidence']:.3f})")