"""
Sequential Time Series Branch Classification Algorithm
Enhanced version with Normal/Candidate part separation and advanced distributional features
Designed as feature creation layer for RL architecture
"""

import numpy as np
from dataclasses import dataclass
from typing import List, Dict, Tuple, Optional
from enum import Enum
from scipy import stats
from scipy.stats import wasserstein_distance, energy_distance, ks_2samp, mannwhitneyu, ttest_ind
import warnings
warnings.filterwarnings('ignore')

class BranchDecision(Enum):
    NEW_BRANCH = "new_branch"
    CONTINUE_BRANCH = "continue_branch" 
    STRUCTURAL_BREAK = "structural_break"

@dataclass
class TimeSeriesSample:
    """Complete sample with normal and candidate parts"""
    normal_part: np.ndarray
    candidate_part: np.ndarray
    timestamp: int
    label: int = None  # For supervised learning
    metadata: Dict = None

@dataclass 
class Branch:
    """Branch containing either normal or candidate parts"""
    id: str
    part_type: str  # 'normal' or 'candidate'
    samples: List[np.ndarray]  # List of normal or candidate parts
    is_active: bool = True
    confidence: float = 1.0
    last_updated: int = 0
    baseline_metrics: Dict = None  # Baseline distributional metrics
    
    def get_tail(self, length: int = 100) -> np.ndarray:
        """Get the tail end of the branch for comparison"""
        if not self.samples:
            return np.array([])
        
        all_data = np.concatenate(self.samples)
        return all_data[-length:] if len(all_data) > length else all_data
    
    def get_recent_samples(self, n: int = 5) -> List[np.ndarray]:
        """Get n most recent samples"""
        return self.samples[-n:] if len(self.samples) >= n else self.samples
    
    def total_length(self) -> int:
        return sum(len(sample) for sample in self.samples)
    
    def update_baseline_metrics(self):
        """Update baseline distributional metrics after adding new samples"""
        if not self.samples:
            return
            
        # Concatenate recent samples for baseline
        recent_data = np.concatenate(self.get_recent_samples(10))
        
        self.baseline_metrics = {
            'mean': np.mean(recent_data),
            'std': np.std(recent_data),
            'skewness': stats.skew(recent_data),
            'kurtosis': stats.kurtosis(recent_data),
            'median': np.median(recent_data),
            'iqr': np.percentile(recent_data, 75) - np.percentile(recent_data, 25),
            'min': np.min(recent_data),
            'max': np.max(recent_data)
        }

class SequentialBranchClassifier:
    """
    Enhanced Sequential Branch Classifier for Normal/Candidate Parts
    Features advanced distributional and statistical test metrics
    """
    
    def __init__(self, 
                 similarity_threshold: float = 0.7,
                 break_threshold: float = 0.3,
                 max_active_branches: int = 10,
                 tail_comparison_length: int = 100):
        
        self.similarity_threshold = similarity_threshold
        self.break_threshold = break_threshold
        self.max_active_branches = max_active_branches
        self.tail_comparison_length = tail_comparison_length
        
        # Separate branches for normal and candidate parts
        self.normal_branches: List[Branch] = []
        self.candidate_branches: List[Branch] = []
        self.completed_normal_branches: List[Branch] = []
        self.completed_candidate_branches: List[Branch] = []
        
        self.decision_history: List[Dict] = []
        
    def process_new_sample(self, sample: TimeSeriesSample) -> Tuple[Dict, Dict]:
        """
        Process new sample with both normal and candidate parts
        
        Returns:
            - Normal part decision results
            - Candidate part decision results
        """
        
        # Process normal part
        normal_results = self._process_part(
            sample.normal_part, 
            'normal', 
            sample.timestamp,
            sample.label
        )
        
        # Process candidate part
        candidate_results = self._process_part(
            sample.candidate_part, 
            'candidate', 
            sample.timestamp,
            sample.label
        )
        
        # Maintain branch health
        self._maintain_branches()
        
        return normal_results, candidate_results
    
    def _process_part(self, part_data: np.ndarray, part_type: str, 
                     timestamp: int, label: int = None) -> Dict:
        """Process individual part (normal or candidate)"""
        
        # Get appropriate branches
        if part_type == 'normal':
            active_branches = self.normal_branches
            completed_branches = self.completed_normal_branches
        else:
            active_branches = self.candidate_branches
            completed_branches = self.completed_candidate_branches
        
        # Step 1: Compute advanced similarities
        similarities = self._compute_advanced_similarities(part_data, active_branches)
        
        # Step 2: Detect structural breaks
        break_scores = self._detect_structural_breaks(part_data, active_branches)
        
        # Step 3: Assess novelty with advanced metrics
        novelty_features = self._assess_advanced_novelty(
            part_data, active_branches, completed_branches
        )
        
        # Step 4: Make decision
        decision, branch_id, metadata = self._make_decision(
            part_data, similarities, break_scores, novelty_features, part_type
        )
        
        # Step 5: Execute decision
        self._execute_decision(
            decision, branch_id, part_data, part_type, timestamp, active_branches
        )
        
        return {
            'decision': decision,
            'branch_id': branch_id,
            'part_type': part_type,
            'similarities': similarities,
            'break_scores': break_scores,
            'novelty_features': novelty_features,
            'metadata': metadata,
            'timestamp': timestamp,
            'label': label
        }
    
    def _compute_advanced_similarities(self, new_part: np.ndarray, 
                                     branches: List[Branch]) -> Dict[str, Dict]:
        """Compute advanced similarity metrics with distributional features"""
        similarities = {}
        
        for branch in branches:
            if not branch.samples:
                similarities[branch.id] = self._empty_similarity_features()
                continue
            
            # Get recent branch data for comparison
            branch_tail = branch.get_tail(self.tail_comparison_length)
            
            # Compute comprehensive similarity features
            sim_features = self._compute_similarity_features(new_part, branch_tail)
            similarities[branch.id] = sim_features
            
        return similarities
    
    def _compute_similarity_features(self, x: np.ndarray, y: np.ndarray) -> Dict:
        """Compute comprehensive similarity features"""
        if len(x) == 0 or len(y) == 0:
            return self._empty_similarity_features()
        
        features = {}
        
        # 1. Basic correlation
        features['correlation'] = self._safe_correlation(x, y)
        
        # 2. Statistical similarity
        features['statistical_similarity'] = self._statistical_similarity(x, y)
        
        # 3. Distributional Features
        features.update(self._compute_distributional_features(x, y))
        
        # 4. Statistical Test Features
        features.update(self._compute_statistical_test_features(x, y))
        
        # 5. Composite similarity score
        features['composite_similarity'] = self._compute_composite_similarity(features)
        
        return features
    
    def _compute_distributional_features(self, x: np.ndarray, y: np.ndarray) -> Dict:
        """Compute distributional comparison features"""
        features = {}
        
        try:
            # Wasserstein Distance (Earth Mover's Distance)
            features['wasserstein_distance'] = wasserstein_distance(x, y)
            features['wasserstein_similarity'] = 1 / (1 + features['wasserstein_distance'])
        except:
            features['wasserstein_distance'] = np.inf
            features['wasserstein_similarity'] = 0.0
        
        try:
            # Energy Distance
            features['energy_distance'] = energy_distance(x, y)
            features['energy_similarity'] = 1 / (1 + features['energy_distance'])
        except:
            features['energy_distance'] = np.inf
            features['energy_similarity'] = 0.0
        
        try:
            # Jensen-Shannon Divergence
            features['js_divergence'] = self._jensen_shannon_divergence(x, y)
            features['js_similarity'] = 1 / (1 + features['js_divergence'])
        except:
            features['js_divergence'] = np.inf
            features['js_similarity'] = 0.0
        
        try:
            # Cramér-von Mises test
            features['cramer_von_mises'] = self._cramer_von_mises_test(x, y)
            features['cvm_similarity'] = 1 / (1 + features['cramer_von_mises'])
        except:
            features['cramer_von_mises'] = np.inf
            features['cvm_similarity'] = 0.0
        
        return features
    
    def _compute_statistical_test_features(self, x: np.ndarray, y: np.ndarray) -> Dict:
        """Compute statistical test features"""
        features = {}
        
        try:
            # T-test
            t_stat, t_pval = ttest_ind(x, y)
            features['t_statistic'] = abs(t_stat) if not np.isnan(t_stat) else 0.0
            features['t_pvalue'] = t_pval if not np.isnan(t_pval) else 1.0
            features['t_similarity'] = t_pval  # Higher p-value = more similar
        except:
            features['t_statistic'] = 0.0
            features['t_pvalue'] = 1.0
            features['t_similarity'] = 1.0
        
        try:
            # Mann-Whitney U test
            u_stat, u_pval = mannwhitneyu(x, y, alternative='two-sided')
            features['mannwhitney_statistic'] = u_stat
            features['mannwhitney_pvalue'] = u_pval
            features['mannwhitney_similarity'] = u_pval
        except:
            features['mannwhitney_statistic'] = 0.0
            features['mannwhitney_pvalue'] = 1.0
            features['mannwhitney_similarity'] = 1.0
        
        try:
            # Kolmogorov-Smirnov test
            ks_stat, ks_pval = ks_2samp(x, y)
            features['ks_statistic'] = ks_stat
            features['ks_pvalue'] = ks_pval
            features['ks_similarity'] = ks_pval
        except:
            features['ks_statistic'] = 0.0
            features['ks_pvalue'] = 1.0
            features['ks_similarity'] = 1.0
        
        try:
            # Cohen's D (effect size)
            features['cohens_d'] = self._cohens_d(x, y)
            features['cohens_d_similarity'] = 1 / (1 + abs(features['cohens_d']))
        except:
            features['cohens_d'] = 0.0
            features['cohens_d_similarity'] = 1.0
        
        try:
            # Cliff's Delta (non-parametric effect size)
            features['cliffs_delta'] = self._cliffs_delta(x, y)
            features['cliffs_delta_similarity'] = 1 - abs(features['cliffs_delta'])
        except:
            features['cliffs_delta'] = 0.0
            features['cliffs_delta_similarity'] = 1.0
        
        return features
    
    def _assess_advanced_novelty(self, new_part: np.ndarray, 
                               active_branches: List[Branch],
                               completed_branches: List[Branch]) -> Dict:
        """Assess novelty using advanced distributional metrics"""
        
        if not active_branches and not completed_branches:
            return self._maximum_novelty_features()
        
        all_similarities = []
        distributional_distances = []
        statistical_test_scores = []
        
        # Compare with active branches
        for branch in active_branches:
            for sample in branch.get_recent_samples(3):
                sim_features = self._compute_similarity_features(new_part, sample)
                all_similarities.append(sim_features['composite_similarity'])
                distributional_distances.append(sim_features['wasserstein_distance'])
                statistical_test_scores.append(sim_features['t_pvalue'])
        
        # Compare with completed branches (sample)
        for branch in completed_branches[-5:]:
            for sample in branch.get_recent_samples(2):
                sim_features = self._compute_similarity_features(new_part, sample)
                all_similarities.append(sim_features['composite_similarity'])
                distributional_distances.append(sim_features['wasserstein_distance'])
                statistical_test_scores.append(sim_features['t_pvalue'])
        
        if not all_similarities:
            return self._maximum_novelty_features()
        
        # Compute novelty features
        novelty_features = {
            'max_similarity': max(all_similarities),
            'mean_similarity': np.mean(all_similarities),
            'min_distributional_distance': min(distributional_distances),
            'mean_distributional_distance': np.mean(distributional_distances),
            'max_statistical_pvalue': max(statistical_test_scores),
            'mean_statistical_pvalue': np.mean(statistical_test_scores),
            'composite_novelty': 1.0 - max(all_similarities),
            'distributional_novelty': np.mean(distributional_distances),
            'statistical_novelty': 1.0 - max(statistical_test_scores)
        }
        
        return novelty_features    

    def _make_decision(self, part_data: np.ndarray, similarities: Dict, 
                      break_scores: Dict, novelty_features: Dict, 
                      part_type: str) -> Tuple[BranchDecision, str, Dict]:
        """Enhanced decision making with advanced features"""
        
        metadata = {
            'similarities': similarities,
            'break_scores': break_scores,
            'novelty_features': novelty_features,
            'part_type': part_type
        }
        
        # Check if we have any branches to compare with
        if not similarities:
            branch_id = f"{part_type}_branch_{self._get_next_branch_id(part_type)}"
            return BranchDecision.NEW_BRANCH, branch_id, metadata
        
        # Find best candidate based on composite similarity
        best_branch_id = max(similarities.keys(), 
                           key=lambda k: similarities[k]['composite_similarity'])
        best_similarity = similarities[best_branch_id]['composite_similarity']
        best_break_score = break_scores.get(best_branch_id, 1.0)
        
        # Decision logic using advanced features
        composite_novelty = novelty_features.get('composite_novelty', 1.0)
        
        # OPTION 1: NEW BRANCH (high novelty or no good matches)
        if (composite_novelty > 0.8 or 
            best_similarity < self.similarity_threshold):
            
            branch_id = f"{part_type}_branch_{self._get_next_branch_id(part_type)}"
            return BranchDecision.NEW_BRANCH, branch_id, metadata
        
        # OPTION 3: STRUCTURAL BREAK (good similarity but high break probability)
        if (best_similarity > self.similarity_threshold and 
            best_break_score > self.break_threshold):
            
            metadata['split_branch'] = best_branch_id
            metadata['break_score'] = best_break_score
            
            branch_id = f"{part_type}_branch_{self._get_next_branch_id(part_type)}"
            return BranchDecision.STRUCTURAL_BREAK, branch_id, metadata
        
        # OPTION 2: CONTINUE BRANCH (good similarity, low break probability)
        if (best_similarity > self.similarity_threshold and 
            best_break_score <= self.break_threshold):
            
            return BranchDecision.CONTINUE_BRANCH, best_branch_id, metadata
        
        # Default: NEW BRANCH
        branch_id = f"{part_type}_branch_{self._get_next_branch_id(part_type)}"
        return BranchDecision.NEW_BRANCH, branch_id, metadata
    
    def _execute_decision(self, decision: BranchDecision, branch_id: str, 
                         part_data: np.ndarray, part_type: str, timestamp: int,
                         active_branches: List[Branch]):
        """Execute the classification decision"""
        
        if decision == BranchDecision.NEW_BRANCH:
            # Create new branch
            new_branch = Branch(
                id=branch_id, 
                part_type=part_type,
                samples=[part_data], 
                last_updated=timestamp
            )
            new_branch.update_baseline_metrics()
            active_branches.append(new_branch)
            
        elif decision == BranchDecision.CONTINUE_BRANCH:
            # Add to existing branch
            branch = next(b for b in active_branches if b.id == branch_id)
            branch.samples.append(part_data)
            branch.last_updated = timestamp
            branch.update_baseline_metrics()
            
        elif decision == BranchDecision.STRUCTURAL_BREAK:
            # Create new branch (structural break case)
            new_branch = Branch(
                id=branch_id, 
                part_type=part_type,
                samples=[part_data], 
                last_updated=timestamp
            )
            new_branch.update_baseline_metrics()
            active_branches.append(new_branch)
    
    def _detect_structural_breaks(self, new_part: np.ndarray, 
                                branches: List[Branch]) -> Dict[str, float]:
        """Detect structural breaks using advanced metrics"""
        break_scores = {}
        
        for branch in branches:
            if not branch.samples:
                break_scores[branch.id] = 1.0
                continue
            
            branch_tail = branch.get_tail(self.tail_comparison_length)
            if len(branch_tail) == 0:
                break_scores[branch.id] = 1.0
                continue
            
            # Combine tail + new_part for break detection
            combined = np.concatenate([branch_tail, new_part])
            break_point = len(branch_tail)
            
            # Multiple break detection methods
            variance_change = self._variance_change_test(combined, break_point)
            mean_change = self._mean_change_test(combined, break_point)
            distribution_change = self._distribution_change_test(combined, break_point)
            
            # Advanced distributional break detection
            distributional_break = self._distributional_break_test(branch_tail, new_part)
            
            break_scores[branch.id] = max(
                variance_change, mean_change, distribution_change, distributional_break
            )
        
        return break_scores
    
    def _distributional_break_test(self, before: np.ndarray, after: np.ndarray) -> float:
        """Advanced distributional break test"""
        try:
            # Combine multiple distributional tests
            ks_stat, ks_pval = ks_2samp(before, after)
            energy_dist = energy_distance(before, after)
            wasserstein_dist = wasserstein_distance(before, after)
            
            # Normalize and combine
            ks_score = ks_stat  # Already 0-1
            energy_score = min(1.0, energy_dist / 10)  # Normalize
            wasserstein_score = min(1.0, wasserstein_dist / (np.std(before) + np.std(after) + 1e-8))
            
            return np.mean([ks_score, energy_score, wasserstein_score])
        except:
            return 0.0
    
    def _maintain_branches(self):
        """Maintain branch health for both normal and candidate branches"""
        self._maintain_branch_set(self.normal_branches, self.completed_normal_branches)
        self._maintain_branch_set(self.candidate_branches, self.completed_candidate_branches)
    
    def _maintain_branch_set(self, active_branches: List[Branch], 
                           completed_branches: List[Branch]):
        """Maintain a specific set of branches"""
        if not active_branches:
            return
        
        current_time = max(b.last_updated for b in active_branches)
        
        # Remove stale branches
        for branch in active_branches[:]:
            if current_time - branch.last_updated > 1000:
                branch.is_active = False
                completed_branches.append(branch)
                active_branches.remove(branch)
        
        # Limit active branches
        if len(active_branches) > self.max_active_branches:
            oldest = min(active_branches, key=lambda b: b.last_updated)
            oldest.is_active = False
            completed_branches.append(oldest)
            active_branches.remove(oldest)
    
    # Helper methods for advanced metrics
    def _jensen_shannon_divergence(self, x: np.ndarray, y: np.ndarray) -> float:
        """Compute Jensen-Shannon divergence"""
        try:
            # Create histograms
            bins = np.linspace(min(np.min(x), np.min(y)), 
                             max(np.max(x), np.max(y)), 50)
            p, _ = np.histogram(x, bins=bins, density=True)
            q, _ = np.histogram(y, bins=bins, density=True)
            
            # Normalize
            p = p / np.sum(p) + 1e-10
            q = q / np.sum(q) + 1e-10
            
            # Compute JS divergence
            m = 0.5 * (p + q)
            js_div = 0.5 * stats.entropy(p, m) + 0.5 * stats.entropy(q, m)
            return js_div
        except:
            return 0.0
    
    def _cramer_von_mises_test(self, x: np.ndarray, y: np.ndarray) -> float:
        """Simplified Cramér-von Mises test statistic"""
        try:
            # Combine and sort
            combined = np.concatenate([x, y])
            combined_sorted = np.sort(combined)
            
            # Compute empirical CDFs
            cdf_x = np.searchsorted(np.sort(x), combined_sorted, side='right') / len(x)
            cdf_y = np.searchsorted(np.sort(y), combined_sorted, side='right') / len(y)
            
            # Cramér-von Mises statistic
            cvm_stat = np.sum((cdf_x - cdf_y) ** 2)
            return cvm_stat / len(combined_sorted)
        except:
            return 0.0
    
    def _cohens_d(self, x: np.ndarray, y: np.ndarray) -> float:
        """Compute Cohen's D effect size"""
        try:
            pooled_std = np.sqrt(((len(x) - 1) * np.var(x) + (len(y) - 1) * np.var(y)) / 
                               (len(x) + len(y) - 2))
            if pooled_std == 0:
                return 0.0
            return (np.mean(x) - np.mean(y)) / pooled_std
        except:
            return 0.0
    
    def _cliffs_delta(self, x: np.ndarray, y: np.ndarray) -> float:
        """Compute Cliff's Delta effect size"""
        try:
            n1, n2 = len(x), len(y)
            if n1 == 0 or n2 == 0:
                return 0.0
            
            # Count pairs where x > y, x < y
            greater = np.sum(x[:, np.newaxis] > y[np.newaxis, :])
            lesser = np.sum(x[:, np.newaxis] < y[np.newaxis, :])
            
            return (greater - lesser) / (n1 * n2)
        except:
            return 0.0
    
    def _safe_correlation(self, x: np.ndarray, y: np.ndarray) -> float:
        """Safe correlation computation"""
        try:
            min_len = min(len(x), len(y))
            if min_len < 2:
                return 0.0
            
            x_aligned = x[-min_len:] if len(x) > min_len else x
            y_aligned = y[-min_len:] if len(y) > min_len else y
            
            correlation = np.corrcoef(x_aligned, y_aligned)[0, 1]
            return max(0, correlation) if not np.isnan(correlation) else 0.0
        except:
            return 0.0
    
    def _statistical_similarity(self, x: np.ndarray, y: np.ndarray) -> float:
        """Statistical properties similarity"""
        try:
            if len(x) == 0 or len(y) == 0:
                return 0.0
            
            mean_diff = abs(np.mean(x) - np.mean(y))
            std_diff = abs(np.std(x) - np.std(y))
            
            mean_sim = 1 / (1 + mean_diff)
            std_sim = 1 / (1 + std_diff)
            
            return (mean_sim + std_sim) / 2
        except:
            return 0.0
    
    def _compute_composite_similarity(self, features: Dict) -> float:
        """Compute composite similarity from all features"""
        try:
            # Weight different similarity components
            weights = {
                'correlation': 0.15,
                'statistical_similarity': 0.10,
                'wasserstein_similarity': 0.20,
                'energy_similarity': 0.15,
                'js_similarity': 0.15,
                'cvm_similarity': 0.10,
                't_similarity': 0.05,
                'mannwhitney_similarity': 0.05,
                'ks_similarity': 0.05
            }
            
            composite = 0.0
            total_weight = 0.0
            
            for feature, weight in weights.items():
                if feature in features and not np.isnan(features[feature]):
                    composite += weight * features[feature]
                    total_weight += weight
            
            return composite / total_weight if total_weight > 0 else 0.0
        except:
            return 0.0
    
    def _empty_similarity_features(self) -> Dict:
        """Return empty similarity features"""
        return {
            'correlation': 0.0,
            'statistical_similarity': 0.0,
            'wasserstein_distance': np.inf,
            'wasserstein_similarity': 0.0,
            'energy_distance': np.inf,
            'energy_similarity': 0.0,
            'js_divergence': np.inf,
            'js_similarity': 0.0,
            'cramer_von_mises': np.inf,
            'cvm_similarity': 0.0,
            't_statistic': 0.0,
            't_pvalue': 1.0,
            't_similarity': 1.0,
            'mannwhitney_statistic': 0.0,
            'mannwhitney_pvalue': 1.0,
            'mannwhitney_similarity': 1.0,
            'ks_statistic': 0.0,
            'ks_pvalue': 1.0,
            'ks_similarity': 1.0,
            'cohens_d': 0.0,
            'cohens_d_similarity': 1.0,
            'cliffs_delta': 0.0,
            'cliffs_delta_similarity': 1.0,
            'composite_similarity': 0.0
        }
    
    def _maximum_novelty_features(self) -> Dict:
        """Return maximum novelty features"""
        return {
            'max_similarity': 0.0,
            'mean_similarity': 0.0,
            'min_distributional_distance': np.inf,
            'mean_distributional_distance': np.inf,
            'max_statistical_pvalue': 1.0,
            'mean_statistical_pvalue': 1.0,
            'composite_novelty': 1.0,
            'distributional_novelty': 1.0,
            'statistical_novelty': 0.0
        }
    
    def _get_next_branch_id(self, part_type: str) -> int:
        """Get next branch ID for given part type"""
        if part_type == 'normal':
            return len(self.normal_branches) + len(self.completed_normal_branches)
        else:
            return len(self.candidate_branches) + len(self.completed_candidate_branches)
    
    # Break detection methods (simplified versions)
    def _variance_change_test(self, data: np.ndarray, break_point: int) -> float:
        """Test for variance change at break point"""
        if break_point <= 10 or break_point >= len(data) - 10:
            return 0.0
        
        before = data[:break_point]
        after = data[break_point:]
        
        var_before = np.var(before)
        var_after = np.var(after)
        
        if var_before == 0 and var_after == 0:
            return 0.0
        
        ratio = max(var_before, var_after) / (min(var_before, var_after) + 1e-8)
        return min(1.0, (ratio - 1) / 10)
    
    def _mean_change_test(self, data: np.ndarray, break_point: int) -> float:
        """Test for mean change at break point"""
        if break_point <= 10 or break_point >= len(data) - 10:
            return 0.0
        
        before = data[:break_point]
        after = data[break_point:]
        
        mean_diff = abs(np.mean(before) - np.mean(after))
        pooled_std = np.sqrt((np.var(before) + np.var(after)) / 2)
        
        if pooled_std == 0:
            return 1.0 if mean_diff > 0 else 0.0
        
        t_stat = mean_diff / pooled_std
        return min(1.0, t_stat / 5)
    
    def _distribution_change_test(self, data: np.ndarray, break_point: int) -> float:
        """Test for distribution change"""
        if break_point <= 10 or break_point >= len(data) - 10:
            return 0.0
        
        before = data[:break_point]
        after = data[break_point:]
        
        try:
            ks_stat, _ = ks_2samp(before, after)
            return min(1.0, ks_stat)
        except:
            return 0.0
    
    def get_feature_summary(self) -> Dict:
        """Get comprehensive feature summary"""
        return {
            'normal_branches': {
                'active': len(self.normal_branches),
                'completed': len(self.completed_normal_branches),
                'total_samples': sum(len(b.samples) for b in self.normal_branches)
            },
            'candidate_branches': {
                'active': len(self.candidate_branches),
                'completed': len(self.completed_candidate_branches),
                'total_samples': sum(len(b.samples) for b in self.candidate_branches)
            },
            'decision_history_length': len(self.decision_history)
        }
    
    def extract_features_for_ml(self, sample: TimeSeriesSample) -> Dict:
        """Extract comprehensive features for ML pipeline"""
        normal_results, candidate_results = self.process_new_sample(sample)
        
        # Combine features from both parts
        features = {
            'normal_features': self._extract_part_features(normal_results),
            'candidate_features': self._extract_part_features(candidate_results),
            'cross_part_features': self._compute_cross_part_features(
                sample.normal_part, sample.candidate_part
            )
        }
        
        return features
    
    def _extract_part_features(self, results: Dict) -> Dict:
        """Extract features from part processing results"""
        features = {}
        
        # Decision features
        features['decision'] = results['decision'].value
        features['branch_id'] = results['branch_id']
        
        # Similarity features
        if results['similarities']:
            best_sim = max(results['similarities'].values(), 
                          key=lambda x: x['composite_similarity'])
            features.update({f'best_{k}': v for k, v in best_sim.items()})
        
        # Novelty features
        features.update({f'novelty_{k}': v for k, v in results['novelty_features'].items()})
        
        # Break scores
        if results['break_scores']:
            features['max_break_score'] = max(results['break_scores'].values())
            features['mean_break_score'] = np.mean(list(results['break_scores'].values()))
        
        return features
    
    def _compute_cross_part_features(self, normal_part: np.ndarray, 
                                   candidate_part: np.ndarray) -> Dict:
        """Compute features comparing normal and candidate parts"""
        return self._compute_similarity_features(normal_part, candidate_part)


# Pipeline for real dataset
def create_pipeline_on_real_data():
    """Create and test pipeline on real dataset"""
    import pandas as pd
    
    print("🚀 Creating Sequential Branch Classifier Pipeline on Real Data")
    print("=" * 70)
    
    # Load real data
    try:
        X_train = pd.read_parquet('X_train.parquet')
        y_train = pd.read_parquet('y_train.parquet')
        
        print(f"📊 Loaded data: X={X_train.shape}, y={len(y_train)}")
        
        # Extract labels
        if 'structural_breakpoint' in y_train.columns:
            labels = y_train['structural_breakpoint'].astype(int).values
        else:
            labels = y_train.iloc[:, 0].astype(int).values
        
        print(f"📊 Labels distribution: {dict(zip(*np.unique(labels, return_counts=True)))}")
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None
    
    # Initialize classifier
    classifier = SequentialBranchClassifier(
        similarity_threshold=0.6,
        break_threshold=0.4,
        max_active_branches=15,
        tail_comparison_length=200
    )
    
    print("\n🔬 Processing samples with advanced distributional features...")
    
    # Process samples (use subset for demonstration)
    n_samples = min(100, len(X_train) - 1)
    all_features = []
    
    for i in range(n_samples):
        # Create normal/candidate parts (split each sample)
        sample_data = X_train.iloc[i].values
        mid_point = len(sample_data) // 2
        
        normal_part = sample_data[:mid_point]
        candidate_part = sample_data[mid_point:]
        
        # Create sample
        sample = TimeSeriesSample(
            normal_part=normal_part,
            candidate_part=candidate_part,
            timestamp=i,
            label=labels[i]
        )
        
        # Extract comprehensive features
        features = classifier.extract_features_for_ml(sample)
        features['sample_id'] = i
        features['label'] = labels[i]
        all_features.append(features)
        
        # Progress reporting
        if (i + 1) % 20 == 0:
            print(f"   Processed {i+1}/{n_samples} samples...")
            summary = classifier.get_feature_summary()
            print(f"   Normal branches: {summary['normal_branches']['active']} active")
            print(f"   Candidate branches: {summary['candidate_branches']['active']} active")
    
    print(f"\n✅ Pipeline complete! Processed {len(all_features)} samples")
    
    # Feature summary
    summary = classifier.get_feature_summary()
    print(f"\n📊 Final Summary:")
    print(f"   Normal branches: {summary['normal_branches']}")
    print(f"   Candidate branches: {summary['candidate_branches']}")
    
    return classifier, all_features


if __name__ == "__main__":
    # Run pipeline on real data
    classifier, features = create_pipeline_on_real_data()
    
    if features:
        print(f"\n🎯 Feature extraction complete!")
        print(f"   Total feature sets: {len(features)}")
        print(f"   Ready for RL architecture integration!")