{"feature_count": 47, "sample_count": 10001, "insights": {"decimal_precision_features": {"description": "Detect artificial precision limitations in data", "key_patterns": ["Values with 1-3 decimal places only", "Excessive trailing zeros", "Different precision between normal/candidate parts"], "interpretation": "High scores suggest data preprocessing or constraints"}, "round_number_features": {"description": "Identify suspicious round number patterns", "key_patterns": ["Extremes at exact values (0.0, 0.1, -0.1)", "Specific suspicious values (0.07, -0.07, 0.05)", "High percentage of round numbers"], "interpretation": "Indicates potential data clipping or boundaries"}, "clustering_features": {"description": "Detect value clustering near data extremes", "key_patterns": ["Multiple values clustered at min/max", "Asymmetric clustering patterns", "Different clustering between parts"], "interpretation": "Strong clustering suggests truncation effects"}, "boundary_features": {"description": "Identify artificial boundaries and constraints", "key_patterns": ["Symmetric bounds (±same value)", "Suspicious round ranges", "Constrained value ranges"], "interpretation": "Reveals preprocessing with artificial limits"}, "tail_flatness_features": {"description": "Detect flat tails from truncation", "key_patterns": ["Consecutive equal values at extremes", "Flat-line patterns in data", "Truncated distribution tails"], "interpretation": "Flat tails indicate systematic truncation"}, "cross_part_features": {"description": "Compare truncation between normal/candidate", "key_patterns": ["Consistent truncation across parts", "Differential truncation effects", "Aligned extreme values"], "interpretation": "Reveals systematic vs. localized effects"}}}