"""
Test Single Branch Learner on Real Dataset
"""

import numpy as np
import pandas as pd
from single_branch_learner import SingleBranchLearner
from sklearn.metrics import roc_auc_score, accuracy_score
import matplotlib.pyplot as plt
from collections import Counter
import json

def load_and_test_real_data():
    """Load real data and test single branch learner"""
    
    print("📊 Testing Single Branch Learner on Real Dataset")
    print("=" * 60)
    
    # Load data
    try:
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        if 'structural_breakpoint' in y_data.columns:
            labels = y_data['structural_breakpoint'].astype(int).values
        else:
            labels = y_data.iloc[:, 0].astype(int).values
        
        print(f"   Loaded: X={X_data.shape}, y={len(labels)}")
        print(f"   Label distribution: {dict(Counter(labels))}")
        
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        return None
    
    # Create normal/candidate pairs from real data
    normal_parts, candidate_parts, segment_labels = create_real_data_pairs(X_data, labels, max_samples=300)
    
    if len(normal_parts) == 0:
        print("   ❌ No valid pairs created")
        return None
    
    print(f"   Created {len(normal_parts)} normal/candidate pairs")
    print(f"   Pair label distribution: {dict(Counter(segment_labels))}")
    
    # Initialize learner
    learner = SingleBranchLearner(
        segment_length=normal_parts.shape[1],
        max_history=500,
        learning_rate=0.001
    )
    
    # Split data for training/testing
    split_idx = int(len(normal_parts) * 0.7)
    
    train_normal = normal_parts[:split_idx]
    train_candidate = candidate_parts[:split_idx]
    train_labels = segment_labels[:split_idx]
    
    test_normal = normal_parts[split_idx:]
    test_candidate = candidate_parts[split_idx:]
    test_labels = segment_labels[split_idx:]
    
    print(f"\n🔧 Training on {len(train_normal)} samples...")
    
    # Training phase
    train_results = []
    for i, (normal, candidate, label) in enumerate(zip(train_normal, train_candidate, train_labels)):
        result = learner.add_sample(normal, candidate, label)
        train_results.append(result)
        
        if (i + 1) % 50 == 0:
            print(f"   Trained {i+1}/{len(train_normal)} samples")
    
    print(f"\n🎯 Testing on {len(test_normal)} samples...")
    
    # Testing phase
    test_predictions = []
    test_probabilities = []
    
    for normal, candidate in zip(test_normal, test_candidate):
        prob = learner.predict_label_probability(normal, candidate)
        pred = 1 if prob > 0.5 else 0
        
        test_predictions.append(pred)
        test_probabilities.append(prob)
    
    # Calculate metrics
    test_accuracy = accuracy_score(test_labels, test_predictions)
    
    if len(np.unique(test_labels)) > 1:
        test_auc = roc_auc_score(test_labels, test_probabilities)
    else:
        test_auc = 0.5
    
    # Get final statistics
    stats = learner.get_statistics()
    
    results = {
        'test_accuracy': test_accuracy,
        'test_auc': test_auc,
        'train_samples': len(train_normal),
        'test_samples': len(test_normal),
        'learner_stats': stats,
        'test_predictions': test_predictions,
        'test_probabilities': test_probabilities,
        'test_labels': test_labels.tolist()
    }
    
    print(f"\n🏆 Results:")
    print(f"   Test Accuracy: {test_accuracy:.4f}")
    print(f"   Test AUC: {test_auc:.4f}")
    print(f"   Avg Processing Time: {stats['avg_processing_time']*1000:.1f}ms")
    print(f"   Memory Usage: {stats['memory_usage_mb']:.2f}MB")
    
    return results, learner

def create_real_data_pairs(X_data, labels, max_samples=300, segment_length=25):
    """Create normal/candidate pairs from real data"""
    
    normal_parts = []
    candidate_parts = []
    segment_labels = []
    
    # Get numeric data
    numeric_cols = X_data.select_dtypes(include=[np.number]).columns
    numeric_data = X_data[numeric_cols].values
    
    print(f"   Numeric data shape: {numeric_data.shape}")
    
    if numeric_data.shape[1] >= segment_length * 2:
        # Use feature vectors split into normal/candidate
        n_samples = min(max_samples, numeric_data.shape[0], len(labels))
        
        for i in range(n_samples):
            full_features = numeric_data[i, :segment_length * 2]
            
            if np.isfinite(full_features).all() and np.std(full_features) > 1e-6:
                normal_part = full_features[:segment_length].astype(np.float32)
                candidate_part = full_features[segment_length:segment_length*2].astype(np.float32)
                
                normal_parts.append(normal_part)
                candidate_parts.append(candidate_part)
                segment_labels.append(labels[i])
    
    else:
        # Use time series approach
        if numeric_data.shape[1] > 0:
            time_series = numeric_data[:, 0]
            step_size = segment_length
            
            for i in range(0, len(time_series) - segment_length * 2, step_size):
                if len(normal_parts) >= max_samples:
                    break
                
                normal_part = time_series[i:i + segment_length]
                candidate_part = time_series[i + segment_length:i + segment_length * 2]
                
                if (np.isfinite(normal_part).all() and np.isfinite(candidate_part).all() and
                    np.std(normal_part) > 1e-6 and np.std(candidate_part) > 1e-6):
                    
                    normal_parts.append(normal_part.astype(np.float32))
                    candidate_parts.append(candidate_part.astype(np.float32))
                    
                    mid_idx = min(i + segment_length, len(labels) - 1)
                    segment_labels.append(labels[mid_idx])
    
    return np.array(normal_parts), np.array(candidate_parts), np.array(segment_labels)

def visualize_results(results):
    """Visualize the results"""
    
    print(f"\n📊 Creating Visualizations...")
    
    fig, axes = plt.subplots(2, 2, figsize=(12, 10))
    fig.suptitle('Single Branch Learner Results on Real Dataset', fontsize=14)
    
    test_probs = results['test_probabilities']
    test_labels = results['test_labels']
    test_preds = results['test_predictions']
    
    # 1. Probability distribution by true label
    label_0_probs = [p for p, l in zip(test_probs, test_labels) if l == 0]
    label_1_probs = [p for p, l in zip(test_probs, test_labels) if l == 1]
    
    if label_0_probs:
        axes[0, 0].hist(label_0_probs, bins=15, alpha=0.5, label='Label 0', color='blue')
    if label_1_probs:
        axes[0, 0].hist(label_1_probs, bins=15, alpha=0.5, label='Label 1', color='red')
    
    axes[0, 0].set_xlabel('Predicted Probability')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Probability Distribution by True Label')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # 2. Confusion matrix
    from sklearn.metrics import confusion_matrix
    cm = confusion_matrix(test_labels, test_preds)
    
    im = axes[0, 1].imshow(cm, interpolation='nearest', cmap='Blues')
    axes[0, 1].set_title('Confusion Matrix')
    
    # Add text annotations
    for i in range(cm.shape[0]):
        for j in range(cm.shape[1]):
            axes[0, 1].text(j, i, str(cm[i, j]), ha='center', va='center')
    
    axes[0, 1].set_xlabel('Predicted')
    axes[0, 1].set_ylabel('True')
    
    # 3. ROC Curve
    if len(np.unique(test_labels)) > 1:
        from sklearn.metrics import roc_curve
        fpr, tpr, _ = roc_curve(test_labels, test_probs)
        
        axes[1, 0].plot(fpr, tpr, linewidth=2, label=f'ROC (AUC = {results["test_auc"]:.3f})')
        axes[1, 0].plot([0, 1], [0, 1], 'k--', alpha=0.5, label='Random')
        axes[1, 0].set_xlabel('False Positive Rate')
        axes[1, 0].set_ylabel('True Positive Rate')
        axes[1, 0].set_title('ROC Curve')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Prediction timeline
    axes[1, 1].plot(test_probs, 'b-', alpha=0.7, label='Predicted Prob')
    axes[1, 1].scatter(range(len(test_labels)), test_labels, c='red', s=20, alpha=0.7, label='True Labels')
    axes[1, 1].axhline(y=0.5, color='gray', linestyle='--', alpha=0.5, label='Threshold')
    axes[1, 1].set_xlabel('Sample Index')
    axes[1, 1].set_ylabel('Value')
    axes[1, 1].set_title('Predictions vs True Labels')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('single_branch_real_results.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visualization saved to 'single_branch_real_results.png'")

def main():
    """Main function"""
    
    print("🚀 Single Branch Learner Real Dataset Test")
    print("=" * 60)
    print("🎯 Incremental learning with kernel tricks, MI, and KL divergence")
    print("=" * 60)
    
    # Test on real data
    results, learner = load_and_test_real_data()
    
    if results is None:
        print("❌ Test failed")
        return
    
    # Create visualizations
    visualize_results(results)
    
    # Save simplified results
    simple_results = {
        'test_accuracy': float(results['test_accuracy']),
        'test_auc': float(results['test_auc']),
        'train_samples': int(results['train_samples']),
        'test_samples': int(results['test_samples']),
        'avg_processing_time_ms': float(results['learner_stats']['avg_processing_time'] * 1000),
        'memory_usage_mb': float(results['learner_stats']['memory_usage_mb'])
    }
    
    with open('single_branch_real_results.json', 'w') as f:
        json.dump(simple_results, f, indent=2)
    
    print(f"\n🎉 Single Branch Learning Test Complete!")
    print(f"   ✅ Incremental storage: {results['learner_stats']['stored_normal_parts']} normal + {results['learner_stats']['stored_candidate_parts']} candidate parts")
    print(f"   ✅ Kernel similarity: RBF kernel with advanced similarity measures")
    print(f"   ✅ Neural MI estimation: MINE algorithm for mutual information")
    print(f"   ✅ KL divergence: Distribution comparison between parts")
    print(f"   ✅ Label probability learning: Combined similarity features")
    print(f"   📊 Final AUC: {results['test_auc']:.4f}")
    print(f"   💾 Results saved to 'single_branch_real_results.json'")

if __name__ == "__main__":
    main()