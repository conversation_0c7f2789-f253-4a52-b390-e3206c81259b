#!/usr/bin/env python3
"""
Pattern-Based Ensemble Pipeline v2 with Proper CV
Implements proper 5-fold CV for ensemble validation
"""

import numpy as np
import pandas as pd
import xgboost as xgb
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score
from sklearn.preprocessing import StandardScaler
import warnings
import joblib
from typing import Dict, List, Tuple
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatternBasedEnsembleV2:
    """Pattern-based ensemble with proper CV validation"""
    
    def __init__(self, n_folds: int = 5, n_trials: int = 5, random_state: int = 42):
        self.n_folds = n_folds
        self.n_trials = n_trials
        self.random_state = random_state
        self.pattern_best_params = {}
        self.ensemble_weights = {}
        
    def load_data(self, features_path: str, labels_path: str, volatility_path: str):
        """Load data"""
        features_df = joblib.load(features_path)
        # features_df = pd.read_parquet(features_path)
        labels_df = pd.read_parquet(labels_path)
        labels = labels_df.iloc[:, 0].values.astype(int)
        volatility_df = pd.read_csv(volatility_path)
        
        logger.info(f"Loaded: {features_df.shape[0]} samples, {len(volatility_df['pattern_type'].unique())} patterns")
        return features_df, labels, volatility_df
    
    def optimize_pattern_hyperparameters(self, X: pd.DataFrame, y: np.ndarray, pattern: str) -> Dict:
        """Optimize hyperparameters for a pattern using 5-fold CV"""
        logger.info(f"Optimizing {pattern}...")
        
        def objective(trial):
            params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'tree_method': 'hist',
                'device': 'cuda',
                'random_state': self.random_state,
                'verbosity': 0,
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
            }
            
            # 5-fold CV
            skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
            cv_scores = []
            
            for train_idx, val_idx in skf.split(X, y):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_val_scaled = scaler.transform(X_val)
                
                model = xgb.XGBClassifier(**params)
                model.fit(X_train_scaled, y_train, verbose=False)
                
                y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
                cv_scores.append(roc_auc_score(y_val, y_pred_proba))
            
            return np.mean(cv_scores)
        
        study = optuna.create_study(direction='maximize', sampler=optuna.samplers.TPESampler(seed=self.random_state))
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
        
        logger.info(f"  Best AUC: {study.best_value:.4f}")
        return study.best_params
    
    def train_pattern_model(self, X: pd.DataFrame, y: np.ndarray, best_params: Dict):
        """Train final pattern model on full data"""
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'tree_method': 'hist',
            'device': 'cuda',
            'random_state': self.random_state,
            'verbosity': 0,
            **best_params
        }
        
        model = xgb.XGBClassifier(**xgb_params)
        model.fit(X_scaled, y)
        
        return model, scaler
    
    def ensemble_cv_validation(self, features_df: pd.DataFrame, labels: np.ndarray, volatility_df: pd.DataFrame) -> float:
        """Proper 5-fold CV for ensemble validation"""
        logger.info("Running ensemble 5-fold CV...")
        
        # Reset indices to ensure alignment
        features_df = features_df.reset_index(drop=True)
        volatility_df = volatility_df.reset_index(drop=True)
        
        skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(features_df, labels)):
            logger.info(f"  Fold {fold + 1}/5")
            
            # Split data using integer indices
            X_train, X_val = features_df.iloc[train_idx], features_df.iloc[val_idx]
            y_train, y_val = labels[train_idx], labels[val_idx]
            vol_train, vol_val = volatility_df.iloc[train_idx], volatility_df.iloc[val_idx]
            
            # Train pattern models on train fold
            fold_models = {}
            fold_scalers = {}
            
            for pattern in vol_train['pattern_type'].unique():
                pattern_mask = vol_train['pattern_type'] == pattern
                pattern_positions = np.where(pattern_mask)[0]  # Get positions within train fold
                
                if len(pattern_positions) < 20:  # Skip small patterns
                    continue
                
                X_pattern = X_train.iloc[pattern_positions]
                y_pattern = y_train[pattern_positions]
                
                # Check both classes exist
                if len(np.unique(y_pattern)) < 2:
                    continue
                
                # Use pre-optimized parameters
                if pattern in self.pattern_best_params:
                    model, scaler = self.train_pattern_model(X_pattern, y_pattern, self.pattern_best_params[pattern])
                    fold_models[pattern] = model
                    fold_scalers[pattern] = scaler
            
            # Predict on validation fold
            val_proba = np.zeros(len(val_idx))
            
            for pattern in fold_models.keys():
                pattern_mask = vol_val['pattern_type'] == pattern
                pattern_positions = np.where(pattern_mask)[0]  # Get positions within val fold
                
                if len(pattern_positions) == 0:
                    continue
                
                X_pattern = X_val.iloc[pattern_positions]
                X_scaled = fold_scalers[pattern].transform(X_pattern)
                pattern_proba = fold_models[pattern].predict_proba(X_scaled)[:, 1]
                
                weight = self.ensemble_weights.get(pattern, 1.0)
                val_proba[pattern_positions] = pattern_proba * weight
            
            # Normalize by total weight
            total_weight = sum(self.ensemble_weights.values())
            if total_weight > 0:
                val_proba = val_proba / total_weight
            
            # Calculate fold AUC
            fold_auc = roc_auc_score(y_val, val_proba)
            cv_scores.append(fold_auc)
            logger.info(f"    Fold AUC: {fold_auc:.4f}")
        
        ensemble_cv_auc = np.mean(cv_scores)
        logger.info(f"Ensemble CV AUC: {ensemble_cv_auc:.4f} ± {np.std(cv_scores):.4f}")
        
        return ensemble_cv_auc
    
    def train_ensemble(self, features_df: pd.DataFrame, labels: np.ndarray, volatility_df: pd.DataFrame):
        """Train complete ensemble with proper CV"""
        logger.info("Training pattern-based ensemble v2...")
        
        # Step 1: Optimize hyperparameters for each pattern
        pattern_groups = {}
        for pattern in volatility_df['pattern_type'].unique():
            pattern_mask = volatility_df['pattern_type'] == pattern
            pattern_indices = volatility_df[pattern_mask].index
            
            X_pattern = features_df.iloc[pattern_indices]
            y_pattern = labels[pattern_indices]
            
            if len(pattern_indices) < 50 or len(np.unique(y_pattern)) < 2:
                logger.warning(f"Skipping pattern {pattern}: insufficient samples or single class")
                continue
            
            # Optimize hyperparameters
            best_params = self.optimize_pattern_hyperparameters(X_pattern, y_pattern, pattern)
            self.pattern_best_params[pattern] = best_params
            
            pattern_groups[pattern] = {
                'features': X_pattern,
                'labels': y_pattern,
                'size': len(pattern_indices)
            }
        
        # Step 2: Calculate ensemble weights (equal for now)
        for pattern in self.pattern_best_params.keys():
            self.ensemble_weights[pattern] = 1.0 / len(self.pattern_best_params)
        
        # Step 3: Proper ensemble CV validation
        ensemble_cv_auc = self.ensemble_cv_validation(features_df, labels, volatility_df)
        
        # Step 4: Train final models on full data
        self.final_models = {}
        self.final_scalers = {}
        
        for pattern, group_data in pattern_groups.items():
            if pattern in self.pattern_best_params:
                model, scaler = self.train_pattern_model(
                    group_data['features'], 
                    group_data['labels'], 
                    self.pattern_best_params[pattern]
                )
                self.final_models[pattern] = model
                self.final_scalers[pattern] = scaler
        
        logger.info(f"✅ Ensemble training completed. CV AUC: {ensemble_cv_auc:.4f}")
        
        return {
            'ensemble_cv_auc': ensemble_cv_auc,
            'pattern_best_params': self.pattern_best_params,
            'ensemble_weights': self.ensemble_weights,
            'n_patterns': len(self.final_models)
        }
    
    def predict(self, features_df: pd.DataFrame, volatility_df: pd.DataFrame) -> np.ndarray:
        """Make ensemble predictions"""
        n_samples = len(features_df)
        ensemble_proba = np.zeros(n_samples)
        
        for pattern in self.final_models.keys():
            pattern_mask = volatility_df['pattern_type'] == pattern
            pattern_indices = volatility_df[pattern_mask].index
            
            if len(pattern_indices) == 0:
                continue
            
            X_pattern = features_df.iloc[pattern_indices]
            X_scaled = self.final_scalers[pattern].transform(X_pattern)
            pattern_proba = self.final_models[pattern].predict_proba(X_scaled)[:, 1]
            
            weight = self.ensemble_weights[pattern]
            ensemble_proba[pattern_indices] = pattern_proba * weight
        
        # Normalize
        total_weight = sum(self.ensemble_weights.values())
        if total_weight > 0:
            ensemble_proba = ensemble_proba / total_weight
        
        return ensemble_proba
    
    def save_models(self, save_path: str):
        """Save models"""
        save_data = {
            'final_models': self.final_models,
            'final_scalers': self.final_scalers,
            'pattern_best_params': self.pattern_best_params,
            'ensemble_weights': self.ensemble_weights
        }
        joblib.dump(save_data, save_path)
        logger.info(f"Models saved to {save_path}")

def main():
    """Main execution"""
    config = {
        'features_path': "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/ratios_updated_v4_scaled_normalized",
        # 'features_path': "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet",
        'labels_path': "y_train.parquet",
        'volatility_path': "volatility_pattern_analysis.csv",
        'n_folds': 5,
        'n_trials': 5,
        'random_state': 42
    }
    
    # Initialize ensemble
    ensemble = PatternBasedEnsembleV2(
        n_folds=config['n_folds'],
        n_trials=config['n_trials'],
        random_state=config['random_state']
    )
    
    # Load data
    features_df, labels, volatility_df = ensemble.load_data(
        config['features_path'],
        config['labels_path'],
        config['volatility_path']
    )
    
    # Train ensemble with proper CV
    results = ensemble.train_ensemble(features_df, labels, volatility_df)
    
    # Print results
    logger.info("\n" + "="*50)
    logger.info("ENSEMBLE RESULTS")
    logger.info("="*50)
    logger.info(f"Ensemble CV AUC: {results['ensemble_cv_auc']:.4f}")
    logger.info(f"Number of patterns: {results['n_patterns']}")
    
    # Save models
    ensemble.save_models('pattern_ensemble_v2_models.joblib')
    
    return ensemble, results

if __name__ == "__main__":
    ensemble, results = main()