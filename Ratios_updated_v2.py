import numpy as np
import pandas as pd
from tsfresh.feature_extraction.feature_calculators import (
    energy_ratio_by_chunks, fft_aggregated, index_mass_quantile,
    binned_entropy, last_location_of_maximum, lempel_ziv_complexity,
    longest_strike_above_mean, longest_strike_below_mean,
    mean_second_derivative_central, percentage_of_reoccurring_datapoints_to_all_datapoints,
    percentage_of_reoccurring_values_to_all_values, ratio_beyond_r_sigma,
    ratio_value_number_to_time_series_length
)
from scipy.stats import norm, kstest, anderson_ksamp, cramervonmises_2samp
from scipy.stats import norm, kstest, anderson, cramervonmises
from statsmodels.stats.diagnostic import lilliefors
from statsmodels.tsa.stattools import coint
from statsmodels.stats.stattools import jarque_bera
from statsmodels.regression.linear_model import OLS
import numpy as np
import pycatch22
from scipy.signal import periodogram, welch, spectrogram, find_peaks, peak_prominences, savgol_filter, detrend, hilbert
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.tsa.stattools import grangercausalitytests
from arch import arch_model
import warnings
from multiprocessing import Pool
from functools import partial
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix=''):
    """Compute all 4 relative measures for any pre/post feature pair"""
    relatives = {}
    
    # 1. Standard ratio
    relatives[f'{segment_name_prefix}ratio_{feature_name}'] = (
        post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
    )
    
    # 2. Absolute difference (normalized)
    if pre_val and pre_val != 0 and not np.isnan(post_val):
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = abs(post_val - pre_val) / abs(pre_val)
    else:
        relatives[f'{segment_name_prefix}abs_diff_{feature_name}'] = np.nan
        
    # 3. Log ratio
    if pre_val and pre_val > 0 and post_val and post_val > 0:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.log(post_val / pre_val)
    else:
        relatives[f'{segment_name_prefix}log_ratio_{feature_name}'] = np.nan
        
    # 4. Symmetric ratio
    if not np.isnan(pre_val) and not np.isnan(post_val) and (abs(pre_val) + abs(post_val)) > 0:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = (post_val - pre_val) / (abs(post_val) + abs(pre_val))
    else:
        relatives[f'{segment_name_prefix}sym_ratio_{feature_name}'] = np.nan
        
    return relatives

def safe_adf_stat(arr):
    try:
        if len(arr) < 10:
            return np.nan
        return adfuller(arr[~np.isnan(arr)], maxlag=1)[0]
    except Exception:
        return np.nan

def safe_kpss_stat(arr):
    try:
        if len(arr) < 10:
            return np.nan
        return kpss(arr[~np.isnan(arr)], nlags='auto')[0]
    except Exception:
        return np.nan

def compute_tsfresh_feature(feature_func, arr, params=None, min_length=2):
    try:
        if len(arr) < min_length:
            return np.nan
        if params:
            result = feature_func(arr, params)
            if isinstance(result, list) and len(result) > 0:
                return float(result[0][1]) if len(result[0]) > 1 else float(result)
            return float(result)
        return float(feature_func(arr))
    except Exception:
        return np.nan

def compute_features_for_segment_v2(pre, post, whole, segment_name_prefix=''):
    """Enhanced feature computation with comprehensive relative measures"""
    feats = {}
    
    if len(pre) < 5 or len(post) < 5:
        return feats
    
    # Define segments
    mean_whole = np.nanmean(whole)
    above_mean = whole[whole > mean_whole]
    below_mean = whole[whole < mean_whole]
    pre_above_mean = pre[pre > np.nanmean(pre)]
    pre_below_mean = pre[pre < np.nanmean(pre)]
    post_above_mean = post[post > np.nanmean(post)]
    post_below_mean = post[post < np.nanmean(post)]

    # 1. LENGTH FEATURES - All 4 relatives
    len_pre, len_post, len_whole = len(pre), len(post), len(whole)
    len_above, len_below = len(above_mean), len(below_mean)
    len_pre_above, len_pre_below = len(pre_above_mean), len(pre_below_mean)
    len_post_above, len_post_below = len(post_above_mean), len(post_below_mean)
    
    length_features = [
        ('post_pre_length', len_pre, len_post),
        ('above_below_length', len_below, len_above),
        ('pre_above_below_length', len_pre_below, len_pre_above),
        ('post_above_below_length', len_post_below, len_post_above),
        ('above_length_change', len_pre_above, len_post_above),
        ('below_length_change', len_pre_below, len_post_below)
    ]
    
    for feature_name, pre_val, post_val in length_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 2. BASIC STATISTICAL FEATURES - All 4 relatives
    mean_pre, mean_post = np.nanmean(pre), np.nanmean(post)
    std_pre, std_post = np.nanstd(pre, ddof=1), np.nanstd(post, ddof=1)
    var_pre, var_post = np.nanvar(pre, ddof=1), np.nanvar(post, ddof=1)
    skew_pre, skew_post = stats.skew(pre, nan_policy='omit'), stats.skew(post, nan_policy='omit')
    kurt_pre, kurt_post = stats.kurtosis(pre, nan_policy='omit'), stats.kurtosis(post, nan_policy='omit')
    
    basic_features = [
        ('mean', mean_pre, mean_post),
        ('std', std_pre, std_post),
        ('var', var_pre, var_post),
        ('skew', skew_pre, skew_post),
        ('kurt', kurt_pre, kurt_post)
    ]
    
    for feature_name, pre_val, post_val in basic_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 3. RANGE & VOLATILITY FEATURES - All 4 relatives
    range_pre = np.nanmax(pre) - np.nanmin(pre) if len(pre) > 0 else np.nan
    range_post = np.nanmax(post) - np.nanmin(post) if len(post) > 0 else np.nan
    vol_pre = np.nanstd(np.diff(pre)) if len(pre) > 1 else np.nan
    vol_post = np.nanstd(np.diff(post)) if len(post) > 1 else np.nan
    min_pre, min_post = np.nanmin(pre), np.nanmin(post)
    max_pre, max_post = np.nanmax(pre), np.nanmax(post)
    
    range_features = [
        ('range', range_pre, range_post),
        ('volatility', vol_pre, vol_post),
        ('min', min_pre, min_post),
        ('max', max_pre, max_post)
    ]
    
    for feature_name, pre_val, post_val in range_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 4. STATIONARITY FEATURES - All 4 relatives
    adf_pre, adf_post = safe_adf_stat(pre), safe_adf_stat(post)
    kpss_pre, kpss_post = safe_kpss_stat(pre), safe_kpss_stat(post)
    
    stationarity_features = [
        ('adf_stat', adf_pre, adf_post),
        ('kpss_stat', kpss_pre, kpss_post)
    ]
    
    for feature_name, pre_val, post_val in stationarity_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 5. CONDITIONAL EXPECTATION FEATURES - All 4 relatives
    cond_exp_above_pre = np.nanmean(pre_above_mean) if len(pre_above_mean) > 0 else np.nan
    cond_exp_above_post = np.nanmean(post_above_mean) if len(post_above_mean) > 0 else np.nan
    cond_exp_below_pre = np.nanmean(pre_below_mean) if len(pre_below_mean) > 0 else np.nan
    cond_exp_below_post = np.nanmean(post_below_mean) if len(post_below_mean) > 0 else np.nan
    cond_exp_whole_above = np.nanmean(above_mean) if len(above_mean) > 0 else np.nan
    cond_exp_whole_below = np.nanmean(below_mean) if len(below_mean) > 0 else np.nan
    
    conditional_features = [
        ('conditional_exp_above', cond_exp_above_pre, cond_exp_above_post),
        ('conditional_exp_below', cond_exp_below_pre, cond_exp_below_post),
        ('pre_above_vs_below', cond_exp_below_pre, cond_exp_above_pre),
        ('post_above_vs_below', cond_exp_below_post, cond_exp_above_post),
        ('whole_above_vs_below', cond_exp_whole_below, cond_exp_whole_above)
    ]
    
    for feature_name, pre_val, post_val in conditional_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 6. QUANTILE FEATURES - All 4 relatives
    if len(pre) > 0 and len(post) > 0:
        quantiles = [0.05, 0.25, 0.5, 0.75, 0.95]
        q_pre = np.nanquantile(pre, quantiles)
        q_post = np.nanquantile(post, quantiles)
        
        for i, q in enumerate([5, 25, 50, 75, 95]):
            relatives = compute_all_relatives(q_pre[i], q_post[i], f'q{q}', segment_name_prefix)
            feats.update(relatives)

    # 7. TSFRESH FEATURES - All 4 relatives
    # Energy Ratio by Chunks
    for i in range(2):
        energy_pre = compute_tsfresh_feature(
            energy_ratio_by_chunks, pre, [{"num_segments": 2, "segment_focus": i}], min_length=2
        )
        energy_post = compute_tsfresh_feature(
            energy_ratio_by_chunks, post, [{"num_segments": 2, "segment_focus": i}], min_length=2
        )
        relatives = compute_all_relatives(energy_pre, energy_post, f'energy_ratio_chunk{i+1}', segment_name_prefix)
        feats.update(relatives)

    # FFT Aggregated
    for aggtype in ["centroid", "variance"]:
        fft_pre = compute_tsfresh_feature(fft_aggregated, pre, [{"aggtype": aggtype}], min_length=10)
        fft_post = compute_tsfresh_feature(fft_aggregated, post, [{"aggtype": aggtype}], min_length=10)
        relatives = compute_all_relatives(fft_pre, fft_post, f'fft_agg_{aggtype}', segment_name_prefix)
        feats.update(relatives)

    # Index Mass Quantile
    for q in [0.25, 0.5, 0.75]:
        imq_pre = compute_tsfresh_feature(index_mass_quantile, pre, [{"q": q}], min_length=5)
        imq_post = compute_tsfresh_feature(index_mass_quantile, post, [{"q": q}], min_length=5)
        relatives = compute_all_relatives(imq_pre, imq_post, f'index_mass_quantile_q{int(q*100)}', segment_name_prefix)
        feats.update(relatives)

    # Other TSFRESH features
    tsfresh_features = [
        (binned_entropy, {"max_bins": 10}, 'binned_entropy'),
        (last_location_of_maximum, None, 'last_location_of_maximum'),
        (lempel_ziv_complexity, 100, 'lempel_ziv_complexity'),
        (mean_second_derivative_central, None, 'mean_second_derivative_central'),
        (percentage_of_reoccurring_datapoints_to_all_datapoints, None, 'pct_reoccurring_datapoints'),
        (percentage_of_reoccurring_values_to_all_values, None, 'pct_reoccurring_values'),
        (ratio_value_number_to_time_series_length, None, 'unique_values')
    ]
    
    for func, params, name in tsfresh_features:
        pre_val = compute_tsfresh_feature(func, pre, params, min_length=5)
        post_val = compute_tsfresh_feature(func, post, params, min_length=5)
        relatives = compute_all_relatives(pre_val, post_val, name, segment_name_prefix)
        feats.update(relatives)

    # 8. SPECTRAL FEATURES - All 4 relatives
    def spectral_power_periodogram(arr):
        if len(arr) > 10:
            try:
                f, Pxx = periodogram(arr, fs=1.0, scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan

    def spectral_power_welch(arr):
        if len(arr) > 10:
            try:
                f, Pxx = welch(arr, nperseg=min(len(arr)//2, 256), scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan

    def spectrogram_entropy(seg):
        if len(seg) < 20:
            return np.nan
        try:
            f, t, Sxx = spectrogram(seg, fs=1.0, nperseg=min(100, len(seg)//2), noverlap=None)
            Sxx = Sxx / np.sum(Sxx) if np.sum(Sxx) != 0 else np.ones_like(Sxx) / Sxx.size
            return -np.sum(Sxx * np.log2(Sxx + 1e-10))
        except Exception:
            return np.nan

    spectral_funcs = [
        (spectral_power_periodogram, 'power_periodogram'),
        (spectral_power_welch, 'power_welch'),
        (spectrogram_entropy, 'spec_entropy')
    ]
    
    for func, name in spectral_funcs:
        pre_val = func(pre)
        post_val = func(post)
        relatives = compute_all_relatives(pre_val, post_val, name, segment_name_prefix)
        feats.update(relatives)

    # 9. SIGNAL PROCESSING FEATURES - All 4 relatives
    # Peak Finding
    if len(pre) > 5 and len(post) > 5:
        try:
            peaks_pre, _ = find_peaks(pre, height=np.mean(pre) + np.std(pre))
            peaks_post, _ = find_peaks(post, height=np.mean(post) + np.std(post))
            num_peaks_pre, num_peaks_post = len(peaks_pre), len(peaks_post)
            
            prominences_pre = peak_prominences(pre, peaks_pre)[0] if len(peaks_pre) > 0 else np.array([0])
            prominences_post = peak_prominences(post, peaks_post)[0] if len(peaks_post) > 0 else np.array([0])
            mean_prominence_pre = np.mean(prominences_pre)
            mean_prominence_post = np.mean(prominences_post)
            
            peak_features = [
                ('num_peaks', num_peaks_pre, num_peaks_post),
                ('mean_prominence', mean_prominence_pre, mean_prominence_post)
            ]
            
            for feature_name, pre_val, post_val in peak_features:
                relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
                feats.update(relatives)
        except Exception:
            pass

    # Savitzky-Golay Filtering
    if len(pre) > 7 and len(post) > 7:
        try:
            pre_smooth = savgol_filter(pre, window_length=7, polyorder=2)
            post_smooth = savgol_filter(post, window_length=7, polyorder=2)
            smooth_residuals_pre = np.std(pre - pre_smooth)
            smooth_residuals_post = np.std(post - post_smooth)
            
            relatives = compute_all_relatives(smooth_residuals_pre, smooth_residuals_post, 'smooth_residuals', segment_name_prefix)
            feats.update(relatives)
        except Exception:
            pass

    # Detrending
    if len(pre) > 5 and len(post) > 5:
        try:
            pre_detrended = detrend(pre, type='linear')
            post_detrended = detrend(post, type='linear')
            std_detrended_pre = np.std(pre_detrended)
            std_detrended_post = np.std(post_detrended)
            
            relatives = compute_all_relatives(std_detrended_pre, std_detrended_post, 'std_detrended', segment_name_prefix)
            feats.update(relatives)
        except Exception:
            pass

    # Hilbert Transform
    if len(pre) > 5 and len(post) > 5:
        try:
            analytic_pre = hilbert(pre)
            analytic_post = hilbert(post)
            env_pre = np.abs(analytic_pre)
            env_post = np.abs(analytic_post)
            mean_envelope_pre = np.mean(env_pre)
            mean_envelope_post = np.mean(env_post)
            
            relatives = compute_all_relatives(mean_envelope_pre, mean_envelope_post, 'mean_envelope', segment_name_prefix)
            feats.update(relatives)
        except Exception:
            pass

    # 10. CATCH22 FEATURES - All 4 relatives
    if len(pre) >= 20 and len(post) >= 20:
        try:
            pre_no_nan = pre[~np.isnan(pre)]
            post_no_nan = post[~np.isnan(post)]
            catch22_pre = pycatch22.catch22_all(pre_no_nan)
            catch22_post = pycatch22.catch22_all(post_no_nan)
            
            for feature_name, pre_val, post_val in zip(catch22_pre['names'], catch22_pre['values'], catch22_post['values']):
                relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
                feats.update(relatives)
        except Exception:
            pass

    # 11. AUTOCORRELATION FEATURES - All 4 relatives
    def lag1_autocorr(arr):
        if len(arr) > 1:
            arr0 = arr - np.nanmean(arr)
            return np.corrcoef(arr0[:-1], arr0[1:])[0, 1] if len(arr0) > 1 else np.nan
        return np.nan

    def estimate_ar1(arr):
        if len(arr) > 1:
            x_prev = arr[:-1]
            x_next = arr[1:]
            phi = np.polyfit(x_prev, x_next, deg=1)[0]
            return phi
        return np.nan

    ac_pre, ac_post = lag1_autocorr(pre), lag1_autocorr(post)
    phi_pre, phi_post = estimate_ar1(pre), estimate_ar1(post)
    
    autocorr_features = [
        ('ac', ac_pre, ac_post),
        ('phi', phi_pre, phi_post)
    ]
    
    for feature_name, pre_val, post_val in autocorr_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 12. LONGEST STRIKE FEATURES - All 4 relatives
    def longest_strike_above_mean(arr):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        streaks = []
        current_streak = 0
        for val in arr:
            if val > mean:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0

    def longest_strike_below_mean(arr):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        streaks = []
        current_streak = 0
        for val in arr:
            if val < mean:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0

    lsam_pre, lsam_post = longest_strike_above_mean(pre), longest_strike_above_mean(post)
    lsbm_pre, lsbm_post = longest_strike_below_mean(pre), longest_strike_below_mean(post)
    
    strike_features = [
        ('longest_strike_above_mean', lsam_pre, lsam_post),
        ('longest_strike_below_mean', lsbm_pre, lsbm_post)
    ]
    
    for feature_name, pre_val, post_val in strike_features:
        relatives = compute_all_relatives(pre_val, post_val, feature_name, segment_name_prefix)
        feats.update(relatives)

    # 13. RATIO BEYOND R SIGMA - All 4 relatives
    def ratio_beyond_r_sigma(arr, r):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        std = np.nanstd(arr, ddof=1)
        if std == 0:
            return np.nan
        return np.sum(np.abs(arr - mean) > r * std) / len(arr)

    for r in [1, 2]:
        rbs_pre = ratio_beyond_r_sigma(pre, r)
        rbs_post = ratio_beyond_r_sigma(post, r)
        relatives = compute_all_relatives(rbs_pre, rbs_post, f'beyond_{r}_sigma', segment_name_prefix)
        feats.update(relatives)

    # 14. GARCH VOLATILITY FEATURES - All 4 relatives
    def garch_volatility(arr):
        if len(arr) > 30:
            try:
                from arch import arch_model
                model = arch_model(arr, vol='Garch', p=1, q=1, rescale=True).fit(disp='off')
                return np.mean(np.sqrt(model.conditional_volatility))
            except Exception:
                return np.nan
        return np.nan

    garch_pre = garch_volatility(pre)
    garch_post = garch_volatility(post)
    relatives = compute_all_relatives(garch_pre, garch_post, 'garch', segment_name_prefix)
    feats.update(relatives)

    # 15. SSA FEATURES - All 4 relatives for each SSA component
    def ssa_features(arr, window_size=None, k=3):
        if len(arr) < 20:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }
        try:
            arr = arr[~np.isnan(arr)]
            n = len(arr)
            window_size = window_size or min(40, n // 2)
            if window_size < 2 or window_size > n:
                return {
                    'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                    'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
                }
            L = window_size
            K = n - L + 1
            X = np.array([arr[i:i+L] for i in range(K)])
            U, s, Vt = np.linalg.svd(X, full_matrices=False)
            singular_values = s[:k]
            singular_values = np.pad(singular_values, (0, max(0, k - len(singular_values))), constant_values=np.nan)
            X_reconstructed = np.zeros_like(X)
            for i in range(min(k, len(s))):
                X_reconstructed += s[i] * np.outer(U[:, i], Vt[i, :])
            reconstructed_series = np.zeros(n)
            count = np.zeros(n)
            for i in range(K):
                for j in range(L):
                    if i + j < n:
                        reconstructed_series[i + j] += X_reconstructed[i, j]
                        count[i + j] += 1
            reconstructed_series = reconstructed_series / count
            reconstruction_error = np.nanmean((arr - reconstructed_series) ** 2)
            trend = s[0] * np.outer(U[:, 0], Vt[0, :])
            trend_series = np.zeros(n)
            count = np.zeros(n)
            for i in range(K):
                for j in range(L):
                    if i + j < n:
                        trend_series[i + j] += trend[i, j]
                        count[i + j] += 1
            trend_series = trend_series / count
            trend_mean = np.nanmean(trend_series)
            trend_std = np.nanstd(trend_series, ddof=1) if len(trend_series) > 1 else np.nan
            return {
                'ssa_singular_value_1': singular_values[0],
                'ssa_singular_value_2': singular_values[1] if len(singular_values) > 1 else np.nan,
                'ssa_singular_value_3': singular_values[2] if len(singular_values) > 2 else np.nan,
                'ssa_reconstruction_error': reconstruction_error,
                'ssa_trend_mean': trend_mean,
                'ssa_trend_std': trend_std
            }
        except Exception:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }

    if len(pre) >= 20 and len(post) >= 20:
        try:
            ssa_pre = ssa_features(pre)
            ssa_post = ssa_features(post)
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                relatives = compute_all_relatives(ssa_pre[key], ssa_post[key], key, segment_name_prefix)
                feats.update(relatives)
        except Exception:
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                relatives = compute_all_relatives(np.nan, np.nan, key, segment_name_prefix)
                feats.update(relatives)
    else:
        for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                    'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
            relatives = compute_all_relatives(np.nan, np.nan, key, segment_name_prefix)
            feats.update(relatives)

    # 16. STATISTICAL TESTS - Raw values for segments + ratios
    def ryan_joiner_test(arr):
        if len(arr) < 4:
            return np.nan
        try:
            arr = arr[~np.isnan(arr)]
            sorted_data = np.sort(arr)
            n = len(sorted_data)
            mu, sigma = np.mean(sorted_data), np.std(sorted_data, ddof=1)
            if sigma == 0:
                return np.nan
            z_scores = (sorted_data - mu) / sigma
            expected_quantiles = norm.ppf((np.arange(1, n + 1) - 0.375) / (n + 0.25))
            correlation = np.corrcoef(z_scores, expected_quantiles)[0, 1]
            return correlation
        except Exception:
            return np.nan

    def cusum_test(series):
        if len(series) < 5:
            return np.nan
        try:
            mean = np.mean(series)
            cusum = np.cumsum(series - mean)
            return np.max(np.abs(cusum)) / np.std(series, ddof=1)
        except Exception:
            return np.nan

    from scipy import stats
    try:
        from statsmodels.stats.diagnostic import lilliefors
        from statsmodels.tsa.stattools import adfuller, kpss

        stat_tests = [
            ('ryan_joiner', ryan_joiner_test),
            ('shapiro_wilk', lambda x: stats.shapiro(x[~np.isnan(x)])[0] if len(x[~np.isnan(x)]) >= 3 else np.nan),
            ('lilliefors', lambda x: lilliefors(x[~np.isnan(x)])[0] if len(x[~np.isnan(x)]) >= 4 else np.nan),
            ('adf', lambda x: adfuller(x[~np.isnan(x)], maxlag=1)[0] if len(x[~np.isnan(x)]) >= 3 else np.nan),
            ('kpss', lambda x: kpss(x[~np.isnan(x)], nlags='auto')[0] if len(x[~np.isnan(x)]) >= 3 else np.nan),
            ('cusum', cusum_test),
        ]

        for test_name, test_func in stat_tests:
            for segment, seg_data in [
                ('pre', pre), ('post', post),
                ('above_mean', above_mean), ('below_mean', below_mean),
                ('pre_above_mean', pre_above_mean), ('pre_below_mean', pre_below_mean),
                ('post_above_mean', post_above_mean), ('post_below_mean', post_below_mean)
            ]:
                feats[f'{segment_name_prefix}{segment}_{test_name}'] = test_func(seg_data) if len(seg_data) >= 5 else np.nan
            
            # Compute ratios for pre/post segments
            pre_val = feats.get(f'{segment_name_prefix}pre_{test_name}', np.nan)
            post_val = feats.get(f'{segment_name_prefix}post_{test_name}', np.nan)
            relatives = compute_all_relatives(pre_val, post_val, test_name, segment_name_prefix)
            feats.update(relatives)
    except ImportError:
        pass

    # 17. TWO-SAMPLE TESTS - Raw values
    def cliffs_delta(x, y):
        if len(x) < 2 or len(y) < 2:
            return np.nan
        try:
            x, y = x[~np.isnan(x)], y[~np.isnan(y)]
            n_x, n_y = len(x), len(y)
            diffs = np.array([1 if xi > yj else (-1 if xi < yj else 0) for xi in x for yj in y])
            return np.sum(diffs) / (n_x * n_y)
        except Exception:
            return np.nan

    def jensen_shannon_divergence(p, q):
        if len(p) < 2 or len(q) < 2:
            return np.nan
        try:
            p, q = p[~np.isnan(p)], q[~np.isnan(q)]
            p_hist, _ = np.histogram(p, bins=50, density=True)
            q_hist, _ = np.histogram(q, bins=50, density=True)
            p_hist = p_hist / np.sum(p_hist) + 1e-10
            q_hist = q_hist / np.sum(q_hist) + 1e-10
            m = 0.5 * (p_hist + q_hist)
            jsd = 0.5 * (np.sum(p_hist * np.log2(p_hist / m)) + np.sum(q_hist * np.log2(q_hist / m)))
            return jsd
        except Exception:
            return np.nan

    def welch_ttest(x, y):
        if len(x) < 2 or len(y) < 2:
            return np.nan
        try:
            x, y = x[~np.isnan(x)], y[~np.isnan(y)]
            t_stat, _ = stats.ttest_ind(x, y, equal_var=False)
            return t_stat
        except Exception:
            return np.nan

    def cohens_d(x, y):
        if len(x) < 2 or len(y) < 2:
            return np.nan
        try:
            x, y = x[~np.isnan(x)], y[~np.isnan(y)]
            n_x, n_y = len(x), len(y)
            mean_x, mean_y = np.mean(x), np.mean(y)
            var_x, var_y = np.var(x, ddof=1), np.var(y, ddof=1)
            pooled_std = np.sqrt(((n_x - 1) * var_x + (n_y - 1) * var_y) / (n_x + n_y - 2))
            return (mean_x - mean_y) / pooled_std if pooled_std > 0 else np.nan
        except Exception:
            return np.nan

    def glass_delta(x, y):
        if len(x) < 2 or len(y) < 2:
            return np.nan
        try:
            x, y = x[~np.isnan(x)], y[~np.isnan(y)]
            mean_x, mean_y = np.mean(x), np.mean(y)
            std_x = np.std(x, ddof=1)
            return (mean_x - mean_y) / std_x if std_x > 0 else np.nan
        except Exception:
            return np.nan

    def safe_granger_causality(x, y, maxlag=1):
        if len(x) < 10 or len(y) < 10:
            return np.nan
        try:
            from statsmodels.tsa.stattools import grangercausalitytests
            x_clean = x[~np.isnan(x)]
            y_clean = y[~np.isnan(y)]
            min_len = min(len(x_clean), len(y_clean))
            if min_len < 10:
                return np.nan
            x_aligned = x_clean[:min_len]
            y_aligned = y_clean[:min_len]
            data = np.column_stack([y_aligned, x_aligned])
            result = grangercausalitytests(data, maxlag=maxlag, verbose=False)
            return result[maxlag][0]['ssr_ftest'][0]
        except Exception:
            return np.nan

    def safe_coint(x, y):
        try:
            from statsmodels.tsa.stattools import coint
            x_clean = x[~np.isnan(x)]
            y_clean = y[~np.isnan(y)]
            min_len = min(len(x_clean), len(y_clean))
            if min_len < 10:
                return np.nan
            return coint(x_clean[:min_len], y_clean[:min_len])[0]
        except Exception:
            return np.nan

    try:
        from scipy.stats import kstest, anderson_ksamp, cramervonmises_2samp

        two_sample_tests = [
            ('cliffs_delta', lambda x, y: cliffs_delta(x, y)),
            ('jsd', lambda x, y: jensen_shannon_divergence(x, y)),
            ('ks', lambda x, y: kstest(x[~np.isnan(x)], y[~np.isnan(y)])[0] if len(x[~np.isnan(x)]) >= 2 and len(y[~np.isnan(y)]) >= 2 else np.nan),
            ('anderson', lambda x, y: anderson_ksamp([x[~np.isnan(x)], y[~np.isnan(y)]])[0] if len(x[~np.isnan(x)]) >= 2 and len(y[~np.isnan(y)]) >= 2 else np.nan),
            ('cramer_von_mises', lambda x, y: cramervonmises_2samp(x[~np.isnan(x)], y[~np.isnan(y)]).statistic if len(x[~np.isnan(x)]) >= 2 and len(y[~np.isnan(y)]) >= 2 else np.nan),
            ('welch_ttest', welch_ttest),
            ('cohens_d', cohens_d),
            ('glass_delta', glass_delta),
            ('granger_causality', safe_granger_causality),
            ('coint', lambda x, y: safe_coint(x, y))
        ]

        for test_name, test_func in two_sample_tests:
            feats[f'{segment_name_prefix}{test_name}'] = test_func(pre, post) if len(pre) >= 5 and len(post) >= 5 else np.nan
            # For above and below mean comparisons
            feats[f'{segment_name_prefix}above_below_{test_name}'] = test_func(above_mean, below_mean) if len(above_mean) >= 5 and len(below_mean) >= 5 else np.nan
            feats[f'{segment_name_prefix}pre_above_below_{test_name}'] = test_func(pre_above_mean, pre_below_mean) if len(pre_above_mean) >= 5 and len(pre_below_mean) >= 5 else np.nan
            feats[f'{segment_name_prefix}post_above_below_{test_name}'] = test_func(post_above_mean, post_below_mean) if len(post_above_mean) >= 5 and len(post_below_mean) >= 5 else np.nan
    except ImportError:
        pass

    return feats

def process_series_v2(args, **kwargs):
    """Enhanced process_series with comprehensive relative measures"""
    index, series, split_point = args
    try:
        series = np.array(series, dtype=float)
        series = series[~np.isnan(series)]
        
        if len(series) < 10:
            return {}

        if split_point is None or not (0 < split_point < len(series)):
            split_point = len(series) // 2
        
        pre = series[:split_point]
        post = series[split_point:]
        
        if len(pre) < 5 or len(post) < 5:
            return {}

        feats = compute_features_for_segment_v2(pre=pre, post=post, whole=series)
        return feats
    
    except Exception as e:
        logging.error(f"Error processing series {index}: {str(e)}")
        return {}

def extract_features_parallel_v2(series_list, split_points, n_processes=None):
    """Enhanced parallel feature extraction with comprehensive relatives"""
    import multiprocessing as mp
    import time
    
    if n_processes is None:
        n_processes = max(1, mp.cpu_count() - 1)
    
    args = [(i, series_list[i], split_points[i]) for i in range(len(series_list))]
    total_series = len(args)
    
    print(f"Initializing {n_processes} worker processes for {total_series} series...")
    start_time = time.time()
    
    with mp.Pool(processes=n_processes) as pool:
        with mp.Manager() as manager:
            progress_counter = manager.Value('i', 0)
            progress_lock = manager.Lock()
            
            def update_progress(result):
                with progress_lock:
                    progress_counter.value += 1
                    completed = progress_counter.value
                    elapsed = time.time() - start_time
                    progress_pct = (completed / total_series) * 100
                    
                    if completed > 0:
                        eta = elapsed / completed * (total_series - completed)
                        rate = completed / elapsed if elapsed > 0 else 0
                        
                        print(f"\r\033[K[{completed:>4}/{total_series}] "
                              f"{progress_pct:5.1f}% | "
                              f"Rate: {rate:.2f} series/s | "
                              f"Elapsed: {elapsed/60:4.1f}m | "
                              f"ETA: {eta/60:4.1f}m", 
                              end='', flush=True)
            
            async_results = []
            for arg in args:
                async_result = pool.apply_async(process_series_v2, (arg,), callback=update_progress)
                async_results.append(async_result)
            
            results = []
            for async_result in async_results:
                results.append(async_result.get())
    
    print()
    elapsed_total = time.time() - start_time
    throughput = total_series / elapsed_total
    print(f"✓ Extraction completed: {elapsed_total/60:.2f}m total "
          f"({throughput:.2f} series/s average)")
    
    feature_df = pd.DataFrame(results)
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    n_features = feature_df.shape[1]
    sparsity = (feature_df.isna().sum().sum() / feature_df.size) * 100
    print(f"Feature matrix: {total_series} × {n_features} ({sparsity:.1f}% sparse)")
    
    return feature_df

if __name__ == "__main__":
    # Example usage
    print("Ratios_updated_v2.py - Comprehensive ratio-based feature extraction")
    print("Features: Length, Statistical, Range, Stationarity, Conditional, Quantile,")
    print("         TSFRESH, Spectral, Signal Processing, Catch22, Autocorr, Strikes")
    print("Relatives: Standard ratio, Absolute difference, Log ratio, Symmetric ratio")
    print("Expected ~600+ features per time series")