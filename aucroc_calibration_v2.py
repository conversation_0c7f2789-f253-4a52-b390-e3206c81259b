#!/usr/bin/env python3
"""
AUROC Calibration V2 with Advanced Techniques
Includes class weighting, SMOTE, threshold optimization, and ensemble methods
"""

import numpy as np
import pandas as pd
import xgboost as xgb
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix, roc_curve, precision_recall_curve
from sklearn.calibration import CalibratedClassifierCV
from imblearn.over_sampling import SMOTE, BorderlineSMOTE, ADASYN
from imblearn.combine import SMOTETomek, SMOTEENN
import logging
from typing import Dict, Tuple
import warnings
import gc
import torch

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AdvancedAUCROCCalibrator:
    """Advanced AUROC calibration with class imbalance handling and ensemble methods"""
    
    def __init__(self, n_folds=5, n_trials=100, random_state=42):
        self.n_folds = n_folds
        self.n_trials = n_trials
        self.random_state = random_state
        self.pattern_studies = {}
        self.ensemble_models = {}
        
    def load_data(self):
        """Load prepared features and labels"""
        logger.info("📊 Loading prepared features and labels...")
        
        try:
            # Load features
            features_path = "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet"
            X = pd.read_parquet(features_path)
            logger.info(f"   ✅ Features loaded: {X.shape}")
            
            # Load labels
            y_data = pd.read_parquet('y_train.parquet')
            y = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
            logger.info(f"   ✅ Labels loaded: {len(y)}")
            
            # Analyze class imbalance
            unique, counts = np.unique(y, return_counts=True)
            class_ratio = counts[1] / counts[0] if len(counts) > 1 else 1.0
            logger.info(f"   📊 Class distribution: {dict(zip(unique, counts))}")
            logger.info(f"   ⚖️  Class imbalance ratio: {class_ratio:.3f}")
            
            return X, y, class_ratio
            
        except Exception as e:
            logger.error(f"❌ Error loading data: {e}")
            raise
    
    def apply_sampling_strategy(self, X: pd.DataFrame, y: np.ndarray, strategy: str) -> Tuple[pd.DataFrame, np.ndarray]:
        """Apply different sampling strategies for class imbalance"""
        logger.info(f"🔄 Applying sampling strategy: {strategy}")
        
        if strategy == "none":
            return X, y
        
        # Initialize sampler based on strategy
        samplers = {
            "smote": SMOTE(random_state=self.random_state),
            "borderline_smote": BorderlineSMOTE(random_state=self.random_state),
            "adasyn": ADASYN(random_state=self.random_state),
            "smote_tomek": SMOTETomek(random_state=self.random_state),
            "smote_enn": SMOTEENN(random_state=self.random_state)
        }
        
        if strategy not in samplers:
            logger.warning(f"Unknown strategy {strategy}, using original data")
            return X, y
        
        try:
            sampler = samplers[strategy]
            X_resampled, y_resampled = sampler.fit_resample(X, y)
            
            logger.info(f"   Original: {X.shape[0]} samples")
            logger.info(f"   Resampled: {X_resampled.shape[0]} samples")
            logger.info(f"   New distribution: {pd.Series(y_resampled).value_counts().to_dict()}")
            
            return pd.DataFrame(X_resampled, columns=X.columns), y_resampled
            
        except Exception as e:
            logger.warning(f"Sampling failed: {e}, using original data")
            return X, y

    def optimize_hyperparameters_advanced(self, X: pd.DataFrame, y: np.ndarray, class_ratio: float, 
                                        sampling_strategy: str = "none") -> Dict:
        """Advanced hyperparameter optimization with class imbalance handling"""
        logger.info(f"🔧 Advanced hyperparameter optimization (sampling: {sampling_strategy})...")
        
        def objective(trial):
            # Suggest hyperparameters including class weighting
            params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'tree_method': 'hist',
                'max_bin': 128,
                'device': 'cuda',
                'booster': 'gbtree',
                'random_state': self.random_state,
                'verbosity': 0,
                'early_stopping_rounds': 50,
                # Class imbalance handling
                'scale_pos_weight': trial.suggest_float('scale_pos_weight', 1.0, 5.0) if sampling_strategy == "none" else 1.0,
                # Standard hyperparameters
                'n_estimators': trial.suggest_int('n_estimators', 800, 3000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'gamma': trial.suggest_float('gamma', 1e-8, 10.0, log=True)
            }
            
            # Cross-validation with sampling
            skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
            cv_scores = []
            best_iterations = []
            
            for train_idx, val_idx in skf.split(X, y):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # Apply sampling strategy to training data only
                X_train_sampled, y_train_sampled = self.apply_sampling_strategy(X_train, y_train, sampling_strategy)
                
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train_sampled)
                X_val_scaled = scaler.transform(X_val)
                
                # Train model with early stopping
                model = xgb.XGBClassifier(**params)
                model.fit(
                    X_train_scaled, y_train_sampled,
                    eval_set=[(X_val_scaled, y_val)],
                    verbose=False
                )
                
                best_iteration = model.best_iteration if hasattr(model, 'best_iteration') else params['n_estimators']
                best_iterations.append(best_iteration)
                
                # Predict and score
                y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
                score = roc_auc_score(y_val, y_pred_proba)
                cv_scores.append(score)
            
            # Store best iterations for this trial
            trial.set_user_attr('best_iterations', best_iterations)
            trial.set_user_attr('max_best_iteration', max(best_iterations))
            trial.set_user_attr('mean_best_iteration', np.mean(best_iterations))
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
            return np.mean(cv_scores)
        
        # Create and run study
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=self.random_state)
        )
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=True)
        
        logger.info(f"   ✅ Best AUC: {study.best_value:.4f}")
        logger.info(f"   📊 Best params: {study.best_params}")
        
        # Get optimal parameters
        best_trial = study.best_trial
        optimal_n_estimators = best_trial.user_attrs.get('max_best_iteration', best_trial.params.get('n_estimators', 100))
        
        # Store study for analysis
        self.pattern_studies[sampling_strategy] = study
        
        # Return best params
        best_params_with_optimal = study.best_params.copy()
        best_params_with_optimal['optimal_n_estimators'] = optimal_n_estimators
        best_params_with_optimal['cv_auc'] = study.best_value
        best_params_with_optimal['sampling_strategy'] = sampling_strategy
        
        return best_params_with_optimal
    
    def find_optimal_threshold(self, y_true: np.ndarray, y_pred_proba: np.ndarray) -> Dict:
        """Find optimal classification threshold using multiple criteria"""
        logger.info("🎯 Finding optimal classification threshold...")
        
        # Calculate ROC curve
        fpr, tpr, roc_thresholds = roc_curve(y_true, y_pred_proba)
        
        # Calculate Precision-Recall curve
        precision, recall, pr_thresholds = precision_recall_curve(y_true, y_pred_proba)
        
        # Method 1: Youden's J statistic (maximize TPR - FPR)
        j_scores = tpr - fpr
        youden_threshold = roc_thresholds[np.argmax(j_scores)]
        
        # Method 2: Maximize F1 score
        f1_scores = 2 * (precision * recall) / (precision + recall + 1e-8)
        f1_threshold = pr_thresholds[np.argmax(f1_scores)]
        
        # Method 3: Balance precision and recall
        pr_diff = np.abs(precision - recall)
        balanced_threshold = pr_thresholds[np.argmin(pr_diff)]
        
        # Method 4: Maximize geometric mean of sensitivity and specificity
        gmean_scores = np.sqrt(tpr * (1 - fpr))
        gmean_threshold = roc_thresholds[np.argmax(gmean_scores)]
        
        thresholds = {
            'youden': youden_threshold,
            'f1_optimal': f1_threshold,
            'balanced_pr': balanced_threshold,
            'gmean': gmean_threshold,
            'default': 0.5
        }
        
        logger.info(f"   🎯 Optimal thresholds:")
        for name, threshold in thresholds.items():
            logger.info(f"      {name}: {threshold:.4f}")
        
        return thresholds
    
    def evaluate_with_thresholds(self, X: pd.DataFrame, y: np.ndarray, best_params: Dict) -> Dict:
        """Evaluate model with different thresholds"""
        logger.info("📊 Evaluating with multiple thresholds...")
        
        sampling_strategy = best_params.get('sampling_strategy', 'none')
        optimal_n_estimators = best_params.get('optimal_n_estimators', 100)
        
        skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
        
        all_y_true = []
        all_y_pred_proba = []
        
        # Collect all predictions for threshold optimization
        for train_idx, val_idx in skf.split(X, y):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # Apply sampling strategy
            X_train_sampled, y_train_sampled = self.apply_sampling_strategy(X_train, y_train, sampling_strategy)
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train_sampled)
            X_val_scaled = scaler.transform(X_val)
            
            # Train model
            xgb_params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'booster': 'gbtree',
                'random_state': self.random_state,
                'verbosity': 0,
                'tree_method': 'hist',
                'device': 'cuda',
                'n_estimators': optimal_n_estimators,
                **{k: v for k, v in best_params.items() if k not in ['optimal_n_estimators', 'cv_auc', 'sampling_strategy']}
            }
            
            model = xgb.XGBClassifier(**xgb_params)
            model.fit(X_train_scaled, y_train_sampled, verbose=False)
            
            # Predictions
            y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
            
            all_y_true.extend(y_val)
            all_y_pred_proba.extend(y_pred_proba)
        
        # Find optimal thresholds
        optimal_thresholds = self.find_optimal_threshold(all_y_true, all_y_pred_proba)
        
        # Evaluate each threshold
        threshold_results = {}
        
        for threshold_name, threshold_value in optimal_thresholds.items():
            y_pred = (np.array(all_y_pred_proba) >= threshold_value).astype(int)
            
            # Calculate metrics
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            auc = roc_auc_score(all_y_true, all_y_pred_proba)
            accuracy = accuracy_score(all_y_true, y_pred)
            precision = precision_score(all_y_true, y_pred, zero_division=0)
            recall = recall_score(all_y_true, y_pred)
            f1 = f1_score(all_y_true, y_pred)
            
            # Confusion matrix
            tn, fp, fn, tp = confusion_matrix(all_y_true, y_pred).ravel()
            
            threshold_results[threshold_name] = {
                'threshold': threshold_value,
                'auc': auc,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1': f1,
                'tp': tp, 'tn': tn, 'fp': fp, 'fn': fn
            }
            
            logger.info(f"   {threshold_name:12s} (t={threshold_value:.3f}): AUC={auc:.4f}, F1={f1:.4f}, P={precision:.4f}, R={recall:.4f}")
        
        return {
            'threshold_results': threshold_results,
            'all_predictions': (all_y_true, all_y_pred_proba),
            'optimal_thresholds': optimal_thresholds
        }
    
    def create_ensemble_models(self, X: pd.DataFrame, y: np.ndarray, class_ratio: float) -> Dict:
        """Create ensemble of models with different strategies"""
        logger.info("🎭 Creating ensemble models with different strategies...")
        
        strategies = ["none", 'smote_tomek','smote_enn']# "smote", "borderline_smote"]
        ensemble_results = {}
        
        for strategy in strategies:
            logger.info(f"   Training model with strategy: {strategy}")
            
            # Optimize hyperparameters for this strategy
            best_params = self.optimize_hyperparameters_advanced(X, y, class_ratio, strategy)
            
            # Store results
            ensemble_results[strategy] = {
                'best_params': best_params,
                'cv_auc': best_params['cv_auc']
            }
            
            logger.info(f"      ✅ {strategy}: AUC = {best_params['cv_auc']:.4f}")
        
        # Find best strategy
        best_strategy = max(ensemble_results.keys(), key=lambda k: ensemble_results[k]['cv_auc'])
        logger.info(f"   🏆 Best strategy: {best_strategy} (AUC: {ensemble_results[best_strategy]['cv_auc']:.4f})")
        
        return ensemble_results, best_strategy
    
    def run_advanced_calibration(self):
        """Run complete advanced AUROC calibration pipeline"""
        logger.info("🚀 Starting advanced AUROC calibration pipeline...")
        
        # Load data
        X, y, class_ratio = self.load_data()
        
        # Create ensemble models with different strategies
        ensemble_results, best_strategy = self.create_ensemble_models(X, y, class_ratio)
        
        # Get best parameters
        best_params = ensemble_results[best_strategy]['best_params']
        
        # Evaluate with multiple thresholds
        threshold_evaluation = self.evaluate_with_thresholds(X, y, best_params)
        
        # Find best threshold based on F1 score
        best_threshold_name = max(threshold_evaluation['threshold_results'].keys(), 
                                key=lambda k: threshold_evaluation['threshold_results'][k]['f1'])
        best_threshold_results = threshold_evaluation['threshold_results'][best_threshold_name]
        
        # Calibrate probabilities for best model
        logger.info("🎯 Calibrating probabilities for best model...")
        calibration_results = self.calibrate_probabilities_advanced(X, y, best_params)
        
        # Print final results
        logger.info("\n🎉 ADVANCED CALIBRATION COMPLETED!")
        logger.info("=" * 80)
        logger.info(f"📊 FINAL RESULTS:")
        logger.info(f"   Best Strategy:     {best_strategy}")
        logger.info(f"   Best Threshold:    {best_threshold_name} ({best_threshold_results['threshold']:.3f})")
        logger.info(f"   ROC AUC:          {best_threshold_results['auc']:.4f}")
        logger.info(f"   F1 Score:         {best_threshold_results['f1']:.4f}")
        logger.info(f"   Precision:        {best_threshold_results['precision']:.4f}")
        logger.info(f"   Recall:           {best_threshold_results['recall']:.4f}")
        logger.info(f"   Accuracy:         {best_threshold_results['accuracy']:.4f}")
        
        logger.info(f"\n🎯 CONFUSION MATRIX (Best Threshold):")
        logger.info(f"   True Negatives:   {best_threshold_results['tn']:5d} | False Positives: {best_threshold_results['fp']:5d}")
        logger.info(f"   False Negatives:  {best_threshold_results['fn']:5d} | True Positives:  {best_threshold_results['tp']:5d}")
        
        # Compare all strategies
        logger.info(f"\n📈 STRATEGY COMPARISON:")
        for strategy, results in ensemble_results.items():
            logger.info(f"   {strategy:15s}: AUC = {results['cv_auc']:.4f}")
        
        return {
            'ensemble_results': ensemble_results,
            'best_strategy': best_strategy,
            'best_params': best_params,
            'threshold_evaluation': threshold_evaluation,
            'best_threshold_results': best_threshold_results,
            'calibration_results': calibration_results
        }
    
    def calibrate_probabilities_advanced(self, X: pd.DataFrame, y: np.ndarray, best_params: Dict) -> Dict:
        """Advanced probability calibration"""
        logger.info("🎯 Advanced probability calibration...")
        
        sampling_strategy = best_params.get('sampling_strategy', 'none')
        optimal_n_estimators = best_params.get('optimal_n_estimators', 100)
        
        # Apply sampling to full dataset
        X_sampled, y_sampled = self.apply_sampling_strategy(X, y, sampling_strategy)
        
        # Prepare base model
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'booster': 'gbtree',
            'random_state': self.random_state,
            'verbosity': 0,
            'tree_method': 'hist',
            'device': 'cuda',
            'n_estimators': optimal_n_estimators,
            **{k: v for k, v in best_params.items() if k not in ['optimal_n_estimators', 'cv_auc', 'sampling_strategy']}
        }
        
        base_model = xgb.XGBClassifier(**xgb_params)
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X_sampled)
        
        # Calibrate with cross-validation
        calibrated_model = CalibratedClassifierCV(
            base_model, 
            method='sigmoid',  # Platt scaling
            cv=self.n_folds
        )
        
        calibrated_model.fit(X_scaled, y_sampled)
        
        logger.info("   ✅ Advanced probability calibration completed")
        
        return {
            'calibrated_model': calibrated_model,
            'scaler': scaler,
            'base_params': xgb_params,
            'sampling_strategy': sampling_strategy
        }

def main():
    """Main execution function"""
    calibrator = AdvancedAUCROCCalibrator(n_folds=5, n_trials=20, random_state=42)  # Reduced trials for faster execution
    results = calibrator.run_advanced_calibration()
    
    # Save results
    import joblib
    joblib.dump(results, 'auroc_calibration_v2_results.joblib')
    logger.info("💾 Results saved to 'auroc_calibration_v2_results.joblib'")
    
    return results

if __name__ == "__main__":
    results = main()