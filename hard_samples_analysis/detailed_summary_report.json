{"total_samples": 10001, "analysis_overview": {"mean_prediction_variance": 0.0034727726164163588, "mean_misclassification_rate": 0.22021047895210483, "samples_with_high_misclassification": 2181, "samples_near_boundary": 4537}, "hardest_samples": {"top_10_by_variance": [{"sample_idx": 7937, "prediction_variance": 0.032820795, "misclassification_rate": 0.3874999999999999, "true_label": 1}, {"sample_idx": 532, "prediction_variance": 0.028545225, "misclassification_rate": 0.4875, "true_label": 0}, {"sample_idx": 6838, "prediction_variance": 0.027922928, "misclassification_rate": 0.4875, "true_label": 0}, {"sample_idx": 7069, "prediction_variance": 0.026039686, "misclassification_rate": 0.5375, "true_label": 0}, {"sample_idx": 199, "prediction_variance": 0.025623506, "misclassification_rate": 0.9125, "true_label": 0}, {"sample_idx": 4798, "prediction_variance": 0.025236715, "misclassification_rate": 0.5125, "true_label": 1}, {"sample_idx": 113, "prediction_variance": 0.025145376, "misclassification_rate": 0.475, "true_label": 1}, {"sample_idx": 9101, "prediction_variance": 0.024655078, "misclassification_rate": 0.1125, "true_label": 0}, {"sample_idx": 7104, "prediction_variance": 0.02457482, "misclassification_rate": 0.5875, "true_label": 0}, {"sample_idx": 3873, "prediction_variance": 0.023055542, "misclassification_rate": 0.0875, "true_label": 1}], "top_10_by_misclassification": [{"sample_idx": 6, "misclassification_rate": 1.0, "prediction_variance": 0.0028298083, "true_label": 1}, {"sample_idx": 66, "misclassification_rate": 1.0, "prediction_variance": 0.0025527028, "true_label": 1}, {"sample_idx": 84, "misclassification_rate": 1.0, "prediction_variance": 0.0023311344, "true_label": 1}, {"sample_idx": 88, "misclassification_rate": 1.0, "prediction_variance": 0.001533841, "true_label": 1}, {"sample_idx": 91, "misclassification_rate": 1.0, "prediction_variance": 0.0009100355, "true_label": 1}, {"sample_idx": 210, "misclassification_rate": 1.0, "prediction_variance": 0.0021940637, "true_label": 1}, {"sample_idx": 233, "misclassification_rate": 1.0, "prediction_variance": 0.0029478804, "true_label": 1}, {"sample_idx": 257, "misclassification_rate": 1.0, "prediction_variance": 0.00087625376, "true_label": 1}, {"sample_idx": 258, "misclassification_rate": 1.0, "prediction_variance": 0.002842322, "true_label": 1}, {"sample_idx": 346, "misclassification_rate": 1.0, "prediction_variance": 0.004469265, "true_label": 1}], "top_10_by_composite": [{"sample_idx": 45, "composite_difficulty": 0.6027795952744782, "prediction_variance": 0.00879624, "misclassification_rate": 1.0}, {"sample_idx": 770, "composite_difficulty": 0.602470084419474, "prediction_variance": 0.006929985, "misclassification_rate": 1.0}, {"sample_idx": 9587, "composite_difficulty": 0.6024559115525335, "prediction_variance": 0.006146998, "misclassification_rate": 1.0}, {"sample_idx": 5672, "composite_difficulty": 0.601757658738643, "prediction_variance": 0.009138036, "misclassification_rate": 1.0}, {"sample_idx": 8279, "composite_difficulty": 0.6015501509886235, "prediction_variance": 0.0076825046, "misclassification_rate": 1.0}, {"sample_idx": 4898, "composite_difficulty": 0.6014963950496167, "prediction_variance": 0.004957585, "misclassification_rate": 1.0}, {"sample_idx": 6062, "composite_difficulty": 0.6014686474576593, "prediction_variance": 0.005020688, "misclassification_rate": 1.0}, {"sample_idx": 9470, "composite_difficulty": 0.601366038993001, "prediction_variance": 0.004548547, "misclassification_rate": 1.0}, {"sample_idx": 6832, "composite_difficulty": 0.6011580264661461, "prediction_variance": 0.0063188984, "misclassification_rate": 1.0}, {"sample_idx": 6737, "composite_difficulty": 0.6010297570377588, "prediction_variance": 0.0026639784, "misclassification_rate": 1.0}]}, "categories": {"high_variance": {"count": 500, "sample_ids": [103, 108, 239, 296, 412, 468, 538, 555, 716, 984, 1054, 1238, 1298, 1360, 1483, 1536, 1587, 1653, 1817, 1951, 2199, 2208, 2253, 2269, 2356, 2505, 2591, 2666, 2744, 2816, 2878, 3257, 3367, 3400, 3471, 3505, 3512, 3588, 3628, 3661, 3672, 3758, 3819, 3823, 3853, 3861, 4041, 4081, 4173, 4184]}, "high_misclassification": {"count": 2181, "sample_ids": [6, 22, 33, 34, 66, 84, 88, 91, 103, 210, 233, 257, 258, 270, 276, 296, 346, 381, 414, 444, 447, 485, 538, 559, 567, 588, 606, 628, 643, 679, 680, 683, 690, 698, 716, 742, 756, 770, 810, 818, 876, 914, 925, 944, 968, 1005, 1019, 1047, 1062, 1075]}, "boundary_cases": {"count": 4537, "sample_ids": [7, 18, 20, 22, 24, 33, 34, 39, 43, 66, 83, 84, 102, 103, 108, 134, 176, 183, 192, 196, 239, 249, 261, 270, 273, 276, 296, 307, 320, 346, 351, 355, 356, 391, 392, 397, 409, 412, 447, 459, 468, 473, 485, 498, 522, 538, 547, 553, 555, 572]}, "feature_sensitive": {"count": 897, "sample_ids": [103, 108, 239, 261, 296, 320, 392, 412, 468, 538, 547, 716, 742, 770, 984, 1054, 1209, 1213, 1238, 1298, 1360, 1406, 1459, 1466, 1483, 1495, 1536, 1598, 1653, 1720, 1770, 1817, 1951, 1986, 2096, 2187, 2199, 2208, 2238, 2253, 2269, 2356, 2371, 2452, 2455, 2489, 2505, 2542, 2608, 2621]}, "composite_hard": {"count": 100, "sample_ids": [45, 770, 9587, 5672, 8279, 4898, 6062, 9470, 6832, 6737, 85, 8508, 483, 7895, 7375, 3025, 1654, 3738, 6794, 5747, 2271, 6712, 9524, 9057, 8856, 8640, 1267, 8280, 5249, 1436, 9510, 1125, 3107, 5868, 811, 7340, 3620, 341, 9969, 1884, 8663, 8060, 1865, 208, 9167, 9275, 8355, 4075, 7852, 8810]}}}