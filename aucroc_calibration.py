#!/usr/bin/env python3
"""
AUROC Calibration with GPU XGBoost
Full hyperparameter optimization using prepared features dataset
"""

import numpy as np
import pandas as pd
import xgboost as xgb
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
from sklearn.calibration import CalibratedClassifierCV
import logging
from typing import Dict
import warnings

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class AUCROCCalibrator:
    """GPU XGBoost AUROC calibration with Optuna hyperparameter optimization"""
    
    def __init__(self, n_folds=5, n_trials=100, random_state=42):
        self.n_folds = n_folds
        self.n_trials = n_trials
        self.random_state = random_state
        self.pattern_studies = {}
        
    def load_data(self):
        """Load prepared features and labels"""
        logger.info("📊 Loading prepared features and labels...")
        
        try:
            # Load features
            features_path = "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet"
            X = pd.read_parquet(features_path)
            logger.info(f"   ✅ Features loaded: {X.shape}")
            
            # Load labels
            y_data = pd.read_parquet('y_train.parquet')
            y = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
            logger.info(f"   ✅ Labels loaded: {len(y)}")
            logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
            
            return X, y
            
        except Exception as e:
            logger.error(f"❌ Error loading data: {e}")
            raise
    
    def optimize_hyperparameters(self, X: pd.DataFrame, y: np.ndarray, pattern: str = "full_dataset") -> Dict:
        """Optimize hyperparameters using Optuna with GPU XGBoost"""
        logger.info(f"🔧 Optimizing hyperparameters for pattern '{pattern}'...")
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'tree_method': 'hist',
                'device': 'cuda',
                'booster': 'gbtree',
                'random_state': self.random_state,
                'verbosity': 0,
                'early_stopping_rounds': 50,
                # Optuna suggestions
                'n_estimators': trial.suggest_int('n_estimators', 800, 3000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'gamma': trial.suggest_float('gamma', 1e-8, 10.0, log=True)
            }
            
            # Cross-validation
            skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
            cv_scores = []
            best_iterations = []
            
            for train_idx, val_idx in skf.split(X, y):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_val_scaled = scaler.transform(X_val)
                
                # Train model with early stopping
                model = xgb.XGBClassifier(**params)
                model.fit(
                    X_train_scaled, y_train,
                    eval_set=[(X_val_scaled, y_val)],
                    verbose=False
                )
                
                best_iteration = model.best_iteration if hasattr(model, 'best_iteration') else params['n_estimators']
                best_iterations.append(best_iteration)
                
                # Predict and score
                y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
                score = roc_auc_score(y_val, y_pred_proba)
                cv_scores.append(score)
            
            # Store best iterations for this trial
            trial.set_user_attr('best_iterations', best_iterations)
            trial.set_user_attr('max_best_iteration', max(best_iterations))
            trial.set_user_attr('mean_best_iteration', np.mean(best_iterations))
            
            return np.mean(cv_scores)
        
        # Create and run study
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=self.random_state)
        )
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=True)
        
        logger.info(f"   ✅ Best AUC for '{pattern}': {study.best_value:.4f}")
        logger.info(f"   📊 Best params: {study.best_params}")
        
        # Get optimal n_estimators from best trial
        best_trial = study.best_trial
        optimal_n_estimators = best_trial.user_attrs.get('max_best_iteration', best_trial.params.get('n_estimators', 100))
        logger.info(f"   🎯 Optimal n_estimators from Optuna: {optimal_n_estimators}")
        
        # Store study for analysis
        self.pattern_studies[pattern] = study
        
        # Return best params with optimal n_estimators and CV AUC
        best_params_with_optimal = study.best_params.copy()
        best_params_with_optimal['optimal_n_estimators'] = optimal_n_estimators
        best_params_with_optimal['cv_auc'] = study.best_value
        
        return best_params_with_optimal
    
    def train_pattern_model(self, X: pd.DataFrame, y: np.ndarray, pattern: str, best_params: Dict) -> Dict:
        """Train final model using Optuna-determined optimal parameters"""
        logger.info(f"🎯 Training final model for pattern '{pattern}' on full dataset...")
        
        # Get optimal n_estimators from Optuna study
        optimal_n_estimators = best_params.get('optimal_n_estimators', best_params.get('n_estimators', 100))
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Prepare XGBoost parameters with optimal n_estimators
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'booster': 'gbtree',
            'random_state': self.random_state,
            'verbosity': 0,
            'tree_method': 'hist',
            'device': 'cuda',
            'n_estimators': optimal_n_estimators,
            **{k: v for k, v in best_params.items() if k not in ['optimal_n_estimators', 'cv_auc']}
        }
        
        # Train model on full dataset
        model = xgb.XGBClassifier(**xgb_params)
        model.fit(X_scaled, y, verbose=False)
        
        # Use CV validation AUC from Optuna (realistic performance)
        cv_auc = best_params.get('cv_auc', 0.5)
        
        # Summary statistics
        results_summary = {
            'pattern': pattern,
            'n_samples': len(y),
            'n_features': X.shape[1],
            'label_distribution': pd.Series(y).value_counts().to_dict(),
            'train_auc': cv_auc,
            'optimal_n_estimators': optimal_n_estimators,
            'model': model,
            'scaler': scaler,
            'feature_importance': model.feature_importances_,
            'best_params': best_params
        }
        
        logger.info(f"   ✅ Pattern '{pattern}' training completed:")
        logger.info(f"      CV AUC: {cv_auc:.4f}")
        logger.info(f"      Optimal n_estimators: {optimal_n_estimators}")
        logger.info(f"      Features: {X.shape[1]}")
        
        return results_summary
    
    def evaluate_model_cv(self, X: pd.DataFrame, y: np.ndarray, best_params: Dict) -> Dict:
        """Comprehensive cross-validation evaluation"""
        logger.info("📊 Performing comprehensive CV evaluation...")
        
        skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
        
        cv_metrics = {
            'auc': [], 'accuracy': [], 'precision': [], 'recall': [], 'f1': [],
            'tp': [], 'tn': [], 'fp': [], 'fn': []
        }
        
        all_y_true = []
        all_y_pred_proba = []
        feature_importances = []
        
        optimal_n_estimators = best_params.get('optimal_n_estimators', best_params.get('n_estimators', 100))
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
            logger.info(f"   Fold {fold + 1}/{self.n_folds}...")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Train model
            xgb_params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'booster': 'gbtree',
                'random_state': self.random_state,
                'verbosity': 0,
                'tree_method': 'hist',
                'device': 'cuda',
                'n_estimators': optimal_n_estimators,
                **{k: v for k, v in best_params.items() if k not in ['optimal_n_estimators', 'cv_auc']}
            }
            
            model = xgb.XGBClassifier(**xgb_params)
            model.fit(X_train_scaled, y_train, verbose=False)
            
            # Predictions
            y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
            y_pred = (y_pred_proba >= 0.5).astype(int)
            
            # Store for overall analysis
            all_y_true.extend(y_val)
            all_y_pred_proba.extend(y_pred_proba)
            
            # Calculate metrics
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
            
            auc = roc_auc_score(y_val, y_pred_proba)
            accuracy = accuracy_score(y_val, y_pred)
            precision = precision_score(y_val, y_pred, zero_division=0)
            recall = recall_score(y_val, y_pred)
            f1 = f1_score(y_val, y_pred)
            
            # Confusion matrix
            tn, fp, fn, tp = confusion_matrix(y_val, y_pred).ravel()
            
            # Store metrics
            cv_metrics['auc'].append(auc)
            cv_metrics['accuracy'].append(accuracy)
            cv_metrics['precision'].append(precision)
            cv_metrics['recall'].append(recall)
            cv_metrics['f1'].append(f1)
            cv_metrics['tp'].append(tp)
            cv_metrics['tn'].append(tn)
            cv_metrics['fp'].append(fp)
            cv_metrics['fn'].append(fn)
            
            # Store feature importances
            feature_importances.append(model.feature_importances_)
            
            logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
        
        # Calculate mean metrics
        mean_metrics = {metric: np.mean(values) for metric, values in cv_metrics.items()}
        std_metrics = {metric: np.std(values) for metric, values in cv_metrics.items()}
        
        # Overall confusion matrix
        all_y_pred = (np.array(all_y_pred_proba) >= 0.5).astype(int)
        overall_cm = confusion_matrix(all_y_true, all_y_pred)
        
        # Feature importance analysis
        mean_importances = np.mean(feature_importances, axis=0)
        importance_df = pd.DataFrame({
            'feature': X.columns,
            'importance': mean_importances
        }).sort_values('importance', ascending=False)
        
        return {
            'cv_metrics': cv_metrics,
            'mean_metrics': mean_metrics,
            'std_metrics': std_metrics,
            'confusion_matrix': overall_cm,
            'feature_importances': importance_df,
            'all_predictions': (all_y_true, all_y_pred_proba)
        }
    
    def calibrate_probabilities(self, X: pd.DataFrame, y: np.ndarray, best_params: Dict) -> Dict:
        """Calibrate model probabilities using Platt scaling"""
        logger.info("🎯 Calibrating model probabilities...")
        
        optimal_n_estimators = best_params.get('optimal_n_estimators', best_params.get('n_estimators', 100))
        
        # Prepare base model
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'booster': 'gbtree',
            'random_state': self.random_state,
            'verbosity': 0,
            'tree_method': 'hist',
            'device': 'cuda',
            'n_estimators': optimal_n_estimators,
            **{k: v for k, v in best_params.items() if k not in ['optimal_n_estimators', 'cv_auc']}
        }
        
        base_model = xgb.XGBClassifier(**xgb_params)
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Calibrate with cross-validation
        calibrated_model = CalibratedClassifierCV(
            base_model, 
            method='sigmoid',  # Platt scaling
            cv=self.n_folds
        )
        
        calibrated_model.fit(X_scaled, y)
        
        logger.info("   ✅ Probability calibration completed")
        
        return {
            'calibrated_model': calibrated_model,
            'scaler': scaler,
            'base_params': xgb_params
        }
    
    def run_full_calibration(self):
        """Run complete AUROC calibration pipeline"""
        logger.info("🚀 Starting full AUROC calibration pipeline...")
        
        # Load data
        X, y = self.load_data()
        
        # Optimize hyperparameters
        best_params = self.optimize_hyperparameters(X, y)
        
        # Train final model
        model_results = self.train_pattern_model(X, y, "full_dataset", best_params)
        
        # Comprehensive evaluation
        cv_results = self.evaluate_model_cv(X, y, best_params)
        
        # Calibrate probabilities
        calibration_results = self.calibrate_probabilities(X, y, best_params)
        
        # Print final results
        logger.info("\n🎉 CALIBRATION COMPLETED!")
        logger.info("=" * 60)
        logger.info(f"📊 FINAL RESULTS:")
        logger.info(f"   ROC AUC:    {cv_results['mean_metrics']['auc']:.4f} ± {cv_results['std_metrics']['auc']:.4f}")
        logger.info(f"   F1 Score:   {cv_results['mean_metrics']['f1']:.4f} ± {cv_results['std_metrics']['f1']:.4f}")
        logger.info(f"   Precision:  {cv_results['mean_metrics']['precision']:.4f} ± {cv_results['std_metrics']['precision']:.4f}")
        logger.info(f"   Recall:     {cv_results['mean_metrics']['recall']:.4f} ± {cv_results['std_metrics']['recall']:.4f}")
        logger.info(f"   Accuracy:   {cv_results['mean_metrics']['accuracy']:.4f} ± {cv_results['std_metrics']['accuracy']:.4f}")
        
        # Top features
        logger.info(f"\n🏆 TOP 10 FEATURES:")
        for i, (_, row) in enumerate(cv_results['feature_importances'].head(10).iterrows()):
            logger.info(f"   {i+1:2d}. {row['feature']:50s} {row['importance']:.4f}")
        
        return {
            'model_results': model_results,
            'cv_results': cv_results,
            'calibration_results': calibration_results,
            'best_params': best_params
        }

def main():
    """Main execution function"""
    calibrator = AUCROCCalibrator(n_folds=5, n_trials=20, random_state=42)
    results = calibrator.run_full_calibration()
    
    # Save results
    import joblib
    joblib.dump(results, 'auroc_calibration_results.joblib')
    logger.info("💾 Results saved to 'auroc_calibration_results.joblib'")
    
    return results

if __name__ == "__main__":
    results = main()