#!/usr/bin/env python3
"""
Improved Single Continuous Time Series Finder
More sophisticated algorithm with relaxed but intelligent matching criteria
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import DBSCAN
import networkx as nx
import warnings
from typing import Dict, List, Tuple, Optional
from collections import defaultdict

warnings.filterwarnings('ignore')

class ImprovedSingleSeriesFinder:
    """
    Improved algorithm to find single continuous time series with adaptive matching
    """
    
    def __init__(self, verbose: bool = True):
        self.verbose = verbose
        self.data = None
        self.time_series_dict = {}
        self.connection_candidates = []
        self.best_reconstruction = None
        
    def load_data(self, sample_size: int = 1000):
        """Load time series data"""
        if self.verbose:
            print("📊 Loading time series data for improved single series finding...")
        
        try:
            self.data = pd.read_parquet('X_train.parquet')
            if isinstance(self.data.index, pd.MultiIndex):
                self.data = self.data.reset_index()
            
            grouped = self.data.groupby('id')
            all_ids = list(grouped.groups.keys())
            
            if len(all_ids) > sample_size:
                sampled_ids = np.random.choice(all_ids, sample_size, replace=False)
            else:
                sampled_ids = all_ids
            
            for series_id in sampled_ids:
                series_data = grouped.get_group(series_id).sort_values('time')
                
                values = series_data['value'].values
                periods = series_data['period'].values
                times = series_data['time'].values
                
                # Enhanced statistics
                self.time_series_dict[series_id] = {
                    'values': values,
                    'periods': periods,
                    'times': times,
                    'length': len(values),
                    'start_value': values[0],
                    'end_value': values[-1],
                    'mean': np.mean(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'trend': np.polyfit(range(len(values)), values, 1)[0] if len(values) > 1 else 0,
                    'start_trend': np.polyfit(range(min(50, len(values))), values[:min(50, len(values))], 1)[0] if len(values) > 10 else 0,
                    'end_trend': np.polyfit(range(min(50, len(values))), values[-min(50, len(values)):], 1)[0] if len(values) > 10 else 0,
                    'volatility': np.std(np.diff(values)) if len(values) > 1 else 0,
                    'normal_ratio': np.mean(periods == 0),
                    'candidate_ratio': np.mean(periods == 1)
                }
            
            if self.verbose:
                lengths = [ts['length'] for ts in self.time_series_dict.values()]
                print(f"   ✅ Loaded {len(self.time_series_dict)} time series")
                print(f"   ✅ Length range: {min(lengths)} - {max(lengths)}")
                print(f"   ✅ Average length: {np.mean(lengths):.1f}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return False
    
    def find_adaptive_connections(self, window_size: int = 50):
        """Find connections using adaptive matching criteria"""
        if self.verbose:
            print(f"\n🔍 Finding adaptive connections (window: {window_size})...")
        
        connection_candidates = []
        series_list = list(self.time_series_dict.items())
        
        for i, (id1, ts1) in enumerate(series_list):
            for j, (id2, ts2) in enumerate(series_list):
                if i == j:
                    continue
                
                if len(ts1['values']) < window_size or len(ts2['values']) < window_size:
                    continue
                
                # Multi-level connection analysis
                connection_score = self._evaluate_adaptive_connection(ts1, ts2, window_size)
                
                if connection_score['is_candidate']:
                    connection_candidates.append({
                        'from_id': id1,
                        'to_id': id2,
                        'score': connection_score['total_score'],
                        'value_match': connection_score['value_match'],
                        'pattern_match': connection_score['pattern_match'],
                        'characteristic_match': connection_score['characteristic_match'],
                        'trend_match': connection_score['trend_match'],
                        'details': connection_score
                    })
        
        # Sort by score
        connection_candidates.sort(key=lambda x: x['score'], reverse=True)
        
        # Filter to keep only the best connections
        top_connections = connection_candidates[:min(100, len(connection_candidates))]
        
        if self.verbose:
            print(f"   📊 Connection candidates found: {len(connection_candidates)}")
            print(f"   📊 Top connections kept: {len(top_connections)}")
            if top_connections:
                print(f"   📊 Best connection score: {top_connections[0]['score']:.4f}")
                print(f"   📊 Best connection: {top_connections[0]['from_id']} → {top_connections[0]['to_id']}")
        
        self.connection_candidates = top_connections
        return top_connections
    
    def _evaluate_adaptive_connection(self, ts1: Dict, ts2: Dict, window_size: int) -> Dict:
        """Evaluate connection using adaptive criteria"""
        
        # Extract comparison windows
        ending = ts1['values'][-window_size:]
        beginning = ts2['values'][:window_size]
        
        # 1. Value continuity (adaptive threshold)
        value_gap = abs(ts1['end_value'] - ts2['start_value'])
        combined_std = (ts1['std'] + ts2['std']) / 2
        value_threshold = max(0.01, combined_std * 0.2)  # Adaptive threshold
        value_match = value_gap < value_threshold
        value_score = max(0, 1 - value_gap / value_threshold)
        
        # 2. Pattern similarity
        if np.std(ending) > 0 and np.std(beginning) > 0:
            pattern_correlation = np.corrcoef(ending, beginning)[0, 1]
            if np.isnan(pattern_correlation):
                pattern_correlation = 0
        else:
            pattern_correlation = 0
        
        pattern_match = abs(pattern_correlation) > 0.3  # Relaxed threshold
        pattern_score = abs(pattern_correlation)
        
        # 3. Trend continuity
        trend_diff = abs(ts1['end_trend'] - ts2['start_trend'])
        trend_threshold = max(0.001, abs(ts1['trend']) * 0.5)
        trend_match = trend_diff < trend_threshold
        trend_score = max(0, 1 - trend_diff / trend_threshold)
        
        # 4. Statistical characteristics similarity
        mean_diff = abs(ts1['mean'] - ts2['mean'])
        mean_threshold = combined_std * 0.5
        mean_match = mean_diff < mean_threshold
        
        std_ratio = min(ts1['std'], ts2['std']) / max(ts1['std'], ts2['std']) if max(ts1['std'], ts2['std']) > 0 else 0
        std_match = std_ratio > 0.5
        
        volatility_ratio = min(ts1['volatility'], ts2['volatility']) / max(ts1['volatility'], ts2['volatility']) if max(ts1['volatility'], ts2['volatility']) > 0 else 0
        volatility_match = volatility_ratio > 0.5
        
        characteristic_match = mean_match and std_match and volatility_match
        characteristic_score = (
            max(0, 1 - mean_diff / mean_threshold) * 0.4 +
            std_ratio * 0.3 +
            volatility_ratio * 0.3
        )
        
        # 5. Range compatibility
        range_overlap = max(0, min(ts1['max'], ts2['max']) - max(ts1['min'], ts2['min']))
        range1 = ts1['max'] - ts1['min']
        range2 = ts2['max'] - ts2['min']
        range_score = range_overlap / min(range1, range2) if min(range1, range2) > 0 else 0
        
        # 6. Period compatibility
        period_diff = abs(ts1['normal_ratio'] - ts2['normal_ratio'])
        period_score = max(0, 1 - period_diff * 2)  # Penalize large differences
        
        # Total score (weighted combination)
        total_score = (
            value_score * 0.25 +
            pattern_score * 0.20 +
            trend_score * 0.15 +
            characteristic_score * 0.20 +
            range_score * 0.10 +
            period_score * 0.10
        )
        
        # Adaptive candidacy criteria
        is_candidate = (
            total_score > 0.4 and  # Minimum overall score
            (value_match or pattern_match) and  # At least one strong indicator
            characteristic_score > 0.3  # Basic characteristic similarity
        )
        
        return {
            'is_candidate': is_candidate,
            'total_score': total_score,
            'value_match': value_match,
            'pattern_match': pattern_match,
            'characteristic_match': characteristic_match,
            'trend_match': trend_match,
            'value_score': value_score,
            'pattern_score': pattern_score,
            'trend_score': trend_score,
            'characteristic_score': characteristic_score,
            'range_score': range_score,
            'period_score': period_score,
            'value_gap': value_gap,
            'pattern_correlation': pattern_correlation,
            'trend_diff': trend_diff
        }
    
    def build_connection_graph(self):
        """Build graph from connection candidates"""
        if self.verbose:
            print("\n🔗 Building connection graph...")
        
        graph = nx.DiGraph()
        
        # Add nodes
        for series_id in self.time_series_dict.keys():
            graph.add_node(series_id, **self.time_series_dict[series_id])
        
        # Add edges
        for connection in self.connection_candidates:
            graph.add_edge(
                connection['from_id'],
                connection['to_id'],
                weight=connection['score'],
                **connection
            )
        
        if self.verbose:
            print(f"   📊 Graph nodes: {graph.number_of_nodes()}")
            print(f"   📊 Graph edges: {graph.number_of_edges()}")
        
        return graph
    
    def find_best_chains(self, graph: nx.DiGraph, min_length: int = 3):
        """Find the best chains in the connection graph"""
        if self.verbose:
            print(f"\n🔍 Finding best chains (min length: {min_length})...")
        
        # Find all simple paths
        all_chains = []
        
        # Start from nodes with no incoming edges or low in-degree
        start_candidates = [n for n in graph.nodes() if graph.in_degree(n) <= 1]
        
        if not start_candidates:
            start_candidates = list(graph.nodes())
        
        for start_node in start_candidates:
            # Find paths from this start node
            for target_node in graph.nodes():
                if start_node != target_node:
                    try:
                        # Find all simple paths
                        paths = list(nx.all_simple_paths(graph, start_node, target_node, cutoff=10))
                        for path in paths:
                            if len(path) >= min_length:
                                # Calculate chain quality
                                chain_score = self._evaluate_chain_quality(graph, path)
                                all_chains.append({
                                    'path': path,
                                    'length': len(path),
                                    'score': chain_score,
                                    'total_length': sum(self.time_series_dict[node]['length'] for node in path)
                                })
                    except nx.NetworkXNoPath:
                        continue
        
        # Sort by score
        all_chains.sort(key=lambda x: x['score'], reverse=True)
        
        # Remove duplicate paths
        unique_chains = []
        seen_paths = set()
        
        for chain in all_chains:
            path_tuple = tuple(chain['path'])
            if path_tuple not in seen_paths:
                unique_chains.append(chain)
                seen_paths.add(path_tuple)
        
        if self.verbose:
            print(f"   📊 Total chains found: {len(all_chains)}")
            print(f"   📊 Unique chains: {len(unique_chains)}")
            if unique_chains:
                best = unique_chains[0]
                print(f"   📊 Best chain: {len(best['path'])} fragments, score: {best['score']:.4f}")
                print(f"   📊 Best chain path: {' → '.join(map(str, best['path']))}")
                print(f"   📊 Total reconstructed length: {best['total_length']:,} points")
        
        return unique_chains
    
    def _evaluate_chain_quality(self, graph: nx.DiGraph, path: List[int]) -> float:
        """Evaluate the quality of a chain"""
        
        if len(path) < 2:
            return 0
        
        # Get connection scores
        connection_scores = []
        for i in range(len(path) - 1):
            edge_data = graph.get_edge_data(path[i], path[i + 1])
            if edge_data:
                connection_scores.append(edge_data['score'])
            else:
                connection_scores.append(0)
        
        # Chain quality metrics
        avg_connection_score = np.mean(connection_scores) if connection_scores else 0
        min_connection_score = np.min(connection_scores) if connection_scores else 0
        
        # Length bonus (prefer longer chains)
        length_bonus = min(1.0, len(path) / 10)
        
        # Consistency bonus (prefer consistent connection quality)
        consistency_bonus = 1 - np.std(connection_scores) if len(connection_scores) > 1 else 1
        
        # Total reconstructed length bonus
        total_length = sum(self.time_series_dict[node]['length'] for node in path)
        length_score = min(1.0, total_length / 10000)
        
        # Combined score
        chain_score = (
            avg_connection_score * 0.4 +
            min_connection_score * 0.2 +
            length_bonus * 0.2 +
            consistency_bonus * 0.1 +
            length_score * 0.1
        )
        
        return chain_score
    
    def reconstruct_from_chain(self, chain_path: List[int]):
        """Reconstruct time series from chain"""
        if self.verbose:
            print(f"\n🔧 Reconstructing from chain: {' → '.join(map(str, chain_path))}")
        
        if len(chain_path) < 2:
            return None
        
        # Reconstruct
        reconstructed_values = []
        reconstructed_periods = []
        reconstructed_times = []
        fragment_info = []
        
        current_time = 0
        
        for i, fragment_id in enumerate(chain_path):
            fragment = self.time_series_dict[fragment_id]
            
            start_idx = len(reconstructed_values)
            
            # Add fragment
            reconstructed_values.extend(fragment['values'])
            reconstructed_periods.extend(fragment['periods'])
            
            # Create continuous time
            fragment_times = list(range(current_time, current_time + len(fragment['values'])))
            reconstructed_times.extend(fragment_times)
            current_time += len(fragment['values'])
            
            fragment_info.append({
                'fragment_id': fragment_id,
                'start_idx': start_idx,
                'end_idx': len(reconstructed_values) - 1,
                'length': len(fragment['values'])
            })
        
        # Calculate quality
        quality = self._calculate_reconstruction_quality(
            reconstructed_values, reconstructed_periods, chain_path
        )
        
        reconstruction = {
            'chain_path': chain_path,
            'values': np.array(reconstructed_values),
            'periods': np.array(reconstructed_periods),
            'times': np.array(reconstructed_times),
            'fragment_info': fragment_info,
            'total_length': len(reconstructed_values),
            'n_fragments': len(chain_path),
            'quality': quality
        }
        
        if self.verbose:
            print(f"   ✅ Reconstructed length: {len(reconstructed_values):,} points")
            print(f"   ✅ Quality score: {quality['overall']:.4f}")
        
        return reconstruction
    
    def _calculate_reconstruction_quality(self, values: List[float], periods: List[int], 
                                        chain_path: List[int]) -> Dict:
        """Calculate reconstruction quality"""
        
        values = np.array(values)
        periods = np.array(periods)
        
        # Smoothness
        diffs = np.abs(np.diff(values))
        smoothness = 1 / (1 + np.mean(diffs))
        
        # Statistical consistency
        fragment_means = [np.mean(self.time_series_dict[fid]['values']) for fid in chain_path]
        fragment_stds = [np.std(self.time_series_dict[fid]['values']) for fid in chain_path]
        
        mean_consistency = 1 - np.std(fragment_means) / (np.mean(fragment_means) + 1e-8)
        std_consistency = 1 - np.std(fragment_stds) / (np.mean(fragment_stds) + 1e-8)
        
        # Length score
        length_score = min(1.0, len(values) / 5000)
        
        # Period distribution
        normal_ratio = np.mean(periods == 0)
        period_score = min(1.0, normal_ratio / 0.7)
        
        # Overall
        overall = (
            smoothness * 0.3 +
            (mean_consistency + std_consistency) / 2 * 0.3 +
            length_score * 0.2 +
            period_score * 0.2
        )
        
        return {
            'overall': overall,
            'smoothness': smoothness,
            'mean_consistency': mean_consistency,
            'std_consistency': std_consistency,
            'length_score': length_score,
            'period_score': period_score
        }
    
    def plot_best_reconstruction(self, reconstruction: Dict, save_path: str = "best_single_series.png"):
        """Plot the best reconstruction"""
        if self.verbose:
            print("\n📊 Plotting best reconstruction...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 10))
        fig.suptitle('Best Single Continuous Time Series Reconstruction', fontsize=16, fontweight='bold')
        
        values = reconstruction['values']
        periods = reconstruction['periods']
        times = reconstruction['times']
        fragment_info = reconstruction['fragment_info']
        
        # Plot 1: Full series
        ax1 = axes[0, 0]
        
        normal_mask = periods == 0
        candidate_mask = periods == 1
        
        ax1.plot(times[normal_mask], values[normal_mask], 'b.', alpha=0.6, markersize=0.3, label='Normal')
        ax1.plot(times[candidate_mask], values[candidate_mask], 'r.', alpha=0.6, markersize=0.3, label='Candidate')
        
        # Mark fragment boundaries
        for i, frag in enumerate(fragment_info[1:], 1):
            ax1.axvline(x=times[frag['start_idx']], color='green', linestyle='--', alpha=0.7)
        
        ax1.set_xlabel('Time')
        ax1.set_ylabel('Value')
        ax1.set_title(f'Reconstructed Series ({len(values):,} points)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot 2: Fragment connections
        ax2 = axes[0, 1]
        
        # Show first few connections in detail
        for i in range(min(3, len(fragment_info) - 1)):
            frag1 = fragment_info[i]
            frag2 = fragment_info[i + 1]
            
            # Get connection region
            start_idx = max(0, frag1['end_idx'] - 50)
            end_idx = min(len(values), frag2['start_idx'] + 50)
            
            conn_times = times[start_idx:end_idx]
            conn_values = values[start_idx:end_idx]
            conn_periods = periods[start_idx:end_idx]
            
            normal_mask_conn = conn_periods == 0
            candidate_mask_conn = conn_periods == 1
            
            ax2.plot(conn_times[normal_mask_conn], conn_values[normal_mask_conn], 'b-', alpha=0.7, linewidth=1)
            ax2.plot(conn_times[candidate_mask_conn], conn_values[candidate_mask_conn], 'r-', alpha=0.7, linewidth=1)
            ax2.axvline(x=times[frag2['start_idx']], color='green', linestyle='--', alpha=0.8)
        
        ax2.set_xlabel('Time')
        ax2.set_ylabel('Value')
        ax2.set_title('Fragment Connections (First 3)')
        ax2.grid(True, alpha=0.3)
        
        # Plot 3: Quality metrics
        ax3 = axes[0, 2]
        
        quality = reconstruction['quality']
        metrics = ['Overall', 'Smoothness', 'Mean Consistency', 'Std Consistency', 'Length', 'Period']
        scores = [
            quality['overall'],
            quality['smoothness'],
            quality['mean_consistency'],
            quality['std_consistency'],
            quality['length_score'],
            quality['period_score']
        ]
        
        bars = ax3.bar(metrics, scores, alpha=0.7)
        ax3.set_ylabel('Score')
        ax3.set_title('Quality Metrics')
        ax3.set_ylim(0, 1)
        plt.setp(ax3.get_xticklabels(), rotation=45, ha='right')
        ax3.grid(True, alpha=0.3, axis='y')
        
        # Plot 4: Fragment lengths
        ax4 = axes[1, 0]
        
        fragment_lengths = [f['length'] for f in fragment_info]
        fragment_ids = [f['fragment_id'] for f in fragment_info]
        
        bars = ax4.bar(range(len(fragment_lengths)), fragment_lengths, alpha=0.7)
        ax4.set_xlabel('Fragment Index')
        ax4.set_ylabel('Length')
        ax4.set_title('Fragment Lengths')
        ax4.grid(True, alpha=0.3, axis='y')
        
        # Add fragment IDs
        for i, (bar, fid) in enumerate(zip(bars, fragment_ids)):
            ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(fragment_lengths)*0.01, 
                    str(fid), ha='center', va='bottom', fontsize=8, rotation=90)
        
        # Plot 5: Value distribution
        ax5 = axes[1, 1]
        ax5.hist(values, bins=50, alpha=0.7, edgecolor='black', density=True)
        ax5.set_xlabel('Value')
        ax5.set_ylabel('Density')
        ax5.set_title('Value Distribution')
        ax5.grid(True, alpha=0.3)
        
        # Plot 6: Summary info
        ax6 = axes[1, 2]
        ax6.axis('off')
        
        summary_text = f"""Reconstruction Summary:
        
Fragments: {len(fragment_info)}
Total Length: {len(values):,} points
Quality Score: {quality['overall']:.3f}

Fragment IDs:
{' → '.join(map(str, reconstruction['chain_path']))}

Quality Breakdown:
• Smoothness: {quality['smoothness']:.3f}
• Mean Consistency: {quality['mean_consistency']:.3f}
• Std Consistency: {quality['std_consistency']:.3f}
• Length Score: {quality['length_score']:.3f}
• Period Score: {quality['period_score']:.3f}

Period Distribution:
• Normal: {np.sum(periods == 0):,} ({np.mean(periods == 0)*100:.1f}%)
• Candidate: {np.sum(periods == 1):,} ({np.mean(periods == 1)*100:.1f}%)"""
        
        ax6.text(0.05, 0.95, summary_text, transform=ax6.transAxes, fontsize=10,
                verticalalignment='top', fontfamily='monospace',
                bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
        ax6.set_title('Summary')
        
        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        
        if self.verbose:
            print(f"   ✅ Plot saved to {save_path}")
    
    def run_improved_reconstruction(self, sample_size: int = 1000, window_size: int = 50):
        """Run the complete improved reconstruction algorithm"""
        print("🚀 IMPROVED SINGLE CONTINUOUS TIME SERIES FINDER")
        print("="*70)
        
        # Load data
        if not self.load_data(sample_size):
            return None
        
        # Find adaptive connections
        connections = self.find_adaptive_connections(window_size)
        
        if not connections:
            print("❌ No connections found")
            return None
        
        # Build graph
        graph = self.build_connection_graph()
        
        # Find best chains
        chains = self.find_best_chains(graph, min_length=3)
        
        if not chains:
            print("❌ No chains found")
            return None
        
        # Reconstruct best chain
        best_chain = chains[0]
        reconstruction = self.reconstruct_from_chain(best_chain['path'])
        
        if not reconstruction:
            print("❌ Reconstruction failed")
            return None
        
        # Plot results
        self.plot_best_reconstruction(reconstruction)
        
        # Store result
        self.best_reconstruction = {
            'reconstruction': reconstruction,
            'chain_info': best_chain,
            'all_chains': chains[:10],  # Top 10 chains
            'connections': connections[:20]  # Top 20 connections
        }
        
        # Summary
        print(f"\n📋 IMPROVED RECONSTRUCTION SUMMARY:")
        print(f"   ✅ Best chain: {len(best_chain['path'])} fragments")
        print(f"   ✅ Chain score: {best_chain['score']:.4f}")
        print(f"   ✅ Total length: {reconstruction['total_length']:,} points")
        print(f"   ✅ Quality score: {reconstruction['quality']['overall']:.4f}")
        print(f"   ✅ Fragment path: {' → '.join(map(str, best_chain['path']))}")
        
        return self.best_reconstruction

def main():
    """Run improved single series finder"""
    finder = ImprovedSingleSeriesFinder(verbose=True)
    result = finder.run_improved_reconstruction(
        sample_size=1000,
        window_size=50
    )
    return finder, result

if __name__ == "__main__":
    finder, result = main()