#!/usr/bin/env python3
"""
Temporal Position Probing v3
Compare quantile-based segments and analyze by volatility clusters
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
from multiprocessing import Pool, cpu_count
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

try:
    import xgboost as xgb
    XGB_AVAILABLE = True
except ImportError:
    from sklearn.ensemble import RandomForestClassifier
    XGB_AVAILABLE = False
    print("⚠️  XGBoost not available, using RandomForest")

def load_data_with_clusters():
    """Load time series data, labels, and cluster assignments"""
    print("📊 Loading data with cluster assignments...")
    
    # Load time series and labels
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    # Load cluster assignments and volatility patterns
    try:
        clusters_df = pd.read_csv('time_series_statistics_with_clusters.csv')
        cluster_map = dict(zip(clusters_df['series_id'], clusters_df['cluster']))
        print(f"   ✅ Loaded cluster assignments for {len(cluster_map)} series")
    except:
        print("   ⚠️  No cluster file found, proceeding without clusters")
        cluster_map = {}
    
    # Load volatility patterns if available
    try:
        volatility_df = pd.read_csv('volatility_pattern_analysis.csv')
        volatility_map = dict(zip(volatility_df['series_id'], volatility_df['pattern_type']))
        print(f"   ✅ Loaded volatility patterns for {len(volatility_map)} series")
    except:
        print("   ⚠️  No volatility patterns found")
        volatility_map = {}
    
    time_series_list = []
    labels = []
    clusters = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for i, (id_, group) in enumerate(grouped):
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
            clusters.append(cluster_map.get(i, -1))  # -1 for unknown cluster
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
            clusters.append(cluster_map.get(idx, -1))
    
    print(f"   ✅ Loaded {len(time_series_list)} series with labels and clusters")
    
    # Use volatility patterns if available, otherwise use cluster mapping
    if volatility_map:
        pattern_types = [volatility_map.get(i, 'unknown') for i in range(len(time_series_list))]
    else:
        # Map cluster numbers to pattern names (fixed mapping)
        cluster_names = {0: 'spiky', 1: 'volatile', 2: 'moderate', 3: 'calm', 4: 'clipped'}
        pattern_types = [cluster_names.get(c, 'unknown') for c in clusters]
    
    pattern_counts = pd.Series(pattern_types).value_counts()
    print(f"   📊 Pattern distribution: {pattern_counts.to_dict()}")
    
    return time_series_list, np.array(labels), np.array(clusters), pattern_types

def compute_comparison_features(normal_segment, post_segment):
    """Compute comparison features between segments"""
    if len(normal_segment) < 3 or len(post_segment) < 3:
        return {}
    
    features = {
        'mean_diff': np.mean(post_segment) - np.mean(normal_segment),
        'std_diff': np.std(post_segment, ddof=1) - np.std(normal_segment, ddof=1),
        'var_ratio': np.var(post_segment, ddof=1) / np.var(normal_segment, ddof=1) if np.var(normal_segment, ddof=1) > 0 else 1,
        'range_diff': (np.max(post_segment) - np.min(post_segment)) - (np.max(normal_segment) - np.min(normal_segment)),
        'cv_ratio': (np.std(post_segment, ddof=1) / abs(np.mean(post_segment))) / (np.std(normal_segment, ddof=1) / abs(np.mean(normal_segment))) if np.mean(normal_segment) != 0 and np.mean(post_segment) != 0 else 1,
        'skew_diff': pd.Series(post_segment).skew() - pd.Series(normal_segment).skew(),
        'kurt_diff': pd.Series(post_segment).kurtosis() - pd.Series(normal_segment).kurtosis()
    }
    
    # Statistical tests
    try:
        _, features['ks_pvalue'] = stats.ks_2samp(normal_segment, post_segment)
        _, features['mw_pvalue'] = stats.mannwhitneyu(normal_segment, post_segment, alternative='two-sided')
        features['t_stat'], features['t_pvalue'] = stats.ttest_ind(normal_segment, post_segment)
    except:
        features['ks_pvalue'] = features['mw_pvalue'] = features['t_stat'] = features['t_pvalue'] = np.nan
    
    return features

def extract_quantile_segments(series):
    """Extract quantile-based temporal segments"""
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 20:
        return {}
    
    split_point = len(series_clean) // 2
    normal_full = series_clean[:split_point]
    post_full = series_clean[split_point:]
    
    methods = {}
    
    # Original best performers
    if len(normal_full) >= 4:
        # First 25% vs full post
        first_25_len = max(3, len(normal_full) // 4)
        methods['first_25pct_vs_full'] = (normal_full[:first_25_len], post_full)
        
        # First 50% vs full post  
        first_50_len = max(3, len(normal_full) // 2)
        methods['first_50pct_vs_full'] = (normal_full[:first_50_len], post_full)
        
        # First 75% vs full post
        first_75_len = max(3, int(len(normal_full) * 0.75))
        methods['first_75pct_vs_full'] = (normal_full[:first_75_len], post_full)
    
    # Quantile-based segments (NEW)
    if len(normal_full) >= 8 and len(post_full) >= 8:
        # Quarter quantiles of normal vs quarter quantiles of post
        normal_q25_len = max(3, len(normal_full) // 4)
        post_q25_len = max(3, len(post_full) // 4)
        
        # First quarter vs first quarter
        methods['q25_first_vs_q25_first'] = (normal_full[:normal_q25_len], post_full[:post_q25_len])
        
        # First quarter vs last quarter  
        methods['q25_first_vs_q25_last'] = (normal_full[:normal_q25_len], post_full[-post_q25_len:])
        
        # Last quarter vs first quarter
        methods['q25_last_vs_q25_first'] = (normal_full[-normal_q25_len:], post_full[:post_q25_len])
        
        # Last quarter vs last quarter
        methods['q25_last_vs_q25_last'] = (normal_full[-normal_q25_len:], post_full[-post_q25_len:])
    
    # Percentile-based segments (NEW)
    if len(normal_full) >= 10 and len(post_full) >= 10:
        # 10th percentile segments
        normal_p10_len = max(3, int(len(normal_full) * 0.1))
        post_p10_len = max(3, int(len(post_full) * 0.1))
        
        methods['p10_first_vs_p10_first'] = (normal_full[:normal_p10_len], post_full[:post_p10_len])
        methods['p10_first_vs_p10_last'] = (normal_full[:normal_p10_len], post_full[-post_p10_len:])
        
        # 20th percentile segments
        normal_p20_len = max(3, int(len(normal_full) * 0.2))
        post_p20_len = max(3, int(len(post_full) * 0.2))
        
        methods['p20_first_vs_p20_first'] = (normal_full[:normal_p20_len], post_full[:post_p20_len])
        methods['p20_first_vs_p20_last'] = (normal_full[:normal_p20_len], post_full[-post_p20_len:])
    
    # Equal absolute length segments (from v2)
    min_len = min(len(normal_full), len(post_full))
    if min_len >= 10:
        quarter_len = min_len // 4
        if quarter_len >= 3:
            methods['equal_quarter_first'] = (normal_full[:quarter_len], post_full[:quarter_len])
            methods['equal_quarter_last'] = (normal_full[-quarter_len:], post_full[-quarter_len:])
    
    return methods

def process_single_series(args):
    """Process a single time series for temporal comparisons"""
    series, label, cluster, pattern_type = args
    series_clean = np.array(series)[~np.isnan(series)]
    
    if len(series_clean) < 20:
        return {}
    
    try:
        methods = extract_quantile_segments(series)
    except Exception:
        return {}
    
    # Compute features for each method
    series_results = {}
    for method_name, (normal_seg, post_seg) in methods.items():
        try:
            features = compute_comparison_features(normal_seg, post_seg)
            features['label'] = label
            features['cluster'] = cluster
            features['pattern_type'] = pattern_type
            series_results[method_name] = features
        except Exception:
            continue
    
    return series_results

def evaluate_method_parallel(args):
    """Evaluate a single method with cross-validation"""
    method_name, method_features = args
    
    if len(method_features) < 20:
        return None
    
    try:
        # Convert to DataFrame
        features_df = pd.DataFrame(method_features)
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        
        # Prepare for classification
        feature_cols = [col for col in features_df.columns if col not in ['label', 'cluster', 'pattern_type']]
        X = features_df[feature_cols].fillna(features_df[feature_cols].median())
        y = features_df['label'].values
        
        if len(np.unique(y)) < 2:
            return None
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        if XGB_AVAILABLE:
            model = xgb.XGBClassifier(
                n_estimators=100,
                max_depth=6,
                learning_rate=0.1,
                random_state=42,
                tree_method='gpu_hist',
                gpu_id=0,
                eval_metric='auc',
                n_jobs=1
            )
        else:
            model = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced', n_jobs=1)
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
        
        # Overall performance
        result = {
            'method': method_name,
            'mean_auroc': cv_scores.mean(),
            'std_auroc': cv_scores.std(),
            'n_samples': len(features_df),
            'break_rate': y.mean()
        }
        
        # Performance by pattern type
        pattern_performance = {}
        for pattern in features_df['pattern_type'].unique():
            if pattern == 'unknown':
                continue
                
            pattern_mask = features_df['pattern_type'] == pattern
            if pattern_mask.sum() < 10:  # Need minimum samples
                continue
                
            try:
                X_pattern = X[pattern_mask]
                y_pattern = y[pattern_mask]
                
                if len(np.unique(y_pattern)) < 2:
                    continue
                
                X_pattern_scaled = scaler.fit_transform(X_pattern)
                pattern_scores = cross_val_score(model, X_pattern_scaled, y_pattern, cv=cv, scoring='roc_auc')
                
                pattern_performance[pattern] = {
                    'auroc': pattern_scores.mean(),
                    'std': pattern_scores.std(),
                    'n_samples': len(y_pattern),
                    'break_rate': y_pattern.mean()
                }
            except:
                continue
        
        result['pattern_performance'] = pattern_performance
        return result
        
    except Exception:
        return None

def test_quantile_methods(time_series_list, labels, clusters, pattern_types, max_samples=100000):
    """Test quantile-based temporal methods"""
    print("🔧 Testing quantile-based temporal methods...")
    
    # Limit samples for faster testing
    if len(time_series_list) > max_samples:
        indices = np.random.choice(len(time_series_list), max_samples, replace=False)
        time_series_list = [time_series_list[i] for i in indices]
        labels = labels[indices]
        clusters = clusters[indices]
        pattern_types = [pattern_types[i] for i in indices]
    
    print(f"   Processing {len(time_series_list)} series with {cpu_count()} cores...")
    
    # Parallel processing of time series
    n_processes = max(1, cpu_count() - 1)
    
    with Pool(processes=n_processes) as pool:
        series_args = [(series, label, cluster, pattern) 
                      for series, label, cluster, pattern in zip(time_series_list, labels, clusters, pattern_types)]
        
        results_list = list(tqdm(
            pool.imap(process_single_series, series_args),
            total=len(series_args),
            desc="   Processing series",
            ncols=80
        ))
    
    # Aggregate results by method
    all_method_results = {}
    for series_result in results_list:
        for method_name, features in series_result.items():
            if method_name not in all_method_results:
                all_method_results[method_name] = []
            all_method_results[method_name].append(features)
    
    print(f"   Collected results for {len(all_method_results)} methods")
    
    # Parallel evaluation of methods
    print("   Evaluating methods with cross-validation...")
    
    method_args = [(method_name, method_features) for method_name, method_features in all_method_results.items()]
    
    with Pool(processes=n_processes) as pool:
        evaluation_results = list(tqdm(
            pool.imap(evaluate_method_parallel, method_args),
            total=len(method_args),
            desc="   Evaluating methods",
            ncols=80
        ))
    
    # Filter out None results
    results = [result for result in evaluation_results if result is not None]
    
    return results

def analyze_quantile_results(results):
    """Analyze quantile-based temporal results"""
    print("\n📈 QUANTILE-BASED TEMPORAL ANALYSIS RESULTS")
    print("=" * 70)
    
    # Convert to DataFrame for easier analysis
    results_df = pd.DataFrame([{
        'method': r['method'],
        'mean_auroc': r['mean_auroc'],
        'std_auroc': r['std_auroc'],
        'n_samples': r['n_samples'],
        'break_rate': r['break_rate']
    } for r in results])
    
    # Sort by AUROC performance
    results_sorted = results_df.sort_values('mean_auroc', ascending=False)
    
    print("🏆 All methods ranked by performance:")
    for i, (_, row) in enumerate(results_sorted.iterrows()):
        print(f"   {i+1:2d}. {row['method']:30}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    # Analyze by method category
    print(f"\n📊 Method category analysis:")
    
    categories = {
        'percentage_vs_full': ['first_25pct_vs_full', 'first_50pct_vs_full', 'first_75pct_vs_full'],
        'quantile_quarters': ['q25_first_vs_q25_first', 'q25_first_vs_q25_last', 'q25_last_vs_q25_first', 'q25_last_vs_q25_last'],
        'percentile_segments': ['p10_first_vs_p10_first', 'p10_first_vs_p10_last', 'p20_first_vs_p20_first', 'p20_first_vs_p20_last'],
        'equal_absolute': ['equal_quarter_first', 'equal_quarter_last']
    }
    
    for category, methods in categories.items():
        category_results = results_df[results_df['method'].isin(methods)]
        if len(category_results) > 0:
            best_method = category_results.loc[category_results['mean_auroc'].idxmax()]
            avg_auroc = category_results['mean_auroc'].mean()
            print(f"   {category:20}: Best = {best_method['mean_auroc']:.4f} ({best_method['method']})")
            print(f"   {'':<20}  Avg  = {avg_auroc:.4f}")
    
    # Pattern-specific analysis
    print(f"\n🎯 PATTERN-SPECIFIC PERFORMANCE ANALYSIS:")
    print("=" * 70)
    
    # Collect pattern performance data
    pattern_data = {}
    for result in results:
        method_name = result['method']
        for pattern, perf in result.get('pattern_performance', {}).items():
            if pattern not in pattern_data:
                pattern_data[pattern] = {}
            pattern_data[pattern][method_name] = perf
    
    # Analyze each pattern
    for pattern in ['spiky', 'volatile', 'moderate', 'calm', 'clipped']:
        if pattern in pattern_data:
            print(f"\n📊 {pattern.upper()} PATTERN:")
            
            # Get all methods for this pattern
            pattern_methods = []
            for method, perf in pattern_data[pattern].items():
                pattern_methods.append({
                    'method': method,
                    'auroc': perf['auroc'],
                    'std': perf['std'],
                    'n_samples': perf['n_samples']
                })
            
            # Sort by AUROC
            pattern_methods.sort(key=lambda x: x['auroc'], reverse=True)
            
            print(f"   Top 5 methods for {pattern}:")
            for i, method_data in enumerate(pattern_methods[:5]):
                print(f"   {i+1}. {method_data['method']:30}: {method_data['auroc']:.4f} ± {method_data['std']:.4f} (n={method_data['n_samples']})")
    
    # Best method overall
    best_method = results_sorted.iloc[0]
    print(f"\n🎯 Best overall method: {best_method['method']}")
    print(f"   AUROC: {best_method['mean_auroc']:.4f} ± {best_method['std_auroc']:.4f}")
    print(f"   Samples: {best_method['n_samples']}")
    
    return results_df, pattern_data

def create_pattern_comparison_table(pattern_data):
    """Create comparison table across patterns"""
    print(f"\n📋 PATTERN COMPARISON TABLE:")
    print("=" * 100)
    
    # Get common methods across patterns
    all_methods = set()
    for pattern_methods in pattern_data.values():
        all_methods.update(pattern_methods.keys())
    
    # Create comparison for all available methods
    top_methods = sorted(list(all_methods))
    
    print(f"{'Method':<30} {'Spiky':<8} {'Volatile':<8} {'Moderate':<8} {'Calm':<8} {'Clipped':<8}")
    print("-" * 100)
    
    comparison_data = []
    for method in top_methods:
        if method in all_methods:
            row_data = {'method': method}
            row_str = f"{method:<30}"
            
            for pattern in ['spiky', 'volatile', 'moderate', 'calm', 'clipped']:
                if pattern in pattern_data and method in pattern_data[pattern]:
                    auroc = pattern_data[pattern][method]['auroc']
                    row_str += f" {auroc:.4f}  "
                    row_data[pattern] = auroc
                else:
                    row_str += f" {'N/A':<6}  "
                    row_data[pattern] = np.nan
            
            print(row_str)
            comparison_data.append(row_data)
    
    return pd.DataFrame(comparison_data)

def main():
    """Main pipeline execution"""
    print("🚀 TEMPORAL POSITION PROBING V3 - QUANTILE & PATTERN ANALYSIS")
    print("=" * 70)
    
    # Load data with clusters
    time_series_list, labels, clusters, pattern_types = load_data_with_clusters()
    
    # Test quantile-based methods
    results = test_quantile_methods(time_series_list, labels, clusters, pattern_types)
    
    # Analyze results
    results_df, pattern_data = analyze_quantile_results(results)
    
    # Create pattern comparison
    comparison_df = create_pattern_comparison_table(pattern_data)
    
    # Save results
    results_df.to_csv('temporal_position_v3_results.csv', index=False)
    comparison_df.to_csv('temporal_position_pattern_comparison.csv', index=False)
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Method results: 'temporal_position_v3_results.csv'")
    print(f"   📊 Pattern comparison: 'temporal_position_pattern_comparison.csv'")
    
    return results_df, pattern_data, comparison_df

if __name__ == "__main__":
    results_df, pattern_data, comparison_df = main()