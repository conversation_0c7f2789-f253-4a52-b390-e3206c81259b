#!/usr/bin/env python3
"""
Test script for the enhanced adaptive truncation detection pipeline
"""

import numpy as np
import matplotlib.pyplot as plt
from Thermodynamics_truncated import AdaptiveTruncationDetector, TruncationAwareThermodynamicAnalyzer

def create_test_samples():
    """Create various types of truncated time series for testing"""
    np.random.seed(42)
    
    samples = {}
    
    # 1. Clean data (no truncation)
    samples['clean'] = np.random.normal(0, 1, 1000)
    
    # 2. Precision truncation (rounded to 2 decimal places)
    raw_data = np.random.normal(0, 1, 1000)
    samples['precision_truncated'] = np.round(raw_data, 2)
    
    # 3. Boundary truncation (clipped to [-0.5, 0.5])
    raw_data = np.random.normal(0, 1, 1000)
    samples['boundary_truncated'] = np.clip(raw_data, -0.5, 0.5)
    
    # 4. Quantized data (multiples of 0.1)
    raw_data = np.random.normal(0, 1, 1000)
    samples['quantized'] = np.round(raw_data / 0.1) * 0.1
    
    # 5. Flat segments (some values artificially set to constant)
    raw_data = np.random.normal(0, 1, 1000)
    flat_indices = np.random.choice(1000, 200, replace=False)
    raw_data[flat_indices] = 0.0
    samples['flat_segments'] = raw_data
    
    # 6. Mixed truncation (multiple issues)
    raw_data = np.random.normal(0, 1, 1000)
    raw_data = np.clip(raw_data, -0.7, 0.7)  # Boundary
    raw_data = np.round(raw_data, 1)  # Precision
    samples['mixed_truncation'] = raw_data
    
    # 7. Subtle truncation (harder to detect)
    raw_data = np.random.normal(0, 1, 1000)
    # Slightly bias towards round numbers
    for i in range(len(raw_data)):
        if np.random.random() < 0.1:  # 10% chance
            raw_data[i] = np.round(raw_data[i] * 2) / 2  # Round to nearest 0.5
    samples['subtle_truncation'] = raw_data
    
    return samples

def test_adaptive_detection():
    """Test the adaptive truncation detection system"""
    print("🧪 Testing Adaptive Truncation Detection Pipeline")
    print("=" * 60)
    
    # Create test samples
    samples = create_test_samples()
    sample_list = list(samples.values())
    
    # Initialize detector
    detector = AdaptiveTruncationDetector()
    
    # Calibrate on the dataset
    print("\n📊 Calibrating detector on test dataset...")
    detector.calibrate_on_dataset(sample_list, verbose=True)
    
    print("\n🔍 Analyzing individual samples:")
    print("-" * 60)
    
    # Test each sample type
    for sample_name, sample_data in samples.items():
        print(f"\n📈 Sample: {sample_name}")
        
        # Get truncation metrics
        metrics = detector.compute_truncation_confidence(sample_data)
        
        print(f"   Truncation Confidence: {metrics['truncation_confidence']:.3f}")
        print(f"   Precision Quality: {metrics['precision_quality_score']:.3f}")
        print(f"   Value Quality: {metrics['value_quality_score']:.3f}")
        print(f"   Boundary Constraint: {metrics['boundary_constraint_factor']:.3f}")
        print(f"   Clustering Artifacts: {metrics['clustering_artifact_score']:.3f}")
        print(f"   Flat Segments: {metrics['flat_segment_ratio']:.3f}")
        
        # Interpretation
        if metrics['truncation_confidence'] > 0.8:
            quality = "🟢 HIGH QUALITY"
        elif metrics['truncation_confidence'] > 0.5:
            quality = "🟡 MODERATE QUALITY"
        else:
            quality = "🔴 LOW QUALITY (TRUNCATED)"
        
        print(f"   Assessment: {quality}")

def test_thermodynamic_analysis():
    """Test the full thermodynamic analysis with truncation awareness"""
    print("\n\n🌡️  Testing Truncation-Aware Thermodynamic Analysis")
    print("=" * 60)
    
    # Create test samples
    samples = create_test_samples()
    sample_list = list(samples.values())
    
    # Initialize analyzer
    analyzer = TruncationAwareThermodynamicAnalyzer()
    
    # Calibrate the truncation detector
    analyzer.truncation_detector.calibrate_on_dataset(sample_list, verbose=False)
    
    print("\n🔬 Analyzing thermodynamic properties:")
    print("-" * 60)
    
    # Analyze a few key samples
    key_samples = ['clean', 'precision_truncated', 'boundary_truncated', 'mixed_truncation']
    
    for sample_name in key_samples:
        sample_data = samples[sample_name]
        
        print(f"\n📊 Sample: {sample_name}")
        
        # Perform full thermodynamic analysis
        results = analyzer.analyze_time_series(sample_data)
        
        print(f"   Truncation Confidence: {results['truncation_confidence']:.3f}")
        print(f"   Robust Potential Energy: {results['robust_potential_energy_length_weighted']:.4f}")
        print(f"   Robust Kinetic Energy: {results['robust_kinetic_energy_variance_based']:.4f}")
        print(f"   Quality-Weighted Total Energy: {results['quality_weighted_total_energy']:.4f}")
        print(f"   Clustering-Corrected Entropy: {results['clustering_corrected_entropy']:.4f}")

def create_visualization():
    """Create visualization of truncation detection results"""
    print("\n\n📊 Creating Visualization...")
    
    samples = create_test_samples()
    sample_list = list(samples.values())
    
    detector = AdaptiveTruncationDetector()
    detector.calibrate_on_dataset(sample_list, verbose=False)
    
    # Analyze all samples
    results = {}
    for name, data in samples.items():
        metrics = detector.compute_truncation_confidence(data)
        results[name] = metrics
    
    # Create comparison plot
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Adaptive Truncation Detection Results', fontsize=16)
    
    # Plot 1: Truncation Confidence Scores
    names = list(results.keys())
    confidences = [results[name]['truncation_confidence'] for name in names]
    
    axes[0, 0].bar(range(len(names)), confidences, color=['green' if c > 0.8 else 'orange' if c > 0.5 else 'red' for c in confidences])
    axes[0, 0].set_title('Truncation Confidence Scores')
    axes[0, 0].set_ylabel('Confidence (1=Clean, 0=Truncated)')
    axes[0, 0].set_xticks(range(len(names)))
    axes[0, 0].set_xticklabels(names, rotation=45, ha='right')
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Precision Quality Scores
    precision_scores = [results[name]['precision_quality_score'] for name in names]
    axes[0, 1].bar(range(len(names)), precision_scores, color='skyblue')
    axes[0, 1].set_title('Precision Quality Scores')
    axes[0, 1].set_ylabel('Quality Score')
    axes[0, 1].set_xticks(range(len(names)))
    axes[0, 1].set_xticklabels(names, rotation=45, ha='right')
    axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: Value Quality Scores
    value_scores = [results[name]['value_quality_score'] for name in names]
    axes[1, 0].bar(range(len(names)), value_scores, color='lightcoral')
    axes[1, 0].set_title('Value Quality Scores')
    axes[1, 0].set_ylabel('Quality Score')
    axes[1, 0].set_xticks(range(len(names)))
    axes[1, 0].set_xticklabels(names, rotation=45, ha='right')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 4: Multiple Metrics Comparison
    metrics_to_plot = ['boundary_constraint_factor', 'clustering_artifact_score', 'flat_segment_ratio']
    x = np.arange(len(names))
    width = 0.25
    
    for i, metric in enumerate(metrics_to_plot):
        values = [results[name][metric] for name in names]
        axes[1, 1].bar(x + i*width, values, width, label=metric.replace('_', ' ').title())
    
    axes[1, 1].set_title('Truncation Indicators Comparison')
    axes[1, 1].set_ylabel('Score')
    axes[1, 1].set_xticks(x + width)
    axes[1, 1].set_xticklabels(names, rotation=45, ha='right')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('adaptive_truncation_detection_results.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visualization saved as 'adaptive_truncation_detection_results.png'")

if __name__ == "__main__":
    # Run all tests
    test_adaptive_detection()
    test_thermodynamic_analysis()
    create_visualization()
    
    print("\n\n🎉 All tests completed successfully!")
    print("The adaptive truncation detection pipeline is now fully operational.")
    print("Key improvements:")
    print("  • No hard-coded bounds - adapts to actual data patterns")
    print("  • Learns from dataset to identify suspicious patterns")
    print("  • Multi-scale detection across precision, boundaries, values, and clustering")
    print("  • Sophisticated weighting when calibrated on real data")
    print("  • Comprehensive truncation confidence scoring")