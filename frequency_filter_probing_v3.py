import numpy as np
import pandas as pd
from scipy.signal import welch, butter, filtfilt, hilbert, medfilt
from scipy.stats import pearsonr, moment
import xgboost as xgb
from sklearn.model_selection import cross_val_score
import multiprocessing as mp
from tqdm import tqdm
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

def design_bandpass_filter(center_freq, bandwidth_ratio=0.2, fs=500, order=4):
    """Design bandpass filter around center frequency."""
    bandwidth = center_freq * bandwidth_ratio
    low_freq = max(0.01, center_freq - bandwidth/2)
    high_freq = min(fs/2 - 0.01, center_freq + bandwidth/2)
    
    if low_freq >= high_freq:
        return None, None
    
    try:
        b, a = butter(order, [low_freq, high_freq], btype='band', fs=fs)
        return b, a
    except Exception:
        return None, None

def extract_welch_psd_features(f, psd, prefix=''):
    """Extract Welch PSD-derived features from frequency_features_optuna.py"""
    features = {}
    
    if len(f) < 2 or len(psd) < 2:
        return {f'{prefix}_{k}': np.nan for k in ['centroid', 'spread', 'skew', 'kurt', 'slope', 'median']}
    
    try:
        # Spectral centroid (mean frequency)
        features[f'{prefix}_spectral_centroid'] = np.sum(f * psd) / np.sum(psd) if np.sum(psd) > 0 else np.nan
        
        # Spectral spread (standard deviation)
        if not np.isnan(features[f'{prefix}_spectral_centroid']):
            features[f'{prefix}_spectral_spread'] = np.sqrt(np.sum(((f - features[f'{prefix}_spectral_centroid']) ** 2) * psd) / np.sum(psd))
        else:
            features[f'{prefix}_spectral_spread'] = np.nan
        
        # Spectral skewness and kurtosis
        if len(psd) > 3:
            features[f'{prefix}_spectral_skew'] = moment(psd, moment=3)
            features[f'{prefix}_spectral_kurt'] = moment(psd, moment=4)
        else:
            features[f'{prefix}_spectral_skew'] = np.nan
            features[f'{prefix}_spectral_kurt'] = np.nan
        
        # Spectral slope (power law exponent)
        valid_idx = (f[1:] > 0) & (psd[1:] > 0)
        if np.sum(valid_idx) > 2:
            features[f'{prefix}_spectral_slope'] = np.polyfit(np.log(f[1:][valid_idx]), np.log(psd[1:][valid_idx]), 1)[0]
        else:
            features[f'{prefix}_spectral_slope'] = np.nan
        
        # Spectral median
        cumsum_psd = np.cumsum(psd)
        median_idx = np.where(cumsum_psd >= np.sum(psd)/2)[0]
        if len(median_idx) > 0:
            features[f'{prefix}_spectral_median'] = f[median_idx[0]]
        else:
            features[f'{prefix}_spectral_median'] = np.nan
            
    except Exception:
        for k in ['centroid', 'spread', 'skew', 'kurt', 'slope', 'median']:
            features[f'{prefix}_spectral_{k}'] = np.nan
    
    return features

def extract_v3_features(series, tstar, fs=500, bandwidth_ratio=0.2, window_size=5, 
                       smoothing_strategy='pre_post'):
    """
    V3: Extract features with pre/post vs whole smoothing comparison + Welch PSD features.
    
    smoothing_strategy: 'pre_post' or 'whole'
    """
    features = {}
    
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 20 or len(post_segment) < 20:
        return {f'v3_{k}': np.nan for k in range(40)}
    
    try:
        # 1. APPLY SMOOTHING STRATEGY
        if smoothing_strategy == 'pre_post':
            # Smooth pre and post segments separately
            pre_smoothed = medfilt(pre_segment, kernel_size=window_size)
            post_smoothed = medfilt(post_segment, kernel_size=window_size)
        else:  # 'whole'
            # Smooth whole series then split
            whole_smoothed = medfilt(series, kernel_size=window_size)
            pre_smoothed = whole_smoothed[:tstar]
            post_smoothed = whole_smoothed[tstar:]
        
        # 2. WELCH PSD FEATURES (from frequency_features_optuna.py)
        f_pre, psd_pre = welch(pre_smoothed, fs=fs, nperseg=64)
        f_post, psd_post = welch(post_smoothed, fs=fs, nperseg=64)
        
        # Extract Welch PSD features for pre/post
        pre_welch_features = extract_welch_psd_features(f_pre, psd_pre, 'pre')
        post_welch_features = extract_welch_psd_features(f_post, psd_post, 'post')
        features.update(pre_welch_features)
        features.update(post_welch_features)
        
        # Welch PSD ratios
        for key in ['spectral_centroid', 'spectral_spread', 'spectral_skew', 'spectral_kurt', 'spectral_slope', 'spectral_median']:
            pre_val = pre_welch_features.get(f'pre_{key}', np.nan)
            post_val = post_welch_features.get(f'post_{key}', np.nan)
            features[f'{key}_ratio'] = post_val / pre_val if pre_val != 0 and not np.isnan(pre_val) and not np.isnan(post_val) else np.nan
        
        # 3. DOMINANT FREQUENCY ANALYSIS
        dominant_freq_pre = f_pre[np.argmax(psd_pre)]
        dominant_freq_post = f_post[np.argmax(psd_post)]
        
        features['dominant_freq_ratio'] = dominant_freq_post / dominant_freq_pre if dominant_freq_pre > 0 else np.nan
        features['dominant_freq_diff'] = abs(dominant_freq_post - dominant_freq_pre)
        
        # 4. POWER FEATURES
        power_pre = np.sum(psd_pre) * (f_pre[1] - f_pre[0]) if len(f_pre) > 1 else np.sum(psd_pre)
        power_post = np.sum(psd_post) * (f_post[1] - f_post[0]) if len(f_post) > 1 else np.sum(psd_post)
        
        features['total_power_ratio'] = power_post / power_pre if power_pre > 0 else np.nan
        features['dominant_power_ratio'] = psd_post[np.argmax(psd_post)] / psd_pre[np.argmax(psd_pre)] if psd_pre[np.argmax(psd_pre)] > 0 else np.nan
        
        # 5. FREQUENCY FILTERING FEATURES
        b, a = design_bandpass_filter(dominant_freq_pre, bandwidth_ratio, fs)
        if b is not None:
            post_filtered = filtfilt(b, a, post_smoothed)
            post_residual = post_smoothed - post_filtered
            
            power_original = np.var(post_smoothed)
            power_filtered = np.var(post_filtered)
            power_residual = np.var(post_residual)
            
            features['preservation_ratio'] = power_filtered / power_original if power_original > 0 else np.nan
            features['noise_ratio'] = power_residual / power_original if power_original > 0 else np.nan
        else:
            features['preservation_ratio'] = np.nan
            features['noise_ratio'] = np.nan
        
        # 6. SMOOTHING EFFECTIVENESS COMPARISON
        if smoothing_strategy == 'pre_post':
            # Compare individual segment smoothing
            pre_smooth_effect = np.var(pre_segment - pre_smoothed) / np.var(pre_segment) if np.var(pre_segment) > 0 else np.nan
            post_smooth_effect = np.var(post_segment - post_smoothed) / np.var(post_segment) if np.var(post_segment) > 0 else np.nan
        else:
            # Compare whole series smoothing
            whole_smooth_effect = np.var(series - whole_smoothed) / np.var(series) if np.var(series) > 0 else np.nan
            pre_smooth_effect = whole_smooth_effect
            post_smooth_effect = whole_smooth_effect
        
        features['pre_smoothing_effect'] = pre_smooth_effect
        features['post_smoothing_effect'] = post_smooth_effect
        features['smoothing_effect_ratio'] = post_smooth_effect / pre_smooth_effect if pre_smooth_effect > 0 else np.nan
        
        # 7. ENVELOPE FEATURES
        envelope_pre = np.abs(hilbert(pre_smoothed))
        envelope_post = np.abs(hilbert(post_smoothed))
        
        features['envelope_mean_ratio'] = np.mean(envelope_post) / np.mean(envelope_pre) if np.mean(envelope_pre) > 0 else np.nan
        features['envelope_std_ratio'] = np.std(envelope_post) / np.std(envelope_pre) if np.std(envelope_pre) > 0 else np.nan
        
        # 8. AUTOCORRELATION FEATURES
        def autocorr_at_lag(x, lag=1):
            if len(x) <= lag:
                return np.nan
            return np.corrcoef(x[:-lag], x[lag:])[0, 1] if len(x) > lag else np.nan
        
        ac_pre = autocorr_at_lag(pre_smoothed, lag=1)
        ac_post = autocorr_at_lag(post_smoothed, lag=1)
        features['autocorr_change'] = abs(ac_post - ac_pre) if not (np.isnan(ac_pre) or np.isnan(ac_post)) else np.nan
        
        # # 9. STRATEGY-SPECIFIC FEATURES
        # features['smoothing_strategy'] = 1 if smoothing_strategy == 'whole' else 0
        # features['window_size'] = window_size
        
    except Exception as e:
        return {f'v3_{k}': np.nan for k in range(40)}
    
    return features

def extract_v3_features_wrapper(args):
    """Wrapper for parallel processing."""
    series, tstar, fs, bandwidth_ratio, window_size, smoothing_strategy = args
    return extract_v3_features(series, tstar, fs, bandwidth_ratio, window_size, smoothing_strategy)

def evaluate_v3_approach(all_series, all_tstars, all_labels, 
                        window_sizes=[5, 7, 9], 
                        smoothing_strategies=['pre_post', 'whole'],
                        bandwidth_ratio=0.2):
    """Evaluate V3 approach with parallel processing."""
    results = {}
    n_processes = max(1, mp.cpu_count() - 1)
    
    print(f"Evaluating V3 approach with {n_processes} processes...")
    
    for smoothing_strategy in smoothing_strategies:
        for window_size in window_sizes:
            config_name = f"median_{smoothing_strategy}_w{window_size}_b{bandwidth_ratio}"
            print(f"\nTesting: {config_name}")
            
            # Prepare arguments for parallel processing
            args_list = [(series, tstar, 500, bandwidth_ratio, window_size, smoothing_strategy) 
                        for series, tstar in zip(all_series, all_tstars)]
            
            # Extract features in parallel
            with mp.Pool(processes=n_processes) as pool:
                feature_list = list(tqdm(
                    pool.imap(extract_v3_features_wrapper, args_list),
                    total=len(args_list),
                    desc=f"Extracting {config_name}"
                ))
            
            feature_df = pd.DataFrame(feature_list)
            feature_df = feature_df.fillna(feature_df.median())
            
            if feature_df.shape[1] == 0:
                continue
            
            try:
                model = xgb.XGBClassifier(
                    n_estimators=100, random_state=42, 
                    tree_method='gpu_hist', gpu_id=0, eval_metric='auc'
                )
                cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
                auc_score = np.mean(cv_scores)
                auc_std = np.std(cv_scores)
                
                results[config_name] = {
                    'auc_mean': auc_score,
                    'auc_std': auc_std,
                    'n_features': feature_df.shape[1],
                    'smoothing_strategy': smoothing_strategy,
                    'window_size': window_size
                }
                
                print(f"AUC: {auc_score:.4f} ± {auc_std:.4f} ({feature_df.shape[1]} features)")
                
                # Feature importance for best result
                if auc_score == max([r.get('auc_mean', 0) for r in results.values()]):
                    model.fit(feature_df, all_labels)
                    feature_importance = pd.DataFrame({
                        'feature': feature_df.columns,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    
                    print("Top 5 features:")
                    for _, row in feature_importance.head().iterrows():
                        print(f"  {row['feature']}: {row['importance']:.4f}")
                
            except Exception as e:
                print(f"Error: {e}")
                results[config_name] = {'auc_mean': np.nan, 'auc_std': np.nan, 'n_features': 0}
    
    return results

def load_data(data_path):
    """Load time series data from parquet files."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series, all_tstars, all_labels = [], [], []
        
        for id_, group in X_train.groupby(level='id'):
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(int(y_train.loc[id_]))
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    parser = argparse.ArgumentParser(description='Frequency Filter Probing v3')
    parser.add_argument('--data_path', type=str, default='.', help='Path to data directory')
    parser.add_argument('--window_sizes', nargs='+', type=int, default=[5, 7, 9],
                       help='Window sizes to test')
    parser.add_argument('--smoothing_strategies', nargs='+', default=['pre_post', 'whole'],
                       help='Smoothing strategies to test')
    parser.add_argument('--bandwidth_ratio', type=float, default=0.2,
                       help='Bandwidth ratio for frequency filtering')
    
    args = parser.parse_args()
    
    print("Frequency Filter Probing v3")
    print("="*50)
    print("NEW FEATURES:")
    print("• Pre/post vs whole smoothing comparison")
    print("• Welch PSD-derived features (centroid, spread, skew, kurt, slope, median)")
    print("• Parallel processing with progress bars")
    print("• Median filter only, window sizes:", args.window_sizes)
    print("• Smoothing strategies:", args.smoothing_strategies)
    print("• Bandwidth ratio:", args.bandwidth_ratio)
    
    # Load data
    all_series, all_tstars, all_labels = load_data(args.data_path)
    if all_series is None:
        sys.exit(1)
    
    print(f"Loaded {len(all_series)} time series")
    
    # Run analysis
    results = evaluate_v3_approach(
        all_series, all_tstars, all_labels,
        window_sizes=args.window_sizes,
        smoothing_strategies=args.smoothing_strategies,
        bandwidth_ratio=args.bandwidth_ratio
    )
    
    print("\n" + "="*50)
    print("SUMMARY RESULTS")
    print("="*50)
    
    # Sort results by AUC
    sorted_results = sorted(results.items(), key=lambda x: x[1].get('auc_mean', 0), reverse=True)
    
    print("Pre/Post vs Whole Smoothing Comparison:")
    for config_name, result in sorted_results:
        if not np.isnan(result['auc_mean']):
            strategy = result['smoothing_strategy']
            window = result['window_size']
            print(f"{strategy:>8} w{window}: AUC = {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
    
    if sorted_results:
        best_config, best_result = sorted_results[0]
        print(f"\n🏆 Best: {best_config} (AUC = {best_result['auc_mean']:.4f})")
        
        # Compare strategies
        pre_post_results = [r for c, r in results.items() if 'pre_post' in c and not np.isnan(r['auc_mean'])]
        whole_results = [r for c, r in results.items() if 'whole' in c and not np.isnan(r['auc_mean'])]
        
        if pre_post_results and whole_results:
            avg_pre_post = np.mean([r['auc_mean'] for r in pre_post_results])
            avg_whole = np.mean([r['auc_mean'] for r in whole_results])
            print(f"\nStrategy Comparison:")
            print(f"Pre/Post smoothing avg: {avg_pre_post:.4f}")
            print(f"Whole smoothing avg: {avg_whole:.4f}")
            print(f"Difference: {avg_pre_post - avg_whole:+.4f}")

if __name__ == "__main__":
    main()