import numpy as np
import pandas as pd
from scipy.signal import welch, periodogram
from scipy.stats import moment
import optuna
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import cross_val_score
import warnings
warnings.filterwarnings('ignore')

def extract_frequency_features(series, tstar, fs=500, nperseg_pre=64, nperseg_post=64, 
                              n_dominant=3, freq_bands=None, psd_method='welch'):
    """
    Extract frequency-based features from pre/post segments.
    
    Parameters:
    - series: 1D numpy array
    - tstar: split point
    - fs: sampling frequency
    - nperseg_pre/post: segment lengths for PSD estimation
    - n_dominant: number of dominant frequencies to track
    - freq_bands: frequency bands to analyze separately
    - psd_method: 'welch' or 'periodogram'
    """
    features = {}
    
    # Handle edge cases
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 10 or len(post_segment) < 10:
        return {f'freq_{k}': np.nan for k in range(50)}  # Return NaN features
    
    # Adjust nperseg for segment lengths
    nperseg_pre = min(len(pre_segment)//2, nperseg_pre)
    nperseg_post = min(len(post_segment)//2, nperseg_post)
    
    if nperseg_pre < 8:
        nperseg_pre = len(pre_segment)
    if nperseg_post < 8:
        nperseg_post = len(post_segment)
    
    try:
        # Compute PSD
        if psd_method == 'welch':
            f_pre, psd_pre = welch(pre_segment, fs=fs, nperseg=nperseg_pre)
            f_post, psd_post = welch(post_segment, fs=fs, nperseg=nperseg_post)
        else:  # periodogram
            f_pre, psd_pre = periodogram(pre_segment, fs=fs)
            f_post, psd_post = periodogram(post_segment, fs=fs)
        
        # 1. DOMINANT FREQUENCY FEATURES
        dominant_idx_pre = np.argmax(psd_pre)
        dominant_idx_post = np.argmax(psd_post)
        
        dominant_freq_pre = f_pre[dominant_idx_pre]
        dominant_freq_post = f_post[dominant_idx_post]
        
        features['dominant_freq_ratio'] = dominant_freq_post / dominant_freq_pre if dominant_freq_pre > 0 else np.nan
        features['dominant_freq_diff'] = abs(dominant_freq_post - dominant_freq_pre)
        features['dominant_freq_rel_diff'] = abs(dominant_freq_post - dominant_freq_pre) / dominant_freq_pre if dominant_freq_pre > 0 else np.nan
        
        # 2. POWER FEATURES
        power_pre = np.sum(psd_pre) * (f_pre[1] - f_pre[0]) if len(f_pre) > 1 else np.sum(psd_pre)
        power_post = np.sum(psd_post) * (f_post[1] - f_post[0]) if len(f_post) > 1 else np.sum(psd_post)
        
        features['total_power_ratio'] = power_post / power_pre if power_pre > 0 else np.nan
        features['total_power_diff'] = abs(power_post - power_pre)
        features['total_power_rel_diff'] = abs(power_post - power_pre) / power_pre if power_pre > 0 else np.nan
        
        # Dominant frequency power ratio
        dominant_power_pre = psd_pre[dominant_idx_pre]
        dominant_power_post = psd_post[dominant_idx_post]
        features['dominant_power_ratio'] = dominant_power_post / dominant_power_pre if dominant_power_pre > 0 else np.nan
        
        # 3. ALIGNED FREQUENCY COMPARISON (by order)
        sorted_idx_pre = np.argsort(psd_pre)[::-1][:n_dominant]
        sorted_idx_post = np.argsort(psd_post)[::-1][:n_dominant]
        
        for i in range(min(n_dominant, len(sorted_idx_pre), len(sorted_idx_post))):
            freq_pre_i = f_pre[sorted_idx_pre[i]]
            freq_post_i = f_post[sorted_idx_post[i]]
            power_pre_i = psd_pre[sorted_idx_pre[i]]
            power_post_i = psd_post[sorted_idx_post[i]]
            
            features[f'freq_rank{i+1}_ratio'] = freq_post_i / freq_pre_i if freq_pre_i > 0 else np.nan
            features[f'power_rank{i+1}_ratio'] = power_post_i / power_pre_i if power_pre_i > 0 else np.nan
        
        # 4. SPECTRAL SHAPE FEATURES
        # Spectral slope (power law exponent)
        if len(f_pre) > 2 and len(f_post) > 2:
            # Avoid log(0) by starting from index 1
            valid_idx_pre = (f_pre[1:] > 0) & (psd_pre[1:] > 0)
            valid_idx_post = (f_post[1:] > 0) & (psd_post[1:] > 0)
            
            if np.sum(valid_idx_pre) > 2:
                slope_pre = np.polyfit(np.log(f_pre[1:][valid_idx_pre]), np.log(psd_pre[1:][valid_idx_pre]), 1)[0]
            else:
                slope_pre = np.nan
                
            if np.sum(valid_idx_post) > 2:
                slope_post = np.polyfit(np.log(f_post[1:][valid_idx_post]), np.log(psd_post[1:][valid_idx_post]), 1)[0]
            else:
                slope_post = np.nan
            
            features['spectral_slope_ratio'] = slope_post / slope_pre if slope_pre != 0 and not np.isnan(slope_pre) else np.nan
            features['spectral_slope_diff'] = abs(slope_post - slope_pre) if not (np.isnan(slope_pre) or np.isnan(slope_post)) else np.nan
        
        # Spectral centroid (mean frequency)
        spectral_centroid_pre = np.sum(f_pre * psd_pre) / np.sum(psd_pre) if np.sum(psd_pre) > 0 else np.nan
        spectral_centroid_post = np.sum(f_post * psd_post) / np.sum(psd_post) if np.sum(psd_post) > 0 else np.nan
        
        features['spectral_centroid_ratio'] = spectral_centroid_post / spectral_centroid_pre if spectral_centroid_pre > 0 else np.nan
        features['spectral_centroid_diff'] = abs(spectral_centroid_post - spectral_centroid_pre)
        
        # Spectral spread (standard deviation)
        if not np.isnan(spectral_centroid_pre) and not np.isnan(spectral_centroid_post):
            spectral_spread_pre = np.sqrt(np.sum(((f_pre - spectral_centroid_pre) ** 2) * psd_pre) / np.sum(psd_pre))
            spectral_spread_post = np.sqrt(np.sum(((f_post - spectral_centroid_post) ** 2) * psd_post) / np.sum(psd_post))
            features['spectral_spread_ratio'] = spectral_spread_post / spectral_spread_pre if spectral_spread_pre > 0 else np.nan
        else:
            features['spectral_spread_ratio'] = np.nan
        
        # Spectral skewness and kurtosis
        if len(psd_pre) > 3 and len(psd_post) > 3:
            features['spectral_skew_pre'] = moment(psd_pre, moment=3)
            features['spectral_skew_post'] = moment(psd_post, moment=3)
            features['spectral_skew_ratio'] = features['spectral_skew_post'] / features['spectral_skew_pre'] if features['spectral_skew_pre'] != 0 else np.nan
            
            features['spectral_kurt_pre'] = moment(psd_pre, moment=4)
            features['spectral_kurt_post'] = moment(psd_post, moment=4)
            features['spectral_kurt_ratio'] = features['spectral_kurt_post'] / features['spectral_kurt_pre'] if features['spectral_kurt_pre'] != 0 else np.nan
        
        # 5. FREQUENCY BAND ANALYSIS
        if freq_bands is not None:
            for band_name, (low_freq, high_freq) in freq_bands.items():
                # Pre segment band power
                band_mask_pre = (f_pre >= low_freq) & (f_pre <= high_freq)
                band_power_pre = np.sum(psd_pre[band_mask_pre]) * (f_pre[1] - f_pre[0]) if np.sum(band_mask_pre) > 0 else 0
                
                # Post segment band power
                band_mask_post = (f_post >= low_freq) & (f_post <= high_freq)
                band_power_post = np.sum(psd_post[band_mask_post]) * (f_post[1] - f_post[0]) if np.sum(band_mask_post) > 0 else 0
                
                features[f'{band_name}_power_ratio'] = band_power_post / band_power_pre if band_power_pre > 0 else np.nan
                features[f'{band_name}_power_rel'] = band_power_pre / power_pre if power_pre > 0 else np.nan
                features[f'{band_name}_power_rel_post'] = band_power_post / power_post if power_post > 0 else np.nan
        
        # 6. SPECTRAL MEDIAN
        cumsum_pre = np.cumsum(psd_pre)
        cumsum_post = np.cumsum(psd_post)
        
        median_idx_pre = np.where(cumsum_pre >= np.sum(psd_pre)/2)[0]
        median_idx_post = np.where(cumsum_post >= np.sum(psd_post)/2)[0]
        
        if len(median_idx_pre) > 0 and len(median_idx_post) > 0:
            spectral_median_pre = f_pre[median_idx_pre[0]]
            spectral_median_post = f_post[median_idx_post[0]]
            features['spectral_median_ratio'] = spectral_median_post / spectral_median_pre if spectral_median_pre > 0 else np.nan
        else:
            features['spectral_median_ratio'] = np.nan
            
    except Exception as e:
        # Return NaN features if computation fails
        return {f'freq_{k}': np.nan for k in range(50)}
    
    return features

def objective(trial, all_series, all_tstars, all_labels):
    """
    Optuna objective function to optimize frequency feature parameters.
    """
    # Suggest hyperparameters
    fs = trial.suggest_float('fs', 100, 2000, log=True)
    nperseg_base = trial.suggest_int('nperseg_base', 16, 256)
    nperseg_factor_pre = trial.suggest_float('nperseg_factor_pre', 0.5, 2.0)
    nperseg_factor_post = trial.suggest_float('nperseg_factor_post', 0.5, 2.0)
    n_dominant = trial.suggest_int('n_dominant', 1, 5)
    psd_method = trial.suggest_categorical('psd_method', ['welch', 'periodogram'])
    
    # Frequency bands
    use_freq_bands = trial.suggest_categorical('use_freq_bands', [True, False])
    freq_bands = None
    if use_freq_bands:
        low_cutoff = trial.suggest_float('low_cutoff', 0.01, 0.3)
        mid_cutoff = trial.suggest_float('mid_cutoff', 0.3, 0.7)
        high_cutoff = trial.suggest_float('high_cutoff', 0.7, 1.0)
        freq_bands = {
            'low': (0, low_cutoff * fs/2),
            'mid': (low_cutoff * fs/2, mid_cutoff * fs/2),
            'high': (mid_cutoff * fs/2, high_cutoff * fs/2)
        }
    
    # Extract features for all series
    feature_list = []
    for series, tstar in zip(all_series, all_tstars):
        nperseg_pre = int(nperseg_base * nperseg_factor_pre)
        nperseg_post = int(nperseg_base * nperseg_factor_post)
        
        features = extract_frequency_features(
            series, tstar, fs=fs, 
            nperseg_pre=nperseg_pre, nperseg_post=nperseg_post,
            n_dominant=n_dominant, freq_bands=freq_bands, 
            psd_method=psd_method
        )
        feature_list.append(features)
    
    # Convert to DataFrame
    feature_df = pd.DataFrame(feature_list)
    
    # Handle missing values
    feature_df = feature_df.fillna(feature_df.median())
    
    # Check if we have valid features
    if feature_df.shape[1] == 0 or feature_df.isna().all().all():
        return 0.5  # Return baseline AUC
    
    # Train model and evaluate
    try:
        model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=1)
        
        # Use cross-validation for more robust evaluation
        cv_scores = cross_val_score(model, feature_df, all_labels, cv=3, scoring='roc_auc')
        auc_score = np.mean(cv_scores)
        
        # Pruning: report intermediate result
        trial.report(auc_score, step=0)
        
        if trial.should_prune():
            raise optuna.exceptions.TrialPruned()
            
        return auc_score
        
    except Exception as e:
        return 0.5  # Return baseline if model fails

def run_frequency_optimization(all_series, all_tstars, all_labels, n_trials=100):
    """
    Run Optuna optimization for frequency features.
    """
    print(f"Starting frequency feature optimization with {n_trials} trials...")
    
    # Create study
    study = optuna.create_study(
        direction='maximize',
        pruner=optuna.pruners.MedianPruner(n_startup_trials=10, n_warmup_steps=5)
    )
    
    # Optimize
    study.optimize(
        lambda trial: objective(trial, all_series, all_tstars, all_labels),
        n_trials=n_trials,
        show_progress_bar=True
    )
    
    print(f"\nOptimization completed!")
    print(f"Best AUC: {study.best_value:.4f}")
    print(f"Best parameters: {study.best_params}")
    
    return study

def extract_best_features(all_series, all_tstars, best_params):
    """
    Extract features using the best parameters found by Optuna.
    """
    print("Extracting features with best parameters...")
    
    # Parse best parameters
    fs = best_params['fs']
    nperseg_base = best_params['nperseg_base']
    nperseg_factor_pre = best_params['nperseg_factor_pre']
    nperseg_factor_post = best_params['nperseg_factor_post']
    n_dominant = best_params['n_dominant']
    psd_method = best_params['psd_method']
    
    freq_bands = None
    if best_params.get('use_freq_bands', False):
        low_cutoff = best_params['low_cutoff']
        mid_cutoff = best_params['mid_cutoff']
        high_cutoff = best_params['high_cutoff']
        freq_bands = {
            'low': (0, low_cutoff * fs/2),
            'mid': (low_cutoff * fs/2, mid_cutoff * fs/2),
            'high': (mid_cutoff * fs/2, high_cutoff * fs/2)
        }
    
    # Extract features
    feature_list = []
    for series, tstar in zip(all_series, all_tstars):
        nperseg_pre = int(nperseg_base * nperseg_factor_pre)
        nperseg_post = int(nperseg_base * nperseg_factor_post)
        
        features = extract_frequency_features(
            series, tstar, fs=fs,
            nperseg_pre=nperseg_pre, nperseg_post=nperseg_post,
            n_dominant=n_dominant, freq_bands=freq_bands,
            psd_method=psd_method
        )
        feature_list.append(features)
    
    feature_df = pd.DataFrame(feature_list)
    feature_df = feature_df.fillna(feature_df.median())
    
    print(f"Extracted {feature_df.shape[1]} frequency features")
    return feature_df

if __name__ == "__main__":
    # Example usage
    print("Frequency Features Optuna Pipeline")
    print("This script optimizes frequency-based feature extraction parameters")
    print("for structural break detection using Optuna hyperparameter optimization.")