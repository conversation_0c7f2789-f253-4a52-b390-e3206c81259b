import numpy as np
import pandas as pd
from scipy.signal import welch, butter, filtfilt, hilbert, medfilt
from scipy.stats import pearsonr, moment
from statsmodels.tsa.stattools import adfuller
import xgboost as xgb
from sklearn.model_selection import cross_val_score
import multiprocessing as mp
from tqdm import tqdm
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

def extract_welch_psd_features(f, psd, prefix=''):
    """Extract Welch PSD-derived features."""
    features = {}
    
    if len(f) < 2 or len(psd) < 2:
        return {f'{prefix}_spectral_{k}': np.nan for k in ['centroid', 'spread', 'skew', 'kurt', 'slope', 'median']}
    
    try:
        # Spectral centroid (mean frequency)
        features[f'{prefix}_spectral_centroid'] = np.sum(f * psd) / np.sum(psd) if np.sum(psd) > 0 else np.nan
        
        # Spectral spread (standard deviation)
        if not np.isnan(features[f'{prefix}_spectral_centroid']):
            features[f'{prefix}_spectral_spread'] = np.sqrt(np.sum(((f - features[f'{prefix}_spectral_centroid']) ** 2) * psd) / np.sum(psd))
        else:
            features[f'{prefix}_spectral_spread'] = np.nan
        
        # Spectral skewness and kurtosis
        if len(psd) > 3:
            features[f'{prefix}_spectral_skew'] = moment(psd, moment=3)
            features[f'{prefix}_spectral_kurt'] = moment(psd, moment=4)
        else:
            features[f'{prefix}_spectral_skew'] = np.nan
            features[f'{prefix}_spectral_kurt'] = np.nan
        
        # Spectral slope (power law exponent)
        valid_idx = (f[1:] > 0) & (psd[1:] > 0)
        if np.sum(valid_idx) > 2:
            features[f'{prefix}_spectral_slope'] = np.polyfit(np.log(f[1:][valid_idx]), np.log(psd[1:][valid_idx]), 1)[0]
        else:
            features[f'{prefix}_spectral_slope'] = np.nan
        
        # Spectral median
        cumsum_psd = np.cumsum(psd)
        median_idx = np.where(cumsum_psd >= np.sum(psd)/2)[0]
        if len(median_idx) > 0:
            features[f'{prefix}_spectral_median'] = f[median_idx[0]]
        else:
            features[f'{prefix}_spectral_median'] = np.nan
            
    except Exception:
        for k in ['centroid', 'spread', 'skew', 'kurt', 'slope', 'median']:
            features[f'{prefix}_spectral_{k}'] = np.nan
    
    return features

def check_stationarity(series, alpha=0.05):
    """Check if series is stationary using ADF test."""
    try:
        result = adfuller(series, autolag='AIC')
        return result[1] <= alpha
    except:
        return True

def make_stationary(series):
    """Apply differencing if series is non-stationary."""
    if len(series) < 10:
        return series
    
    if not check_stationarity(series):
        diff_series = np.diff(series)
        if len(diff_series) > 10 and not check_stationarity(diff_series):
            return np.diff(diff_series)
        return diff_series
    return series

def generate_synthetic_continuation(pre_segment, post_length, fs=500):
    """Generate synthetic signal continuation based on pre-segment characteristics."""
    if len(pre_segment) < 10:
        return np.full(post_length, np.nan)
    
    # Extract dominant frequency from pre-segment
    f_pre, psd_pre = welch(pre_segment, fs=fs, nperseg=min(len(pre_segment)//2, 64))
    dominant_freq = f_pre[np.argmax(psd_pre)]
    
    # Extract amplitude characteristics
    envelope_pre = np.abs(hilbert(pre_segment))
    mean_amplitude = np.mean(envelope_pre)
    
    # Generate time vector for post segment
    t_post = np.arange(post_length) / fs
    
    # Generate synthetic continuation
    synthetic_signal = mean_amplitude * np.sin(2 * np.pi * dominant_freq * t_post)
    
    # Add phase continuity from pre-segment end
    pre_phase = np.angle(hilbert(pre_segment)[-1])
    phase_offset = pre_phase - (2 * np.pi * dominant_freq * 0)
    synthetic_signal = mean_amplitude * np.sin(2 * np.pi * dominant_freq * t_post + phase_offset)
    
    return synthetic_signal, dominant_freq, mean_amplitude
         
def extract_v4_features(series, tstar, fs=500, window_size=5, use_stationarity=False):
    """Extract V4 features with frequency extrapolation analysis."""
    features = {}
    
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    
    # Apply stationarity preprocessing if requested
    if use_stationarity:
        series = make_stationary(series)
        # Adjust tstar for potentially shorter series
        tstar = min(int(tstar), len(series)-1)
    else:
        tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 20 or len(post_segment) < 20:
        return {f'v4_{k}': np.nan for k in range(30)}
    
    try:
        # 1. WELCH PSD FEATURES ON RAW SIGNALS
        f_pre_raw, psd_pre_raw = welch(pre_segment, fs=fs, nperseg=min(len(pre_segment)//2, 64))
        f_post_raw, psd_post_raw = welch(post_segment, fs=fs, nperseg=min(len(post_segment)//2, 64))
        
        # Extract Welch PSD features
        pre_welch = extract_welch_psd_features(f_pre_raw, psd_pre_raw, 'pre')
        post_welch = extract_welch_psd_features(f_post_raw, psd_post_raw, 'post')
        features.update(pre_welch)
        features.update(post_welch)
        
        # PSD feature ratios
        for key in ['spectral_centroid', 'spectral_spread', 'spectral_skew', 'spectral_kurt', 'spectral_slope', 'spectral_median']:
            pre_val = pre_welch.get(f'pre_{key}', np.nan)
            post_val = post_welch.get(f'post_{key}', np.nan)
            features[f'{key}_ratio'] = post_val / pre_val if pre_val != 0 and not np.isnan(pre_val) and not np.isnan(post_val) else np.nan
        
        # 2. FREQUENCY EXTRAPOLATION ANALYSIS
        synthetic_signal, dominant_freq, expected_amplitude = generate_synthetic_continuation(
            pre_segment, len(post_segment), fs
        )
        
        if not np.isnan(synthetic_signal).all():
            # Extrapolation error (MSE)
            features['extrapolation_mse'] = np.mean((post_segment - synthetic_signal) ** 2)
            features['extrapolation_mae'] = np.mean(np.abs(post_segment - synthetic_signal))
            
            # Normalized extrapolation error
            post_var = np.var(post_segment)
            features['extrapolation_error_norm'] = features['extrapolation_mse'] / post_var if post_var > 0 else np.nan
            
            # Correlation with expected continuation
            features['extrapolation_correlation'], _ = pearsonr(post_segment, synthetic_signal)
            
            # Phase drift analysis
            post_analytic = hilbert(post_segment)
            synthetic_analytic = hilbert(synthetic_signal)
            
            phase_post = np.angle(post_analytic)
            phase_synthetic = np.angle(synthetic_analytic)
            phase_diff = np.abs(phase_post - phase_synthetic)
            
            # Unwrap phase differences
            phase_diff = np.unwrap(phase_diff)
            features['phase_drift_mean'] = np.mean(phase_diff)
            features['phase_drift_std'] = np.std(phase_diff)
            features['phase_drift_trend'] = np.polyfit(np.arange(len(phase_diff)), phase_diff, 1)[0] if len(phase_diff) > 1 else np.nan
            
            # Amplitude consistency
            post_envelope = np.abs(post_analytic)
            features['amplitude_consistency'] = np.mean(post_envelope) / expected_amplitude if expected_amplitude > 0 else np.nan
            features['amplitude_deviation'] = np.std(post_envelope - expected_amplitude) / expected_amplitude if expected_amplitude > 0 else np.nan
            
            # Frequency persistence
            dominant_freq_post = f_post_raw[np.argmax(psd_post_raw)]
            features['frequency_persistence'] = dominant_freq_post / dominant_freq if dominant_freq > 0 else np.nan
            features['frequency_drift_abs'] = abs(dominant_freq_post - dominant_freq)
            features['frequency_drift_rel'] = abs(dominant_freq_post - dominant_freq) / dominant_freq if dominant_freq > 0 else np.nan
            
        else:
            # Fill with NaN if synthetic generation failed
            for key in ['extrapolation_mse', 'extrapolation_mae', 'extrapolation_error_norm', 
                       'extrapolation_correlation', 'phase_drift_mean', 'phase_drift_std', 
                       'phase_drift_trend', 'amplitude_consistency', 'amplitude_deviation',
                       'frequency_persistence', 'frequency_drift_abs', 'frequency_drift_rel']:
                features[key] = np.nan
        
        # 3. SMOOTHING AND ADDITIONAL ANALYSIS
        pre_smoothed = medfilt(pre_segment, kernel_size=window_size)
        post_smoothed = medfilt(post_segment, kernel_size=window_size)
        
        # Smoothing effectiveness
        pre_smooth_effect = np.var(pre_segment - pre_smoothed) / np.var(pre_segment) if np.var(pre_segment) > 0 else np.nan
        post_smooth_effect = np.var(post_segment - post_smoothed) / np.var(post_segment) if np.var(post_segment) > 0 else np.nan
        
        features['pre_smoothing_effect'] = pre_smooth_effect
        features['post_smoothing_effect'] = post_smooth_effect
        features['smoothing_effect_ratio'] = post_smooth_effect / pre_smooth_effect if pre_smooth_effect > 0 else np.nan
        
        # Autocorrelation analysis
        def autocorr_at_lag(x, lag=1):
            if len(x) <= lag:
                return np.nan
            return np.corrcoef(x[:-lag], x[lag:])[0, 1] if len(x) > lag else np.nan
        
        ac_pre = autocorr_at_lag(pre_segment, lag=1)
        ac_post = autocorr_at_lag(post_segment, lag=1)
        features['autocorr_change'] = abs(ac_post - ac_pre) if not (np.isnan(ac_pre) or np.isnan(ac_post)) else np.nan
        
        # Cross-correlation for phase shift detection
        if len(pre_segment) >= len(post_segment):
            xcorr = np.correlate(pre_segment, post_segment, mode='valid')
            features['cross_correlation_max'] = np.max(xcorr) / (np.linalg.norm(pre_segment[:len(post_segment)]) * np.linalg.norm(post_segment)) if len(xcorr) > 0 else np.nan
        else:
            xcorr = np.correlate(post_segment, pre_segment, mode='valid')
            features['cross_correlation_max'] = np.max(xcorr) / (np.linalg.norm(post_segment[:len(pre_segment)]) * np.linalg.norm(pre_segment)) if len(xcorr) > 0 else np.nan
            
    except Exception as e:
        return {f'v4_{k}': np.nan for k in range(30)}
    
    return features

def extract_v4_features_wrapper(args):
    """Wrapper for parallel processing."""
    series, tstar, fs, window_size, use_stationarity = args
    return extract_v4_features(series, tstar, fs, window_size, use_stationarity)

def evaluate_v4_approach(all_series, all_tstars, all_labels, window_sizes=[5, 7, 9], test_stationarity=True):
    """Evaluate V4 approach with parallel processing and progress bars."""
    results = {}
    n_processes = max(1, mp.cpu_count() - 1)
    
    print(f"Evaluating V4 approach with {n_processes} processes...")
    
    stationarity_options = [False, True] if test_stationarity else [False]
    
    for use_stationarity in stationarity_options:
        for window_size in window_sizes:
            config_name = f"v4_w{window_size}{'_stat' if use_stationarity else ''}"
            print(f"\nTesting: {config_name}")
            
            # Prepare arguments for parallel processing
            args_list = [(series, tstar, 500, window_size, use_stationarity) 
                        for series, tstar in zip(all_series, all_tstars)]
            
            # Extract features in parallel with progress bar
            with mp.Pool(processes=n_processes) as pool:
                feature_list = list(tqdm(
                    pool.imap(extract_v4_features_wrapper, args_list),
                    total=len(args_list),
                    desc=f"Extracting {config_name}"
                ))
            
            feature_df = pd.DataFrame(feature_list)
            feature_df = feature_df.fillna(feature_df.median())
            
            if feature_df.shape[1] == 0:
                continue
            
            try:
                model = xgb.XGBClassifier(
                    n_estimators=100, random_state=42, 
                    tree_method='gpu_hist', gpu_id=0, eval_metric='auc'
                )
                cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
                auc_score = np.mean(cv_scores)
                auc_std = np.std(cv_scores)
                
                results[config_name] = {
                    'auc_mean': auc_score,
                    'auc_std': auc_std,
                    'n_features': feature_df.shape[1],
                    'window_size': window_size
                }
                
                print(f"AUC: {auc_score:.4f} ± {auc_std:.4f} ({feature_df.shape[1]} features)")
                
                # Feature importance for best result
                if auc_score == max([r.get('auc_mean', 0) for r in results.values()]):
                    model.fit(feature_df, all_labels)
                    feature_importance = pd.DataFrame({
                        'feature': feature_df.columns,
                        'importance': model.feature_importances_
                    }).sort_values('importance', ascending=False)
                    
                    print("Top 10 features:")
                    for _, row in feature_importance.head(10).iterrows():
                        print(f"  {row['feature']}: {row['importance']:.4f}")
                
            except Exception as e:
                print(f"Error: {e}")
                results[config_name] = {'auc_mean': np.nan, 'auc_std': np.nan, 'n_features': 0}
    
    return results

def load_data(data_path):
    """Load time series data from parquet files."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series, all_tstars, all_labels = [], [], []
        
        for id_, group in X_train.groupby(level='id'):
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(int(y_train.loc[id_]))
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    parser = argparse.ArgumentParser(description='Frequency Features Probing v4')
    parser.add_argument('--data_path', type=str, default='.', help='Path to data directory')
    parser.add_argument('--window_sizes', nargs='+', type=int, default=[5, 7, 9],
                       help='Window sizes for median filtering')
    
    args = parser.parse_args()
    
    print("Frequency Features Probing v4")
    print("="*50)
    print("NEW FEATURES:")
    print("• Frequency extrapolation from pre to post segment")
    print("• Synthetic signal generation based on pre-segment characteristics")
    print("• Extrapolation error analysis (MSE, MAE, correlation)")
    print("• Phase drift detection and trend analysis")
    print("• Amplitude consistency and deviation metrics")
    print("• Frequency persistence and drift measurements")
    print("• Cross-correlation for phase shift detection")
    print(f"• Window sizes: {args.window_sizes}")
    
    # Load data
    all_series, all_tstars, all_labels = load_data(args.data_path)
    if all_series is None:
        sys.exit(1)
    
    print(f"Loaded {len(all_series)} time series")
    print(f"Labels distribution: {np.bincount(all_labels)}")
    
    # Run analysis
    results = evaluate_v4_approach(
        all_series, all_tstars, all_labels,
        window_sizes=args.window_sizes
    )
    
    print("\n" + "="*50)
    print("SUMMARY RESULTS")
    print("="*50)
    
    # Sort results by AUC
    sorted_results = sorted(results.items(), key=lambda x: x[1].get('auc_mean', 0), reverse=True)
    
    for config_name, result in sorted_results:
        if not np.isnan(result['auc_mean']):
            print(f"{config_name}: AUC = {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
    
    if sorted_results:
        best_config, best_result = sorted_results[0]
        print(f"\n🏆 Best: {best_config} (AUC = {best_result['auc_mean']:.4f})")

if __name__ == "__main__":
    main()