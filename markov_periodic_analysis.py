#!/usr/bin/env python3
"""
Markov-based odd/even periodicity analysis for time series dataset
Analyzes non-randomness in odd/even indexed patterns using transition matrices
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import chi2_contingency, ks_2samp, rankdata
from sklearn.preprocessing import QuantileTransformer
from joblib import Parallel, delayed
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def preprocess_series(ts):
    """Preprocess time series for periodicity analysis"""
    ts = np.array(ts)
    
    # Remove NaN values
    ts_clean = ts[~np.isnan(ts)]
    if len(ts_clean) < 10:
        return None
    
    # Quantile normalization to [0,1] uniform
    qt = QuantileTransformer(output_distribution='uniform', n_quantiles=min(20, len(ts_clean)))
    ts_normalized = qt.fit_transform(ts_clean.reshape(-1, 1)).flatten()
    
    return ts_normalized

def build_transition_matrix(ts, period_length=2):
    """Build transition matrix for periodic position analysis"""
    if len(ts) < period_length * 2:
        return None
    
    # Create position-based states (0 to period_length-1)
    n_states = period_length
    transitions = np.zeros((n_states, n_states))
    
    for i in range(len(ts) - 1):
        current_pos = i % period_length  # Current position in cycle
        next_pos = (i + 1) % period_length  # Next position in cycle
        
        # Discretize values into high/low states for transition counting
        current_val_state = 1 if ts[i] > 0.5 else 0
        next_val_state = 1 if ts[i + 1] > 0.5 else 0
        
        # Count transitions between value states at different cycle positions
        # Weight by position to capture periodic effects
        weight = 1.0
        if current_pos != next_pos:  # Cross-position transitions
            transitions[current_val_state, next_val_state] += weight
    
    # Normalize to probabilities
    row_sums = transitions.sum(axis=1)
    transitions = np.divide(transitions, row_sums[:, np.newaxis], 
                          out=np.zeros_like(transitions), where=row_sums[:, np.newaxis]!=0)
    
    return transitions

def analyze_multi_period_patterns(ts, periods_to_test=[2, 3, 4, 5, 6, 7, 8, 10, 12]):
    """Analyze multiple periodic patterns using Markov chains and statistical tests"""
    # Preprocess
    ts_processed = preprocess_series(ts)
    if ts_processed is None:
        return None
    
    results = {}
    
    for period in periods_to_test:
        if len(ts_processed) < period * 4:  # Need at least 4 cycles
            continue
            
        try:
            # Split into period-based groups
            period_groups = []
            for phase in range(period):
                phase_values = ts_processed[phase::period]  # indices phase, phase+period, phase+2*period...
                if len(phase_values) >= 3:
                    period_groups.append(phase_values)
            
            if len(period_groups) < 2:
                continue
            
            # Statistical tests between different phases
            max_ks_stat = 0
            min_ks_p = 1.0
            max_wilcoxon_stat = 0
            min_wilcoxon_p = 1.0
            
            # Compare all pairs of phases
            for i in range(len(period_groups)):
                for j in range(i + 1, len(period_groups)):
                    # KS test
                    ks_stat, ks_p = ks_2samp(period_groups[i], period_groups[j])
                    max_ks_stat = max(max_ks_stat, ks_stat)
                    min_ks_p = min(min_ks_p, ks_p)
                    
                    # Wilcoxon test
                    wilcoxon_stat, wilcoxon_p = stats.ranksums(period_groups[i], period_groups[j])
                    max_wilcoxon_stat = max(max_wilcoxon_stat, abs(wilcoxon_stat))
                    min_wilcoxon_p = min(min_wilcoxon_p, wilcoxon_p)
            
            # Build transition matrix for this period
            transition_matrix = build_transition_matrix(ts_processed, period)
            chi2_stat = 0
            chi2_p = 1.0
            
            if transition_matrix is not None and transition_matrix.size > 1:
                try:
                    chi2_stat, chi2_p = chi2_contingency(transition_matrix + 1e-10)[:2]
                except:
                    pass
            
            # Autocorrelation at this period lag
            autocorr_lag = 0
            if len(ts_processed) > period:
                try:
                    autocorr_lag = np.corrcoef(ts_processed[:-period], ts_processed[period:])[0, 1]
                    autocorr_lag = abs(autocorr_lag) if not np.isnan(autocorr_lag) else 0
                except:
                    autocorr_lag = 0
            
            # Phase variance analysis
            phase_means = [np.mean(group) for group in period_groups]
            phase_vars = [np.var(group) for group in period_groups]
            mean_variance = np.var(phase_means)  # Variance between phase means
            max_var_ratio = max(phase_vars) / (min(phase_vars) + 1e-10)
            
            results[f'period_{period}'] = {
                'period': period,
                'chi2_stat': chi2_stat,
                'chi2_p_value': chi2_p,
                'max_ks_stat': max_ks_stat,
                'min_ks_p_value': min_ks_p,
                'max_wilcoxon_stat': max_wilcoxon_stat,
                'min_wilcoxon_p_value': min_wilcoxon_p,
                'autocorr_lag': autocorr_lag,
                'phase_mean_variance': mean_variance,
                'max_variance_ratio': max_var_ratio,
                'n_phases': len(period_groups),
                'min_phase_size': min(len(group) for group in period_groups),
                'series_length': len(ts_processed)
            }
            
        except Exception as e:
            continue
    
    return results

def analyze_single_series(idx_ts_pair):
    """Analyze single time series for multi-period patterns"""
    idx, ts = idx_ts_pair
    
    try:
        result = analyze_multi_period_patterns(ts)
        if result is None:
            return {'index': idx, 'valid': False}
        
        result['index'] = idx
        result['valid'] = True
        return result
        
    except Exception as e:
        return {'index': idx, 'valid': False, 'error': str(e)}

def analyze_dataset_periodicity(series_list, n_jobs=-1):
    """Analyze entire dataset for multi-period patterns"""
    print(f"🔍 Analyzing {len(series_list)} time series for multi-period patterns...")
    
    # Process in parallel
    indexed_series = list(enumerate(series_list))
    results = Parallel(n_jobs=n_jobs)(
        delayed(analyze_single_series)(idx_ts) 
        for idx_ts in tqdm(indexed_series, desc="Processing series")
    )
    
    # Filter valid results
    valid_results = [r for r in results if r.get('valid', False)]
    invalid_count = len(results) - len(valid_results)
    
    print(f"📊 Analysis completed: {len(valid_results)} valid, {invalid_count} invalid series")
    
    if len(valid_results) == 0:
        print("❌ No valid results to analyze")
        return None
    
    # Aggregate statistics across all periods
    periods_tested = [12, 13, 14, 15, 16, 17, 18, 19, 20]
    # periods_tested = [2, 3, 4, 5, 6, 7, 8, 10, 12]
    period_summaries = {}
    
    for period in periods_tested:
        # print(periods_tested)
        period_key = f'period_{period}'
        
        # Extract metrics for this period across all series
        chi2_stats = []
        chi2_p_values = []
        ks_stats = []
        ks_p_values = []
        wilcoxon_stats = []
        wilcoxon_p_values = []
        autocorrs = []
        phase_variances = []
        
        for result in valid_results:
            if period_key in result:
                period_data = result[period_key]
                chi2_stats.append(period_data['chi2_stat'])
                chi2_p_values.append(period_data['chi2_p_value'])
                ks_stats.append(period_data['max_ks_stat'])
                ks_p_values.append(period_data['min_ks_p_value'])
                wilcoxon_stats.append(period_data['max_wilcoxon_stat'])
                wilcoxon_p_values.append(period_data['min_wilcoxon_p_value'])
                autocorrs.append(period_data['autocorr_lag'])
                phase_variances.append(period_data['phase_mean_variance'])
        
        if len(chi2_p_values) == 0:
            continue
            
        # Significance counts
        alpha = 0.05
        significant_chi2 = sum(1 for p in chi2_p_values if p < alpha)
        significant_ks = sum(1 for p in ks_p_values if p < alpha)
        significant_wilcoxon = sum(1 for p in wilcoxon_p_values if p < alpha)
        
        # Strong evidence
        strong_chi2 = sum(1 for p in chi2_p_values if p < 0.01)
        strong_ks = sum(1 for p in ks_p_values if p < 0.01)
        strong_autocorr = sum(1 for a in autocorrs if a > 0.3)
        
        period_summaries[period] = {
            'period': period,
            'n_series': len(chi2_p_values),
            'significant_chi2': significant_chi2,
            'significant_ks': significant_ks,
            'significant_wilcoxon': significant_wilcoxon,
            'strong_chi2': strong_chi2,
            'strong_ks': strong_ks,
            'strong_autocorr': strong_autocorr,
            'mean_chi2_stat': np.mean(chi2_stats),
            'mean_ks_stat': np.mean(ks_stats),
            'mean_autocorr': np.mean(autocorrs),
            'mean_phase_variance': np.mean(phase_variances),
            'significance_rate': (significant_chi2 + significant_ks + significant_wilcoxon) / (3 * len(chi2_p_values)),
            'strong_evidence_rate': (strong_chi2 + strong_ks + strong_autocorr) / (3 * len(chi2_p_values))
        }
    
    # Overall summary
    summary = {
        'total_series': len(series_list),
        'valid_series': len(valid_results),
        'invalid_series': invalid_count,
        'periods_tested': periods_tested,
        'period_summaries': period_summaries
    }
    
    return summary, valid_results

def print_concise_summary(summary):
    """Print concise analysis summary for multi-period patterns"""
    if summary is None:
        print("❌ No valid analysis results")
        return
    
    print("\n" + "="*70)
    print("🎯 MULTI-PERIOD PATTERN ANALYSIS SUMMARY")
    print("="*70)
    
    print(f"📊 Dataset: {summary['total_series']} total, {summary['valid_series']} valid series")
    print(f"🔍 Periods tested: {summary['periods_tested']}")
    
    # Find most significant periods
    period_summaries = summary['period_summaries']
    if not period_summaries:
        print("❌ No period analysis results")
        return
    
    # Sort periods by significance rate
    sorted_periods = sorted(period_summaries.items(), 
                          key=lambda x: x[1]['significance_rate'], reverse=True)
    
    print(f"\n📈 PERIOD-BY-PERIOD RESULTS:")
    print("-" * 70)
    print(f"{'Period':<8} {'Series':<8} {'Sig%':<8} {'Strong%':<8} {'Chi²':<8} {'KS':<8} {'AutoCorr':<8}")
    print("-" * 70)
    
    max_significance = 0
    strongest_period = None
    
    for period, data in sorted_periods:
        sig_rate = data['significance_rate'] * 100
        strong_rate = data['strong_evidence_rate'] * 100
        
        if sig_rate > max_significance:
            max_significance = sig_rate
            strongest_period = period
        
        print(f"{period:<8} {data['n_series']:<8} {sig_rate:<7.1f}% {strong_rate:<7.1f}% "
              f"{data['mean_chi2_stat']:<7.3f} {data['mean_ks_stat']:<7.3f} {data['mean_autocorr']:<7.3f}")
    
    # Overall assessment
    print(f"\n🎯 OVERALL ASSESSMENT:")
    
    if max_significance > 15:
        assessment = f"🔴 STRONG periodicity detected (period {strongest_period})"
    elif max_significance > 10:
        assessment = f"🟡 MODERATE periodicity detected (period {strongest_period})"
    elif max_significance > 6:
        assessment = f"🟢 WEAK periodicity detected (period {strongest_period})"
    else:
        assessment = "⚪ NO significant periodic patterns detected"
    
    print(f"   {assessment}")
    print(f"   Maximum significance rate: {max_significance:.1f}%")
    
    # Highlight top 3 periods
    print(f"\n🏆 TOP 3 MOST SIGNIFICANT PERIODS:")
    for i, (period, data) in enumerate(sorted_periods[:3]):
        sig_rate = data['significance_rate'] * 100
        strong_count = data['strong_chi2'] + data['strong_ks'] + data['strong_autocorr']
        print(f"   {i+1}. Period {period}: {sig_rate:.1f}% significance, {strong_count} strong evidence cases")
    
    # Summary statistics
    all_sig_rates = [data['significance_rate'] for data in period_summaries.values()]
    all_strong_rates = [data['strong_evidence_rate'] for data in period_summaries.values()]
    
    print(f"\n📊 SUMMARY STATISTICS:")
    print(f"   Mean significance rate across periods: {np.mean(all_sig_rates)*100:.1f}%")
    print(f"   Max significance rate: {np.max(all_sig_rates)*100:.1f}%")
    print(f"   Mean strong evidence rate: {np.mean(all_strong_rates)*100:.1f}%")

def analyze_dataset_periodicity_with_breaks(series_list, tstars_list, n_jobs=-1):
    """Analyze dataset for periodicity patterns, separately for pre/post break segments"""
    print(f"🔍 Analyzing {len(series_list)} time series for periodicity (pre/post break)...")
    
    # Create segments for analysis
    pre_segments = []
    post_segments = []
    
    for i, (ts, tstar) in enumerate(zip(series_list, tstars_list)):
        # Pre-break segment
        if tstar > 20:  # Need minimum length for analysis
            pre_segments.append(ts[:tstar])
        
        # Post-break segment  
        if len(ts) - tstar > 20:  # Need minimum length for analysis
            post_segments.append(ts[tstar:])
    
    print(f"📊 Created {len(pre_segments)} pre-break and {len(post_segments)} post-break segments")
    
    # Analyze pre-break segments
    print("🔬 Analyzing pre-break segments...")
    pre_summary, pre_results = analyze_dataset_periodicity(pre_segments, n_jobs=n_jobs)
    
    # Analyze post-break segments
    print("🔬 Analyzing post-break segments...")
    post_summary, post_results = analyze_dataset_periodicity(post_segments, n_jobs=n_jobs)
    
    return pre_summary, post_summary, pre_results, post_results

def print_segment_summary(summary, segment_name):
    """Print summary for a specific segment (pre/post break)"""
    if summary is None:
        print(f"\n❌ No valid {segment_name} analysis results")
        return
    
    print(f"\n" + "="*70)
    print(f"🎯 {segment_name} SEGMENT ANALYSIS")
    print("="*70)
    
    print(f"📊 {segment_name}: {summary['valid_series']} valid segments")
    
    # Find most significant periods
    period_summaries = summary['period_summaries']
    if not period_summaries:
        print(f"❌ No {segment_name.lower()} period analysis results")
        return
    
    # Sort periods by significance rate
    sorted_periods = sorted(period_summaries.items(), 
                          key=lambda x: x[1]['significance_rate'], reverse=True)
    
    max_significance = 0
    strongest_period = None
    
    for period, data in sorted_periods:
        sig_rate = data['significance_rate'] * 100
        if sig_rate > max_significance:
            max_significance = sig_rate
            strongest_period = period
    
    # Assessment
    if max_significance > 15:
        assessment = f"🔴 STRONG periodicity (period {strongest_period})"
    elif max_significance > 10:
        assessment = f"🟡 MODERATE periodicity (period {strongest_period})"
    elif max_significance > 6:
        assessment = f"🟢 WEAK periodicity (period {strongest_period})"
    else:
        assessment = "⚪ NO significant periodicity"
    
    print(f"   {assessment} - {max_significance:.1f}% max significance")
    
    # Top 3 periods
    print(f"   Top periods: ", end="")
    for i, (period, data) in enumerate(sorted_periods[:3]):
        sig_rate = data['significance_rate'] * 100
        print(f"P{period}({sig_rate:.1f}%)", end=" " if i < 2 else "\n")

def print_comparison_summary(pre_summary, post_summary):
    """Compare pre-break vs post-break periodicity"""
    print(f"\n" + "="*70)
    print("🔄 PRE-BREAK vs POST-BREAK COMPARISON")
    print("="*70)
    
    if pre_summary is None or post_summary is None:
        print("❌ Cannot compare - missing data")
        return
    
    # Compare top periods
    pre_periods = pre_summary['period_summaries']
    post_periods = post_summary['period_summaries']
    
    if not pre_periods or not post_periods:
        print("❌ Insufficient data for comparison")
        return
    
    # Find strongest periods in each segment
    pre_max = max(pre_periods.items(), key=lambda x: x[1]['significance_rate'])
    post_max = max(post_periods.items(), key=lambda x: x[1]['significance_rate'])
    
    pre_max_sig = pre_max[1]['significance_rate'] * 100
    post_max_sig = post_max[1]['significance_rate'] * 100
    
    print(f"📊 Strongest periodicity:")
    print(f"   Pre-break:  Period {pre_max[0]} ({pre_max_sig:.1f}% significance)")
    print(f"   Post-break: Period {post_max[0]} ({post_max_sig:.1f}% significance)")
    
    # Overall comparison
    if abs(pre_max_sig - post_max_sig) > 5:
        if pre_max_sig > post_max_sig:
            print(f"🔍 Pre-break shows stronger periodicity (+{pre_max_sig - post_max_sig:.1f}%)")
        else:
            print(f"🔍 Post-break shows stronger periodicity (+{post_max_sig - pre_max_sig:.1f}%)")
    else:
        print(f"🔍 Similar periodicity levels in both segments")
    
    # Period-by-period comparison for common periods
    common_periods = set(pre_periods.keys()) & set(post_periods.keys())
    if common_periods:
        print(f"\n📈 Period-by-period comparison:")
        print(f"{'Period':<8} {'Pre%':<8} {'Post%':<8} {'Diff':<8}")
        print("-" * 32)
        
        for period in sorted(common_periods):
            pre_sig = pre_periods[period]['significance_rate'] * 100
            post_sig = post_periods[period]['significance_rate'] * 100
            diff = post_sig - pre_sig
            
            print(f"{period:<8} {pre_sig:<7.1f}% {post_sig:<7.1f}% {diff:+6.1f}%")

def extract_series_boundary_label(X_train: pd.DataFrame, y_train: pd.Series):
    """Converts X_train with multiindex ['id', 'time'] and y_train Series into:
    - all_series: list of 1D np.ndarrays (float)
    - all_tstars: list of ints (boundary index where period == 1 starts)
    - all_labels: list of 0/1 ints (from y_train)
    """
    all_series = []
    all_tstars = []
    all_labels = []
    
    grouped = X_train.groupby(level='id')
    for id_, group in grouped:
        # Sort by time in case it's not sorted
        group = group.sort_index(level='time')
        values = group['value'].values  # full time series
        periods = group['period'].values  # same length
        
        # Boundary index is first point where period switches to 1
        try:
            tstar = np.where(periods == 1)[0][0]  # first index where period==1
        except IndexError:
            # No break in this series
            tstar = len(values)  # one past end — we won't use it for training
        
        label = int(y_train.loc[id_])  # 0 or 1
        
        all_series.append(values)
        all_tstars.append(tstar)
        all_labels.append(label)
    
    return all_series, all_tstars, all_labels

if __name__ == "__main__":
    # Load dataset
    try:
        # Load from parquet files
        import pandas as pd
        X_train = pd.read_parquet('X_train.parquet')
        y_train = pd.read_parquet('y_train.parquet').squeeze()
        print(f"📁 Loaded dataset: X_train {X_train.shape}, y_train {len(y_train)}")
        
        # Extract time series properly
        series_list, tstars_list, labels_list = extract_series_boundary_label(X_train, y_train)
        print(f"📊 Extracted {len(series_list)} time series")
        
    except Exception as e:
        print(f"❌ Could not load dataset: {e}")
        print("🔧 Generating synthetic test data...")
        
        # Generate test data with some odd/even patterns
        np.random.seed(42)
        series_list = []
        
        # Add some series with odd/even patterns
        for i in range(50):
            ts = np.random.normal(0, 1, 200)
            if i < 10:  # Add odd/even bias to first 10 series
                ts[1::2] += 0.5  # Boost odd indices
            series_list.append(ts)
    
    # Run analysis with pre/post break segments
    pre_summary, post_summary, pre_results, post_results = analyze_dataset_periodicity_with_breaks(
        series_list, tstars_list, n_jobs=-1
    )
    
    # Print summaries
    print_segment_summary(pre_summary, "PRE-BREAK")
    print_segment_summary(post_summary, "POST-BREAK")
    
    # Compare pre vs post
    print_comparison_summary(pre_summary, post_summary)