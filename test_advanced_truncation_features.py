#!/usr/bin/env python3
"""
Test script for the advanced truncation features in Thermodynamics_truncated.py
"""

import numpy as np
import pandas as pd
from Thermodynamics_truncated import AdaptiveTruncationDetector, TruncationAwareThermodynamicAnalyzer

def create_test_data():
    """Create various types of truncated data for testing"""
    np.random.seed(42)
    
    test_data = {}
    
    # 1. Clean data
    test_data['clean'] = np.random.normal(0, 1, 500)
    
    # 2. Hard clipped data
    raw_data = np.random.normal(0, 1, 500)
    test_data['hard_clipped'] = np.clip(raw_data, -0.5, 0.5)
    
    # 3. Precision truncated (rounded to 2 decimals)
    raw_data = np.random.normal(0, 1, 500)
    test_data['precision_truncated'] = np.round(raw_data, 2)
    
    # 4. Quantized data (multiples of 0.1)
    raw_data = np.random.normal(0, 1, 500)
    test_data['quantized'] = np.round(raw_data / 0.1) * 0.1
    
    # 5. Mixed truncation
    raw_data = np.random.normal(0, 1, 500)
    mixed_data = np.clip(raw_data, -0.3, 0.3)  # Boundary clipping
    mixed_data = np.round(mixed_data, 1)  # Precision truncation
    test_data['mixed_truncation'] = mixed_data
    
    # 6. Flat segments
    raw_data = np.random.normal(0, 1, 500)
    # Set random segments to constant values
    flat_indices = np.random.choice(500, 100, replace=False)
    raw_data[flat_indices] = 0.0
    test_data['flat_segments'] = raw_data
    
    return test_data

def test_basic_features():
    """Test basic truncation detection without advanced features"""
    print("🧪 Testing Basic Truncation Detection")
    print("=" * 60)
    
    test_data = create_test_data()
    detector = AdaptiveTruncationDetector()
    
    # Calibrate on all test data
    detector.calibrate_on_dataset(list(test_data.values()), verbose=False)
    
    for name, data in test_data.items():
        print(f"\n📊 {name.upper()}:")
        
        # Basic analysis
        basic_results = detector.compute_truncation_confidence(data, include_advanced_features=False)
        
        print(f"   Truncation Confidence: {basic_results['truncation_confidence']:.3f}")
        print(f"   Precision Quality: {basic_results['precision_quality_score']:.3f}")
        print(f"   Boundary Constraint: {basic_results['boundary_constraint_factor']:.3f}")
        print(f"   Clustering Artifacts: {basic_results['clustering_artifact_score']:.3f}")

def test_advanced_features():
    """Test advanced truncation features"""
    print("\n\n🔬 Testing Advanced Truncation Features")
    print("=" * 60)
    
    test_data = create_test_data()
    detector = AdaptiveTruncationDetector()
    
    # Calibrate on all test data
    detector.calibrate_on_dataset(list(test_data.values()), verbose=False)
    
    for name, data in test_data.items():
        print(f"\n📊 {name.upper()} - Advanced Analysis:")
        
        # Advanced analysis
        advanced_results = detector.compute_truncation_confidence(data, include_advanced_features=True)
        
        print(f"   Truncation Confidence: {advanced_results['truncation_confidence']:.3f}")
        
        # Show key advanced features if they exist
        advanced_keys = [
            'boundary_concentration_ratio',
            'exact_zero_count', 
            'soft_clipping_score',
            'mean_precision',
            'precision_entropy',
            'dominant_step_frequency',
            'histogram_spikiness',
            'composite_truncation_score'
        ]
        
        for key in advanced_keys:
            if key in advanced_results:
                print(f"   {key}: {advanced_results[key]:.3f}")

def test_thermodynamic_analysis_with_features():
    """Test thermodynamic analysis with advanced features"""
    print("\n\n🌡️  Testing Thermodynamic Analysis with Advanced Features")
    print("=" * 60)
    
    test_data = create_test_data()
    analyzer = TruncationAwareThermodynamicAnalyzer()
    
    # Calibrate the truncation detector
    analyzer.truncation_detector.calibrate_on_dataset(list(test_data.values()), verbose=False)
    
    # Test a few key samples
    key_samples = ['clean', 'hard_clipped', 'mixed_truncation']
    
    for name in key_samples:
        if name in test_data:
            data = test_data[name]
            
            print(f"\n📊 {name.upper()} - Thermodynamic Analysis:")
            
            # Analyze with advanced features
            results = analyzer.analyze_time_series(data, include_advanced_features=True)
            
            print(f"   Truncation Confidence: {results['truncation_confidence']:.3f}")
            print(f"   Quality-Weighted Total Energy: {results['quality_weighted_total_energy']:.4f}")
            print(f"   Robust Potential Energy: {results['robust_potential_energy_harmonic']:.4f}")
            print(f"   Robust Kinetic Energy: {results['robust_kinetic_energy_variance_based']:.4f}")
            
            # Show advanced features if available
            if 'composite_truncation_score' in results:
                print(f"   Composite Truncation Score: {results['composite_truncation_score']:.3f}")
            if 'boundary_concentration_ratio' in results:
                print(f"   Boundary Concentration: {results['boundary_concentration_ratio']:.3f}")

def compare_basic_vs_advanced():
    """Compare basic vs advanced feature detection"""
    print("\n\n⚖️  Comparing Basic vs Advanced Feature Detection")
    print("=" * 60)
    
    test_data = create_test_data()
    detector = AdaptiveTruncationDetector()
    detector.calibrate_on_dataset(list(test_data.values()), verbose=False)
    
    print(f"{'Sample':<20} {'Basic Conf':<12} {'Advanced Conf':<15} {'Difference':<12}")
    print("-" * 60)
    
    for name, data in test_data.items():
        # Basic analysis
        basic_results = detector.compute_truncation_confidence(data, include_advanced_features=False)
        basic_conf = basic_results['truncation_confidence']
        
        # Advanced analysis
        advanced_results = detector.compute_truncation_confidence(data, include_advanced_features=True)
        advanced_conf = advanced_results['truncation_confidence']
        
        difference = advanced_conf - basic_conf
        
        print(f"{name:<20} {basic_conf:<12.3f} {advanced_conf:<15.3f} {difference:+12.3f}")

def main():
    """Run all tests"""
    print("🚀 Testing Advanced Truncation Features")
    print("=" * 80)
    
    try:
        test_basic_features()
        test_advanced_features()
        test_thermodynamic_analysis_with_features()
        compare_basic_vs_advanced()
        
        print("\n\n🎉 All tests completed successfully!")
        print("\nAdvanced Features Available:")
        print("  ✅ Boundary concentration analysis")
        print("  ✅ Precision entropy calculation")
        print("  ✅ Quantization step detection")
        print("  ✅ Distribution shape analysis")
        print("  ✅ Histogram anomaly detection")
        print("  ✅ Temporal clipping patterns")
        print("  ✅ Entropy-based measures")
        print("  ✅ Composite truncation scoring")
        
        print(f"\nUsage:")
        print(f"```python")
        print(f"# Enable advanced features")
        print(f"results = detector.compute_truncation_confidence(ts, include_advanced_features=True)")
        print(f"")
        print(f"# Or in thermodynamic analysis")
        print(f"results = analyzer.analyze_time_series(ts, include_advanced_features=True)")
        print(f"```")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()