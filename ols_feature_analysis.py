"""
Analysis of OLS-derived features from 1280_TSFRESHplusCatch22.py
Cross-validation evaluation of structural break test features
"""

import pandas as pd
import numpy as np
import joblib
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
from sklearn.metrics import roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns

def analyze_ols_features():
    """Analyze OLS-derived structural break test features"""
    
    print("Loading 1280 feature dataset...")
    X = joblib.load('1280_tsfresh_catch22_cca')
    y_train = pd.read_parquet('y_train.parquet')
    y = y_train.iloc[:len(X)].values.ravel()
    
    print(f"Dataset shape: {X.shape}")
    
    # Identify OLS-derived features
    ols_features = [
        'p_chow', 'score_chow', 'chow_stat',
        'p_wald', 'score_wald', 'wald_stat', 
        'p_supf', 'score_supf', 'supf_stat',
        'p_bp', 'score_bp', 'bp_stat'
    ]
    
    # Find all OLS feature columns (including binned versions)
    ols_columns = []
    for feature in ols_features:
        matching_cols = [col for col in X.columns if feature in col]
        ols_columns.extend(matching_cols)
    
    print(f"Found {len(ols_columns)} OLS-derived features:")
    for col in ols_columns[:10]:  # Show first 10
        print(f"  {col}")
    if len(ols_columns) > 10:
        print(f"  ... and {len(ols_columns) - 10} more")
    
    # Extract OLS features
    X_ols = X[ols_columns].fillna(X[ols_columns].median())
    
    # Remove constant features
    constant_features = X_ols.columns[X_ols.std() == 0]
    if len(constant_features) > 0:
        print(f"Removing {len(constant_features)} constant features")
        X_ols = X_ols.drop(columns=constant_features)
    
    print(f"Final OLS feature matrix: {X_ols.shape}")
    
    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    # Test different models
    models = {
        'LightGBM': lgb.LGBMClassifier(n_estimators=100, random_state=42, verbose=-1),
        'RandomForest': RandomForestClassifier(n_estimators=100, random_state=42)
    }
    
    results = {}
    
    print("\nCross-validation results for OLS features only:")
    print("-" * 50)
    
    for name, model in models.items():
        scores = cross_val_score(model, X_ols, y, cv=cv, scoring='roc_auc')
        results[name] = {
            'mean': scores.mean(),
            'std': scores.std(),
            'scores': scores
        }
        print(f"{name}: {scores.mean():.4f} ± {scores.std():.4f}")
    
    # Compare with all features
    print("\nComparison with all 1280 features:")
    print("-" * 50)
    
    X_all = X.fillna(X.median())
    constant_features_all = X_all.columns[X_all.std() == 0]
    if len(constant_features_all) > 0:
        X_all = X_all.drop(columns=constant_features_all)
    
    for name, model in models.items():
        scores_all = cross_val_score(model, X_all, y, cv=cv, scoring='roc_auc')
        scores_ols = results[name]['scores']
        
        print(f"{name}:")
        print(f"  All features: {scores_all.mean():.4f} ± {scores_all.std():.4f}")
        print(f"  OLS only:     {scores_ols.mean():.4f} ± {scores_ols.std():.4f}")
        print(f"  Difference:   {scores_all.mean() - scores_ols.mean():.4f}")
    
    # Feature importance analysis
    print("\nOLS Feature Importance Analysis:")
    print("-" * 50)
    
    # Train LightGBM for feature importance
    lgb_model = lgb.LGBMClassifier(n_estimators=100, random_state=42, verbose=-1)
    lgb_model.fit(X_ols, y)
    
    # Get feature importance
    feature_importance = pd.DataFrame({
        'feature': X_ols.columns,
        'importance': lgb_model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print("Top 15 most important OLS features:")
    for i, (_, row) in enumerate(feature_importance.head(15).iterrows()):
        print(f"{i+1:2d}. {row['feature'][:50]:<50} ({row['importance']:.4f})")
    
    # Analyze feature categories
    print("\nOLS Feature Category Analysis:")
    print("-" * 50)
    
    categories = {
        'Chow Test': ['chow'],
        'Wald Test': ['wald'], 
        'supF Test': ['supf'],
        'Bai-Perron Test': ['bp'],
        'P-values': ['p_'],
        'Statistics': ['_stat'],
        'Scores': ['score_'],
        'Binned': ['bin']
    }
    
    category_importance = {}
    for category, patterns in categories.items():
        matching_features = feature_importance[
            feature_importance['feature'].str.contains('|'.join(patterns), case=False)
        ]
        if len(matching_features) > 0:
            category_importance[category] = {
                'count': len(matching_features),
                'total_importance': matching_features['importance'].sum(),
                'mean_importance': matching_features['importance'].mean()
            }
    
    for category, stats in category_importance.items():
        print(f"{category:<15}: {stats['count']:2d} features, "
              f"total: {stats['total_importance']:.3f}, "
              f"mean: {stats['mean_importance']:.4f}")
    
    # Create visualization
    plt.figure(figsize=(12, 8))
    
    # Top features plot
    plt.subplot(2, 2, 1)
    top_15 = feature_importance.head(15)
    plt.barh(range(len(top_15)), top_15['importance'])
    plt.yticks(range(len(top_15)), [f[:25] for f in top_15['feature']])
    plt.xlabel('Feature Importance')
    plt.title('Top 15 OLS Features')
    plt.gca().invert_yaxis()
    
    # Category importance
    plt.subplot(2, 2, 2)
    categories_plot = list(category_importance.keys())
    importances_plot = [category_importance[cat]['total_importance'] for cat in categories_plot]
    plt.bar(categories_plot, importances_plot)
    plt.xlabel('Feature Category')
    plt.ylabel('Total Importance')
    plt.title('OLS Feature Categories')
    plt.xticks(rotation=45)
    
    # Model comparison
    plt.subplot(2, 2, 3)
    model_names = list(results.keys())
    ols_scores = [results[name]['mean'] for name in model_names]
    
    # Get all feature scores for comparison
    all_scores = []
    for name, model in models.items():
        scores = cross_val_score(model, X_all, y, cv=cv, scoring='roc_auc')
        all_scores.append(scores.mean())
    
    x = np.arange(len(model_names))
    width = 0.35
    
    plt.bar(x - width/2, all_scores, width, label='All Features', alpha=0.8)
    plt.bar(x + width/2, ols_scores, width, label='OLS Only', alpha=0.8)
    plt.xlabel('Models')
    plt.ylabel('ROC AUC')
    plt.title('Model Performance Comparison')
    plt.xticks(x, model_names)
    plt.legend()
    
    # Feature importance distribution
    plt.subplot(2, 2, 4)
    plt.hist(feature_importance['importance'], bins=20, alpha=0.7)
    plt.xlabel('Feature Importance')
    plt.ylabel('Frequency')
    plt.title('OLS Feature Importance Distribution')
    
    plt.tight_layout()
    plt.savefig('ols_feature_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    # Summary
    print("\nSUMMARY:")
    print("=" * 50)
    print(f"• OLS features alone achieve {results['LightGBM']['mean']:.4f} ROC AUC")
    print(f"• This is {results['LightGBM']['mean']/0.5 - 1:.1%} better than random")
    print(f"• Top OLS feature: {feature_importance.iloc[0]['feature']}")
    print(f"• Most important category: {max(category_importance.items(), key=lambda x: x[1]['total_importance'])[0]}")
    
    # Save results
    analysis_results = {
        'ols_features': ols_columns,
        'cv_results': results,
        'feature_importance': feature_importance.to_dict('records'),
        'category_importance': category_importance
    }
    
    joblib.dump(analysis_results, 'ols_feature_analysis_results.joblib')
    print(f"• Results saved to: ols_feature_analysis_results.joblib")

if __name__ == "__main__":
    analyze_ols_features()