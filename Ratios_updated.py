

import numpy as np
import pandas as pd
from tsfresh.feature_extraction.feature_calculators import (
    energy_ratio_by_chunks, fft_aggregated, index_mass_quantile,
    binned_entropy, last_location_of_maximum, lempel_ziv_complexity,
    longest_strike_above_mean, longest_strike_below_mean,
    mean_second_derivative_central, percentage_of_reoccurring_datapoints_to_all_datapoints,
    percentage_of_reoccurring_values_to_all_values, ratio_beyond_r_sigma,
    ratio_value_number_to_time_series_length
)
from scipy.stats import norm, kstest, anderson_ksamp, cramervonmises_2samp
from scipy.stats import norm, kstest, anderson, cramervonmises
from statsmodels.stats.diagnostic import lilliefors
from statsmodels.tsa.stattools import coint
from statsmodels.stats.stattools import jarque_bera
from statsmodels.regression.linear_model import OLS
import numpy as np
import pycatch22
from scipy.signal import periodogram, welch, spectrogram, find_peaks, peak_prominences, savgol_filter, detrend, hilbert
from scipy import stats
from statsmodels.tsa.stattools import adfuller, kpss
from statsmodels.tsa.stattools import grangercausalitytests
from arch import arch_model
import warnings
from multiprocessing import Pool
from functools import partial
import logging

def safe_coint(x, y):
    """Safe cointegration test with length alignment."""
    try:
        x_clean = x[~np.isnan(x)]
        y_clean = y[~np.isnan(y)]
        min_len = min(len(x_clean), len(y_clean))
        if min_len < 10:
            return np.nan
        return coint(x_clean[:min_len], y_clean[:min_len])[0]
    except Exception:
        return np.nan
def safe_granger_causality(x, y, maxlag=1):
    """
    Safe implementation of Granger causality test handling length mismatches.
    """
    if len(x) < 10 or len(y) < 10:
        return np.nan
    
    try:
        # Remove NaNs
        x_clean = x[~np.isnan(x)]
        y_clean = y[~np.isnan(y)]
        
        # Ensure equal length by truncating to minimum
        min_len = min(len(x_clean), len(y_clean))
        if min_len < 10:
            return np.nan
            
        x_aligned = x_clean[:min_len]
        y_aligned = y_clean[:min_len]
        
        # Create the data matrix for Granger test
        data = np.column_stack([y_aligned, x_aligned])  # [dependent, independent]
        
        # Perform Granger causality test
        result = grangercausalitytests(data, maxlag=maxlag, verbose=False)
        return result[maxlag][0]['ssr_ftest'][0]
        
    except Exception:
        return np.nan
# Suppress warnings
warnings.filterwarnings('ignore')
def ryan_joiner_test(arr):
    if len(arr) < 4:
        return np.nan
    try:
        arr = arr[~np.isnan(arr)]
        sorted_data = np.sort(arr)
        n = len(sorted_data)
        mu, sigma = np.mean(sorted_data), np.std(sorted_data, ddof=1)
        if sigma == 0:
            return np.nan
        z_scores = (sorted_data - mu) / sigma
        expected_quantiles = norm.ppf((np.arange(1, n + 1) - 0.375) / (n + 0.25))
        correlation = np.corrcoef(z_scores, expected_quantiles)[0, 1]
        return correlation
    except Exception:
        return np.nan

def cliffs_delta(x, y):
    if len(x) < 2 or len(y) < 2:
        return np.nan
    try:
        x, y = x[~np.isnan(x)], y[~np.isnan(y)]
        n_x, n_y = len(x), len(y)
        diffs = np.array([1 if xi > yj else (-1 if xi < yj else 0) for xi in x for yj in y])
        return np.sum(diffs) / (n_x * n_y)
    except Exception:
        return np.nan

def jensen_shannon_divergence(p, q):
    if len(p) < 2 or len(q) < 2:
        return np.nan
    try:
        p, q = p[~np.isnan(p)], q[~np.isnan(q)]
        p_hist, _ = np.histogram(p, bins=50, density=True)
        q_hist, _ = np.histogram(q, bins=50, density=True)
        p_hist = p_hist / np.sum(p_hist) + 1e-10
        q_hist = q_hist / np.sum(q_hist) + 1e-10
        m = 0.5 * (p_hist + q_hist)
        jsd = 0.5 * (np.sum(p_hist * np.log2(p_hist / m)) + np.sum(q_hist * np.log2(q_hist / m)))
        return jsd
    except Exception:
        return np.nan

def welch_ttest(x, y):
    if len(x) < 2 or len(y) < 2:
        return np.nan
    try:
        x, y = x[~np.isnan(x)], y[~np.isnan(y)]
        t_stat, _ = stats.ttest_ind(x, y, equal_var=False)
        return t_stat
    except Exception:
        return np.nan

def cohens_d(x, y):
    if len(x) < 2 or len(y) < 2:
        return np.nan
    try:
        x, y = x[~np.isnan(x)], y[~np.isnan(y)]
        n_x, n_y = len(x), len(y)
        mean_x, mean_y = np.mean(x), np.mean(y)
        var_x, var_y = np.var(x, ddof=1), np.var(y, ddof=1)
        pooled_std = np.sqrt(((n_x - 1) * var_x + (n_y - 1) * var_y) / (n_x + n_y - 2))
        return (mean_x - mean_y) / pooled_std if pooled_std > 0 else np.nan
    except Exception:
        return np.nan

def glass_delta(x, y):
    if len(x) < 2 or len(y) < 2:
        return np.nan
    try:
        x, y = x[~np.isnan(x)], y[~np.isnan(y)]
        mean_x, mean_y = np.mean(x), np.mean(y)
        std_x = np.std(x, ddof=1)
        return (mean_x - mean_y) / std_x if std_x > 0 else np.nan
    except Exception:
        return np.nan

def chow_test(pre, post, tstar):
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        whole = np.concatenate([pre, post])
        n = len(whole)
        X = np.column_stack([np.ones(n), np.arange(n), np.arange(n) >= tstar])
        y = whole
        model = OLS(y, X).fit()
        residuals = model.resid
        ssr_full = np.sum(residuals ** 2)
        model_pre = OLS(pre, np.column_stack([np.ones(len(pre)), np.arange(len(pre))])).fit()
        model_post = OLS(post, np.column_stack([np.ones(len(post)), np.arange(len(post))])).fit()
        ssr_pre = np.sum(model_pre.resid ** 2)
        ssr_post = np.sum(model_post.resid ** 2)
        k = X.shape[1]
        chow_stat = ((ssr_full - (ssr_pre + ssr_post)) / k) / ((ssr_pre + ssr_post) / (n - 2 * k))
        return chow_stat
    except Exception:
        return np.nan

def wald_test(pre, post):
    if len(pre) < 5 or len(post) < 5:
        return np.nan
    try:
        whole = np.concatenate([pre, post])
        n = len(whole)
        X = np.column_stack([np.ones(n), np.arange(n)])
        y = whole
        model = OLS(y, X).fit()
        return model.wald_test('x1=0').statistic[0][0] if hasattr(model.wald_test('x1=0').statistic, '__getitem__') else np.nan
    except Exception:
        return np.nan

def supf_test(series, min_length=10):
    if len(series) < min_length:
        return np.nan
    try:
        n = len(series)
        f_stats = []
        for t in range(int(0.15 * n), int(0.85 * n)):
            X = np.column_stack([np.ones(n), np.arange(n), np.arange(n) >= t])
            y = series
            model = OLS(y, X).fit()
            residuals = model.resid
            ssr = np.sum(residuals ** 2)
            f_stats.append(ssr)
        return max(f_stats) if f_stats else np.nan
    except Exception:
        return np.nan

def cusum_test(series):
    if len(series) < 5:
        return np.nan
    try:
        mean = np.mean(series)
        cusum = np.cumsum(series - mean)
        return np.max(np.abs(cusum)) / np.std(series, ddof=1)
    except Exception:
        return np.nan

def bai_perron_test(series, min_length=15):
    if len(series) < min_length:
        return np.nan
    try:
        n = len(series)
        f_stats = []
        for t in range(int(0.15 * n), int(0.85 * n)):
            X = np.column_stack([np.ones(n), np.arange(n), np.arange(n) >= t])
            y = series
            model = OLS(y, X).fit()
            residuals = model.resid
            ssr = np.sum(residuals ** 2)
            f_stats.append(ssr)
        return np.mean(f_stats) if f_stats else np.nan
    except Exception:
        return np.nan
# Logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
def compute_features_for_segment(pre, post, whole, segment_name_prefix='', compute_pre_post=True, 
                                reference_features=None, normalize=False, tstar=None, 
                                whole_indices=None, weight_type='proportion', sigma=0.1, 
                                compute_jsd=False):
    feats = {}
    if not compute_pre_post or len(pre) < 5 or len(post) < 5:
        return feats  # Return empty dict if pre/post computation is disabled or insufficient data

    # Compute standard deviation for normalization (used in some ratios)
    # std_whole = np.nanstd(whole, ddof=1) if len(whole) > 1 else np.nan

    # Above and Below Mean Segments
    mean_whole = np.nanmean(whole)
    above_mean = whole[whole > mean_whole]
    below_mean = whole[whole < mean_whole]
    pre_above_mean = pre[pre > np.nanmean(pre)]
    pre_below_mean = pre[pre < np.nanmean(pre)]
    post_above_mean = post[post > np.nanmean(post)]
    post_below_mean = post[post < np.nanmean(post)]

    # Length Ratios (Important for structural break detection)
    len_pre = len(pre)
    len_post = len(post)
    len_whole = len(whole)
    feats[f'{segment_name_prefix}ratio_post_pre_length'] = len_post / len_pre if len_pre > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_pre_whole_length'] = len_pre / len_whole if len_whole > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_post_whole_length'] = len_post / len_whole if len_whole > 0 else np.nan
    
    # Above/Below segment length ratios
    len_above_mean = len(above_mean)
    len_below_mean = len(below_mean)
    len_pre_above = len(pre_above_mean)
    len_pre_below = len(pre_below_mean)
    len_post_above = len(post_above_mean)
    len_post_below = len(post_below_mean)
    
    feats[f'{segment_name_prefix}ratio_above_below_length'] = len_above_mean / len_below_mean if len_below_mean > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_pre_above_below_length'] = len_pre_above / len_pre_below if len_pre_below > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_post_above_below_length'] = len_post_above / len_post_below if len_post_below > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_above_length_change'] = len_post_above / len_pre_above if len_pre_above > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_below_length_change'] = len_post_below / len_pre_below if len_pre_below > 0 else np.nan

    # Volatility and Range Ratios
    range_pre = np.nanmax(pre) - np.nanmin(pre) if len(pre) > 0 else np.nan
    range_post = np.nanmax(post) - np.nanmin(post) if len(post) > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_range'] = range_post / range_pre if (range_pre and range_pre != 0 and not np.isnan(range_post)) else np.nan
    
    # Volatility (first differences standard deviation)
    vol_pre = np.nanstd(np.diff(pre)) if len(pre) > 1 else np.nan
    vol_post = np.nanstd(np.diff(post)) if len(post) > 1 else np.nan
    feats[f'{segment_name_prefix}ratio_volatility'] = vol_post / vol_pre if (vol_pre and vol_pre != 0 and not np.isnan(vol_post)) else np.nan
    # Stationarity Test Ratios (ADF and KPSS statistics as ratios)
    def safe_adf_stat(arr):
        try:
            if len(arr) < 10:
                return np.nan
            return adfuller(arr[~np.isnan(arr)], maxlag=1)[0]
        except Exception:
            return np.nan
    
    def safe_kpss_stat(arr):
        try:
            if len(arr) < 10:
                return np.nan
            return kpss(arr[~np.isnan(arr)], nlags='auto')[0]
        except Exception:
            return np.nan
    
    adf_pre = safe_adf_stat(pre)
    adf_post = safe_adf_stat(post)
    kpss_pre = safe_kpss_stat(pre)
    kpss_post = safe_kpss_stat(post)
    
    feats[f'{segment_name_prefix}ratio_adf_stat'] = adf_post / adf_pre if (adf_pre and adf_pre != 0 and not np.isnan(adf_post)) else np.nan
    feats[f'{segment_name_prefix}ratio_kpss_stat'] = kpss_post / kpss_pre if (kpss_pre and kpss_pre != 0 and not np.isnan(kpss_post)) else np.nan

    # Basic Statistical Ratios
    mean_pre = np.nanmean(pre) if len(pre) > 0 else np.nan
    mean_post = np.nanmean(post) if len(post) > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_mean'] = mean_post / mean_pre if (mean_pre and mean_pre != 0 and not np.isnan(mean_post)) else np.nan

    std_pre = np.nanstd(pre, ddof=1) if len(pre) > 1 else np.nan
    std_post = np.nanstd(post, ddof=1) if len(post) > 1 else np.nan
    feats[f'{segment_name_prefix}ratio_std'] = std_post / std_pre if (std_pre and std_pre > 0 and not np.isnan(std_post)) else np.nan

    var_pre = np.nanvar(pre, ddof=1) if len(pre) > 1 else np.nan
    var_post = np.nanvar(post, ddof=1) if len(post) > 1 else np.nan
    feats[f'{segment_name_prefix}ratio_var'] = var_post / var_pre if (var_pre and var_pre > 0 and not np.isnan(var_post)) else np.nan

    skew_pre = stats.skew(pre, nan_policy='omit') if len(pre) > 2 else np.nan
    skew_post = stats.skew(post, nan_policy='omit') if len(post) > 2 else np.nan
    feats[f'{segment_name_prefix}ratio_skew'] = skew_post / skew_pre if (skew_pre and skew_pre != 0 and not np.isnan(skew_post)) else np.nan

    kurt_pre = stats.kurtosis(pre, nan_policy='omit') if len(pre) > 2 else np.nan
    kurt_post = stats.kurtosis(post, nan_policy='omit') if len(post) > 2 else np.nan
    feats[f'{segment_name_prefix}ratio_kurt'] = kurt_post / kurt_pre if (kurt_pre and kurt_pre != 0 and not np.isnan(kurt_post)) else np.nan

    # TSFRESH Feature Ratios
    def compute_tsfresh_feature(feature_func, arr, params=None, min_length=2):
        try:
            if len(arr) < min_length:
                return np.nan
            if params:
                result = feature_func(arr, params)
                if isinstance(result, list) and len(result) > 0:
                    return float(result[0][1]) if len(result[0]) > 1 else float(result)
                return float(result)
            return float(feature_func(arr))
        except Exception:
            return np.nan

    # Energy Ratio by Chunks
    for i in range(2):
        ratio_pre = compute_tsfresh_feature(
            energy_ratio_by_chunks, pre, [{"num_segments": 2, "segment_focus": i}], min_length=2
        )
        ratio_post = compute_tsfresh_feature(
            energy_ratio_by_chunks, post, [{"num_segments": 2, "segment_focus": i}], min_length=2
        )
        feats[f'{segment_name_prefix}ratio_energy_ratio_chunk{i+1}'] = (
            ratio_post / ratio_pre if (ratio_pre and ratio_pre != 0 and not np.isnan(ratio_post)) else np.nan
        )

    # FFT Aggregated
    for aggtype in ["centroid", "variance"]:
        fft_pre = compute_tsfresh_feature(
            fft_aggregated, pre, [{"aggtype": aggtype}], min_length=10
        )
        fft_post = compute_tsfresh_feature(
            fft_aggregated, post, [{"aggtype": aggtype}], min_length=10
        )
        feats[f'{segment_name_prefix}ratio_fft_agg_{aggtype}'] = (
            fft_post / fft_pre if (fft_pre and fft_pre != 0 and not np.isnan(fft_post)) else np.nan
        )

    # Index Mass Quantile
    for q in [0.25, 0.5, 0.75]:
        imq_pre = compute_tsfresh_feature(index_mass_quantile, pre, [{"q": q}], min_length=5)
        imq_post = compute_tsfresh_feature(index_mass_quantile, post, [{"q": q}], min_length=5)
        feats[f'{segment_name_prefix}ratio_index_mass_quantile_q{int(q*100)}'] = (
            imq_post / imq_pre if (imq_pre and imq_pre != 0 and not np.isnan(imq_post)) else np.nan
        )

    # Binned Entropy
    be_pre = compute_tsfresh_feature(binned_entropy, pre, {"max_bins": 10}, min_length=5)
    be_post = compute_tsfresh_feature(binned_entropy, post, {"max_bins": 10}, min_length=5)
    feats[f'{segment_name_prefix}ratio_binned_entropy'] = (
        be_post / be_pre if (be_pre and be_pre != 0 and not np.isnan(be_post)) else np.nan
    )

    # Last Location of Maximum
    llm_pre = compute_tsfresh_feature(last_location_of_maximum, pre, min_length=2)
    llm_post = compute_tsfresh_feature(last_location_of_maximum, post, min_length=2)
    feats[f'{segment_name_prefix}ratio_last_location_of_maximum'] = (
        llm_post / llm_pre if (llm_pre and llm_pre != 0 and not np.isnan(llm_post)) else np.nan
    )

    # Lempel-Ziv Complexity
    lzc_pre = compute_tsfresh_feature(lempel_ziv_complexity, pre, 100, min_length=10)
    lzc_post = compute_tsfresh_feature(lempel_ziv_complexity, post, 100, min_length=10)
    feats[f'{segment_name_prefix}ratio_lempel_ziv_complexity'] = (
        lzc_post / lzc_pre if (lzc_pre and lzc_pre != 0 and not np.isnan(lzc_post)) else np.nan
    )

    # Mean Second Derivative Central
    msdc_pre = compute_tsfresh_feature(mean_second_derivative_central, pre, min_length=3)
    msdc_post = compute_tsfresh_feature(mean_second_derivative_central, post, min_length=3)
    feats[f'{segment_name_prefix}ratio_mean_second_derivative_central'] = (
        msdc_post / msdc_pre if (msdc_pre and msdc_pre != 0 and not np.isnan(msdc_post)) else np.nan
    )

    # Percentage of Reoccurring Datapoints/Values
    prd_pre = compute_tsfresh_feature(percentage_of_reoccurring_datapoints_to_all_datapoints, pre, min_length=2)
    prd_post = compute_tsfresh_feature(percentage_of_reoccurring_datapoints_to_all_datapoints, post, min_length=2)
    prv_pre = compute_tsfresh_feature(percentage_of_reoccurring_values_to_all_values, pre, min_length=2)
    prv_post = compute_tsfresh_feature(percentage_of_reoccurring_values_to_all_values, post, min_length=2)
    feats[f'{segment_name_prefix}ratio_pct_reoccurring_datapoints'] = (
        prd_post / prd_pre if (prd_pre and prd_pre != 0 and not np.isnan(prd_post)) else np.nan
    )
    feats[f'{segment_name_prefix}ratio_pct_reoccurring_values'] = (
        prv_post / prv_pre if (prv_pre and prv_pre != 0 and not np.isnan(prv_post)) else np.nan
    )

    # Ratio Unique Values
    ruv_pre = compute_tsfresh_feature(ratio_value_number_to_time_series_length, pre, min_length=2)
    ruv_post = compute_tsfresh_feature(ratio_value_number_to_time_series_length, post, min_length=2)
    feats[f'{segment_name_prefix}ratio_unique_values'] = (
        ruv_post / ruv_pre if (ruv_pre and ruv_pre != 0 and not np.isnan(ruv_post)) else np.nan
    )

    # Autocorrelation and AR(1) Ratios
    def lag1_autocorr(arr):
        if len(arr) > 1:
            arr0 = arr - np.nanmean(arr)
            return np.corrcoef(arr0[:-1], arr0[1:])[0, 1] if len(arr0) > 1 else np.nan
        return np.nan
    ac_pre = lag1_autocorr(pre)
    ac_post = lag1_autocorr(post)
    feats[f'{segment_name_prefix}ratio_ac'] = (
        ac_post / ac_pre if (ac_pre and ac_pre != 0 and not np.isnan(ac_post)) else np.nan
    )

    def estimate_ar1(arr):
        if len(arr) > 1:
            x_prev = arr[:-1]
            x_next = arr[1:]
            phi = np.polyfit(x_prev, x_next, deg=1)[0]
            return phi
        return np.nan
    phi_pre = estimate_ar1(pre)
    phi_post = estimate_ar1(post)
    feats[f'{segment_name_prefix}ratio_phi'] = (
        phi_post / phi_pre if (phi_pre and phi_pre != 0 and not np.isnan(phi_post)) else np.nan
    )

    # GARCH Volatility Ratio
    def garch_volatility(arr):
        if len(arr) > 30:
            try:
                model = arch_model(arr, vol='Garch', p=1, q=1, rescale=True).fit(disp='off')
                return np.mean(np.sqrt(model.conditional_volatility))
            except Exception:
                return np.nan
        return np.nan
    garch_pre = garch_volatility(pre)
    garch_post = garch_volatility(post)
    feats[f'{segment_name_prefix}ratio_garch'] = (
        garch_post / garch_pre if (garch_pre and garch_pre != 0 and not np.isnan(garch_post)) else np.nan
    )

    # Spectral Power Ratios
    def spectral_power_periodogram(arr):
        if len(arr) > 10:
            try:
                f, Pxx = periodogram(arr, fs=1.0, scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan
    power_pre = spectral_power_periodogram(pre)
    power_post = spectral_power_periodogram(post)
    feats[f'{segment_name_prefix}ratio_power_periodogram'] = (
        power_post / power_pre if (power_pre and power_pre != 0 and not np.isnan(power_post)) else np.nan
    )

    def spectral_power_welch(arr):
        if len(arr) > 10:
            try:
                f, Pxx = welch(arr, nperseg=min(len(arr)//2, 256), scaling='spectrum')
                return np.sum(Pxx)
            except Exception:
                return np.nan
        return np.nan
    power_pre_welch = spectral_power_welch(pre)
    power_post_welch = spectral_power_welch(post)
    feats[f'{segment_name_prefix}ratio_power_welch'] = (
        power_post_welch / power_pre_welch if (power_pre_welch and power_pre_welch != 0 and not np.isnan(power_post_welch)) else np.nan
    )

    # Spectrogram Entropy Ratio
    def spectrogram_entropy(seg):
        if len(seg) < 20:
            return np.nan
        try:
            f, t, Sxx = spectrogram(seg, fs=1.0, nperseg=min(100, len(seg)//2), noverlap=None)
            Sxx = Sxx / np.sum(Sxx) if np.sum(Sxx) != 0 else np.ones_like(Sxx) / Sxx.size
            return -np.sum(Sxx * np.log2(Sxx + 1e-10))
        except Exception:
            return np.nan
    spec_entropy_pre = spectrogram_entropy(pre)
    spec_entropy_post = spectrogram_entropy(post)
    feats[f'{segment_name_prefix}ratio_spec_entropy'] = (
        spec_entropy_post / spec_entropy_pre if (spec_entropy_pre and spec_entropy_pre != 0 and not np.isnan(spec_entropy_post)) else np.nan
    )

    # Peak Finding Ratio
    if len(pre) > 5 and len(post) > 5:
        try:
            peaks_pre, _ = find_peaks(pre, height=np.mean(pre) + np.std(pre))
            peaks_post, _ = find_peaks(post, height=np.mean(post) + np.std(post))
            num_peaks_pre = len(peaks_pre)
            num_peaks_post = len(peaks_post)
            feats[f'{segment_name_prefix}ratio_num_peaks'] = (
                num_peaks_post / num_peaks_pre if (num_peaks_pre and num_peaks_pre != 0) else np.nan
            )
            prominences_pre = peak_prominences(pre, peaks_pre)[0] if len(peaks_pre) > 0 else np.array([np.nan])
            prominences_post = peak_prominences(post, peaks_post)[0] if len(peaks_post) > 0 else np.array([np.nan])
            mean_prominence_pre = np.mean(prominences_pre) if len(prominences_pre) > 0 else np.nan
            mean_prominence_post = np.mean(prominences_post) if len(prominences_post) > 0 else np.nan
            feats[f'{segment_name_prefix}ratio_mean_prominence'] = (
                mean_prominence_post / mean_prominence_pre if (mean_prominence_pre and mean_prominence_pre != 0 and not np.isnan(mean_prominence_post)) else np.nan
            )
        except Exception:
            feats[f'{segment_name_prefix}ratio_num_peaks'] = np.nan
            feats[f'{segment_name_prefix}ratio_mean_prominence'] = np.nan
    else:
        feats[f'{segment_name_prefix}ratio_num_peaks'] = np.nan
        feats[f'{segment_name_prefix}ratio_mean_prominence'] = np.nan

    # Savitzky-Golay Residuals Ratio
    if len(pre) > 7 and len(post) > 7:
        try:
            pre_smooth = savgol_filter(pre, window_length=7, polyorder=2)
            post_smooth = savgol_filter(post, window_length=7, polyorder=2)
            smooth_residuals_pre = np.std(pre - pre_smooth) if len(pre_smooth) > 0 else np.nan
            smooth_residuals_post = np.std(post - post_smooth) if len(post_smooth) > 0 else np.nan
            feats[f'{segment_name_prefix}ratio_smooth_residuals'] = (
                smooth_residuals_post / smooth_residuals_pre if (smooth_residuals_pre and smooth_residuals_pre != 0 and not np.isnan(smooth_residuals_post)) else np.nan
            )
        except Exception:
            feats[f'{segment_name_prefix}ratio_smooth_residuals'] = np.nan
    else:
        feats[f'{segment_name_prefix}ratio_smooth_residuals'] = np.nan

    # Detrended Standard Deviation Ratio
    if len(pre) > 5 and len(post) > 5:
        try:
            pre_detrended = detrend(pre, type='linear')
            post_detrended = detrend(post, type='linear')
            std_detrended_pre = np.std(pre_detrended) if len(pre_detrended) > 0 else np.nan
            std_detrended_post = np.std(post_detrended) if len(post_detrended) > 0 else np.nan
            feats[f'{segment_name_prefix}ratio_std_detrended'] = (
                std_detrended_post / std_detrended_pre if (std_detrended_pre and std_detrended_pre != 0 and not np.isnan(std_detrended_post)) else np.nan
            )
        except Exception:
            feats[f'{segment_name_prefix}ratio_std_detrended'] = np.nan
    else:
        feats[f'{segment_name_prefix}ratio_std_detrended'] = np.nan

    # Hilbert Transform Envelope Ratio
    if len(pre) > 5 and len(post) > 5:
        try:
            analytic_pre = hilbert(pre)
            analytic_post = hilbert(post)
            env_pre = np.abs(analytic_pre)
            env_post = np.abs(analytic_post)
            mean_envelope_pre = np.mean(env_pre) if len(env_pre) > 0 else np.nan
            mean_envelope_post = np.mean(env_post) if len(env_post) > 0 else np.nan
            feats[f'{segment_name_prefix}ratio_mean_envelope'] = (
                mean_envelope_post / mean_envelope_pre if (mean_envelope_pre and mean_envelope_pre != 0 and not np.isnan(mean_envelope_post)) else np.nan
            )
        except Exception:
            feats[f'{segment_name_prefix}ratio_mean_envelope'] = np.nan
    else:
        feats[f'{segment_name_prefix}ratio_mean_envelope'] = np.nan

    # Catch22 Feature Ratios
    pycatch22_features = [
        'DN_HistogramMode_5', 'DN_HistogramMode_10', 'DN_OutlierInclude_p_001_mdrmd',
        'DN_OutlierInclude_n_001_mdrmd', 'first1e_acf_tau', 'firstMin_acf',
        'SP_Summaries_welch_rect_area_5_1', 'SP_Summaries_welch_rect_centroid',
        'FC_LocalSimple_mean3_stderr', 'FC_LocalSimple_mean1_tauresrat',
        'MD_hrv_classic_pnn40', 'SB_BinaryStats_mean_longstretch1',
        'SB_BinaryStats_diff_longstretch0', 'SB_MotifThree_quantile_hh',
        'CO_HistogramAMI_even_2_5', 'CO_trev_1_num', 'IN_AutoMutualInfoStats_40_gaussian_fmmi',
        'SB_TransitionMatrix_3ac_sumdiagcov', 'PD_PeriodicityWang_th001',
        'CO_Embed2_Dist_tau_d_expfit_meandiff', 'SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1',
        'SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1'
    ]
    if len(pre) >= 20 and len(post) >= 20:
        try:
            pre_no_nan = pre[~np.isnan(pre)]
            post_no_nan = post[~np.isnan(post)]
            catch22_pre = pycatch22.catch22_all(pre_no_nan)
            catch22_post = pycatch22.catch22_all(post_no_nan)
            for feature_name, pre_val, post_val in zip(catch22_pre['names'], catch22_pre['values'], catch22_post['values']):
                feats[f'{segment_name_prefix}ratio_{feature_name}'] = (
                    post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
                )
        except Exception:
            for feature_name in pycatch22_features:
                feats[f'{segment_name_prefix}ratio_{feature_name}'] = np.nan
    else:
        for feature_name in pycatch22_features:
            feats[f'{segment_name_prefix}ratio_{feature_name}'] = np.nan

    # SSA Feature Ratios
    def ssa_features(arr, window_size=None, k=3):
        if len(arr) < 20:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }
        try:
            arr = arr[~np.isnan(arr)]
            n = len(arr)
            window_size = window_size or min(40, n // 2)
            if window_size < 2 or window_size > n:
                return {
                    'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                    'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
                }
            L = window_size
            K = n - L + 1
            X = np.array([arr[i:i+L] for i in range(K)])
            U, s, Vt = np.linalg.svd(X, full_matrices=False)
            singular_values = s[:k]
            singular_values = np.pad(singular_values, (0, max(0, k - len(singular_values))), constant_values=np.nan)
            X_reconstructed = np.zeros_like(X)
            for i in range(min(k, len(s))):
                X_reconstructed += s[i] * np.outer(U[:, i], Vt[i, :])
            reconstructed_series = np.zeros(n)
            count = np.zeros(n)
            for i in range(K):
                for j in range(L):
                    if i + j < n:
                        reconstructed_series[i + j] += X_reconstructed[i, j]
                        count[i + j] += 1
            reconstructed_series = reconstructed_series / count
            reconstruction_error = np.nanmean((arr - reconstructed_series) ** 2)
            trend = s[0] * np.outer(U[:, 0], Vt[0, :])
            trend_series = np.zeros(n)
            count = np.zeros(n)
            for i in range(K):
                for j in range(L):
                    if i + j < n:
                        trend_series[i + j] += trend[i, j]
                        count[i + j] += 1
            trend_series = trend_series / count
            trend_mean = np.nanmean(trend_series)
            trend_std = np.nanstd(trend_series, ddof=1) if len(trend_series) > 1 else np.nan
            return {
                'ssa_singular_value_1': singular_values[0],
                'ssa_singular_value_2': singular_values[1] if len(singular_values) > 1 else np.nan,
                'ssa_singular_value_3': singular_values[2] if len(singular_values) > 2 else np.nan,
                'ssa_reconstruction_error': reconstruction_error,
                'ssa_trend_mean': trend_mean,
                'ssa_trend_std': trend_std
            }
        except Exception:
            return {
                'ssa_singular_value_1': np.nan, 'ssa_singular_value_2': np.nan, 'ssa_singular_value_3': np.nan,
                'ssa_reconstruction_error': np.nan, 'ssa_trend_mean': np.nan, 'ssa_trend_std': np.nan
            }

    if len(pre) >= 20 and len(post) >= 20:
        try:
            ssa_pre = ssa_features(pre)
            ssa_post = ssa_features(post)
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                pre_val = ssa_pre[key]
                post_val = ssa_post[key]
                feats[f'{segment_name_prefix}ratio_{key}'] = (
                    post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
                )
        except Exception:
            for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                        'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
                feats[f'{segment_name_prefix}ratio_{key}'] = np.nan
    else:
        for key in ['ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                    'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std']:
            feats[f'{segment_name_prefix}ratio_{key}'] = np.nan

    # Quantile Ratios
    if len(pre) > 0 and len(post) > 0:
        try:
            quantiles = [0.25, 0.5, 0.75]
            q_pre = np.nanquantile(pre, quantiles)
            q_post = np.nanquantile(post, quantiles)
            for i, q in enumerate(quantiles):
                feats[f'{segment_name_prefix}ratio_q{int(q*100)}'] = (
                    q_post[i] / q_pre[i] if (q_pre[i] and q_pre[i] != 0 and not np.isnan(q_post[i])) else np.nan
                )
        except Exception:
            for q in [0.25, 0.5, 0.75]:
                feats[f'{segment_name_prefix}ratio_q{int(q*100)}'] = np.nan
    else:
        for q in [0.25, 0.5, 0.75]:
            feats[f'{segment_name_prefix}ratio_q{int(q*100)}'] = np.nan

    # Longest Strike Above/Below Mean
    def longest_strike_above_mean(arr):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        streaks = []
        current_streak = 0
        for val in arr:
            if val > mean:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0

    def longest_strike_below_mean(arr):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        streaks = []
        current_streak = 0
        for val in arr:
            if val < mean:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0

    lsam_pre = longest_strike_above_mean(pre)
    lsam_post = longest_strike_above_mean(post)
    lsbm_pre = longest_strike_below_mean(pre)
    lsbm_post = longest_strike_below_mean(post)
    feats[f'{segment_name_prefix}ratio_longest_strike_above_mean'] = (
        lsam_post / lsam_pre if (lsam_pre and lsam_pre != 0 and not np.isnan(lsam_post)) else np.nan
    )
    feats[f'{segment_name_prefix}ratio_longest_strike_below_mean'] = (
        lsbm_post / lsbm_pre if (lsbm_pre and lsbm_pre != 0 and not np.isnan(lsbm_post)) else np.nan
    )

    # Ratio Beyond r Sigma
    def ratio_beyond_r_sigma(arr, r):
        if len(arr) < 2:
            return np.nan
        mean = np.nanmean(arr)
        std = np.nanstd(arr, ddof=1)
        if std == 0:
            return np.nan
        threshold = mean + r * std
        return np.sum(np.abs(arr - mean) > r * std) / len(arr)

    for r in [1, 2]:
        rbs_pre = ratio_beyond_r_sigma(pre, r)
        rbs_post = ratio_beyond_r_sigma(post, r)
        feats[f'{segment_name_prefix}ratio_beyond_{r}_sigma'] = (
            rbs_post / rbs_pre if (rbs_pre and rbs_pre != 0 and not np.isnan(rbs_post)) else np.nan
        )

    # Enhanced Conditional Expectation Ratios using above/below segments
    
    # 1. Temporal conditional expectation ratios (pre vs post)
    cond_exp_above_pre = np.nanmean(pre_above_mean) if len(pre_above_mean) > 0 else np.nan
    cond_exp_above_post = np.nanmean(post_above_mean) if len(post_above_mean) > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_conditional_exp_above'] = (
        cond_exp_above_post / cond_exp_above_pre if (cond_exp_above_pre and cond_exp_above_pre != 0 and not np.isnan(cond_exp_above_post)) else np.nan
    )

    cond_exp_below_pre = np.nanmean(pre_below_mean) if len(pre_below_mean) > 0 else np.nan
    cond_exp_below_post = np.nanmean(post_below_mean) if len(post_below_mean) > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_conditional_exp_below'] = (
        cond_exp_below_post / cond_exp_below_pre if (cond_exp_below_pre and cond_exp_below_pre != 0 and not np.isnan(cond_exp_below_post)) else np.nan
    )
    
    # 2. Cross-regime conditional expectation ratios (above vs below within periods)
    cond_exp_whole_above = np.nanmean(above_mean) if len(above_mean) > 0 else np.nan
    cond_exp_whole_below = np.nanmean(below_mean) if len(below_mean) > 0 else np.nan
    feats[f'{segment_name_prefix}ratio_whole_above_vs_below'] = (
        cond_exp_whole_above / cond_exp_whole_below if (cond_exp_whole_below and cond_exp_whole_below != 0 and not np.isnan(cond_exp_whole_above)) else np.nan
    )
    
    feats[f'{segment_name_prefix}ratio_pre_above_vs_below'] = (
        cond_exp_above_pre / cond_exp_below_pre if (cond_exp_below_pre and cond_exp_below_pre != 0 and not np.isnan(cond_exp_above_pre)) else np.nan
    )
    
    feats[f'{segment_name_prefix}ratio_post_above_vs_below'] = (
        cond_exp_above_post / cond_exp_below_post if (cond_exp_below_post and cond_exp_below_post != 0 and not np.isnan(cond_exp_above_post)) else np.nan
    )
    
    # 3. Asymmetry change ratio (how the above/below relationship changed)
    pre_asymmetry = feats[f'{segment_name_prefix}ratio_pre_above_vs_below']
    post_asymmetry = feats[f'{segment_name_prefix}ratio_post_above_vs_below']
    feats[f'{segment_name_prefix}ratio_asymmetry_change'] = (
        post_asymmetry / pre_asymmetry if (pre_asymmetry and pre_asymmetry != 0 and not np.isnan(post_asymmetry)) else np.nan
    )

    # Statistical Tests for Pre, Post, Whole, Above Mean, and Below Mean
    stat_tests = [
        ('ryan_joiner', ryan_joiner_test),
        ('shapiro_wilk', lambda x: stats.shapiro(x[~np.isnan(x)])[0] if len(x[~np.isnan(x)]) >= 3 else np.nan),
        ('lilliefors', lambda x: lilliefors(x[~np.isnan(x)])[0] if len(x[~np.isnan(x)]) >= 4 else np.nan),
        ('adf', lambda x: adfuller(x[~np.isnan(x)], maxlag=1)[0] if len(x[~np.isnan(x)]) >= 3 else np.nan),
        ('kpss', lambda x: kpss(x[~np.isnan(x)], nlags='auto')[0] if len(x[~np.isnan(x)]) >= 3 else np.nan),
        # ('supf', lambda x: supf_test(x[~np.isnan(x)])),
        ('cusum', cusum_test),
        # ('bai_perron', lambda x: bai_perron_test(x[~np.isnan(x)]))
    ]

    for test_name, test_func in stat_tests:
        for segment, seg_data in [
            ('pre', pre), ('post', post),# ('whole', whole),
            ('above_mean', above_mean), ('below_mean', below_mean),
            ('pre_above_mean', pre_above_mean), ('pre_below_mean', pre_below_mean),
            ('post_above_mean', post_above_mean), ('post_below_mean', post_below_mean)
        ]:
            feats[f'{segment_name_prefix}{segment}_{test_name}'] = test_func(seg_data) if len(seg_data) >= 5 else np.nan
        # Compute ratios for pre/post segments
        pre_val = feats.get(f'{segment_name_prefix}pre_{test_name}', np.nan)
        post_val = feats.get(f'{segment_name_prefix}post_{test_name}', np.nan)
        feats[f'{segment_name_prefix}ratio_{test_name}'] = (
            post_val / pre_val if (pre_val and pre_val != 0 and not np.isnan(post_val)) else np.nan
        )

    # Two-Sample Tests and Effect Sizes
    two_sample_tests = [
        ('cliffs_delta', lambda x, y: cliffs_delta(x, y)),
        ('jsd', lambda x, y: jensen_shannon_divergence(x, y)),
        ('ks', lambda x, y: kstest(x[~np.isnan(x)], y[~np.isnan(y)])[0] if len(x[~np.isnan(x)]) >= 2 and len(y[~np.isnan(y)]) >= 2 else np.nan),
        ('anderson', lambda x, y: anderson_ksamp([x[~np.isnan(x)], y[~np.isnan(y)]])[0] if len(x[~np.isnan(x)]) >= 2 and len(y[~np.isnan(y)]) >= 2 else np.nan),
        ('cramer_von_mises', lambda x, y: cramervonmises_2samp(x[~np.isnan(x)], y[~np.isnan(y)]).statistic if len(x[~np.isnan(x)]) >= 2 and len(y[~np.isnan(y)]) >= 2 else np.nan),
        ('welch_ttest', welch_ttest),
        ('cohens_d', cohens_d),
        ('glass_delta', glass_delta),
        ('granger_causality', safe_granger_causality),
        # ('chow', lambda x, y: chow_test(x, y, tstar) if tstar is not None else np.nan),
        # ('wald', lambda x, y: wald_test(x, y)),
        ('coint', lambda x, y: safe_coint(x, y))
        # ('coint', lambda x, y: coint(x[~np.isnan(x)], y[~np.isnan(y)])[0] if len(x[~np.isnan(x)]) >= 10 and len(y[~np.isnan(y)]) >= 10 else np.nan)
    ]

    # Note: Two-sample statistical tests replaced with enhanced conditional expectation ratios above
    # The new ratio-based approach is faster, more interpretable, and scale-invariant

    return feats

    
def process_series(args, compute_pre_post=True, normalize=False, tstar=None, weight_type='proportion', sigma=0.1, compute_jsd=False):
    """
    Process a single time series to extract ratio-based features.
    
    Parameters:
    -----------
    args : tuple
        (index, series, split_point) where index is the series identifier, series is the time series data,
        and split_point is the index to split the series into pre and post segments.
    compute_pre_post : bool
        If True, compute features for pre and post segments and their ratios.
    normalize : bool
        If True, normalize features (not used since only ratios are kept).
    tstar : float or None
        Scaling factor for weighting (not used since weights are removed).
    weight_type : str
        Type of weighting (not used since weights are removed).
    sigma : float
        Parameter for Gaussian kernel (not used since weights are removed).
    compute_jsd : bool
        If True, compute Jensen-Shannon divergence (not used since it's non-ratio).
    
    Returns:
    --------
    dict
        Dictionary of ratio-based features for the series.
    """
    index, series, split_point = args
    try:
        # Ensure series is a numpy array and remove NaNs
        series = np.array(series, dtype=float)
        series = series[~np.isnan(series)]
        
        if len(series) < 10:
            logging.warning(f"Series {index} too short after removing NaNs: {len(series)}")
            return {f"ratio_{key}": np.nan for key in [
                'mean', 'std', 'var', 'skew', 'kurt',
                'energy_ratio_chunk1', 'energy_ratio_chunk2',
                'fft_agg_centroid', 'fft_agg_variance',
                'index_mass_quantile_q25', 'index_mass_quantile_q50', 'index_mass_quantile_q75',
                'binned_entropy', 'last_location_of_maximum', 'lempel_ziv_complexity',
                'longest_strike_above_mean', 'longest_strike_below_mean',
                'mean_second_derivative_central', 'pct_reoccurring_datapoints',
                'pct_reoccurring_values', 'beyond_1_sigma', 'beyond_2_sigma',
                'unique_values', 'ac', 'phi', 'garch',
                'power_periodogram', 'power_welch', 'spec_entropy',
                'num_peaks', 'mean_prominence', 'smooth_residuals',
                'std_detrended', 'mean_envelope',
                'ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std',
                'q25', 'q50', 'q75', 'conditional_exp_above', 'conditional_exp_below',
                'ryan_joiner', 'shapiro_wilk', 'lilliefors', 'adf', 'kpss', 'supf', 'cusum', 'bai_perron',
                'cliffs_delta', 'jsd', 'ks', 'anderson', 'cramer_von_mises', 'welch_ttest',
                'cohens_d', 'glass_delta', 'granger_causality', 'chow', 'wald', 'coint'
                ] + [f"{name}" for name in pycatch22.catch22_all(np.ones(20))['names']]}

        # Split the series
        if split_point is None or not (0 < split_point < len(series)):
            split_point = len(series) // 2
        pre = series[:split_point]
        post = series[split_point:]
        
        if len(pre) < 5 or len(post) < 5:
            logging.warning(f"Series {index} has insufficient pre/post data: pre={len(pre)}, post={len(post)}")
            return {f"ratio_{key}": np.nan for key in [
                    'mean', 'std', 'var', 'skew', 'kurt',
                    'energy_ratio_chunk1', 'energy_ratio_chunk2',
                    'fft_agg_centroid', 'fft_agg_variance',
                    'index_mass_quantile_q25', 'index_mass_quantile_q50', 'index_mass_quantile_q75',
                    'binned_entropy', 'last_location_of_maximum', 'lempel_ziv_complexity',
                    'longest_strike_above_mean', 'longest_strike_below_mean',
                    'mean_second_derivative_central', 'pct_reoccurring_datapoints',
                    'pct_reoccurring_values', 'beyond_1_sigma', 'beyond_2_sigma',
                    'unique_values', 'ac', 'phi', 'garch',
                    'power_periodogram', 'power_welch', 'spec_entropy',
                    'num_peaks', 'mean_prominence', 'smooth_residuals',
                    'std_detrended', 'mean_envelope',
                    'ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
                    'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std',
                    'q25', 'q50', 'q75', 'conditional_exp_above', 'conditional_exp_below',
                    'ryan_joiner', 'shapiro_wilk', 'lilliefors', 'adf', 'kpss', 'supf', 'cusum', 'bai_perron',
                    'cliffs_delta', 'jsd', 'ks', 'anderson', 'cramer_von_mises', 'welch_ttest',
                    'cohens_d', 'glass_delta', 'granger_causality', 'chow', 'wald', 'coint'
                ] + [f"{name}" for name in pycatch22.catch22_all(np.ones(20))['names']]}

        # Compute ratio-based features
        feats = compute_features_for_segment(
            pre=pre,
            post=post,
            whole=series,
            segment_name_prefix='',
            compute_pre_post=compute_pre_post,
            normalize=False,
            tstar=tstar,
            whole_indices=None,
            weight_type=weight_type,
            sigma=sigma,
            compute_jsd=False
        )

        # Prefix features without index (to match CrunchDAO submission format)
        return feats
    
    except Exception as e:
        logging.error(f"Error processing series {index}: {str(e)}")
        return {f"ratio_{key}": np.nan for key in [
            'mean', 'std', 'var', 'skew', 'kurt',
            'energy_ratio_chunk1', 'energy_ratio_chunk2',
            'fft_agg_centroid', 'fft_agg_variance',
            'index_mass_quantile_q25', 'index_mass_quantile_q50', 'index_mass_quantile_q75',
            'binned_entropy', 'last_location_of_maximum', 'lempel_ziv_complexity',
            'longest_strike_above_mean', 'longest_strike_below_mean',
            'mean_second_derivative_central', 'pct_reoccurring_datapoints',
            'pct_reoccurring_values', 'beyond_1_sigma', 'beyond_2_sigma',
            'unique_values', 'ac', 'phi', 'garch',
            'power_periodogram', 'power_welch', 'spec_entropy',
            'num_peaks', 'mean_prominence', 'smooth_residuals',
            'std_detrended', 'mean_envelope',
            'ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
            'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std',
            'q25', 'q50', 'q75', 'conditional_exp_above', 'conditional_exp_below'
        ] + [f"{name}" for name in pycatch22.catch22_all(np.ones(20))['names']]}
# import os
# os.chdir('/app/CrunchDAO/structuralbreak/')



# Suppress warnings
warnings.filterwarnings('ignore')

# Logging setup
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
import multiprocessing as mp
from functools import partial
import numpy as np
import pandas as pd
import time

def extract_features_parallel(series_list, split_points, compute_pre_post=True, 
                            normalize=False, tstar=None, weight_type='proportion', 
                            sigma=0.1, compute_jsd=False, n_processes=None):
    """
    Parallel feature extraction with real-time progress monitoring.
    
    Implements asynchronous result collection with shared memory progress counter
    to provide immediate visual feedback during computation-intensive feature extraction.
    
    Parameters:
    -----------
    n_processes : int or None
        Process pool size. Auto-optimized to (cpu_count - 1) for thermal management.
    """
    
    if n_processes is None:
        n_processes = max(1, mp.cpu_count() - 1)
    
    args = [(i, series_list[i], split_points[i]) for i in range(len(series_list))]
    total_series = len(args)
    
    process_func = partial(
        process_series,
        compute_pre_post=compute_pre_post,
        normalize=normalize,
        tstar=tstar,
        weight_type=weight_type,
        sigma=sigma,
        compute_jsd=compute_jsd
    )
    
    print(f"Initializing {n_processes} worker processes for {total_series} series...")
    start_time = time.time()
    
    # Method 1: Asynchronous with callback-based progress
    with mp.Pool(processes=n_processes) as pool:
        # Shared counter for thread-safe progress tracking
        with mp.Manager() as manager:
            progress_counter = manager.Value('i', 0)
            progress_lock = manager.Lock()
            
            def update_progress(result):
                """Callback function executed upon task completion"""
                with progress_lock:
                    progress_counter.value += 1
                    completed = progress_counter.value
                    elapsed = time.time() - start_time
                    progress_pct = (completed / total_series) * 100
                    
                    if completed > 0:
                        eta = elapsed / completed * (total_series - completed)
                        rate = completed / elapsed if elapsed > 0 else 0
                        
                        # Real-time progress with computational metrics
                        print(f"\r\033[K[{completed:>4}/{total_series}] "
                              f"{progress_pct:5.1f}% | "
                              f"Rate: {rate:.2f} series/s | "
                              f"Elapsed: {elapsed/60:4.1f}m | "
                              f"ETA: {eta/60:4.1f}m", 
                              end='', flush=True)
            
            # Submit all tasks asynchronously
            async_results = []
            for arg in args:
                async_result = pool.apply_async(process_func, (arg,), callback=update_progress)
                async_results.append(async_result)
            
            # Collect results while maintaining order
            results = []
            for async_result in async_results:
                results.append(async_result.get())
    
    print()  # New line after progress completion
    elapsed_total = time.time() - start_time
    throughput = total_series / elapsed_total
    print(f"✓ Extraction completed: {elapsed_total/60:.2f}m total "
          f"({throughput:.2f} series/s average)")
    
    # Construct feature matrix with numerical stability checks
    feature_df = pd.DataFrame(results)
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    # Feature matrix diagnostics
    n_features = feature_df.shape[1]
    sparsity = (feature_df.isna().sum().sum() / feature_df.size) * 100
    print(f"Feature matrix: {total_series} × {n_features} ({sparsity:.1f}% sparse)")
    
    return feature_df

def extract_features_batched(series_list, split_points, batch_size=1000, **kwargs):
    """
    Process features in batches to manage memory usage for large datasets.
    """
    total_series = len(series_list)
    all_results = []
    
    for i in range(0, total_series, batch_size):
        end_idx = min(i + batch_size, total_series)
        batch_series = series_list[i:end_idx]
        batch_splits = split_points[i:end_idx]
        
        print(f"Processing batch {i//batch_size + 1}/{(total_series-1)//batch_size + 1}")
        
        batch_features = extract_features_parallel(
            batch_series, batch_splits, **kwargs
        )
        all_results.append(batch_features)
    
    return pd.concat(all_results, ignore_index=True)


def extract_features(series_list, split_points, compute_pre_post=True, normalize=False, 
                    tstar=None, weight_type='proportion', sigma=0.1, compute_jsd=False, 
                    n_processes=8):
    """
    Extract ratio-based features from multiple time series with progress printing.
    
    Parameters:
    -----------
    series_list : list
        List of 1D numpy arrays (time series).
    split_points : list
        List of split points for each series.
    compute_pre_post : bool
        If True, compute features for pre and post segments and their ratios.
    normalize : bool
        If True, normalize features (not used).
    tstar : float or None
        Scaling factor for weighting (not used).
    weight_type : str
        Type of weighting (not used).
    sigma : float
        Parameter for Gaussian kernel (not used).
    compute_jsd : bool
        If True, compute Jensen-Shannon divergence (not used).
    n_processes : int
        Number of processes for parallel computation.
    
    Returns:
    --------
    pd.DataFrame
        DataFrame with ratio-based features for each series.
    """
    import time
    
    # Prepare arguments for parallel processing
    args = [(i, series_list[i], split_points[i]) for i in range(len(series_list))]
    total_series = len(args)
    
    # Process series
    results = []
    start_time = time.time()
    
    # Use sequential processing to avoid pickling issues
    for i, arg in enumerate(args):
        result = process_series(
            arg,
            compute_pre_post=compute_pre_post,
            normalize=normalize,
            tstar=tstar,
            weight_type=weight_type,
            sigma=sigma,
            compute_jsd=compute_jsd
        )
        results.append(result)
        elapsed = time.time() - start_time
        progress = (i + 1) / total_series
        eta = elapsed / (i + 1) * (total_series - i - 1) if i > 0 else 0
        print(f"\rProcessing: {i+1}/{total_series} ({progress*100:.1f}%) | Elapsed: {elapsed/60:.1f} min | ETA: {eta/60:.1f} min", end="", flush=True)

    print()  # Move to next line after completion
    # Combine results into DataFrame
    feature_df = pd.DataFrame(results)
    
    # Replace infinite values with NaN
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    
    return feature_df

    # Load and process data
X_train = pd.read_parquet('/app/CrunchDAO/structuralbreak/data/X_train.parquet')
X_test = pd.read_parquet('/app/CrunchDAO/structuralbreak/data/X_test.reduced.parquet')
y_train = pd.read_parquet('/app/CrunchDAO/structuralbreak/data/y_train.parquet')
y_test = pd.read_parquet('/app/CrunchDAO/structuralbreak/data/y_test.reduced.parquet')

import os
import typing

# Import your dependencies
import joblib
import pandas as pd
import scipy
import sklearn.metrics
import crunch
 

all_series = []
all_tstars = []
all_labels = []

grouped = X_train.groupby(level='id')
for id_, group in grouped:
    # Sort by time in case it's not sorted
    group = group.sort_index(level='time')

    values = group['value'].values  # full time series
    periods = group['period'].values  # same length
    # Boundary index is first point where period switches to 1
    try:
        tstar = np.where(periods == 1)[0][0]  # first index where period==1
    except IndexError:
        # No break in this series
        tstar = len(values)  # one past end — we won't use it for training

    label = int(y_train.loc[id_])  # 0 or 1
    all_series.append(values)
    all_tstars.append(tstar)
    all_labels.append(label)

# Extract series, tstars, and labels
# all_series, all_tstars, all_labels = extract_series_boundary_label(X_train, y_train)
# test_series, test_tstars, test_labels = extract_series_boundary_label(X_test, y_test)

# Extract ratio-based features
train_features = extract_features_parallel(
    series_list=all_series,
    split_points=all_tstars,
    compute_pre_post=True,
    n_processes=8  # Auto-detect optimal number
)

test_features = extract_features_parallel(
    series_list=test_series,
    split_points=test_tstars,
    compute_pre_post=True,
    n_processes=8
)

# # Handle missing values (impute with median)
# train_features.fillna(train_features.median(), inplace=True)
# test_features.fillna(train_features.median(), inplace=True)  # Use train median for consistency

# Train a model
model = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
model.fit(train_features, all_labels)

# Predict probabilities
train_pred = model.predict_proba(train_features)[:, 1]
test_pred = model.predict_proba(test_features)[:, 1]

# Evaluate on training and test sets
train_auroc = sklearn.metrics.roc_auc_score(all_labels, train_pred)
test_auroc = sklearn.metrics.roc_auc_score(test_labels, test_pred)
print(f"Training AUROC: {train_auroc:.4f}")
print(f"Test AUROC: {test_auroc:.4f}")

# Prepare submission
submission = pd.DataFrame({
    'id': X_test.index.get_level_values('id').unique(),
    'pred': test_pred
})
submission.to_csv('submission.csv', index=False)
print("Submission file created: submission.csv")

# Save the model (optional)
joblib.dump(model, 'model.pkl')
print("Model saved: model.pkl")