#!/usr/bin/env python3
"""
Ratio Features Enhancement Module
Adds ratio metrics to existing TSFresh pipeline while maintaining full compatibility
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any
import warnings

warnings.filterwarnings('ignore')

class RatioFeaturesEnhancer:
    """
    Enhances existing feature extraction with ratio metrics where applicable
    Maintains full compatibility with existing pipeline structure
    """
    
    def __init__(self, 
                 safe_division_threshold: float = 1e-12,
                 enable_ratio: bool = True,
                 enable_inverse_ratio: bool = False,
                 enable_log_ratio: bool = False,
                 enable_abs_log_ratio: bool = False,
                 enable_symmetric_ratio: bool = False):
        """
        Initialize the ratio features enhancer
        
        Args:
            safe_division_threshold: Minimum absolute value for denominator in ratios
            enable_ratio: Enable standard ratio (post/pre)
            enable_inverse_ratio: Enable inverse ratio (pre/post)
            enable_log_ratio: Enable log ratio (log(post/pre))
            enable_abs_log_ratio: Enable absolute log ratio (abs(log(post/pre)))
            enable_symmetric_ratio: Enable symmetric ratio ((post-pre)/(post+pre))
        """
        self.safe_division_threshold = safe_division_threshold
        self.enable_ratio = enable_ratio
        self.enable_inverse_ratio = enable_inverse_ratio
        self.enable_log_ratio = enable_log_ratio
        self.enable_abs_log_ratio = enable_abs_log_ratio
        self.enable_symmetric_ratio = enable_symmetric_ratio
        
        # Define feature categories for ratio computation
        self.ratio_applicable_features = self._define_ratio_applicable_features()
        self.two_sided_features = self._define_two_sided_features()
        
        # Define feature categories for ratio computation
        self.ratio_applicable_features = self._define_ratio_applicable_features()
        self.two_sided_features = self._define_two_sided_features()
        
        # Validate that at least one ratio type is enabled
        if not any([enable_ratio, enable_inverse_ratio, enable_log_ratio, enable_abs_log_ratio, enable_symmetric_ratio]):
            print("⚠️  Warning: No ratio types enabled. Setting enable_ratio=True as default.")
            self.enable_ratio = True
        
        print("🔧 Ratio Features Enhancer Initialized")
        print(f"   - Safe division threshold: {safe_division_threshold}")
        print(f"   - Ratio-applicable feature types: {len(self.ratio_applicable_features)}")
        print(f"   - Enabled ratio types:")
        if self.enable_ratio:
            print(f"     ✅ Standard ratio (post/pre)")
        if self.enable_inverse_ratio:
            print(f"     ✅ Inverse ratio (pre/post)")
        if self.enable_log_ratio:
            print(f"     ✅ Log ratio (log(post/pre))")
        if self.enable_abs_log_ratio:
            print(f"     ✅ Absolute log ratio (abs(log(post/pre)))")
        if self.enable_symmetric_ratio:
            print(f"     ✅ Symmetric ratio ((post-pre)/(post+pre))")
    
    def _define_ratio_applicable_features(self) -> Dict[str, List[str]]:
        """Define features where ratios are meaningful"""
        return {
            'tsfresh_features': [
                'energy_ratio_chunk1', 'energy_ratio_chunk2',
                'fft_agg_centroid', 'fft_agg_variance',
                'index_mass_quantile_q25', 'index_mass_quantile_q50', 'index_mass_quantile_q75',
                'binned_entropy', 'last_location_of_maximum', 'lempel_ziv_complexity',
                'longest_strike_above_mean', 'longest_strike_below_mean',
                'mean_second_derivative_central',
                'pct_reoccurring_datapoints', 'pct_reoccurring_values',
                'ratio_beyond_1_sigma', 'ratio_beyond_2_sigma',
                'ratio_unique_values'
            ],
            'signal_processing_features': [
                'power_whole_periodogram', 'power_whole_welch',
                'spec_entropy', 'num_peaks', 'mean_prominence',
                'smooth_residuals', 'std_detrended', 'mean_envelope'
            ],
            'time_series_features': [
                'ac_whole', 'phi', 'garch'
            ],
            'distribution_features': [
                'relfreq_mean', 'relfreq_std'
            ],
            'normality_test_features': [
                'p_sw', 'p_lf', 'rj'
            ],
            'stationarity_test_features': [
                'p_adf', 'p_kpss'
            ],
            'quantile_features': [
                'q25', 'q50', 'q75'
            ],
            'feature_vector_features': [
                'norm_feature_vec_mean', 'norm_feature_vec_std'
            ]
        }
    
    def _define_two_sided_features(self) -> List[str]:
        """Define features that are inherently comparative (no ratios needed)"""
        return [
            # Statistical tests comparing pre vs post
            'p_ttest', 'p_mannwhitney', 'p_ks', 'p_cvm', 'p_chi2',
            'score_ttest', 'score_mannwhitney', 'score_ks', 'score_cvm', 'score_chi2',
            
            # Distance and divergence measures
            'wass_dist', 'score_wass', 'jsd',
            
            # Cross-correlation and convolution
            'max_cross_corr', 'max_cross_corr_lag', 'max_conv', 'mean_coherence',
            
            # Effect sizes (already normalized)
            'cohens_d', 'glass_delta', 'cliffs_delta',
            
            # Structural break tests
            'p_chow', 'p_wald', 'p_supf', 'p_bp', 'chow_stat', 'wald_stat', 'supf_stat', 'bp_stat',
            'score_chow', 'score_wald', 'score_supf', 'score_bp',
            
            # CUSUM statistics
            'cusum_rank_max_abs',
            
            # Cointegration and causality
            'p_coint', 'p_granger', 'score_coint', 'score_granger',
            
            # Mutual information
            'mutual_info',
            
            # Difference features (already computed)
            'diff_mean', 'diff_skew', 'diff_kurt', 'diff_ac', 'diff_phi', 'diff_garch',
            
            # Various score and difference features
            'score_sw', 'score_lf', 'score_adf', 'score_kpss', 'score_rj',
            'diff_sw_p', 'diff_lf', 'diff_adf', 'diff_kpss', 'diff_rj',
            
            # Spectral differences
            'spec_entropy_diff', 'diff_envelope', 'diff_smooth_residuals', 'diff_std_detrended',
            'diff_num_peaks',
            
            # Meta-features for pycatch22 and SSA (already have ratios)
            # These will be handled separately
        ]
    
    def _safe_divide(self, numerator: float, denominator: float) -> float:
        """
        Perform safe division with handling for edge cases
        
        Args:
            numerator: Numerator value
            denominator: Denominator value
            
        Returns:
            Safe division result or NaN
        """
        if np.isnan(numerator) or np.isnan(denominator):
            return np.nan
        
        if abs(denominator) < self.safe_division_threshold:
            return np.nan
        
        result = numerator / denominator
        
        # Handle infinite results
        if np.isinf(result):
            return np.nan
        
        return result
    
    def _extract_feature_base_name(self, feature_name: str) -> Optional[str]:
        """
        Extract base feature name from full feature name
        
        Args:
            feature_name: Full feature name (e.g., 'energy_ratio_chunk1_pre')
            
        Returns:
            Base feature name (e.g., 'energy_ratio_chunk1') or None
        """
        # Remove segment prefixes and suffixes
        for prefix in ['time_bin1_', 'time_bin2_', 'value_bin1_', 'value_bin2_', '']:
            if feature_name.startswith(prefix):
                name_without_prefix = feature_name[len(prefix):]
                
                # Check for _pre, _post, _whole suffixes
                for suffix in ['_pre', '_post', '_whole']:
                    if name_without_prefix.endswith(suffix):
                        base_name = name_without_prefix[:-len(suffix)]
                        return base_name
                
                # Check for weighted versions
                for suffix in ['_pre_weighted', '_post_weighted', '_whole_weighted']:
                    if name_without_prefix.endswith(suffix):
                        base_name = name_without_prefix[:-len(suffix)]
                        return base_name
                
                # Check for normalized versions
                for suffix in ['_pre_norm_zscore', '_post_norm_zscore', '_whole_norm_zscore']:
                    if name_without_prefix.endswith(suffix):
                        base_name = name_without_prefix[:-len(suffix)]
                        return base_name
        
        return None
    
    def _is_ratio_applicable(self, base_feature_name: str) -> bool:
        """
        Check if a feature is applicable for ratio computation
        
        Args:
            base_feature_name: Base feature name
            
        Returns:
            True if ratio is applicable
        """
        # Check if it's in two-sided features (no ratio needed)
        if base_feature_name in self.two_sided_features:
            return False
        
        # Check if it's already a ratio or difference feature
        if base_feature_name.startswith(('ratio_', 'diff_')):
            return False
        
        # Check if it's in ratio-applicable categories
        for category, features in self.ratio_applicable_features.items():
            if base_feature_name in features:
                return True
        
        # Check for pycatch22 features (they already have ratios, but we can verify)
        pycatch22_features = [
            'DN_HistogramMode_5', 'DN_HistogramMode_10', 'DN_OutlierInclude_p_001_mdrmd',
            'DN_OutlierInclude_n_001_mdrmd', 'first1e_acf_tau', 'firstMin_acf',
            'SP_Summaries_welch_rect_area_5_1', 'SP_Summaries_welch_rect_centroid',
            'FC_LocalSimple_mean3_stderr', 'FC_LocalSimple_mean1_tauresrat',
            'MD_hrv_classic_pnn40', 'SB_BinaryStats_mean_longstretch1',
            'SB_BinaryStats_diff_longstretch0', 'SB_MotifThree_quantile_hh',
            'CO_HistogramAMI_even_2_5', 'CO_trev_1_num', 'IN_AutoMutualInfoStats_40_gaussian_fmmi',
            'SB_TransitionMatrix_3ac_sumdiagcov', 'PD_PeriodicityWang_th001',
            'CO_Embed2_Dist_tau_d_expfit_meandiff', 'SC_FluctAnal_2_rsrangefit_50_1_logi_prop_r1',
            'SC_FluctAnal_2_dfa_50_1_2_logi_prop_r1'
        ]
        
        if base_feature_name in pycatch22_features:
            return False  # Already handled in original code
        
        # Check for SSA features (they already have ratios)
        ssa_features = [
            'ssa_singular_value_1', 'ssa_singular_value_2', 'ssa_singular_value_3',
            'ssa_reconstruction_error', 'ssa_trend_mean', 'ssa_trend_std'
        ]
        
        if base_feature_name in ssa_features:
            return False  # Already handled in original code
        
        return False
    
    def add_ratio_features(self, features_dict: Dict[str, Any], 
                          segment_prefix: str = '') -> Dict[str, Any]:
        """
        Add ratio features to existing features dictionary
        
        Args:
            features_dict: Dictionary of existing features
            segment_prefix: Segment prefix (e.g., 'time_bin1_', 'value_bin1_')
            
        Returns:
            Enhanced features dictionary with ratio features
        """
        enhanced_features = features_dict.copy()
        
        # Find all pre/post feature pairs for ratio computation
        pre_features = {}
        post_features = {}
        
        for feature_name, feature_value in features_dict.items():
            if not feature_name.startswith(segment_prefix):
                continue
            
            name_without_prefix = feature_name[len(segment_prefix):]
            
            if name_without_prefix.endswith('_pre'):
                base_name = name_without_prefix[:-4]  # Remove '_pre'
                pre_features[base_name] = feature_value
            elif name_without_prefix.endswith('_post'):
                base_name = name_without_prefix[:-5]  # Remove '_post'
                post_features[base_name] = feature_value
        
        # Compute ratios for applicable features
        for base_name in pre_features.keys():
            if base_name in post_features and self._is_ratio_applicable(base_name):
                pre_value = pre_features[base_name]
                post_value = post_features[base_name]
                
                # 1. Standard ratio (post/pre)
                if self.enable_ratio:
                    ratio_value = self._safe_divide(post_value, pre_value)
                    ratio_feature_name = f"{segment_prefix}ratio_{base_name}"
                    enhanced_features[ratio_feature_name] = ratio_value
                
                # 2. Inverse ratio (pre/post)
                if self.enable_inverse_ratio:
                    inverse_ratio_value = self._safe_divide(pre_value, post_value)
                    inverse_ratio_feature_name = f"{segment_prefix}inverse_ratio_{base_name}"
                    enhanced_features[inverse_ratio_feature_name] = inverse_ratio_value
                
                # 3. Log ratio (log(post/pre))
                if self.enable_log_ratio:
                    ratio_value = self._safe_divide(post_value, pre_value)
                    if not np.isnan(ratio_value) and ratio_value > 0:
                        log_ratio_value = np.log(ratio_value)
                        log_ratio_feature_name = f"{segment_prefix}log_ratio_{base_name}"
                        enhanced_features[log_ratio_feature_name] = log_ratio_value
                    else:
                        enhanced_features[f"{segment_prefix}log_ratio_{base_name}"] = np.nan
                
                # 4. Absolute log ratio (abs(log(post/pre)))
                if self.enable_abs_log_ratio:
                    ratio_value = self._safe_divide(post_value, pre_value)
                    if not np.isnan(ratio_value) and ratio_value > 0:
                        abs_log_ratio_value = abs(np.log(ratio_value))
                        abs_log_ratio_feature_name = f"{segment_prefix}abs_log_ratio_{base_name}"
                        enhanced_features[abs_log_ratio_feature_name] = abs_log_ratio_value
                    else:
                        enhanced_features[f"{segment_prefix}abs_log_ratio_{base_name}"] = np.nan
                
                # 5. Symmetric ratio ((post-pre)/(post+pre))
                if self.enable_symmetric_ratio:
                    if not (np.isnan(pre_value) or np.isnan(post_value)):
                        denominator = abs(post_value) + abs(pre_value)
                        if denominator >= self.safe_division_threshold:
                            symmetric_ratio_value = (post_value - pre_value) / denominator
                            symmetric_ratio_feature_name = f"{segment_prefix}symmetric_ratio_{base_name}"
                            enhanced_features[symmetric_ratio_feature_name] = symmetric_ratio_value
                        else:
                            enhanced_features[f"{segment_prefix}symmetric_ratio_{base_name}"] = np.nan
                    else:
                        enhanced_features[f"{segment_prefix}symmetric_ratio_{base_name}"] = np.nan
        
        return enhanced_features
    
    def enhance_features_dataframe(self, features_df: pd.DataFrame) -> pd.DataFrame:
        """
        Enhance a features DataFrame with ratio features
        
        Args:
            features_df: Original features DataFrame
            
        Returns:
            Enhanced DataFrame with ratio features
        """
        print("🔧 Adding ratio features to DataFrame...")
        
        enhanced_data = []
        
        for idx, row in features_df.iterrows():
            # Convert row to dictionary
            features_dict = row.to_dict()
            
            # Add ratio features for main segment (no prefix)
            enhanced_features = self.add_ratio_features(features_dict, '')
            
            # Add ratio features for time bins
            for i in range(1, 3):  # Assuming 2 time bins
                enhanced_features = self.add_ratio_features(enhanced_features, f'time_bin{i}_')
            
            # Add ratio features for value bins
            for i in range(1, 3):  # Assuming 2 value bins
                enhanced_features = self.add_ratio_features(enhanced_features, f'value_bin{i}_')
            
            enhanced_data.append(enhanced_features)
        
        # Create enhanced DataFrame
        enhanced_df = pd.DataFrame(enhanced_data, index=features_df.index)
        
        # Count new features
        original_features = set(features_df.columns)
        new_features = set(enhanced_df.columns) - original_features
        
        print(f"   ✅ Added {len(new_features)} ratio features")
        print(f"   📊 Total features: {len(features_df.columns)} → {len(enhanced_df.columns)}")
        
        return enhanced_df
    
    def get_ratio_feature_summary(self, features_df: pd.DataFrame) -> Dict[str, Any]:
        """
        Get summary of ratio features that would be added
        
        Args:
            features_df: Features DataFrame
            
        Returns:
            Summary dictionary
        """
        sample_row = features_df.iloc[0].to_dict()
        
        # Find potential ratio features
        potential_ratios = []
        
        for segment_prefix in ['', 'time_bin1_', 'time_bin2_', 'value_bin1_', 'value_bin2_']:
            pre_features = []
            post_features = []
            
            for feature_name in sample_row.keys():
                if not feature_name.startswith(segment_prefix):
                    continue
                
                name_without_prefix = feature_name[len(segment_prefix):]
                
                if name_without_prefix.endswith('_pre'):
                    base_name = name_without_prefix[:-4]
                    if self._is_ratio_applicable(base_name):
                        pre_features.append(base_name)
                elif name_without_prefix.endswith('_post'):
                    base_name = name_without_prefix[:-5]
                    if self._is_ratio_applicable(base_name):
                        post_features.append(base_name)
            
            # Find common features
            common_features = set(pre_features) & set(post_features)
            for base_name in common_features:
                potential_ratios.extend([
                    f"{segment_prefix}ratio_{base_name}",
                    f"{segment_prefix}inverse_ratio_{base_name}",
                    f"{segment_prefix}log_ratio_{base_name}"
                ])
        
        summary = {
            'original_feature_count': len(features_df.columns),
            'potential_ratio_features': len(potential_ratios),
            'total_enhanced_features': len(features_df.columns) + len(potential_ratios),
            'ratio_feature_examples': potential_ratios[:10],  # Show first 10 examples
            'feature_categories_enhanced': list(self.ratio_applicable_features.keys())
        }
        
        return summary

def enhance_tsfresh_pipeline_with_ratios(original_extract_features_func):
    """
    Decorator to enhance the original extract_features function with ratio features
    
    Args:
        original_extract_features_func: Original extract_features function
        
    Returns:
        Enhanced function that includes ratio features
    """
    def enhanced_extract_features(*args, add_ratio_features=True, **kwargs):
        """
        Enhanced extract_features function with ratio features
        
        Args:
            *args: Original function arguments
            add_ratio_features: Whether to add ratio features (default: True)
            **kwargs: Original function keyword arguments
            
        Returns:
            Enhanced DataFrame with ratio features
        """
        # Call original function
        original_df = original_extract_features_func(*args, **kwargs)
        
        if not add_ratio_features:
            return original_df
        
        # Initialize enhancer
        enhancer = RatioFeaturesEnhancer()
        
        # Add ratio features
        enhanced_df = enhancer.enhance_features_dataframe(original_df)
        
        return enhanced_df
    
    return enhanced_extract_features

# Compatibility wrapper for existing pipeline
class EnhancedTSFreshPipeline:
    """
    Wrapper class to enhance existing TSFresh pipeline with ratio features
    while maintaining full compatibility
    """
    
    def __init__(self, original_pipeline_class, add_ratio_features: bool = True, 
                 ratio_config: Optional[Dict[str, bool]] = None):
        """
        Initialize enhanced pipeline
        
        Args:
            original_pipeline_class: Original pipeline class
            add_ratio_features: Whether to add ratio features
            ratio_config: Configuration for ratio types (optional)
        """
        self.original_pipeline = original_pipeline_class
        self.add_ratio_features = add_ratio_features
        
        if add_ratio_features:
            if ratio_config is None:
                # Default configuration: only standard ratio
                ratio_config = {
                    'enable_ratio': True,
                    'enable_inverse_ratio': False,
                    'enable_log_ratio': False,
                    'enable_abs_log_ratio': False,
                    'enable_symmetric_ratio': False
                }
            
            self.enhancer = RatioFeaturesEnhancer(**ratio_config)
        else:
            self.enhancer = None
        
        print(f"🚀 Enhanced TSFresh Pipeline Initialized")
        print(f"   - Ratio features enabled: {add_ratio_features}")
    
    def __getattr__(self, name):
        """Delegate all other attributes to original pipeline"""
        return getattr(self.original_pipeline, name)
    
    def extract_features_batch(self, time_series_list, tstar_list=None):
        """
        Enhanced extract_features_batch with ratio features
        
        Args:
            time_series_list: List of time series
            tstar_list: List of tstar values
            
        Returns:
            Enhanced DataFrame with ratio features
        """
        # Call original method
        original_df = self.original_pipeline.extract_features_batch(time_series_list, tstar_list)
        
        if not self.add_ratio_features or self.enhancer is None:
            return original_df
        
        # Add ratio features
        enhanced_df = self.enhancer.enhance_features_dataframe(original_df)
        
        return enhanced_df

def create_enhanced_pipeline(original_pipeline, add_ratio_features: bool = True):
    """
    Create enhanced pipeline from existing pipeline instance
    
    Args:
        original_pipeline: Existing pipeline instance
        add_ratio_features: Whether to add ratio features
        
    Returns:
        Enhanced pipeline instance
    """
    return EnhancedTSFreshPipeline(original_pipeline, add_ratio_features)

# Example usage and testing
def test_ratio_enhancement():
    """Test the ratio enhancement functionality"""
    print("=" * 60)
    print("TESTING RATIO FEATURES ENHANCEMENT")
    print("=" * 60)
    
    # Create sample features dictionary
    sample_features = {
        'energy_ratio_chunk1_pre': 0.3,
        'energy_ratio_chunk1_post': 0.6,
        'fft_agg_centroid_pre': 10.0,
        'fft_agg_centroid_post': 15.0,
        'spec_entropy_pre': 2.5,
        'spec_entropy_post': 3.0,
        'p_ttest': 0.05,  # Two-sided feature (no ratio)
        'jsd': 0.1,  # Two-sided feature (no ratio)
        'mean_pre': 5.0,  # Already has ratio in original code
        'mean_post': 7.0,
        'time_bin1_num_peaks_pre': 3,
        'time_bin1_num_peaks_post': 5,
    }
    
    # Initialize enhancer
    enhancer = RatioFeaturesEnhancer()
    
    # Add ratio features
    enhanced_features = enhancer.add_ratio_features(sample_features, '')
    enhanced_features = enhancer.add_ratio_features(enhanced_features, 'time_bin1_')
    
    print("Original features:")
    for key, value in sample_features.items():
        print(f"  {key}: {value}")
    
    print("\nNew ratio features:")
    for key, value in enhanced_features.items():
        if key not in sample_features:
            print(f"  {key}: {value}")
    
    print(f"\nTotal features: {len(sample_features)} → {len(enhanced_features)}")
    
    return enhanced_features

if __name__ == "__main__":
    # Run test
    test_results = test_ratio_enhancement()
    
    print("\n" + "=" * 60)
    print("RATIO FEATURES ENHANCEMENT MODULE READY")
    print("=" * 60)
    print("✅ Compatible with existing pipeline")
    print("✅ Adds ratio features where applicable")
    print("✅ Maintains all original features")
    print("✅ Safe division handling")
    print("✅ Supports all segment types (main, time bins, value bins)")