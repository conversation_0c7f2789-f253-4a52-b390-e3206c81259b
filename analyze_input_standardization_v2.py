#!/usr/bin/env python3
"""
Analyze Input Standardization v2
Pairwise metric comparison for pre/post segments
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.metrics import roc_auc_score
import warnings

warnings.filterwarnings('ignore')

def load_time_series_data_with_labels():
    """Load and extract time series with labels"""
    try:
        print("📊 Loading data...")
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        time_series_list = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            grouped = X_data.groupby(level='id')
            for id_, group in grouped:
                series_data = group.sort_index(level='time').values.flatten()
                time_series_list.append(series_data)
                label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                labels.append(label)
        else:
            for idx, row in X_data.iterrows():
                time_series_list.append(row.values)
                label = y_data.iloc[idx] if idx < len(y_data) else 0
                labels.append(label)
        
        print(f"   ✅ Loaded {len(time_series_list)} time series")
        return time_series_list, labels
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None, None

def split_and_compute_metrics(time_series_list, labels):
    """Split series and compute pairwise metrics"""
    print("\n🔄 Computing pre/post metrics...")
    
    results = []
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        if i % 1000 == 0:
            print(f"   Processing {i+1}/{len(time_series_list)}...")
        
        series = np.array(series)
        if len(series) < 20:
            continue
            
        # Split at midpoint
        mid = len(series) // 2
        pre_seg = series[:mid]
        post_seg = series[mid:]
        
        if len(pre_seg) < 10 or len(post_seg) < 10:
            continue
        
        # Compute basic metrics for each segment
        metrics = {}
        
        # Pre-segment metrics
        metrics['pre_mean'] = np.mean(pre_seg)
        metrics['pre_std'] = np.std(pre_seg, ddof=1)
        metrics['pre_median'] = np.median(pre_seg)
        metrics['pre_q25'] = np.percentile(pre_seg, 25)
        metrics['pre_q75'] = np.percentile(pre_seg, 75)
        metrics['pre_iqr'] = metrics['pre_q75'] - metrics['pre_q25']
        metrics['pre_range'] = np.max(pre_seg) - np.min(pre_seg)
        metrics['pre_skew'] = stats.skew(pre_seg)
        metrics['pre_kurt'] = stats.kurtosis(pre_seg)
        metrics['pre_cv'] = metrics['pre_std'] / abs(metrics['pre_mean']) if metrics['pre_mean'] != 0 else np.inf
        
        # Post-segment metrics
        metrics['post_mean'] = np.mean(post_seg)
        metrics['post_std'] = np.std(post_seg, ddof=1)
        metrics['post_median'] = np.median(post_seg)
        metrics['post_q25'] = np.percentile(post_seg, 25)
        metrics['post_q75'] = np.percentile(post_seg, 75)
        metrics['post_iqr'] = metrics['post_q75'] - metrics['post_q25']
        metrics['post_range'] = np.max(post_seg) - np.min(post_seg)
        metrics['post_skew'] = stats.skew(post_seg)
        metrics['post_kurt'] = stats.kurtosis(post_seg)
        metrics['post_cv'] = metrics['post_std'] / abs(metrics['post_mean']) if metrics['post_mean'] != 0 else np.inf
        
        # Pairwise comparisons
        metrics['mean_diff'] = metrics['post_mean'] - metrics['pre_mean']
        metrics['std_diff'] = metrics['post_std'] - metrics['pre_std']
        metrics['median_diff'] = metrics['post_median'] - metrics['pre_median']
        metrics['iqr_diff'] = metrics['post_iqr'] - metrics['pre_iqr']
        metrics['range_diff'] = metrics['post_range'] - metrics['pre_range']
        metrics['skew_diff'] = metrics['post_skew'] - metrics['pre_skew']
        metrics['kurt_diff'] = metrics['post_kurt'] - metrics['pre_kurt']
        
        # Ratios (avoid division by zero)
        metrics['mean_ratio'] = metrics['post_mean'] / metrics['pre_mean'] if metrics['pre_mean'] != 0 else np.inf
        metrics['std_ratio'] = metrics['post_std'] / metrics['pre_std'] if metrics['pre_std'] != 0 else np.inf
        metrics['iqr_ratio'] = metrics['post_iqr'] / metrics['pre_iqr'] if metrics['pre_iqr'] != 0 else np.inf
        
        # Statistical tests
        try:
            t_stat, t_pval = stats.ttest_ind(pre_seg, post_seg)
            ks_stat, ks_pval = stats.ks_2samp(pre_seg, post_seg)
            metrics['t_stat'] = t_stat
            metrics['t_pval'] = t_pval
            metrics['ks_stat'] = ks_stat
            metrics['ks_pval'] = ks_pval
        except:
            metrics['t_stat'] = metrics['t_pval'] = metrics['ks_stat'] = metrics['ks_pval'] = np.nan
        
        # Effect size (Cohen's d)
        pooled_std = np.sqrt(((len(pre_seg) - 1) * np.var(pre_seg, ddof=1) + 
                             (len(post_seg) - 1) * np.var(post_seg, ddof=1)) / 
                            (len(pre_seg) + len(post_seg) - 2))
        metrics['cohens_d'] = metrics['mean_diff'] / pooled_std if pooled_std > 0 else np.nan
        
        # Add metadata
        metrics['series_id'] = i
        metrics['label'] = label
        metrics['pre_length'] = len(pre_seg)
        metrics['post_length'] = len(post_seg)
        
        results.append(metrics)
    
    df = pd.DataFrame(results)
    print(f"   ✅ Computed metrics for {len(df)} series")
    return df

def analyze_pairwise_patterns(df):
    """Analyze pairwise metric patterns"""
    print("\n📊 Analyzing pairwise patterns...")
    
    # Group by label
    normal = df[df['label'] == 0]
    break_series = df[df['label'] == 1]
    
    print(f"   Normal series: {len(normal)} ({len(normal)/len(df)*100:.1f}%)")
    print(f"   Break series: {len(break_series)} ({len(break_series)/len(df)*100:.1f}%)")
    
    # Key metrics to analyze
    metrics = ['mean_diff', 'std_diff', 'median_diff', 'iqr_diff', 'range_diff', 
               'mean_ratio', 'std_ratio', 'iqr_ratio', 'cohens_d', 't_pval', 'ks_pval']
    
    comparison_results = {}
    
    for metric in metrics:
        if metric in df.columns:
            normal_vals = normal[metric].replace([np.inf, -np.inf], np.nan).dropna()
            break_vals = break_series[metric].replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(normal_vals) > 0 and len(break_vals) > 0:
                # Statistical comparison
                try:
                    stat, pval = stats.mannwhitneyu(normal_vals, break_vals, alternative='two-sided')
                    effect_size = (break_vals.median() - normal_vals.median()) / normal_vals.std()
                except:
                    stat = pval = effect_size = np.nan
                
                comparison_results[metric] = {
                    'normal_mean': normal_vals.mean(),
                    'normal_std': normal_vals.std(),
                    'break_mean': break_vals.mean(),
                    'break_std': break_vals.std(),
                    'mann_whitney_stat': stat,
                    'mann_whitney_pval': pval,
                    'effect_size': effect_size
                }
                
                significance = "***" if pval < 0.001 else "**" if pval < 0.01 else "*" if pval < 0.05 else ""
                print(f"   {metric:12}: Normal={normal_vals.mean():.4f}±{normal_vals.std():.4f} | "
                      f"Break={break_vals.mean():.4f}±{break_vals.std():.4f} {significance}")
    
    return comparison_results

def evaluate_classification_performance(df):
    """Evaluate classification performance of different metric combinations"""
    print("\n🎯 Classification performance...")
    
    # Prepare labels
    y = df['label'].values
    
    # Define feature sets
    feature_sets = {
        'differences': ['mean_diff', 'std_diff', 'median_diff', 'iqr_diff', 'range_diff', 'skew_diff', 'kurt_diff'],
        'ratios': ['mean_ratio', 'std_ratio', 'iqr_ratio'],
        'statistical': ['t_stat', 'ks_stat', 'cohens_d'],
        'pre_only': ['pre_mean', 'pre_std', 'pre_median', 'pre_iqr', 'pre_range', 'pre_skew', 'pre_kurt', 'pre_cv'],
        'post_only': ['post_mean', 'post_std', 'post_median', 'post_iqr', 'post_range', 'post_skew', 'post_kurt', 'post_cv'],
        'combined': ['mean_diff', 'std_diff', 'mean_ratio', 'std_ratio', 'cohens_d', 't_pval', 'ks_pval']
    }
    
    # Filter available features
    for set_name in feature_sets:
        available = [f for f in feature_sets[set_name] if f in df.columns]
        feature_sets[set_name] = available
    
    # Model
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    results = {}
    
    for set_name, features in feature_sets.items():
        if not features:
            continue
            
        # Prepare data
        X = df[features].copy()
        X = X.replace([np.inf, -np.inf], np.nan).fillna(X.median())
        
        if X.shape[1] == 0:
            continue
        
        # Standardize
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Cross-validation
        try:
            cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
            results[set_name] = {
                'mean_auroc': cv_scores.mean(),
                'std_auroc': cv_scores.std(),
                'n_features': len(features)
            }
            print(f"   {set_name:12}: AUROC = {cv_scores.mean():.4f} ± {cv_scores.std():.4f} ({len(features)} features)")
        except Exception as e:
            print(f"   {set_name:12}: Error - {e}")
    
    return results

def create_pairwise_visualizations(df, comparison_results):
    """Create pairwise comparison visualizations"""
    print("\n📊 Creating visualizations...")
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    
    # Plot 1: Mean differences by label
    axes[0, 0].hist([df[df['label']==0]['mean_diff'].dropna(), 
                     df[df['label']==1]['mean_diff'].dropna()], 
                    bins=30, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[0, 0].set_xlabel('Mean Difference (Post - Pre)')
    axes[0, 0].set_ylabel('Frequency')
    axes[0, 0].set_title('Mean Change Distribution')
    axes[0, 0].legend()
    
    # Plot 2: Std differences by label
    axes[0, 1].hist([df[df['label']==0]['std_diff'].dropna(), 
                     df[df['label']==1]['std_diff'].dropna()], 
                    bins=30, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[0, 1].set_xlabel('Std Difference (Post - Pre)')
    axes[0, 1].set_ylabel('Frequency')
    axes[0, 1].set_title('Volatility Change Distribution')
    axes[0, 1].legend()
    
    # Plot 3: Mean vs Std ratios
    for label, name in [(0, 'Normal'), (1, 'Break')]:
        mask = df['label'] == label
        mean_ratios = np.clip(df.loc[mask, 'mean_ratio'], 0, 5)
        std_ratios = np.clip(df.loc[mask, 'std_ratio'], 0, 5)
        axes[0, 2].scatter(mean_ratios, std_ratios, alpha=0.6, label=name)
    axes[0, 2].set_xlabel('Mean Ratio (Post/Pre)')
    axes[0, 2].set_ylabel('Std Ratio (Post/Pre)')
    axes[0, 2].set_title('Ratio Changes by Label')
    axes[0, 2].legend()
    axes[0, 2].plot([1, 1], [0, 5], 'k--', alpha=0.3)
    axes[0, 2].plot([0, 5], [1, 1], 'k--', alpha=0.3)
    
    # Plot 4: Cohen's d distribution
    cohens_d_data = [df[df['label']==label]['cohens_d'].dropna() for label in [0, 1]]
    axes[0, 3].boxplot(cohens_d_data, labels=['Normal', 'Break'])
    axes[0, 3].set_ylabel("Cohen's d")
    axes[0, 3].set_title('Effect Size Distribution')
    axes[0, 3].axhline(0, color='red', linestyle='--', alpha=0.5)
    
    # Plot 5: Pre vs Post means
    for label, name in [(0, 'Normal'), (1, 'Break')]:
        mask = df['label'] == label
        axes[1, 0].scatter(df.loc[mask, 'pre_mean'], df.loc[mask, 'post_mean'], 
                          alpha=0.6, label=name)
    axes[1, 0].set_xlabel('Pre-segment Mean')
    axes[1, 0].set_ylabel('Post-segment Mean')
    axes[1, 0].set_title('Pre vs Post Means')
    axes[1, 0].legend()
    # Diagonal line
    min_val, max_val = df[['pre_mean', 'post_mean']].min().min(), df[['pre_mean', 'post_mean']].max().max()
    axes[1, 0].plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5)
    
    # Plot 6: Pre vs Post stds
    for label, name in [(0, 'Normal'), (1, 'Break')]:
        mask = df['label'] == label
        axes[1, 1].scatter(df.loc[mask, 'pre_std'], df.loc[mask, 'post_std'], 
                          alpha=0.6, label=name)
    axes[1, 1].set_xlabel('Pre-segment Std')
    axes[1, 1].set_ylabel('Post-segment Std')
    axes[1, 1].set_title('Pre vs Post Standard Deviations')
    axes[1, 1].legend()
    # Diagonal line
    min_val, max_val = df[['pre_std', 'post_std']].min().min(), df[['pre_std', 'post_std']].max().max()
    axes[1, 1].plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.5)
    
    # Plot 7: Statistical significance
    axes[1, 2].hist([df[df['label']==0]['t_pval'].dropna(), 
                     df[df['label']==1]['t_pval'].dropna()], 
                    bins=20, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[1, 2].axvline(0.05, color='red', linestyle='--', label='p=0.05')
    axes[1, 2].set_xlabel('T-test P-value')
    axes[1, 2].set_ylabel('Frequency')
    axes[1, 2].set_title('Statistical Significance')
    axes[1, 2].legend()
    axes[1, 2].set_yscale('log')
    
    # Plot 8: IQR changes
    axes[1, 3].hist([df[df['label']==0]['iqr_diff'].dropna(), 
                     df[df['label']==1]['iqr_diff'].dropna()], 
                    bins=30, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[1, 3].set_xlabel('IQR Difference (Post - Pre)')
    axes[1, 3].set_ylabel('Frequency')
    axes[1, 3].set_title('Interquartile Range Changes')
    axes[1, 3].legend()
    
    # Plot 9: Range changes
    axes[2, 0].hist([df[df['label']==0]['range_diff'].dropna(), 
                     df[df['label']==1]['range_diff'].dropna()], 
                    bins=30, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[2, 0].set_xlabel('Range Difference (Post - Pre)')
    axes[2, 0].set_ylabel('Frequency')
    axes[2, 0].set_title('Range Changes')
    axes[2, 0].legend()
    
    # Plot 10: Skewness changes
    axes[2, 1].hist([df[df['label']==0]['skew_diff'].dropna(), 
                     df[df['label']==1]['skew_diff'].dropna()], 
                    bins=30, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[2, 1].set_xlabel('Skewness Difference (Post - Pre)')
    axes[2, 1].set_ylabel('Frequency')
    axes[2, 1].set_title('Skewness Changes')
    axes[2, 1].legend()
    
    # Plot 11: Kurtosis changes
    axes[2, 2].hist([df[df['label']==0]['kurt_diff'].dropna(), 
                     df[df['label']==1]['kurt_diff'].dropna()], 
                    bins=30, alpha=0.7, label=['Normal', 'Break'], edgecolor='black')
    axes[2, 2].set_xlabel('Kurtosis Difference (Post - Pre)')
    axes[2, 2].set_ylabel('Frequency')
    axes[2, 2].set_title('Kurtosis Changes')
    axes[2, 2].legend()
    
    # Plot 12: Summary statistics
    axes[2, 3].axis('off')
    
    # Create summary text
    summary_text = "SUMMARY STATISTICS\n\n"
    summary_text += f"Total series: {len(df)}\n"
    summary_text += f"Normal: {len(df[df['label']==0])} ({len(df[df['label']==0])/len(df)*100:.1f}%)\n"
    summary_text += f"Break: {len(df[df['label']==1])} ({len(df[df['label']==1])/len(df)*100:.1f}%)\n\n"
    
    # Add significant differences
    significant_metrics = []
    for metric, results in comparison_results.items():
        if results['mann_whitney_pval'] < 0.05:
            significant_metrics.append(metric)
    
    summary_text += f"Significant differences:\n"
    for metric in significant_metrics[:5]:  # Top 5
        summary_text += f"  {metric}\n"
    
    axes[2, 3].text(0.1, 0.9, summary_text, transform=axes[2, 3].transAxes, 
                   fontsize=11, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('pairwise_metrics_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("   ✅ Saved visualization to 'pairwise_metrics_analysis.png'")

def main():
    """Main analysis function"""
    print("🔍 PAIRWISE METRICS ANALYSIS v2")
    print("=" * 50)
    
    # Load data
    time_series_list, labels = load_time_series_data_with_labels()
    if time_series_list is None:
        return
    
    # Compute metrics
    df = split_and_compute_metrics(time_series_list, labels)
    
    # Analyze patterns
    comparison_results = analyze_pairwise_patterns(df)
    
    # Evaluate classification
    classification_results = evaluate_classification_performance(df)
    
    # Create visualizations
    create_pairwise_visualizations(df, comparison_results)
    
    # Save results
    df.to_csv('pairwise_metrics_results.csv', index=False)
    
    # Save comparison results
    comparison_df = pd.DataFrame(comparison_results).T
    comparison_df.to_csv('pairwise_comparison_stats.csv')
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Metrics data: 'pairwise_metrics_results.csv'")
    print(f"   📊 Comparison stats: 'pairwise_comparison_stats.csv'")
    print(f"   📈 Visualization: 'pairwise_metrics_analysis.png'")
    
    return df, comparison_results, classification_results

if __name__ == "__main__":
    results = main()