import numpy as np
import pandas as pd
from scipy.signal import welch, butter, filtfilt, hilbert
from scipy.stats import pearsonr
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import cross_val_score
import xgboost as xgb
import argparse
import sys
import warnings
warnings.filterwarnings('ignore')

def design_bandpass_filter(center_freq, bandwidth_ratio=0.2, fs=500, order=4):
    """Design bandpass filter around center frequency."""
    bandwidth = center_freq * bandwidth_ratio
    low_freq = max(0.01, center_freq - bandwidth/2)  # Avoid zero frequency
    high_freq = min(fs/2 - 0.01, center_freq + bandwidth/2)  # Avoid Nyquist
    
    if low_freq >= high_freq:
        return None, None
    
    try:
        b, a = butter(order, [low_freq, high_freq], btype='band', fs=fs)
        return b, a
    except Exception:
        return None, None

def extract_filter_preservation_features(series, tstar, fs=500, bandwidth_ratio=0.2):
    """
    Extract features based on filtering post-break with pre-break dominant frequency.
    """
    features = {}
    
    # Handle edge cases
    series = np.nan_to_num(np.array(series), nan=np.nanmean(series))
    tstar = min(int(tstar), len(series)-1)
    
    pre_segment = series[:tstar]
    post_segment = series[tstar:]
    
    if len(pre_segment) < 20 or len(post_segment) < 20:
        return {f'filter_{k}': np.nan for k in range(20)}
    
    try:
        # 1. Find dominant frequency in pre-break segment
        f_pre, psd_pre = welch(pre_segment, fs=fs, nperseg=64)#min(len(pre_segment)//2, 64))
        dominant_freq_pre = f_pre[np.argmax(psd_pre)]
        dominant_power_pre = np.max(psd_pre)
        
        # 2. Design bandpass filter around pre-break dominant frequency
        b, a = design_bandpass_filter(dominant_freq_pre, bandwidth_ratio, fs)
        if b is None:
            return {f'filter_{k}': np.nan for k in range(20)}
        
        # 3. Filter post-break segment with pre-break dominant frequency
        post_filtered = filtfilt(b, a, post_segment)
        post_residual = post_segment - post_filtered
        
        # 4. SIGNAL PRESERVATION FEATURES
        power_original = np.var(post_segment)
        power_filtered = np.var(post_filtered)
        power_residual = np.var(post_residual)
        
        features['preservation_ratio'] = power_filtered / power_original if power_original > 0 else np.nan
        features['noise_ratio'] = power_residual / power_original if power_original > 0 else np.nan
        features['signal_loss'] = 1 - features['preservation_ratio'] if not np.isnan(features['preservation_ratio']) else np.nan
        
        # 5. FREQUENCY SHIFT DETECTION
        f_post, psd_post = welch(post_segment, fs=fs, nperseg=64)#min(len(post_segment)//2, 64))
        f_post_filt, psd_post_filt = welch(post_filtered, fs=fs, nperseg=64)#min(len(post_filtered)//2, 64))
        
        dominant_freq_post = f_post[np.argmax(psd_post)]
        dominant_freq_post_filt = f_post_filt[np.argmax(psd_post_filt)]
        
        features['frequency_drift'] = abs(dominant_freq_post - dominant_freq_pre)
        features['frequency_drift_rel'] = abs(dominant_freq_post - dominant_freq_pre) / dominant_freq_pre if dominant_freq_pre > 0 else np.nan
        features['filtered_freq_shift'] = abs(dominant_freq_post_filt - dominant_freq_pre)
        
        # Spectral leakage (power outside the filter band)
        filter_band_mask = (f_post >= dominant_freq_pre - dominant_freq_pre*bandwidth_ratio/2) & \
                          (f_post <= dominant_freq_pre + dominant_freq_pre*bandwidth_ratio/2)
        power_in_band = np.sum(psd_post[filter_band_mask])
        power_total = np.sum(psd_post)
        features['spectral_leakage'] = 1 - (power_in_band / power_total) if power_total > 0 else np.nan
        
        # 6. TEMPORAL CONSISTENCY FEATURES
        # Envelope consistency
        envelope_pre = np.abs(hilbert(pre_segment))
        envelope_post_filt = np.abs(hilbert(post_filtered))
        
        # Resample to same length for correlation
        min_len = min(len(envelope_pre), len(envelope_post_filt))
        if min_len > 10:
            env_pre_resamp = envelope_pre[:min_len]
            env_post_resamp = envelope_post_filt[:min_len]
            features['envelope_correlation'], _ = pearsonr(env_pre_resamp, env_post_resamp)
        else:
            features['envelope_correlation'] = np.nan
        
        # Autocorrelation consistency
        def autocorr_at_lag(x, lag=1):
            if len(x) <= lag:
                return np.nan
            return np.corrcoef(x[:-lag], x[lag:])[0, 1] if len(x) > lag else np.nan
        
        ac_pre = autocorr_at_lag(pre_segment, lag=1)
        ac_post_filt = autocorr_at_lag(post_filtered, lag=1)
        features['autocorr_change'] = abs(ac_post_filt - ac_pre) if not (np.isnan(ac_pre) or np.isnan(ac_post_filt)) else np.nan
        
        # 7. MAGNITUDE SCALING FEATURES
        # RMS ratio (handles magnitude differences)
        rms_pre = np.sqrt(np.mean(pre_segment**2))
        rms_post = np.sqrt(np.mean(post_segment**2))
        rms_post_filt = np.sqrt(np.mean(post_filtered**2))
        
        features['rms_ratio_original'] = rms_post / rms_pre if rms_pre > 0 else np.nan
        features['rms_ratio_filtered'] = rms_post_filt / rms_pre if rms_pre > 0 else np.nan
        features['rms_preservation'] = rms_post_filt / rms_post if rms_post > 0 else np.nan
        
        # 8. HARMONIC DISTORTION
        # Compare harmonic content
        harmonics = [2, 3, 4]  # Check first few harmonics
        harmonic_distortion = 0
        for h in harmonics:
            harmonic_freq = dominant_freq_pre * h
            if harmonic_freq < fs/2:
                # Find power near harmonic frequency
                harmonic_mask_pre = (f_pre >= harmonic_freq*0.9) & (f_pre <= harmonic_freq*1.1)
                harmonic_mask_post = (f_post >= harmonic_freq*0.9) & (f_post <= harmonic_freq*1.1)
                
                harmonic_power_pre = np.sum(psd_pre[harmonic_mask_pre])
                harmonic_power_post = np.sum(psd_post[harmonic_mask_post])
                
                if harmonic_power_pre > 0:
                    harmonic_distortion += abs(harmonic_power_post - harmonic_power_pre) / harmonic_power_pre
        
        features['harmonic_distortion'] = harmonic_distortion / len(harmonics)
        
        # 9. FILTER EFFECTIVENESS METRICS
        # Signal-to-noise ratio improvement
        snr_original = power_filtered / power_residual if power_residual > 0 else np.nan
        features['snr_improvement'] = snr_original
        
        # Coherence between pre and filtered post
        if len(pre_segment) == len(post_filtered):
            features['pre_post_coherence'], _ = pearsonr(pre_segment, post_filtered)
        else:
            # Resample to same length
            min_len = min(len(pre_segment), len(post_filtered))
            if min_len > 10:
                features['pre_post_coherence'], _ = pearsonr(pre_segment[:min_len], post_filtered[:min_len])
            else:
                features['pre_post_coherence'] = np.nan
        
        # 10. RESIDUAL ANALYSIS
        # Analyze what was filtered out
        residual_power_ratio = power_residual / power_original if power_original > 0 else np.nan
        features['residual_power_ratio'] = residual_power_ratio
        
        # Residual frequency content
        if len(post_residual) > 20:
            f_res, psd_res = welch(post_residual, fs=fs, nperseg=64)#min(len(post_residual)//2, 64))
            residual_dominant_freq = f_res[np.argmax(psd_res)]
            features['residual_dominant_freq'] = residual_dominant_freq
            features['residual_freq_diff'] = abs(residual_dominant_freq - dominant_freq_pre)
        else:
            features['residual_dominant_freq'] = np.nan
            features['residual_freq_diff'] = np.nan
            
    except Exception as e:
        return {f'filter_{k}': np.nan for k in range(20)}
    
    return features

def evaluate_filter_approach(all_series, all_tstars, all_labels, bandwidth_ratios=[0.1, 0.2, 0.3, 0.5]):
    """
    Evaluate the frequency filtering approach with different bandwidth ratios.
    """
    results = {}
    
    print("Evaluating frequency filtering approach...")
    
    for bandwidth_ratio in bandwidth_ratios:
        print(f"\nTesting bandwidth ratio: {bandwidth_ratio}")
        
        # Extract features for all series
        feature_list = []
        for series, tstar in zip(all_series, all_tstars):
            features = extract_filter_preservation_features(series, tstar, bandwidth_ratio=bandwidth_ratio)
            feature_list.append(features)
        
        # Convert to DataFrame
        feature_df = pd.DataFrame(feature_list)
        feature_df = feature_df.fillna(feature_df.median())
        
        if feature_df.shape[1] == 0:
            print(f"No valid features for bandwidth ratio {bandwidth_ratio}")
            continue
        
        # Evaluate with XGBoost GPU
        try:
            model = xgb.XGBClassifier(
                n_estimators=100, 
                random_state=42, 
                tree_method='gpu_hist',
                gpu_id=0,
                eval_metric='auc'
            )
            cv_scores = cross_val_score(model, feature_df, all_labels, cv=5, scoring='roc_auc')
            auc_score = np.mean(cv_scores)
            auc_std = np.std(cv_scores)
            
            results[bandwidth_ratio] = {
                'auc_mean': auc_score,
                'auc_std': auc_std,
                'n_features': feature_df.shape[1],
                'feature_names': list(feature_df.columns)
            }
            
            print(f"AUC: {auc_score:.4f} ± {auc_std:.4f} ({feature_df.shape[1]} features)")
            
            # Feature importance
            model.fit(feature_df, all_labels)
            feature_importance = pd.DataFrame({
                'feature': feature_df.columns,
                'importance': model.feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("Top 5 features:")
            for _, row in feature_importance.head().iterrows():
                print(f"  {row['feature']}: {row['importance']:.4f}")
                
        except Exception as e:
            print(f"Error evaluating bandwidth ratio {bandwidth_ratio}: {e}")
            results[bandwidth_ratio] = {'auc_mean': np.nan, 'auc_std': np.nan, 'n_features': 0}
    
    return results

def compare_with_baseline(all_series, all_tstars, all_labels):
    """
    Compare filtering approach with simple baseline features.
    """
    print("\n" + "="*50)
    print("COMPARISON WITH BASELINE")
    print("="*50)
    
    # Baseline: simple pre/post mean and std ratios
    baseline_features = []
    for series, tstar in zip(all_series, all_tstars):
        series = np.array(series)
        pre = series[:tstar]
        post = series[tstar:]
        
        if len(pre) > 0 and len(post) > 0:
            features = {
                'mean_ratio': np.mean(post) / np.mean(pre) if np.mean(pre) != 0 else np.nan,
                'std_ratio': np.std(post) / np.std(pre) if np.std(pre) != 0 else np.nan,
                'var_ratio': np.var(post) / np.var(pre) if np.var(pre) != 0 else np.nan
            }
        else:
            features = {'mean_ratio': np.nan, 'std_ratio': np.nan, 'var_ratio': np.nan}
        
        baseline_features.append(features)
    
    baseline_df = pd.DataFrame(baseline_features).fillna(0)
    
    # Evaluate baseline
    model = xgb.XGBClassifier(n_estimators=100, random_state=42, tree_method='gpu_hist', gpu_id=0, eval_metric='auc')
    baseline_scores = cross_val_score(model, baseline_df, all_labels, cv=5, scoring='roc_auc')
    baseline_auc = np.mean(baseline_scores)
    
    print(f"Baseline AUC (simple ratios): {baseline_auc:.4f} ± {np.std(baseline_scores):.4f}")
    
    # Evaluate filtering approach
    filter_results = evaluate_filter_approach(all_series, all_tstars, all_labels, [0.2])
    
    if 0.2 in filter_results:
        filter_auc = filter_results[0.2]['auc_mean']
        improvement = filter_auc - baseline_auc
        print(f"Filtering AUC: {filter_auc:.4f}")
        print(f"Improvement: {improvement:+.4f}")
        
        if improvement > 0.01:
            print("✅ Filtering approach shows promise!")
        else:
            print("❌ Filtering approach doesn't improve significantly")
    
    return baseline_auc, filter_results

def load_data(data_path):
    """Load time series data from parquet files."""
    try:
        X_train = pd.read_parquet(f'{data_path}/X_train.parquet')
        y_train = pd.read_parquet(f'{data_path}/y_train.parquet')
        
        all_series = []
        all_tstars = []
        all_labels = []
        
        grouped = X_train.groupby(level='id')
        for id_, group in grouped:
            group = group.sort_index(level='time')
            values = group['value'].values
            periods = group['period'].values
            
            try:
                tstar = np.where(periods == 1)[0][0]
            except IndexError:
                tstar = len(values)
            
            label = int(y_train.loc[id_])
            all_series.append(values)
            all_tstars.append(tstar)
            all_labels.append(label)
        
        return all_series, all_tstars, all_labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None, None, None

def main():
    parser = argparse.ArgumentParser(description='Frequency Filter Probing for Structural Break Detection')
    parser.add_argument('--data_path', type=str, default='.', 
                       help='Path to data directory containing parquet files')
    parser.add_argument('--bandwidth_ratios', nargs='+', type=float, default=[0.1, 0.2, 0.3, 0.5],
                       help='Bandwidth ratios to test')
    parser.add_argument('--fs', type=float, default=500, help='Sampling frequency')
    parser.add_argument('--baseline_only', action='store_true', help='Run baseline comparison only')
    
    args = parser.parse_args()
    
    print("Frequency Filter Probing Pipeline")
    print("="*50)
    print(f"Data path: {args.data_path}")
    print(f"Bandwidth ratios: {args.bandwidth_ratios}")
    print(f"Sampling frequency: {args.fs}")
    
    # Load data
    print("\nLoading data...")
    all_series, all_tstars, all_labels = load_data(args.data_path)
    
    if all_series is None:
        print("Failed to load data. Exiting.")
        sys.exit(1)
    
    print(f"Loaded {len(all_series)} time series")
    print(f"Labels distribution: {np.bincount(all_labels)}")
    
    # Run analysis
    if args.baseline_only:
        baseline_auc, filter_results = compare_with_baseline(all_series, all_tstars, all_labels)
    else:
        results = evaluate_filter_approach(all_series, all_tstars, all_labels, args.bandwidth_ratios)
        
        print("\n" + "="*50)
        print("SUMMARY RESULTS")
        print("="*50)
        
        best_bandwidth = None
        best_auc = 0
        
        for bandwidth, result in results.items():
            if not np.isnan(result['auc_mean']):
                print(f"Bandwidth {bandwidth}: AUC = {result['auc_mean']:.4f} ± {result['auc_std']:.4f}")
                if result['auc_mean'] > best_auc:
                    best_auc = result['auc_mean']
                    best_bandwidth = bandwidth
        
        if best_bandwidth is not None:
            print(f"\n🏆 Best bandwidth: {best_bandwidth} (AUC = {best_auc:.4f})")
        else:
            print("\n❌ No valid results found")

if __name__ == "__main__":
    main()