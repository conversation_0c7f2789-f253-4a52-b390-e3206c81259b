"""
Revised Truncation Analysis - Detect subtle truncation patterns like sample 878
Focus on round numbers, suspicious clustering, and boundary effects
"""

import numpy as np
import pandas as pd
import json
from collections import defaultdict

def detect_subtle_truncation_patterns():
    """Detect subtle truncation patterns across all samples"""
    
    print("🔍 REVISED TRUNCATION ANALYSIS - DETECTING SUBTLE PATTERNS")
    print("=" * 70)
    
    # Load data
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    print(f"📊 Dataset: X={X_data.shape}, y={len(y_data)}")
    
    # Get all unique IDs
    all_ids = y_data.index.tolist()
    print(f"📊 Total samples to analyze: {len(all_ids)}")
    
    suspicious_samples = []
    truncation_patterns = defaultdict(list)
    
    # Analyze each sample
    for i, sample_id in enumerate(all_ids):
        try:
            # Extract sample data
            sample_X = X_data.loc[sample_id]
            sample_y = y_data.loc[sample_id].values[0]
            
            # Separate normal and candidate parts
            normal_mask = sample_X['period'] == 0
            candidate_mask = sample_X['period'] == 1
            
            normal_values = sample_X.loc[normal_mask, 'value'].values
            candidate_values = sample_X.loc[candidate_mask, 'value'].values
            
            if len(normal_values) < 10 or len(candidate_values) < 10:
                continue
            
            # Analyze both parts for truncation
            normal_analysis = analyze_part_for_truncation(normal_values, f"sample_{sample_id}_normal")
            candidate_analysis = analyze_part_for_truncation(candidate_values, f"sample_{sample_id}_candidate")
            
            # Check if either part is suspicious
            sample_suspicious = False
            sample_reasons = []
            
            if normal_analysis['is_suspicious']:
                sample_suspicious = True
                sample_reasons.extend([f"normal_{reason}" for reason in normal_analysis['reasons']])
                
            if candidate_analysis['is_suspicious']:
                sample_suspicious = True
                sample_reasons.extend([f"candidate_{reason}" for reason in candidate_analysis['reasons']])
            
            if sample_suspicious:
                suspicious_samples.append({
                    'id': sample_id,
                    'label': sample_y,
                    'reasons': sample_reasons,
                    'normal_analysis': normal_analysis,
                    'candidate_analysis': candidate_analysis
                })
                
                # Group by pattern type
                for reason in sample_reasons:
                    truncation_patterns[reason].append(sample_id)
            
            # Progress indicator
            if (i + 1) % 1000 == 0:
                print(f"   Analyzed {i+1:,} samples, found {len(suspicious_samples)} suspicious...")
                
        except Exception as e:
            continue
    
    return suspicious_samples, truncation_patterns

def analyze_part_for_truncation(values, part_name):
    """Analyze a single part (normal or candidate) for truncation patterns"""
    
    analysis = {
        'part_name': part_name,
        'n_values': len(values),
        'min_val': np.min(values),
        'max_val': np.max(values),
        'range': np.max(values) - np.min(values),
        'is_suspicious': False,
        'reasons': [],
        'suspicion_score': 0.0
    }
    
    min_val = analysis['min_val']
    max_val = analysis['max_val']
    
    # 1. Check for suspicious round numbers (like 0.070000 in sample 878)
    round_patterns = check_round_number_truncation(min_val, max_val)
    if round_patterns['is_suspicious']:
        analysis['is_suspicious'] = True
        analysis['reasons'].extend(round_patterns['reasons'])
        analysis['suspicion_score'] += round_patterns['score']
    
    # 2. Check for clustering near extremes
    clustering_patterns = check_extreme_clustering(values, min_val, max_val)
    if clustering_patterns['is_suspicious']:
        analysis['is_suspicious'] = True
        analysis['reasons'].extend(clustering_patterns['reasons'])
        analysis['suspicion_score'] += clustering_patterns['score']
    
    # 3. Check for flat-line patterns
    flatline_patterns = check_flatline_patterns(values, min_val, max_val)
    if flatline_patterns['is_suspicious']:
        analysis['is_suspicious'] = True
        analysis['reasons'].extend(flatline_patterns['reasons'])
        analysis['suspicion_score'] += flatline_patterns['score']
    
    # 4. Check for symmetric truncation
    symmetry_patterns = check_symmetric_truncation(min_val, max_val)
    if symmetry_patterns['is_suspicious']:
        analysis['is_suspicious'] = True
        analysis['reasons'].extend(symmetry_patterns['reasons'])
        analysis['suspicion_score'] += symmetry_patterns['score']
    
    # 5. Check for decimal precision patterns
    precision_patterns = check_decimal_precision_patterns(values)
    if precision_patterns['is_suspicious']:
        analysis['is_suspicious'] = True
        analysis['reasons'].extend(precision_patterns['reasons'])
        analysis['suspicion_score'] += precision_patterns['score']
    
    return analysis

def check_round_number_truncation(min_val, max_val):
    """Check for suspicious round number extremes like 0.070000"""
    
    result = {'is_suspicious': False, 'reasons': [], 'score': 0.0}
    
    # Define suspicious round numbers with different precision levels
    round_patterns = [
        # Exact round numbers
        (0.0, 'exact_zero'),
        (1.0, 'exact_one'), (-1.0, 'exact_minus_one'),
        (2.0, 'exact_two'), (-2.0, 'exact_minus_two'),
        (5.0, 'exact_five'), (-5.0, 'exact_minus_five'),
        (10.0, 'exact_ten'), (-10.0, 'exact_minus_ten'),
        
        # Common decimal truncations
        (0.1, 'decimal_one_tenth'), (-0.1, 'decimal_minus_one_tenth'),
        (0.01, 'decimal_one_hundredth'), (-0.01, 'decimal_minus_one_hundredth'),
        (0.5, 'decimal_half'), (-0.5, 'decimal_minus_half'),
        
        # Suspicious precision patterns (like 0.070000)
        (0.07, 'suspicious_007'), (-0.07, 'suspicious_minus_007'),
        (0.03, 'suspicious_003'), (-0.03, 'suspicious_minus_003'),
        (0.05, 'suspicious_005'), (-0.05, 'suspicious_minus_005'),
    ]
    
    # Check min value
    for round_val, pattern_name in round_patterns:
        if abs(min_val - round_val) < 1e-8:
            result['is_suspicious'] = True
            result['reasons'].append(f'round_min_{pattern_name}')
            result['score'] += 1.0
    
    # Check max value  
    for round_val, pattern_name in round_patterns:
        if abs(max_val - round_val) < 1e-8:
            result['is_suspicious'] = True
            result['reasons'].append(f'round_max_{pattern_name}')
            result['score'] += 1.0
    
    # Check for patterns like 0.070000 (exactly N decimal places)
    for val, val_name in [(min_val, 'min'), (max_val, 'max')]:
        val_str = f"{val:.10f}"
        
        # Check if it ends with many zeros (indicating truncation)
        if val_str.endswith('0000') and not val_str.endswith('00000'):
            result['is_suspicious'] = True
            result['reasons'].append(f'decimal_truncation_{val_name}')
            result['score'] += 0.8
        
        # Check for exactly 2-3 significant decimal places
        if '.' in val_str:
            decimal_part = val_str.split('.')[1]
            non_zero_decimals = len(decimal_part.rstrip('0'))
            if non_zero_decimals in [1, 2, 3] and len(decimal_part) >= 6:
                result['is_suspicious'] = True
                result['reasons'].append(f'limited_precision_{val_name}')
                result['score'] += 0.6
    
    return result

def check_extreme_clustering(values, min_val, max_val):
    """Check for clustering near extreme values"""
    
    result = {'is_suspicious': False, 'reasons': [], 'score': 0.0}
    
    # Check clustering at different thresholds
    range_val = max_val - min_val
    if range_val == 0:
        return result
    
    for threshold_pct in [0.1, 0.5, 1.0, 2.0]:
        threshold = threshold_pct / 100.0 * range_val
        
        near_min = np.sum(np.abs(values - min_val) <= threshold)
        near_max = np.sum(np.abs(values - max_val) <= threshold)
        
        total_values = len(values)
        min_pct = near_min / total_values * 100
        max_pct = near_max / total_values * 100
        
        # Suspicious if >1% of values are within small threshold of extremes
        if min_pct > 1.0:
            result['is_suspicious'] = True
            result['reasons'].append(f'min_clustering_{threshold_pct}pct')
            result['score'] += min_pct / 10.0
        
        if max_pct > 1.0:
            result['is_suspicious'] = True
            result['reasons'].append(f'max_clustering_{threshold_pct}pct')
            result['score'] += max_pct / 10.0
    
    return result

def check_flatline_patterns(values, min_val, max_val):
    """Check for flat-line patterns at extremes"""
    
    result = {'is_suspicious': False, 'reasons': [], 'score': 0.0}
    
    # Find consecutive occurrences of extreme values
    min_consecutive = find_max_consecutive_occurrences(values, min_val)
    max_consecutive = find_max_consecutive_occurrences(values, max_val)
    
    # Suspicious if more than 3 consecutive extreme values
    if min_consecutive > 3:
        result['is_suspicious'] = True
        result['reasons'].append(f'flatline_min_{min_consecutive}')
        result['score'] += min_consecutive / 10.0
    
    if max_consecutive > 3:
        result['is_suspicious'] = True
        result['reasons'].append(f'flatline_max_{max_consecutive}')
        result['score'] += max_consecutive / 10.0
    
    return result

def check_symmetric_truncation(min_val, max_val):
    """Check for symmetric truncation patterns"""
    
    result = {'is_suspicious': False, 'reasons': [], 'score': 0.0}
    
    # Check if min and max are symmetric around zero
    if abs(abs(min_val) - abs(max_val)) < 1e-6 and abs(min_val) > 0.01:
        result['is_suspicious'] = True
        result['reasons'].append('symmetric_bounds')
        result['score'] += 1.0
    
    return result

def check_decimal_precision_patterns(values):
    """Check for suspicious decimal precision patterns"""
    
    result = {'is_suspicious': False, 'reasons': [], 'score': 0.0}
    
    # Count values with limited decimal precision
    limited_precision_count = 0
    
    for val in values:
        val_str = f"{val:.10f}"
        if '.' in val_str:
            decimal_part = val_str.split('.')[1]
            non_zero_decimals = len(decimal_part.rstrip('0'))
            
            # Suspicious if many values have exactly 1-3 decimal places
            if non_zero_decimals <= 3 and len(decimal_part) >= 6:
                limited_precision_count += 1
    
    precision_pct = limited_precision_count / len(values) * 100
    
    # Suspicious if >10% of values have limited precision
    if precision_pct > 10.0:
        result['is_suspicious'] = True
        result['reasons'].append(f'limited_precision_{precision_pct:.1f}pct')
        result['score'] += precision_pct / 20.0
    
    return result

def find_max_consecutive_occurrences(values, target_value):
    """Find maximum consecutive occurrences of target value"""
    max_consecutive = 0
    current_consecutive = 0
    
    for val in values:
        if abs(val - target_value) < 1e-10:
            current_consecutive += 1
            max_consecutive = max(max_consecutive, current_consecutive)
        else:
            current_consecutive = 0
    
    return max_consecutive

def analyze_results(suspicious_samples, truncation_patterns):
    """Analyze and summarize the truncation detection results"""
    
    print(f"\n🏆 REVISED TRUNCATION ANALYSIS RESULTS")
    print("=" * 60)
    
    print(f"📊 Summary:")
    print(f"   Total suspicious samples: {len(suspicious_samples)}")
    print(f"   Suspicion rate: {len(suspicious_samples)/10001*100:.2f}%")
    
    if not suspicious_samples:
        print(f"   ✅ No suspicious truncation patterns detected")
        return
    
    # Analyze by pattern type
    print(f"\n🔍 Truncation Patterns Found:")
    for pattern, sample_ids in truncation_patterns.items():
        print(f"   {pattern}: {len(sample_ids)} samples")
    
    # Analyze by label
    label_analysis = defaultdict(int)
    for sample in suspicious_samples:
        label_analysis[sample['label']] += 1
    
    print(f"\n📊 Suspicious samples by label:")
    for label, count in label_analysis.items():
        print(f"   Label {label}: {count} samples")
    
    # Show top suspicious samples
    print(f"\n🚨 TOP 20 MOST SUSPICIOUS SAMPLES:")
    sorted_samples = sorted(suspicious_samples, 
                           key=lambda x: x['normal_analysis']['suspicion_score'] + x['candidate_analysis']['suspicion_score'], 
                           reverse=True)
    
    print("   ID   | Label | Score | Reasons")
    print("   -----|-------|-------|--------")
    
    for sample in sorted_samples[:20]:
        total_score = sample['normal_analysis']['suspicion_score'] + sample['candidate_analysis']['suspicion_score']
        reasons_str = ', '.join(sample['reasons'][:3])  # Show first 3 reasons
        if len(sample['reasons']) > 3:
            reasons_str += '...'
        
        print(f"   {sample['id']:4d} |   {sample['label']}   | {total_score:5.2f} | {reasons_str}")
    
    # Check if sample 878 is in the list
    sample_878_found = any(s['id'] == 878 for s in suspicious_samples)
    if sample_878_found:
        sample_878 = next(s for s in suspicious_samples if s['id'] == 878)
        print(f"\n✅ Sample 878 DETECTED as suspicious!")
        print(f"   Reasons: {sample_878['reasons']}")
        print(f"   Score: {sample_878['normal_analysis']['suspicion_score'] + sample_878['candidate_analysis']['suspicion_score']:.2f}")
    else:
        print(f"\n❌ Sample 878 NOT detected (algorithm needs refinement)")
    
    return sorted_samples

def main():
    """Main analysis function"""
    
    # Run the revised truncation analysis
    suspicious_samples, truncation_patterns = detect_subtle_truncation_patterns()
    
    # Analyze results
    top_suspicious = analyze_results(suspicious_samples, truncation_patterns)
    
    # Save results
    results = {
        'total_suspicious': len(suspicious_samples),
        'truncation_patterns': {k: len(v) for k, v in truncation_patterns.items()},
        'top_suspicious_samples': [
            {
                'id': s['id'],
                'label': s['label'],
                'reasons': s['reasons'],
                'total_score': s['normal_analysis']['suspicion_score'] + s['candidate_analysis']['suspicion_score']
            }
            for s in top_suspicious[:50] if top_suspicious
        ]
    }
    
    with open('revised_truncation_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n💾 Results saved to 'revised_truncation_analysis_results.json'")
    
    return suspicious_samples

if __name__ == "__main__":
    main()