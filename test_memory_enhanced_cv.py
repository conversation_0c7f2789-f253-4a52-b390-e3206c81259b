"""
Walk-Forward CV Test for Memory-Enhanced Single Branch Learner
Tests fractal memory calibration with 3 fractal windows and multiple classifiers
"""

import numpy as np
import pandas as pd
import time
from typing import Dict, List, Tuple
from collections import Counter, deque
from memory_enhanced_single_branch import MemoryEnhancedSingleBranchLearner
from hybrid_branch_classifier_implementation import HybridBranchClassifier
from distributional_branch_classifier import DistributionalBranchClassifier
from sklearn.metrics import roc_auc_score, accuracy_score
from sklearn.model_selection import TimeSeriesSplit
import matplotlib.pyplot as plt
import json

class TripleFractalMemoryCell:
    """Enhanced memory cell with 3 optimal fractal windows"""
    
    def __init__(self, fractal_windows=[500, 1000, 2000], target_ratio=0.2909):
        self.fractal_windows = fractal_windows
        self.target_ratio = target_ratio
        
        # Memory for each fractal window
        self.fractal_memories = {}
        for window in fractal_windows:
            self.fractal_memories[window] = {
                'outcomes': deque(maxlen=window),
                'current_ratio': 0.0,
                'deviation': 0.0,
                'stability_score': 0.0
            }
        
        # Global tracking
        self.total_samples = 0
        self.total_positive = 0
        self.global_ratio = 0.0
        
        # Fractal weights (from optimal windows analysis)
        self.fractal_weights = [0.3, 0.35, 0.35]  # 500, 1000, 2000
        
    def update_memory(self, outcome: int):
        """Update all fractal memories"""
        self.total_samples += 1
        self.total_positive += outcome
        self.global_ratio = self.total_positive / self.total_samples
        
        # Update each fractal window
        for i, window in enumerate(self.fractal_windows):
            memory = self.fractal_memories[window]
            memory['outcomes'].append(outcome)
            
            if len(memory['outcomes']) > 0:
                memory['current_ratio'] = np.mean(list(memory['outcomes']))
                memory['deviation'] = memory['current_ratio'] - self.target_ratio
                
                # Calculate stability (how close to target)
                memory['stability_score'] = max(0, 1 - abs(memory['deviation']) / 0.1)
    
    def get_triple_calibration_factor(self) -> float:
        """Get calibration factor from 3 fractal windows"""
        calibration_factors = []
        
        for i, window in enumerate(self.fractal_windows):
            memory = self.fractal_memories[window]
            
            if len(memory['outcomes']) >= window // 2:  # At least half full
                # Deviation-based calibration
                deviation_factor = memory['deviation']
                
                # Stability-weighted factor
                stability_weight = memory['stability_score']
                
                # Window-specific calibration
                window_calibration = deviation_factor * stability_weight * self.fractal_weights[i]
                calibration_factors.append(window_calibration)
        
        if not calibration_factors:
            return 0.0
        
        # Weighted average of fractal calibrations
        return np.sum(calibration_factors)
    
    def get_fractal_state(self) -> Dict:
        """Get state of all fractal windows"""
        state = {
            'global_ratio': self.global_ratio,
            'target_ratio': self.target_ratio,
            'total_samples': self.total_samples,
            'fractal_states': {}
        }
        
        for window in self.fractal_windows:
            memory = self.fractal_memories[window]
            state['fractal_states'][window] = {
                'current_ratio': memory['current_ratio'],
                'deviation': memory['deviation'],
                'stability_score': memory['stability_score'],
                'samples': len(memory['outcomes'])
            }
        
        return state

class EnhancedMemoryBranchLearner(MemoryEnhancedSingleBranchLearner):
    """Enhanced learner with triple fractal calibration"""
    
    def __init__(self, segment_length: int = 50, max_history: int = 1000, 
                 target_fractal_ratio: float = 0.2909):
        
        # Initialize base learner without fractal memory
        super().__init__(segment_length, max_history, 0.001, 'cpu', target_fractal_ratio)
        
        # Replace with triple fractal memory
        self.triple_fractal_memory = TripleFractalMemoryCell(
            fractal_windows=[500, 1000, 2000],
            target_ratio=target_fractal_ratio
        )
        
    def add_sample(self, normal_part: np.ndarray, candidate_part: np.ndarray, label: int) -> Dict:
        """Add sample with triple fractal calibration"""
        
        start_time = time.time()
        
        # Get base prediction from similarity computation
        similarities = self._compute_all_similarities(normal_part, candidate_part)
        base_prob = self._learn_label_probability(similarities, label)
        
        # Update triple fractal memory
        self.triple_fractal_memory.update_memory(label)
        
        # Get triple fractal calibration
        triple_calibration = self.triple_fractal_memory.get_triple_calibration_factor()
        
        # Apply triple fractal calibration
        calibrated_prob = self._apply_triple_calibration(base_prob, triple_calibration)
        
        # Store sample
        self.normal_parts.append(normal_part.copy())
        self.candidate_parts.append(candidate_part.copy())
        self.labels.append(label)
        self.sample_count += 1
        
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        
        return {
            'sample_id': self.sample_count,
            'label_probability': calibrated_prob,
            'base_probability': base_prob,
            'triple_calibration_factor': triple_calibration,
            'fractal_state': self.triple_fractal_memory.get_fractal_state(),
            'similarities': similarities,
            'processing_time': processing_time
        }
    
    def _apply_triple_calibration(self, base_prob: float, triple_calibration: float) -> float:
        """Apply triple fractal calibration"""
        
        # Apply calibration with bounds
        calibrated = base_prob + triple_calibration * 0.5  # Moderate calibration strength
        
        # Ensure probability bounds
        return np.clip(calibrated, 0.01, 0.99)

def load_real_data_for_enhanced_cv():
    """Load real data for enhanced CV with multiple classifiers"""
    
    print("📊 Loading Real Data for Enhanced Multi-Classifier CV")
    
    try:
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        if 'structural_breakpoint' in y_data.columns:
            labels = y_data['structural_breakpoint'].astype(int).values
        else:
            labels = y_data.iloc[:, 0].astype(int).values
        
        print(f"   Loaded: X={X_data.shape}, y={len(labels)}")
        
        # Create pairs
        normal_parts, candidate_parts, segment_labels = create_enhanced_cv_pairs(X_data, labels, max_samples=400)
        
        print(f"   Created {len(normal_parts)} pairs")
        print(f"   Label distribution: {dict(Counter(segment_labels))}")
        print(f"   Actual fractal ratio: {np.mean(segment_labels):.4f}")
        
        return normal_parts, candidate_parts, segment_labels
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None, None, None

def create_enhanced_cv_pairs(X_data, labels, max_samples=400, segment_length=25):
    """Create pairs for enhanced CV testing"""
    
    normal_parts = []
    candidate_parts = []
    segment_labels = []
    
    numeric_cols = X_data.select_dtypes(include=[np.number]).columns
    numeric_data = X_data[numeric_cols].values
    
    # Use time series approach to maintain temporal order
    if numeric_data.shape[1] > 0:
        time_series = numeric_data[:, 0]
        step_size = segment_length
        
        for i in range(0, len(time_series) - segment_length * 2, step_size):
            if len(normal_parts) >= max_samples:
                break
            
            normal_part = time_series[i:i + segment_length]
            candidate_part = time_series[i + segment_length:i + segment_length * 2]
            
            if (np.isfinite(normal_part).all() and np.isfinite(candidate_part).all() and
                np.std(normal_part) > 1e-6 and np.std(candidate_part) > 1e-6):
                
                normal_parts.append(normal_part.astype(np.float32))
                candidate_parts.append(candidate_part.astype(np.float32))
                
                mid_idx = min(i + segment_length, len(labels) - 1)
                segment_labels.append(labels[mid_idx])
    
    return np.array(normal_parts), np.array(candidate_parts), np.array(segment_labels)

def enhanced_multi_classifier_cv(normal_parts, candidate_parts, labels, n_splits=5):
    """Enhanced CV with multiple classifiers and triple fractal calibration"""
    
    print(f"\n🧠 Enhanced Multi-Classifier Walk-Forward {n_splits}-Fold CV")
    print("=" * 70)
    
    actual_fractal_ratio = np.mean(labels)
    print(f"Using fractal ratio: {actual_fractal_ratio:.4f}")
    
    tscv = TimeSeriesSplit(n_splits=n_splits)
    fold_results = []
    
    for fold_idx, (train_idx, val_idx) in enumerate(tscv.split(normal_parts)):
        print(f"\n📚 Fold {fold_idx + 1}/{n_splits}")
        
        # Split data
        train_normal = normal_parts[train_idx]
        train_candidate = candidate_parts[train_idx]
        train_labels = labels[train_idx]
        
        val_normal = normal_parts[val_idx]
        val_candidate = candidate_parts[val_idx]
        val_labels = labels[val_idx]
        
        print(f"   Train: {len(train_normal)}, Val: {len(val_normal)}")
        print(f"   Train labels: {dict(Counter(train_labels))}")
        print(f"   Val labels: {dict(Counter(val_labels))}")
        
        # Initialize all classifiers
        classifiers = {
            'enhanced_memory': EnhancedMemoryBranchLearner(
                segment_length=normal_parts.shape[1],
                max_history=300,
                target_fractal_ratio=actual_fractal_ratio
            ),
            'hybrid_branch': HybridBranchClassifier(
                max_active_branches=5,
                separate_concatenation=True
            ),
            'distributional': DistributionalBranchClassifier(
                single_branch=True,
                max_branches=5
            )
        }
        
        # Training phase
        print("   🔧 Training all classifiers...")
        
        for i, (normal, candidate, label) in enumerate(zip(train_normal, train_candidate, train_labels)):
            # Train enhanced memory learner
            classifiers['enhanced_memory'].add_sample(normal, candidate, label)
            
            # Train hybrid classifier
            classifiers['hybrid_branch'].process_two_parts(normal, candidate, timestamp=i)
            
            # Train distributional classifier
            combined_segment = np.concatenate([normal, candidate])
            classifiers['distributional'].process_new_segment(combined_segment, f'seg_{i}')
            
            if (i + 1) % 50 == 0:
                # Show enhanced memory state
                result = classifiers['enhanced_memory'].add_sample(normal, candidate, label)
                fractal_state = result['fractal_state']
                print(f"      Step {i+1}: Global ratio={fractal_state['global_ratio']:.3f}, "
                      f"Triple calibration={result['triple_calibration_factor']:.4f}")
        
        # Validation phase
        print("   🎯 Validating all classifiers...")
        
        classifier_results = {}
        
        for name, classifier in classifiers.items():
            val_predictions = []
            val_probabilities = []
            
            for normal, candidate in zip(val_normal, val_candidate):
                if name == 'enhanced_memory':
                    prob = classifier.predict_label_probability(normal, candidate)
                elif name == 'hybrid_branch':
                    result = classifier.process_two_parts(normal, candidate)
                    # Convert decision to probability
                    if 'STRUCTURAL_BREAK' in result['decision']:
                        prob = 0.8
                    elif 'NEW_BRANCH' in result['decision']:
                        prob = 0.6
                    else:
                        prob = 0.3
                else:  # distributional
                    combined_segment = np.concatenate([normal, candidate])
                    result = classifier.process_new_segment(combined_segment)
                    prob = result['confidence'] if 'NEW_BRANCH' in result['action'] else 0.4
                
                pred = 1 if prob > 0.5 else 0
                val_predictions.append(pred)
                val_probabilities.append(prob)
            
            # Calculate metrics
            accuracy = accuracy_score(val_labels, val_predictions)
            auc = roc_auc_score(val_labels, val_probabilities) if len(np.unique(val_labels)) > 1 else 0.5
            
            classifier_results[name] = {
                'accuracy': accuracy,
                'auc': auc,
                'predictions': val_predictions,
                'probabilities': val_probabilities
            }
            
            print(f"      {name:20s}: AUC={auc:.4f}, Accuracy={accuracy:.4f}")
        
        # Ensemble prediction
        ensemble_probs = []
        ensemble_weights = [0.5, 0.3, 0.2]  # Enhanced memory gets highest weight
        
        for i in range(len(val_labels)):
            weighted_prob = 0
            for j, (name, weight) in enumerate(zip(classifier_results.keys(), ensemble_weights)):
                weighted_prob += weight * classifier_results[name]['probabilities'][i]
            ensemble_probs.append(weighted_prob)
        
        ensemble_preds = [1 if p > 0.5 else 0 for p in ensemble_probs]
        ensemble_accuracy = accuracy_score(val_labels, ensemble_preds)
        ensemble_auc = roc_auc_score(val_labels, ensemble_probs) if len(np.unique(val_labels)) > 1 else 0.5
        
        print(f"      {'Ensemble':20s}: AUC={ensemble_auc:.4f}, Accuracy={ensemble_accuracy:.4f}")
        
        # Store fold results
        fold_result = {
            'fold': fold_idx + 1,
            'train_size': len(train_normal),
            'val_size': len(val_normal),
            'classifier_results': classifier_results,
            'ensemble_results': {
                'accuracy': ensemble_accuracy,
                'auc': ensemble_auc,
                'predictions': ensemble_preds,
                'probabilities': ensemble_probs
            },
            'val_labels': val_labels.tolist()
        }
        
        fold_results.append(fold_result)
    
    # Calculate overall metrics
    overall_results = {}
    
    # Individual classifier overall metrics
    for name in classifiers.keys():
        all_preds = []
        all_probs = []
        all_labels = []
        
        for result in fold_results:
            all_preds.extend(result['classifier_results'][name]['predictions'])
            all_probs.extend(result['classifier_results'][name]['probabilities'])
            all_labels.extend(result['val_labels'])
        
        overall_accuracy = accuracy_score(all_labels, all_preds)
        overall_auc = roc_auc_score(all_labels, all_probs) if len(np.unique(all_labels)) > 1 else 0.5
        
        overall_results[name] = {
            'overall_accuracy': overall_accuracy,
            'overall_auc': overall_auc,
            'mean_accuracy': np.mean([r['classifier_results'][name]['accuracy'] for r in fold_results]),
            'mean_auc': np.mean([r['classifier_results'][name]['auc'] for r in fold_results]),
            'std_auc': np.std([r['classifier_results'][name]['auc'] for r in fold_results])
        }
    
    # Ensemble overall metrics
    all_ensemble_preds = []
    all_ensemble_probs = []
    all_labels = []
    
    for result in fold_results:
        all_ensemble_preds.extend(result['ensemble_results']['predictions'])
        all_ensemble_probs.extend(result['ensemble_results']['probabilities'])
        all_labels.extend(result['val_labels'])
    
    overall_results['ensemble'] = {
        'overall_accuracy': accuracy_score(all_labels, all_ensemble_preds),
        'overall_auc': roc_auc_score(all_labels, all_ensemble_probs) if len(np.unique(all_labels)) > 1 else 0.5,
        'mean_accuracy': np.mean([r['ensemble_results']['accuracy'] for r in fold_results]),
        'mean_auc': np.mean([r['ensemble_results']['auc'] for r in fold_results]),
        'std_auc': np.std([r['ensemble_results']['auc'] for r in fold_results])
    }
    
    cv_results = {
        'fold_results': fold_results,
        'overall_results': overall_results,
        'target_fractal_ratio': actual_fractal_ratio,
        'n_splits': n_splits
    }
    
    return cv_results

def plot_enhanced_cv_results(cv_results):
    """Plot enhanced CV results with multiple classifiers"""
    
    print(f"\n📊 Creating Enhanced Multi-Classifier CV Plot...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('Enhanced Multi-Classifier CV Results with Triple Fractal Calibration', fontsize=14)
    
    fold_nums = [r['fold'] for r in cv_results['fold_results']]
    classifier_names = ['enhanced_memory', 'hybrid_branch', 'distributional', 'ensemble']
    colors = ['red', 'blue', 'green', 'purple']
    
    # 1. AUC comparison across classifiers
    for i, name in enumerate(classifier_names):
        if name == 'ensemble':
            aucs = [r['ensemble_results']['auc'] for r in cv_results['fold_results']]
        else:
            aucs = [r['classifier_results'][name]['auc'] for r in cv_results['fold_results']]
        
        axes[0, 0].plot(fold_nums, aucs, 'o-', color=colors[i], label=name.replace('_', ' ').title(), 
                       linewidth=2, markersize=6)
    
    axes[0, 0].axhline(y=0.5, color='gray', linestyle=':', alpha=0.5, label='Random')
    axes[0, 0].set_xlabel('Fold')
    axes[0, 0].set_ylabel('AUC')
    axes[0, 0].set_title('AUC Comparison Across Classifiers')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_ylim(0, 1)
    
    # 2. Overall AUC comparison
    overall_aucs = []
    classifier_labels = []
    
    for name in classifier_names:
        overall_aucs.append(cv_results['overall_results'][name]['overall_auc'])
        classifier_labels.append(name.replace('_', ' ').title())
    
    bars = axes[0, 1].bar(classifier_labels, overall_aucs, color=colors, alpha=0.7)
    axes[0, 1].set_ylabel('Overall AUC')
    axes[0, 1].set_title('Overall AUC Comparison')
    axes[0, 1].set_ylim(0, 1)
    axes[0, 1].grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, auc in zip(bars, overall_aucs):
        axes[0, 1].text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                       f'{auc:.3f}', ha='center', va='bottom')
    
    # 3. Accuracy comparison
    for i, name in enumerate(classifier_names):
        if name == 'ensemble':
            accuracies = [r['ensemble_results']['accuracy'] for r in cv_results['fold_results']]
        else:
            accuracies = [r['classifier_results'][name]['accuracy'] for r in cv_results['fold_results']]
        
        axes[0, 2].plot(fold_nums, accuracies, 'o-', color=colors[i], label=name.replace('_', ' ').title(),
                       linewidth=2, markersize=6)
    
    axes[0, 2].set_xlabel('Fold')
    axes[0, 2].set_ylabel('Accuracy')
    axes[0, 2].set_title('Accuracy Comparison Across Classifiers')
    axes[0, 2].legend()
    axes[0, 2].grid(True, alpha=0.3)
    axes[0, 2].set_ylim(0, 1)
    
    # 4. ROC Curves comparison
    all_labels = []
    classifier_probs = {name: [] for name in classifier_names}
    
    for result in cv_results['fold_results']:
        all_labels.extend(result['val_labels'])
        
        for name in classifier_names:
            if name == 'ensemble':
                classifier_probs[name].extend(result['ensemble_results']['probabilities'])
            else:
                classifier_probs[name].extend(result['classifier_results'][name]['probabilities'])
    
    if len(np.unique(all_labels)) > 1:
        from sklearn.metrics import roc_curve
        
        for i, name in enumerate(classifier_names):
            fpr, tpr, _ = roc_curve(all_labels, classifier_probs[name])
            auc = cv_results['overall_results'][name]['overall_auc']
            axes[1, 0].plot(fpr, tpr, color=colors[i], linewidth=2, 
                           label=f'{name.replace("_", " ").title()} (AUC={auc:.3f})')
        
        axes[1, 0].plot([0, 1], [0, 1], 'k--', alpha=0.5, label='Random')
        axes[1, 0].set_xlabel('False Positive Rate')
        axes[1, 0].set_ylabel('True Positive Rate')
        axes[1, 0].set_title('ROC Curves Comparison')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
    
    # 5. AUC standard deviation (stability)
    std_aucs = []
    for name in classifier_names:
        std_aucs.append(cv_results['overall_results'][name]['std_auc'])
    
    axes[1, 1].bar(classifier_labels, std_aucs, color=colors, alpha=0.7)
    axes[1, 1].set_ylabel('AUC Standard Deviation')
    axes[1, 1].set_title('AUC Stability (Lower = More Stable)')
    axes[1, 1].grid(True, alpha=0.3)
    
    # 6. Performance improvement over baseline
    baseline_auc = cv_results['overall_results']['hybrid_branch']['overall_auc']  # Use hybrid as baseline
    improvements = []
    
    for name in classifier_names:
        improvement = cv_results['overall_results'][name]['overall_auc'] - baseline_auc
        improvements.append(improvement)
    
    colors_improvement = ['green' if imp >= 0 else 'red' for imp in improvements]
    axes[1, 2].bar(classifier_labels, improvements, color=colors_improvement, alpha=0.7)
    axes[1, 2].axhline(y=0, color='black', linestyle='-', alpha=0.5)
    axes[1, 2].set_ylabel('AUC Improvement vs Hybrid Baseline')
    axes[1, 2].set_title('Performance Improvement Analysis')
    axes[1, 2].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('enhanced_multi_classifier_cv_results.png', dpi=300, bbox_inches='tight')
    print("   ✅ Plot saved to 'enhanced_multi_classifier_cv_results.png'")

def print_enhanced_cv_summary(cv_results):
    """Print comprehensive enhanced CV summary"""
    
    print(f"\n🏆 ENHANCED MULTI-CLASSIFIER CV SUMMARY")
    print("=" * 60)
    
    print(f"📊 Overall Performance Comparison:")
    print("   Classifier           | Overall AUC | Mean AUC | Std AUC | Overall Acc")
    print("   --------------------|-------------|----------|---------|-------------")
    
    for name in ['enhanced_memory', 'hybrid_branch', 'distributional', 'ensemble']:
        results = cv_results['overall_results'][name]
        display_name = name.replace('_', ' ').title()
        print(f"   {display_name:19s} |   {results['overall_auc']:.4f}    | {results['mean_auc']:.4f}   | {results['std_auc']:.4f}  |   {results['overall_accuracy']:.4f}")
    
    print(f"\n🧠 Triple Fractal Memory Performance:")
    enhanced_results = cv_results['overall_results']['enhanced_memory']
    print(f"   Enhanced Memory AUC: {enhanced_results['overall_auc']:.4f}")
    print(f"   Stability (Std AUC): {enhanced_results['std_auc']:.4f}")
    
    print(f"\n🎯 Best Performer Analysis:")
    best_classifier = max(cv_results['overall_results'].keys(), 
                         key=lambda k: cv_results['overall_results'][k]['overall_auc'])
    best_auc = cv_results['overall_results'][best_classifier]['overall_auc']
    
    print(f"   Best Classifier: {best_classifier.replace('_', ' ').title()}")
    print(f"   Best AUC: {best_auc:.4f}")
    
    # Performance ranking
    print(f"\n📈 Performance Ranking:")
    sorted_classifiers = sorted(cv_results['overall_results'].items(), 
                               key=lambda x: x[1]['overall_auc'], reverse=True)
    
    for i, (name, results) in enumerate(sorted_classifiers):
        print(f"   {i+1}. {name.replace('_', ' ').title():20s}: {results['overall_auc']:.4f} AUC")
    
    # Improvement analysis
    baseline_auc = cv_results['overall_results']['hybrid_branch']['overall_auc']
    enhanced_auc = cv_results['overall_results']['enhanced_memory']['overall_auc']
    improvement = enhanced_auc - baseline_auc
    
    print(f"\n🚀 Triple Fractal Enhancement:")
    if improvement > 0.01:
        print(f"   ✅ SIGNIFICANT improvement: +{improvement:.4f} AUC over baseline")
    elif improvement > 0:
        print(f"   ⚠️  MODERATE improvement: +{improvement:.4f} AUC over baseline")
    else:
        print(f"   ❌ NO improvement: {improvement:+.4f} AUC vs baseline")

def main():
    """Main function"""
    
    print("🧠 Enhanced Multi-Classifier CV Test with Triple Fractal Calibration")
    print("=" * 80)
    print("🎯 Testing Enhanced Memory + Hybrid + Distributional classifiers")
    print("🔬 Using optimal fractal windows: 500, 1000, 2000")
    print("=" * 80)
    
    # Load data
    normal_parts, candidate_parts, labels = load_real_data_for_enhanced_cv()
    
    if normal_parts is None:
        print("❌ Failed to load data")
        return
    
    # Run enhanced multi-classifier CV
    cv_results = enhanced_multi_classifier_cv(normal_parts, candidate_parts, labels, n_splits=5)
    
    # Print summary
    print_enhanced_cv_summary(cv_results)
    
    # Plot results
    plot_enhanced_cv_results(cv_results)
    
    # Save results
    simple_results = {
        'enhanced_memory_auc': float(cv_results['overall_results']['enhanced_memory']['overall_auc']),
        'hybrid_branch_auc': float(cv_results['overall_results']['hybrid_branch']['overall_auc']),
        'distributional_auc': float(cv_results['overall_results']['distributional']['overall_auc']),
        'ensemble_auc': float(cv_results['overall_results']['ensemble']['overall_auc']),
        'best_classifier': max(cv_results['overall_results'].keys(), 
                              key=lambda k: cv_results['overall_results'][k]['overall_auc']),
        'target_fractal_ratio': float(cv_results['target_fractal_ratio'])
    }
    
    with open('enhanced_multi_classifier_cv_results.json', 'w') as f:
        json.dump(simple_results, f, indent=2)
    
    print(f"\n🎉 Enhanced Multi-Classifier CV Complete!")
    print(f"   Best Performer: {simple_results['best_classifier'].replace('_', ' ').title()}")
    print(f"   Best AUC: {max(simple_results[k] for k in simple_results if k.endswith('_auc')):.4f}")
    print(f"   💾 Results saved to 'enhanced_multi_classifier_cv_results.json'")

if __name__ == "__main__":
    main()