#!/usr/bin/env python3
"""
Direct Granite-TSFM for Structural Break Detection
Using TSPulse directly for classification without feature extraction
"""

import numpy as np
import pandas as pd
import sys
import os
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
import logging
import warnings
import joblib

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Add granite-tsfm to path
granite_path = os.path.join(os.getcwd(), 'granite-tsfm')
if os.path.exists(granite_path):
    sys.path.append(os.path.join(granite_path, 'tsfm_public'))

# Import Granite-TSFM components
try:
    from tsfm_public.models.tspulse import TSPulseForClassification
    import torch
    GRANITE_AVAILABLE = True
    logger.info("✅ Granite-TSFM modules imported successfully")
except ImportError as e:
    GRANITE_AVAILABLE = False
    logger.error(f"❌ Granite-TSFM not available: {e}")
    logger.info("💡 Install with: cd granite-tsfm && pip install '.[notebooks]'")
    sys.exit(1)

def load_raw_time_series():
    """Load raw time series data and labels"""
    logger.info("📊 Loading raw time series data...")
    
    try:
        X = pd.read_parquet('X_train.parquet')
        y = pd.read_parquet('y_train.parquet')
        
        if isinstance(y.index, pd.MultiIndex):
            y = y.reset_index(drop=True).iloc[:, 0] if y.shape[1] == 1 else y.reset_index(drop=True)
        else:
            y = y.iloc[:, 0] if y.shape[1] == 1 else y.squeeze()
        
        min_len = min(len(X), len(y))
        X = X.iloc[:min_len]
        y = y.iloc[:min_len] if hasattr(y, 'iloc') else y[:min_len]
        
        logger.info(f"   ✅ Raw time series: {X.shape[0]} samples, {X.shape[1]} time points")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    except Exception as e:
        logger.error(f"❌ Error loading raw time series data: {e}")
        raise

class GraniteStructuralBreakDetector:
    """TSPulse-based structural break detection using prediction discrepancies"""
    
    def __init__(self, normal_length=256, candidate_length=256, device='cpu'):
        self.device = device
        self.normal_length = normal_length
        self.candidate_length = candidate_length
        self.tspulse_model = None
        self.discrepancy_classifier = None
        
    def initialize_models(self):
        """Initialize TSPulse forecasting model and discrepancy classifier"""
        logger.info("🔧 Initializing TSPulse forecasting model...")
        
        try:
            # Use TSPulse for forecasting/reconstruction
            from tsfm_public.models.tspulse import TSPulseForReconstruction
            self.tspulse_model = TSPulseForReconstruction.from_pretrained(
                "ibm-granite/granite-timeseries-tspulse-r1",
                revision="tspulse-hybrid-dualhead-512-p8-r1"  # For imputation/forecasting
            )
            self.tspulse_model.eval()
            logger.info("   ✅ TSPulse forecasting model loaded successfully")
            
            # Simple classifier for discrepancies
            from sklearn.ensemble import RandomForestClassifier
            self.discrepancy_classifier = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                class_weight='balanced',
                random_state=42
            )
            logger.info("   ✅ Discrepancy classifier initialized")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize models: {e}")
            raise
    
    def split_time_series(self, time_series):
        """Split time series into normal and candidate parts"""
        total_length = len(time_series)
        
        # Split roughly in half
        normal_end = total_length // 2
        
        normal_part = time_series[:normal_end]
        candidate_part = time_series[normal_end:]
        
        return normal_part, candidate_part
    
    def prepare_tspulse_input(self, normal_part):
        """Prepare normal part for TSPulse forecasting"""
        # TSPulse expects exactly 512 sequence length
        if len(normal_part) > 512:
            # Take the last 512 points (most recent history)
            normal_part = normal_part[-512:]
        elif len(normal_part) < 512:
            # Pad to 512 points
            normal_part = np.pad(normal_part, (512 - len(normal_part), 0), mode='edge')
        
        # Reshape for TSPulse: (batch_size, sequence_length, num_features)
        return torch.tensor(normal_part.reshape(1, 512, 1), dtype=torch.float32)
    
    def predict_candidate_with_tspulse(self, normal_part, candidate_length):
        """Use TSPulse to predict/reconstruct the candidate part"""
        try:
            # Prepare input
            ts_input = self.prepare_tspulse_input(normal_part)
            
            # Get TSPulse reconstruction/prediction
            with torch.no_grad():
                outputs = self.tspulse_model(ts_input)
                
                # Extract reconstruction from TSPulse output
                # TSPulse outputs have specific structure - try direct access
                try:
                    if hasattr(outputs, 'reconstruction'):
                        recon = outputs.reconstruction
                        if torch.is_tensor(recon):
                            prediction = recon.detach().cpu().numpy().flatten()
                        else:
                            # Try to access the tensor inside the reconstruction object
                            prediction = recon.detach().cpu().numpy().flatten()
                    else:
                        # Fallback: use input as prediction
                        prediction = ts_input.squeeze().numpy()
                except:
                    # Final fallback: use input
                    prediction = ts_input.squeeze().numpy()
                
                # Adjust prediction length to match candidate
                if len(prediction) > candidate_length:
                    prediction = prediction[:candidate_length]
                elif len(prediction) < candidate_length:
                    # Extend prediction by repeating last values
                    prediction = np.pad(prediction, (0, candidate_length - len(prediction)), mode='edge')
                
                return prediction
                
        except Exception as e:
            logger.warning(f"   ⚠️ TSPulse prediction failed: {e}")
            # Fallback: return zeros
            return np.zeros(candidate_length)
    
    def extract_discrepancy_features(self, true_candidate, predicted_candidate):
        """Extract features from discrepancies between true and predicted candidate parts"""
        if len(true_candidate) != len(predicted_candidate):
            min_len = min(len(true_candidate), len(predicted_candidate))
            true_candidate = true_candidate[:min_len]
            predicted_candidate = predicted_candidate[:min_len]
        
        # Calculate discrepancies
        discrepancy = true_candidate - predicted_candidate
        abs_discrepancy = np.abs(discrepancy)
        
        # Extract comprehensive discrepancy features
        features = [
            # Basic discrepancy statistics
            np.mean(discrepancy),
            np.std(discrepancy),
            np.max(discrepancy),
            np.min(discrepancy),
            np.median(discrepancy),
            
            # Absolute discrepancy statistics
            np.mean(abs_discrepancy),
            np.std(abs_discrepancy),
            np.max(abs_discrepancy),
            np.median(abs_discrepancy),
            
            # Percentiles
            np.percentile(abs_discrepancy, 75),
            np.percentile(abs_discrepancy, 90),
            np.percentile(abs_discrepancy, 95),
            
            # Relative errors
            np.mean(abs_discrepancy / (np.abs(true_candidate) + 1e-8)),
            np.std(abs_discrepancy / (np.abs(true_candidate) + 1e-8)),
            
            # Pattern-based features
            np.sum(abs_discrepancy > np.mean(abs_discrepancy)),
            np.sum(abs_discrepancy > np.mean(abs_discrepancy) + np.std(abs_discrepancy)),
            np.sum(abs_discrepancy > 2 * np.std(abs_discrepancy)),
            
            # Trend discrepancies
            np.sum(np.diff(discrepancy) > 0) if len(discrepancy) > 1 else 0,
            np.sum(np.diff(discrepancy) < 0) if len(discrepancy) > 1 else 0,
            
            # Energy measures
            np.sum(discrepancy ** 2),
            np.sum(abs_discrepancy)
        ]
        
        return np.array(features)
    
    def process_sample(self, time_series):
        """Process a single time series sample to extract discrepancy features"""
        try:
            # Split into normal and candidate parts
            normal_part, candidate_part = self.split_time_series(time_series)
            
            # Use TSPulse to predict candidate part based on normal part
            predicted_candidate = self.predict_candidate_with_tspulse(normal_part, len(candidate_part))
            
            # Extract discrepancy features
            discrepancy_features = self.extract_discrepancy_features(candidate_part, predicted_candidate)
            
            return discrepancy_features
            
        except Exception as e:
            logger.warning(f"   ⚠️ Sample processing failed: {e}")
            # Return zero features as fallback
            return np.zeros(21)  # Match the number of features in extract_discrepancy_features
    
    def train_model(self, X_train, y_train, epochs=10, lr=1e-4, batch_size=16):
        """Train the classification head on structural break data"""
        logger.info(f"🎯 Training TSPulse classification head for {epochs} epochs...")
        
        # Setup optimizer and loss
        optimizer = torch.optim.AdamW(self.model.parameters(), lr=lr, weight_decay=0.01)
        criterion = torch.nn.CrossEntropyLoss(weight=torch.tensor([1.0, 2.0]))  # Weight for imbalanced classes
        
        self.model.train()
        
        for epoch in range(epochs):
            total_loss = 0
            correct = 0
            total = 0
            
            # Process in batches
            indices = np.random.permutation(len(X_train))
            
            for batch_start in range(0, len(X_train), batch_size):
                batch_end = min(batch_start + batch_size, len(X_train))
                batch_indices = indices[batch_start:batch_end]
                
                # Prepare batch
                batch_inputs = []
                batch_labels = []
                
                for idx in batch_indices:
                    ts_input = self.prepare_input(X_train.iloc[idx].values)
                    batch_inputs.append(ts_input.squeeze(0))  # Remove batch dim
                    batch_labels.append(int(y_train.iloc[idx]))
                
                # Stack into batch tensors
                batch_inputs = torch.stack(batch_inputs)
                batch_labels = torch.tensor(batch_labels, dtype=torch.long)
                
                # Forward pass
                optimizer.zero_grad()
                outputs = self.model(batch_inputs)
                loss = criterion(outputs.logits, batch_labels)
                
                # Backward pass
                loss.backward()
                optimizer.step()
                
                # Statistics
                total_loss += loss.item()
                _, predicted = torch.max(outputs.logits.data, 1)
                total += batch_labels.size(0)
                correct += (predicted == batch_labels).sum().item()
            
            accuracy = 100 * correct / total
            avg_loss = total_loss / (len(X_train) // batch_size + 1)
            
            if epoch % 2 == 0:
                logger.info(f"     Epoch {epoch+1}/{epochs}: Loss={avg_loss:.4f}, Acc={accuracy:.2f}%")
        
        self.model.eval()
        logger.info("   ✅ Training completed")
    
    def fit_and_predict_cv(self, X, y, n_folds=5):
        """TSPulse discrepancy-based classification with cross-validation"""
        logger.info("🎯 TSPulse discrepancy-based structural break detection with 5-fold CV...")
        
        # Cross-validation
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        oof_predictions = np.zeros(len(X))
        cv_scores = []
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
            logger.info(f"   Fold {fold + 1}/{n_folds}...")
            
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # Extract discrepancy features for training set
            logger.info(f"     Extracting discrepancy features for {len(X_train)} training samples...")
            train_features = []
            for i in range(len(X_train)):
                features = self.process_sample(X_train.iloc[i].values)
                train_features.append(features)
                if (i + 1) % 100 == 0:
                    logger.info(f"       Processed {i + 1}/{len(X_train)} training samples")
            
            train_features = np.array(train_features)
            
            # Train discrepancy classifier
            logger.info("     Training discrepancy classifier...")
            self.discrepancy_classifier.fit(train_features, y_train)
            
            # Extract discrepancy features for validation set
            logger.info(f"     Extracting discrepancy features for {len(X_val)} validation samples...")
            val_features = []
            for i in range(len(X_val)):
                features = self.process_sample(X_val.iloc[i].values)
                val_features.append(features)
                if (i + 1) % 100 == 0:
                    logger.info(f"       Processed {i + 1}/{len(X_val)} validation samples")
            
            val_features = np.array(val_features)
            
            # Predict on validation set
            val_predictions = self.discrepancy_classifier.predict_proba(val_features)[:, 1]
            oof_predictions[val_idx] = val_predictions
            
            # Calculate AUC
            auc = roc_auc_score(y_val, val_predictions)
            cv_scores.append(auc)
            logger.info(f"     Fold {fold + 1} AUC: {auc:.4f}")
        
        # Overall performance
        overall_auc = roc_auc_score(y, oof_predictions)
        mean_cv_auc = np.mean(cv_scores)
        std_cv_auc = np.std(cv_scores)
        
        logger.info(f"\n📊 Cross-Validation Results:")
        logger.info(f"   Mean CV AUC: {mean_cv_auc:.4f} ± {std_cv_auc:.4f}")
        logger.info(f"   Overall AUC: {overall_auc:.4f}")
        
        return {
            'oof_predictions': oof_predictions,
            'cv_scores': cv_scores,
            'mean_cv_auc': mean_cv_auc,
            'overall_auc': overall_auc
        }

def calculate_comprehensive_metrics(y_true, y_pred_proba, threshold=0.5):
    """Calculate comprehensive classification metrics"""
    y_pred_binary = (y_pred_proba >= threshold).astype(int)
    
    return {
        'auc': roc_auc_score(y_true, y_pred_proba),
        'f1': f1_score(y_true, y_pred_binary),
        'precision': precision_score(y_true, y_pred_binary, zero_division=0),
        'recall': recall_score(y_true, y_pred_binary),
        'accuracy': accuracy_score(y_true, y_pred_binary)
    }

# Main execution
if __name__ == "__main__":
    logger.info("🚀 Starting TSPulse Discrepancy-Based Structural Break Detection...")
    
    # Load data
    X, y = load_raw_time_series()
    
    # Initialize Granite detector
    detector = GraniteStructuralBreakDetector(device='cpu')
    detector.initialize_models()
    
    # Discrepancy-based classification
    results = detector.fit_and_predict_cv(X, y, n_folds=5)
    
    # Calculate comprehensive metrics
    metrics = calculate_comprehensive_metrics(y, results['oof_predictions'])
    
    # Print results
    logger.info("\n🎉 TSPULSE DISCREPANCY-BASED DETECTION COMPLETED!")
    logger.info("=" * 60)
    logger.info("📊 FINAL RESULTS:")
    logger.info(f"   AUC:        {metrics['auc']:.4f}")
    logger.info(f"   F1 Score:   {metrics['f1']:.4f}")
    logger.info(f"   Precision:  {metrics['precision']:.4f}")
    logger.info(f"   Recall:     {metrics['recall']:.4f}")
    logger.info(f"   Accuracy:   {metrics['accuracy']:.4f}")
    
    # Save results
    final_results = {
        'cv_results': results,
        'metrics': metrics
    }
    
    joblib.dump(final_results, 'granite_tsfm_results.joblib')
    logger.info("💾 Results saved to 'granite_tsfm_results.joblib'")