#!/usr/bin/env python3
"""
Pattern-Based Ensemble Pipeline for Structural Break Detection
Divides time series into volatility patterns and trains separate XGBoost models
with Optuna hyperparameter optimization, 5-fold stratified CV, and ensemble prediction
"""

import numpy as np
import pandas as pd
import xgboost as xgb
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import seaborn as sns
from collections import defaultdict
import warnings
import time
import joblib
from typing import Dict, List, Tuple, Optional
import logging

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PatternBasedEnsemble:
    """
    Pattern-based ensemble classifier using separate XGBoost models for each volatility pattern
    """
    
    def __init__(self, 
                 n_folds: int = 5,
                 n_trials: int = 1,
                 early_stopping_rounds: int = 50,
                 random_state: int = 42,
                 n_jobs: int = -1):
        """
        Initialize the pattern-based ensemble
        
        Args:
            n_folds: Number of CV folds
            n_trials: Number of Optuna trials per pattern
            early_stopping_rounds: Early stopping rounds for XGBoost
            random_state: Random state for reproducibility
            n_jobs: Number of parallel jobs
        """
        self.n_folds = n_folds
        self.n_trials = n_trials
        self.early_stopping_rounds = early_stopping_rounds
        self.random_state = random_state
        self.n_jobs = n_jobs
        
        # Storage for models and results
        self.pattern_models = {}
        self.pattern_scalers = {}
        self.pattern_studies = {}
        self.cv_results = {}
        self.ensemble_weights = {}
        
        # Pattern mapping
        self.pattern_mapping = None
        
        logger.info(f"🚀 Pattern-Based Ensemble initialized")
        logger.info(f"   - CV folds: {n_folds}")
        logger.info(f"   - Optuna trials: {n_trials}")
        logger.info(f"   - Early stopping: {early_stopping_rounds}")
    
    def load_data(self, 
                  features_path: str,
                  labels_path: str,
                  volatility_path: str) -> Tuple[pd.DataFrame, np.ndarray, pd.DataFrame]:
        """
        Load preprocessed features, labels, and volatility patterns
        
        Args:
            features_path: Path to prepared features parquet
            labels_path: Path to labels parquet
            volatility_path: Path to volatility analysis CSV
            
        Returns:
            Tuple of (features_df, labels, volatility_df)
        """
        logger.info("📊 Loading data...")
        
        # Load features
        features_df = pd.read_parquet(features_path)
        logger.info(f"   ✅ Features: {features_df.shape}")
        
        # Load labels
        labels_df = pd.read_parquet(labels_path)
        labels = labels_df.iloc[:, 0].values.astype(int)
        logger.info(f"   ✅ Labels: {len(labels)} samples")
        logger.info(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        
        # Load volatility patterns
        volatility_df = pd.read_csv(volatility_path)
        logger.info(f"   ✅ Volatility patterns: {volatility_df.shape}")
        
        # Verify alignment
        assert len(features_df) == len(labels) == len(volatility_df), "Data length mismatch"
        
        # Pattern distribution
        pattern_counts = volatility_df['pattern_type'].value_counts()
        logger.info(f"   📊 Pattern distribution:")
        for pattern, count in pattern_counts.items():
            logger.info(f"      {pattern}: {count} ({count/len(volatility_df)*100:.1f}%)")
        
        return features_df, labels, volatility_df
    
    def create_pattern_groups(self, 
                            features_df: pd.DataFrame, 
                            labels: np.ndarray, 
                            volatility_df: pd.DataFrame) -> Dict[str, Dict]:
        """
        Create pattern-based groups for training
        
        Args:
            features_df: Features DataFrame
            labels: Labels array
            volatility_df: Volatility patterns DataFrame
            
        Returns:
            Dictionary with pattern groups
        """
        logger.info("🔄 Creating pattern groups...")
        
        pattern_groups = {}
        
        for pattern in volatility_df['pattern_type'].unique():
            pattern_mask = volatility_df['pattern_type'] == pattern
            pattern_indices = volatility_df[pattern_mask].index
            
            # Extract features and labels for this pattern
            pattern_features = features_df.iloc[pattern_indices]
            pattern_labels = labels[pattern_indices]
            
            # Check if we have both classes
            unique_labels = np.unique(pattern_labels)
            if len(unique_labels) < 2:
                logger.warning(f"   ⚠️  Pattern '{pattern}' has only one class, skipping")
                continue
            
            # Store pattern data
            pattern_groups[pattern] = {
                'features': pattern_features,
                'labels': pattern_labels,
                'indices': pattern_indices,
                'size': len(pattern_indices),
                'label_dist': pd.Series(pattern_labels).value_counts().to_dict()
            }
            
            logger.info(f"   ✅ Pattern '{pattern}': {len(pattern_indices)} samples")
            logger.info(f"      Label distribution: {pattern_groups[pattern]['label_dist']}")
        
        self.pattern_mapping = pattern_groups
        return pattern_groups
    
    def optimize_hyperparameters(self, 
                                X: pd.DataFrame, 
                                y: np.ndarray, 
                                pattern: str) -> Dict:
        """
        Optimize hyperparameters for a specific pattern using Optuna
        
        Args:
            X: Features for the pattern
            y: Labels for the pattern
            pattern: Pattern name
            
        Returns:
            Best hyperparameters
        """
        logger.info(f"🔧 Optimizing hyperparameters for pattern '{pattern}'...")
        
        def objective(trial):
            # Suggest hyperparameters
            params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'tree_method': 'hist',
                'device': 'cuda',
                'booster': 'gbtree',
                'random_state': self.random_state,
                'verbosity': 0,
                'early_stopping_rounds': 50,
                # Optuna suggestions
                'n_estimators': trial.suggest_int('n_estimators', 800, 3000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3, log=True),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 1e-8, 10.0, log=True),
                'reg_lambda': trial.suggest_float('reg_lambda', 1e-8, 10.0, log=True),
                'min_child_weight': trial.suggest_int('min_child_weight', 1, 10),
                'gamma': trial.suggest_float('gamma', 1e-8, 10.0, log=True)
            }
            
            # Cross-validation
            skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
            cv_scores = []
            best_iterations = []
            
            for train_idx, val_idx in skf.split(X, y):
                X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                # Scale features
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_val_scaled = scaler.transform(X_val)
                
                # Train model (simplified without early stopping for now)
                model = xgb.XGBClassifier(**params)
                model.fit(
                    X_train_scaled, y_train,
                    eval_set=[(X_val_scaled, y_val)],
                    verbose=False
                )
                
                best_iteration = model.best_iteration #params.get('n_estimators', 100)
                best_iterations.append(best_iteration)
                
                # Predict and score
                y_pred_proba = model.predict_proba(X_val_scaled)[:, 1]
                score = roc_auc_score(y_val, y_pred_proba)
                cv_scores.append(score)
            
            # Store best iterations for this trial
            trial.set_user_attr('best_iterations', best_iterations)
            trial.set_user_attr('max_best_iteration', max(best_iterations))
            trial.set_user_attr('mean_best_iteration', np.mean(best_iterations))
            
            return np.mean(cv_scores)
        
        # Create and run study
        study = optuna.create_study(
            direction='maximize',
            sampler=optuna.samplers.TPESampler(seed=self.random_state)
        )
        
        study.optimize(objective, n_trials=self.n_trials, show_progress_bar=False)
        
        logger.info(f"   ✅ Best AUC for '{pattern}': {study.best_value:.4f}")
        logger.info(f"   📊 Best params: {study.best_params}")
        
        # Get optimal n_estimators from best trial
        best_trial = study.best_trial
        optimal_n_estimators = best_trial.user_attrs.get('max_best_iteration')#, best_trial.params.get('n_estimators', 100))
        
        logger.info(f"   🎯 Optimal n_estimators from Optuna: {optimal_n_estimators}")
        
        # Store study for analysis
        self.pattern_studies[pattern] = study
        
        # Return best params with optimal n_estimators and CV AUC
        best_params_with_optimal = study.best_params.copy()
        best_params_with_optimal['optimal_n_estimators'] = optimal_n_estimators
        best_params_with_optimal['cv_auc'] = study.best_value  # Add the actual CV AUC
        
        return best_params_with_optimal
    
    def train_pattern_model(self, 
                          X: pd.DataFrame, 
                          y: np.ndarray, 
                          pattern: str, 
                          best_params: Dict) -> Dict:
        """
        Train final model for a pattern on full dataset using Optuna-determined optimal n_estimators
        
        Args:
            X: Features for the pattern
            y: Labels for the pattern
            pattern: Pattern name
            best_params: Best hyperparameters from optimization (includes optimal_n_estimators)
            
        Returns:
            Training results dictionary
        """
        logger.info(f"🎯 Training final model for pattern '{pattern}' on full dataset...")
        
        # Get optimal n_estimators from Optuna study
        optimal_n_estimators = best_params.get('optimal_n_estimators')#, best_params.get('n_estimators', 100))
        
        # Scale features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Prepare XGBoost parameters with optimal n_estimators
        xgb_params = {
            'objective': 'binary:logistic',
            'eval_metric': 'auc',
            'booster': 'gbtree',
            'random_state': self.random_state,
            'verbosity': 0,        
            'tree_method': 'hist',
            'device': 'cuda',
            'n_estimators': optimal_n_estimators,  # Use Optuna's optimal value
            **{k: v for k, v in best_params.items() if k != 'optimal_n_estimators'}
        }
        
        # Train model on full dataset
        model = xgb.XGBClassifier(**xgb_params)
        model.fit(X_scaled, y, verbose=False)
        
        # Use CV validation AUC from Optuna (realistic performance)
        cv_auc = best_params.get('cv_auc', 0.5)  # Get the actual CV validation score
        
        # Summary statistics
        results_summary = {
            'pattern': pattern,
            'n_samples': len(y),
            'n_features': X.shape[1],
            'label_distribution': pd.Series(y).value_counts().to_dict(),
            'train_auc': cv_auc,  # Use CV AUC instead of inflated training AUC
            'optimal_n_estimators': optimal_n_estimators,
            'model': model,
            'scaler': scaler,
            'feature_importance': model.feature_importances_,
            'best_params': best_params
        }
        
        logger.info(f"   ✅ Pattern '{pattern}' training completed:")
        logger.info(f"      CV AUC: {cv_auc:.4f}")
        logger.info(f"      Optimal n_estimators: {optimal_n_estimators}")
        logger.info(f"      Features: {X.shape[1]}")
        
        return results_summary
    
    def train_ensemble(self, 
                      features_df: pd.DataFrame, 
                      labels: np.ndarray, 
                      volatility_df: pd.DataFrame) -> Dict:
        """
        Train the complete ensemble of pattern-based models
        
        Args:
            features_df: Features DataFrame
            labels: Labels array
            volatility_df: Volatility patterns DataFrame
            
        Returns:
            Complete training results
        """
        logger.info("🚀 Training pattern-based ensemble...")
        
        # Create pattern groups
        pattern_groups = self.create_pattern_groups(features_df, labels, volatility_df)
        
        # Train models for each pattern
        ensemble_results = {}
        
        for pattern, group_data in pattern_groups.items():
            logger.info(f"\n🎯 Processing pattern: {pattern}")
            
            X = group_data['features']
            y = group_data['labels']
            
            # Skip if insufficient samples
            if len(y) < 50:
                logger.warning(f"   ⚠️  Skipping pattern '{pattern}' - insufficient samples ({len(y)})")
                continue
            
            # Optimize hyperparameters
            best_params = self.optimize_hyperparameters(X, y, pattern)
            
            # Train final model
            pattern_results = self.train_pattern_model(X, y, pattern, best_params)
            
            # Store results
            ensemble_results[pattern] = pattern_results
            self.cv_results[pattern] = pattern_results
        
        # Calculate ensemble weights based on validation performance
        self.calculate_ensemble_weights(ensemble_results)
        
        # Train final models on full data
        self.train_final_models(pattern_groups, ensemble_results)
        
        logger.info("✅ Ensemble training completed!")
        
        return ensemble_results
    
    def calculate_ensemble_weights(self, ensemble_results: Dict):
        """
        Calculate ensemble weights based on training performance
        
        Args:
            ensemble_results: Results from pattern model training
        """
        logger.info("⚖️  Calculating ensemble weights...")
        
        # Extract training AUC scores
        pattern_aucs = {}
        for pattern, results in ensemble_results.items():
            pattern_aucs[pattern] = results['train_auc']
        
        # Calculate weights (higher AUC = higher weight)
        total_auc = sum(pattern_aucs.values())
        
        for pattern, auc in pattern_aucs.items():
            weight = auc / total_auc
            self.ensemble_weights[pattern] = weight
            logger.info(f"   {pattern}: AUC={auc:.4f}, Weight={weight:.4f}")
    
    def train_final_models(self, pattern_groups: Dict, ensemble_results: Dict):
        """
        Train final models on full pattern data for deployment using optimal n_estimators
        
        Args:
            pattern_groups: Pattern group data
            ensemble_results: Training results with best parameters and optimal n_estimators
        """
        logger.info("🏁 Training final models on full data...")
        
        for pattern, group_data in pattern_groups.items():
            if pattern not in ensemble_results:
                continue
            
            logger.info(f"   Training final model for '{pattern}'...")
            
            X = group_data['features']
            y = group_data['labels']
            best_params = ensemble_results[pattern]['best_params'].copy()
            
            # Use optimal n_estimators from Optuna study (max of best iterations across folds)
            optimal_n_estimators = best_params.pop('optimal_n_estimators', best_params.get('n_estimators', 100))
            best_params['n_estimators'] = optimal_n_estimators
            
            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            
            # Train final model with optimal parameters
            xgb_params = {
                'objective': 'binary:logistic',
                'eval_metric': 'auc',
                'booster': 'gbtree',
                'random_state': self.random_state,
                'verbosity': 0,
                'tree_method': 'hist',
                'device': 'cuda',
                **best_params
            }
            
            final_model = xgb.XGBClassifier(**xgb_params)
            final_model.fit(X_scaled, y)
            
            # Store final model and scaler
            self.pattern_models[pattern] = final_model
            self.pattern_scalers[pattern] = scaler
            
            logger.info(f"      Final model trained with {optimal_n_estimators} estimators")
    
    def predict(self, 
               features_df: pd.DataFrame, 
               volatility_df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray, Dict]:
        """
        Make ensemble predictions
        
        Args:
            features_df: Features DataFrame
            volatility_df: Volatility patterns DataFrame
            
        Returns:
            Tuple of (predictions, probabilities, pattern_predictions)
        """
        logger.info("🔮 Making ensemble predictions...")
        
        n_samples = len(features_df)
        ensemble_proba = np.zeros(n_samples)
        pattern_predictions = {}
        
        for pattern in self.pattern_models.keys():
            # Get samples for this pattern
            pattern_mask = volatility_df['pattern_type'] == pattern
            pattern_indices = volatility_df[pattern_mask].index
            
            if len(pattern_indices) == 0:
                continue
            
            # Extract features for this pattern
            X_pattern = features_df.iloc[pattern_indices]
            
            # Scale and predict
            X_scaled = self.pattern_scalers[pattern].transform(X_pattern)
            pattern_proba = self.pattern_models[pattern].predict_proba(X_scaled)[:, 1]
            
            # Store pattern predictions
            pattern_predictions[pattern] = {
                'indices': pattern_indices,
                'probabilities': pattern_proba
            }
            
            # Add to ensemble with weights
            weight = self.ensemble_weights.get(pattern, 1.0)
            ensemble_proba[pattern_indices] = pattern_proba * weight
        
        # Normalize ensemble probabilities
        total_weight = sum(self.ensemble_weights.values())
        if total_weight > 0:
            ensemble_proba = ensemble_proba / total_weight
        
        # Convert to binary predictions
        ensemble_pred = (ensemble_proba > 0.5).astype(int)
        
        return ensemble_pred, ensemble_proba, pattern_predictions
    
    def create_comprehensive_report(self, 
                                  ensemble_results: Dict, 
                                  features_df: pd.DataFrame,
                                  labels: np.ndarray,
                                  volatility_df: pd.DataFrame) -> Dict:
        """
        Create comprehensive analysis report
        
        Args:
            ensemble_results: Training results
            features_df: Features DataFrame
            labels: True labels
            volatility_df: Volatility patterns DataFrame
            
        Returns:
            Comprehensive report dictionary
        """
        logger.info("📊 Creating comprehensive report...")
        
        # Make predictions on training data
        pred, pred_proba, pattern_preds = self.predict(features_df, volatility_df)
        
        # Overall ensemble performance
        ensemble_auc = roc_auc_score(labels, pred_proba)
        
        # Pattern-wise performance
        pattern_performance = {}
        for pattern, results in ensemble_results.items():
            pattern_mask = volatility_df['pattern_type'] == pattern
            pattern_labels = labels[pattern_mask]
            pattern_proba = pattern_preds[pattern]['probabilities']
            
            pattern_auc = roc_auc_score(pattern_labels, pattern_proba)
            pattern_performance[pattern] = {
                'n_samples': len(pattern_labels),
                'auc': pattern_auc,
                'train_auc': results['train_auc'],  # Use training AUC instead of CV
                'optimal_n_estimators': results['optimal_n_estimators'],
                'weight': self.ensemble_weights.get(pattern, 0.0)
            }
        
        # Create comprehensive report
        report = {
            'ensemble_performance': {
                'overall_auc': ensemble_auc,
                'n_total_samples': len(labels),
                'n_patterns': len(self.pattern_models)
            },
            'pattern_performance': pattern_performance,
            'ensemble_weights': self.ensemble_weights,
            'training_results': ensemble_results
        }
        
        return report
    
    def save_models(self, save_path: str):
        """Save trained models and components"""
        logger.info(f"💾 Saving models to {save_path}...")
        
        save_data = {
            'pattern_models': self.pattern_models,
            'pattern_scalers': self.pattern_scalers,
            'ensemble_weights': self.ensemble_weights,
            'cv_results': self.cv_results,
            'pattern_studies': self.pattern_studies
        }
        
        joblib.dump(save_data, save_path)
        logger.info("✅ Models saved successfully!")
    
    def load_models(self, load_path: str):
        """Load trained models and components"""
        logger.info(f"📂 Loading models from {load_path}...")
        
        save_data = joblib.load(load_path)
        
        self.pattern_models = save_data['pattern_models']
        self.pattern_scalers = save_data['pattern_scalers']
        self.ensemble_weights = save_data['ensemble_weights']
        self.cv_results = save_data['cv_results']
        self.pattern_studies = save_data.get('pattern_studies', {})
        
        logger.info("✅ Models loaded successfully!")

def create_visualizations(report: Dict, save_path: str = 'ensemble_analysis.png'):
    """
    Create comprehensive visualizations of ensemble performance
    
    Args:
        report: Comprehensive report dictionary
        save_path: Path to save visualization
    """
    logger.info("📊 Creating visualizations...")
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    
    # Plot 1: Pattern AUC comparison
    patterns = list(report['pattern_performance'].keys())
    train_aucs = [report['pattern_performance'][p]['train_auc'] for p in patterns]
    
    axes[0, 0].bar(patterns, train_aucs, alpha=0.7)
    axes[0, 0].set_title('Training AUC by Pattern')
    axes[0, 0].set_ylabel('AUC Score')
    axes[0, 0].tick_params(axis='x', rotation=45)
    axes[0, 0].grid(True, alpha=0.3)
    
    # Plot 2: Ensemble weights
    weights = [report['ensemble_weights'].get(p, 0) for p in patterns]
    axes[0, 1].pie(weights, labels=patterns, autopct='%1.1f%%')
    axes[0, 1].set_title('Ensemble Weights Distribution')
    
    # Plot 3: Sample distribution by pattern
    sample_counts = [report['pattern_performance'][p]['n_samples'] for p in patterns]
    axes[0, 2].bar(patterns, sample_counts, alpha=0.7)
    axes[0, 2].set_title('Sample Count by Pattern')
    axes[0, 2].set_ylabel('Number of Samples')
    axes[0, 2].tick_params(axis='x', rotation=45)
    
    # Plot 4: AUC vs Sample Size
    axes[1, 0].scatter(sample_counts, train_aucs, s=100, alpha=0.7)
    for i, pattern in enumerate(patterns):
        axes[1, 0].annotate(pattern, (sample_counts[i], train_aucs[i]), 
                           xytext=(5, 5), textcoords='offset points')
    axes[1, 0].set_xlabel('Sample Count')
    axes[1, 0].set_ylabel('Train AUC')
    axes[1, 0].set_title('AUC vs Sample Size')
    axes[1, 0].grid(True, alpha=0.3)
    
    # Plot 5: Weight vs Performance
    axes[1, 1].scatter(weights, train_aucs, s=100, alpha=0.7)
    for i, pattern in enumerate(patterns):
        axes[1, 1].annotate(pattern, (weights[i], train_aucs[i]), 
                           xytext=(5, 5), textcoords='offset points')
    axes[1, 1].set_xlabel('Ensemble Weight')
    axes[1, 1].set_ylabel('Train AUC')
    axes[1, 1].set_title('Weight vs Performance')
    axes[1, 1].grid(True, alpha=0.3)
    
    # Plot 6: Overall performance summary
    overall_auc = report['ensemble_performance']['overall_auc']
    n_patterns = report['ensemble_performance']['n_patterns']
    n_samples = report['ensemble_performance']['n_total_samples']
    
    summary_text = f"""
    Ensemble Performance Summary
    
    Overall AUC: {overall_auc:.4f}
    Number of Patterns: {n_patterns}
    Total Samples: {n_samples:,}
    
    Best Pattern: {max(patterns, key=lambda p: report['pattern_performance'][p]['train_auc'])}
    Best AUC: {max(train_aucs):.4f}
    
    Weighted Average AUC: {np.average(train_aucs, weights=weights):.4f}
    """
    
    axes[1, 2].text(0.1, 0.5, summary_text, transform=axes[1, 2].transAxes, 
                    fontsize=10, verticalalignment='center',
                    bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.5))
    axes[1, 2].set_xlim(0, 1)
    axes[1, 2].set_ylim(0, 1)
    axes[1, 2].axis('off')
    axes[1, 2].set_title('Performance Summary')
    
    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()
    
    logger.info(f"✅ Visualizations saved to {save_path}")

def main():
    """Main execution function"""
    logger.info("🚀 Starting Pattern-Based Ensemble Pipeline")
    
    # Configuration
    config = {
        'features_path': "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet",
        'labels_path': "y_train.parquet",
        'volatility_path': "volatility_pattern_analysis.csv",
        'n_folds': 5,
        'n_trials': 1,  # Reduced for faster testing
        'early_stopping_rounds': 50,
        'random_state': 42,
        'n_jobs': -1
    }
    
    try:
        # Initialize ensemble
        ensemble = PatternBasedEnsemble(
            n_folds=config['n_folds'],
            n_trials=config['n_trials'],
            early_stopping_rounds=config['early_stopping_rounds'],
            random_state=config['random_state'],
            n_jobs=config['n_jobs']
        )
        
        # Load data
        features_df, labels, volatility_df = ensemble.load_data(
            config['features_path'],
            config['labels_path'],
            config['volatility_path']
        )
        
        # Train ensemble
        start_time = time.time()
        ensemble_results = ensemble.train_ensemble(features_df, labels, volatility_df)
        training_time = time.time() - start_time
        
        logger.info(f"⏱️  Total training time: {training_time:.2f} seconds")
        
        # Create comprehensive report
        report = ensemble.create_comprehensive_report(
            ensemble_results, features_df, labels, volatility_df
        )
        
        # Print summary
        logger.info("\n" + "="*60)
        logger.info("📋 ENSEMBLE TRAINING SUMMARY")
        logger.info("="*60)
        logger.info(f"Overall Ensemble AUC: {report['ensemble_performance']['overall_auc']:.4f}")
        logger.info(f"Number of Patterns: {report['ensemble_performance']['n_patterns']}")
        logger.info(f"Total Samples: {report['ensemble_performance']['n_total_samples']:,}")
        
        logger.info("\n📊 Pattern Performance:")
        for pattern, perf in report['pattern_performance'].items():
            logger.info(f"  {pattern}:")
            logger.info(f"    Train AUC: {perf['train_auc']:.4f}")
            logger.info(f"    Prediction AUC: {perf['auc']:.4f}")
            logger.info(f"    Optimal n_estimators: {perf['optimal_n_estimators']}")
            logger.info(f"    Samples: {perf['n_samples']:,}")
            logger.info(f"    Weight: {perf['weight']:.4f}")
        
        # Create visualizations
        create_visualizations(report)
        
        # Save models
        ensemble.save_models('pattern_ensemble_models.joblib')
        
        # Save report
        import json
        with open('ensemble_report.json', 'w') as f:
            # Convert numpy types for JSON serialization and exclude non-serializable objects
            json_report = {}
            for key, value in report.items():
                if isinstance(value, dict):
                    json_report[key] = {}
                    for k, v in value.items():
                        # Skip non-JSON serializable objects like models and scalers
                        if k in ['model', 'scaler'] or hasattr(v, 'fit'):
                            continue
                        elif isinstance(v, dict):
                            # Handle nested dictionaries (like training_results)
                            json_report[key][k] = {}
                            for nested_k, nested_v in v.items():
                                if nested_k in ['model', 'scaler'] or hasattr(nested_v, 'fit'):
                                    continue
                                elif isinstance(nested_v, (np.float32, np.float64)):
                                    json_report[key][k][nested_k] = float(nested_v)
                                elif isinstance(nested_v, np.ndarray):
                                    json_report[key][k][nested_k] = nested_v.tolist()
                                else:
                                    json_report[key][k][nested_k] = nested_v
                        elif isinstance(v, (np.float32, np.float64)):
                            json_report[key][k] = float(v)
                        elif isinstance(v, np.ndarray):
                            json_report[key][k] = v.tolist()
                        else:
                            json_report[key][k] = v
                else:
                    if isinstance(value, (np.float32, np.float64)):
                        json_report[key] = float(value)
                    elif isinstance(value, np.ndarray):
                        json_report[key] = value.tolist()
                    else:
                        json_report[key] = value
            json.dump(json_report, f, indent=2)
        
        logger.info("\n💾 Results saved:")
        logger.info("  📊 Models: pattern_ensemble_models.joblib")
        logger.info("  📈 Visualization: ensemble_analysis.png")
        logger.info("  📋 Report: ensemble_report.json")
        
        return ensemble, report
        
    except Exception as e:
        logger.error(f"❌ Error in pipeline execution: {e}")
        raise

if __name__ == "__main__":
    ensemble, report = main()