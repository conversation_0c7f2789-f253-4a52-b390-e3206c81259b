#!/usr/bin/env python3
"""
Test the enhanced analyze_truncation_in_series_list function with pre/post analysis
and thermodynamic features
"""

import numpy as np
from Thermodynamics_truncated import analyze_truncation_in_series_list

def test_enhanced_analysis():
    """Test the enhanced series analysis with all features"""
    print("🧪 Testing Enhanced Series Analysis with Pre/Post Features")
    print("=" * 70)
    
    np.random.seed(42)
    
    # Create test data with structural breaks
    series_list = []
    tstar_list = []
    
    # Create 5 test series with different patterns
    for i in range(5):
        # Pre-break segment (clean)
        pre_segment = np.random.normal(0, 1, 100)
        
        # Post-break segment (with different truncation patterns)
        if i == 0:
            # Clean post-break
            post_segment = np.random.normal(0.5, 1.2, 100)
        elif i == 1:
            # Boundary truncated post-break
            post_raw = np.random.normal(0.5, 1.2, 100)
            post_segment = np.clip(post_raw, -0.3, 0.8)
        elif i == 2:
            # Precision truncated post-break
            post_raw = np.random.normal(0.5, 1.2, 100)
            post_segment = np.round(post_raw, 1)
        elif i == 3:
            # Quantized post-break
            post_raw = np.random.normal(0.5, 1.2, 100)
            post_segment = np.round(post_raw / 0.2) * 0.2
        else:
            # Mixed truncation post-break
            post_raw = np.random.normal(0.5, 1.2, 100)
            post_segment = np.clip(post_raw, -0.2, 0.7)
            post_segment = np.round(post_segment, 1)
        
        # Combine segments
        full_series = np.concatenate([pre_segment, post_segment])
        series_list.append(full_series)
        tstar_list.append(100)  # Break at position 100
    
    print(f"Created {len(series_list)} test series with structural breaks at position 100")
    print()
    
    # Test 1: Basic analysis without thermodynamic features
    print("🔍 Test 1: Basic Analysis with Pre/Post Truncation Features")
    print("-" * 50)
    
    results_basic = analyze_truncation_in_series_list(
        series_list=series_list,
        tstar_list=tstar_list,
        truncation_threshold=0.6,
        calibrate_detector=True,
        detailed_output=True,
        include_advanced_features=True,
        include_thermodynamic_features=False,
        n_jobs=2
    )
    
    print(f"Truncated series found: {len(results_basic['truncated_indices'])}")
    
    # Show some pre/post results
    if 'detailed_results' in results_basic:
        for idx in [1, 2]:  # Show results for series 1 and 2
            if idx in results_basic['detailed_results']:
                metrics = results_basic['detailed_results'][idx]
                print(f"\nSeries {idx} Results:")
                print(f"  Overall confidence: {metrics.get('truncation_confidence', 'N/A'):.3f}")
                print(f"  Pre-break confidence: {metrics.get('pre_truncation_confidence', 'N/A'):.3f}")
                print(f"  Post-break confidence: {metrics.get('post_truncation_confidence', 'N/A'):.3f}")
                print(f"  Truncation asymmetry: {metrics.get('truncation_asymmetry', 'N/A'):.3f}")
    
    print()
    
    # Test 2: Full analysis with thermodynamic features
    print("🌡️  Test 2: Full Analysis with Thermodynamic Features")
    print("-" * 50)
    
    results_thermo = analyze_truncation_in_series_list(
        series_list=series_list[:3],  # Use fewer series for speed
        tstar_list=tstar_list[:3],
        truncation_threshold=0.6,
        calibrate_detector=True,
        detailed_output=True,
        include_advanced_features=True,
        include_thermodynamic_features=True,
        n_jobs=2
    )
    
    print(f"Truncated series found: {len(results_thermo['truncated_indices'])}")
    
    # Show thermodynamic results
    if 'detailed_results' in results_thermo:
        for idx in [0, 1]:  # Show results for first two series
            if idx in results_thermo['detailed_results']:
                metrics = results_thermo['detailed_results'][idx]
                print(f"\nSeries {idx} Thermodynamic Results:")
                print(f"  Overall confidence: {metrics.get('truncation_confidence', 'N/A'):.3f}")
                print(f"  Quality-weighted energy: {metrics.get('quality_weighted_total_energy', 'N/A'):.3f}")
                print(f"  Pre-break energy: {metrics.get('pre_quality_weighted_total_energy', 'N/A'):.3f}")
                print(f"  Post-break energy: {metrics.get('post_quality_weighted_total_energy', 'N/A'):.3f}")
                print(f"  Robust kinetic energy: {metrics.get('robust_kinetic_energy_variance_based', 'N/A'):.3f}")
                
                # Show advanced features if available
                if 'boundary_concentration_ratio' in metrics:
                    print(f"  Boundary concentration: {metrics['boundary_concentration_ratio']:.3f}")
                if 'composite_truncation_score' in metrics:
                    print(f"  Composite truncation score: {metrics['composite_truncation_score']:.3f}")
    
    print()
    
    # Test 3: Analysis without tstar (whole series only)
    print("📊 Test 3: Analysis Without Structural Breaks")
    print("-" * 50)
    
    results_no_tstar = analyze_truncation_in_series_list(
        series_list=series_list[:3],
        tstar_list=None,  # No structural breaks
        truncation_threshold=0.6,
        calibrate_detector=True,
        detailed_output=True,
        include_advanced_features=True,
        include_thermodynamic_features=False,
        n_jobs=2
    )
    
    print(f"Truncated series found: {len(results_no_tstar['truncated_indices'])}")
    
    # Show comparison
    if 'detailed_results' in results_no_tstar:
        for idx in [0, 1]:
            if idx in results_no_tstar['detailed_results']:
                metrics = results_no_tstar['detailed_results'][idx]
                print(f"\nSeries {idx} (Whole Series Analysis):")
                print(f"  Truncation confidence: {metrics.get('truncation_confidence', 'N/A'):.3f}")
                print(f"  Has pre/post features: {metrics.get('has_pre_post', False)}")
    
    print()
    
    return results_basic, results_thermo, results_no_tstar

def compare_analysis_modes():
    """Compare different analysis modes"""
    print("⚖️  Comparing Analysis Modes")
    print("=" * 50)
    
    np.random.seed(123)
    
    # Create a simple test case
    pre_clean = np.random.normal(0, 1, 50)
    post_truncated = np.random.normal(0, 1, 50)
    post_truncated = np.clip(post_truncated, -0.5, 0.5)  # Truncate post-break
    
    test_series = [np.concatenate([pre_clean, post_truncated])]
    test_tstar = [50]
    
    # Mode 1: Basic truncation only
    result1 = analyze_truncation_in_series_list(
        series_list=test_series,
        tstar_list=test_tstar,
        include_advanced_features=False,
        include_thermodynamic_features=False,
        detailed_output=True
    )
    
    # Mode 2: With advanced features
    result2 = analyze_truncation_in_series_list(
        series_list=test_series,
        tstar_list=test_tstar,
        include_advanced_features=True,
        include_thermodynamic_features=False,
        detailed_output=True
    )
    
    # Mode 3: Full thermodynamic analysis
    result3 = analyze_truncation_in_series_list(
        series_list=test_series,
        tstar_list=test_tstar,
        include_advanced_features=True,
        include_thermodynamic_features=True,
        detailed_output=True
    )
    
    print("Analysis Mode Comparison:")
    print(f"{'Mode':<25} {'Features':<15} {'Pre Conf':<10} {'Post Conf':<10}")
    print("-" * 60)
    
    modes = [
        ("Basic", result1, "Basic truncation"),
        ("Advanced", result2, "Advanced features"),
        ("Thermodynamic", result3, "Full thermo")
    ]
    
    for mode_name, result, description in modes:
        if result['detailed_results'] and 0 in result['detailed_results']:
            metrics = result['detailed_results'][0]
            pre_conf = metrics.get('pre_truncation_confidence', 'N/A')
            post_conf = metrics.get('post_truncation_confidence', 'N/A')
            
            if isinstance(pre_conf, float):
                pre_conf = f"{pre_conf:.3f}"
            if isinstance(post_conf, float):
                post_conf = f"{post_conf:.3f}"
            
            print(f"{mode_name:<25} {description:<15} {pre_conf:<10} {post_conf:<10}")

def main():
    """Run all tests"""
    print("🚀 Testing Enhanced analyze_truncation_in_series_list Function")
    print("=" * 80)
    print()
    
    try:
        # Run main tests
        results_basic, results_thermo, results_no_tstar = test_enhanced_analysis()
        
        print()
        compare_analysis_modes()
        
        print("\n" + "=" * 80)
        print("✅ All tests completed successfully!")
        print("\nEnhanced Features Available:")
        print("  🔍 Pre/post structural break analysis")
        print("  🌡️  Full thermodynamic feature calculation")
        print("  📊 Advanced truncation features (60+ metrics)")
        print("  ⚡ Parallel processing with progress bars")
        print("  📋 Comprehensive result summaries")
        
        print(f"\nUsage Examples:")
        print(f"```python")
        print(f"# Basic pre/post analysis")
        print(f"results = analyze_truncation_in_series_list(")
        print(f"    series_list=your_series,")
        print(f"    tstar_list=your_break_points")
        print(f")")
        print(f"")
        print(f"# Full analysis with all features")
        print(f"results = analyze_truncation_in_series_list(")
        print(f"    series_list=your_series,")
        print(f"    tstar_list=your_break_points,")
        print(f"    include_advanced_features=True,")
        print(f"    include_thermodynamic_features=True,")
        print(f"    detailed_output=True")
        print(f")")
        print(f"```")
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()