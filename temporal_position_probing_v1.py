#!/usr/bin/env python3
"""
Temporal Position Probing v1
Test different temporal positions of normal segment for optimal AUROC
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
from multiprocessing import Pool, cpu_count
from functools import partial
from tqdm import tqdm
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and labels"""
    print("📊 Loading data...")
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels)

def compute_comparison_features(normal_segment, post_segment):
    """Compute comparison features between segments"""
    if len(normal_segment) < 3 or len(post_segment) < 3:
        return {}
    
    features = {
        'mean_diff': np.mean(post_segment) - np.mean(normal_segment),
        'std_diff': np.std(post_segment, ddof=1) - np.std(normal_segment, ddof=1),
        'var_ratio': np.var(post_segment, ddof=1) / np.var(normal_segment, ddof=1) if np.var(normal_segment, ddof=1) > 0 else 1,
        'range_diff': (np.max(post_segment) - np.min(post_segment)) - (np.max(normal_segment) - np.min(normal_segment)),
        'cv_ratio': (np.std(post_segment, ddof=1) / abs(np.mean(post_segment))) / (np.std(normal_segment, ddof=1) / abs(np.mean(normal_segment))) if np.mean(normal_segment) != 0 and np.mean(post_segment) != 0 else 1,
        'skew_diff': pd.Series(post_segment).skew() - pd.Series(normal_segment).skew(),
        'kurt_diff': pd.Series(post_segment).kurtosis() - pd.Series(normal_segment).kurtosis()
    }
    
    # Statistical tests
    try:
        _, features['ks_pvalue'] = stats.ks_2samp(normal_segment, post_segment)
        _, features['mw_pvalue'] = stats.mannwhitneyu(normal_segment, post_segment, alternative='two-sided')
        features['t_stat'], features['t_pvalue'] = stats.ttest_ind(normal_segment, post_segment)
    except:
        features['ks_pvalue'] = features['mw_pvalue'] = features['t_stat'] = features['t_pvalue'] = np.nan
    
    return features

class TemporalPositionMethods:
    @staticmethod
    def sliding_window_positions(series, window_sizes=[0.25, 0.5, 0.75]):
        """Method 1: Sliding window approach"""
        series_clean = np.array(series)[~np.isnan(series)]
        split_point = len(series_clean) // 2
        normal_full = series_clean[:split_point]
        post_full = series_clean[split_point:]
        
        methods = {}
        
        # Last X% of normal vs full post
        for size in window_sizes:
            window_len = max(3, int(len(normal_full) * size))
            normal_last = normal_full[-window_len:]
            methods[f'last_{int(size*100)}pct'] = (normal_last, post_full)
        
        # First X% of normal vs full post
        for size in window_sizes:
            window_len = max(3, int(len(normal_full) * size))
            normal_first = normal_full[:window_len]
            methods[f'first_{int(size*100)}pct'] = (normal_first, post_full)
        
        return methods
    
    @staticmethod
    def proximity_based_selection(series, window_sizes=[10, 20, 50]):
        """Method 2: Proximity-based selection"""
        series_clean = np.array(series)[~np.isnan(series)]
        split_point = len(series_clean) // 2
        normal_full = series_clean[:split_point]
        post_full = series_clean[split_point:]
        
        methods = {}
        
        for n in window_sizes:
            if len(normal_full) >= n and len(post_full) >= n:
                # Adjacent comparison
                normal_adjacent = normal_full[-n:]  # Last n points of normal
                post_adjacent = post_full[:n]       # First n points of post
                methods[f'adjacent_{n}'] = (normal_adjacent, post_adjacent)
                
                # Distant comparison
                normal_distant = normal_full[:n]    # First n points of normal
                post_distant = post_full[-n:]       # Last n points of post
                methods[f'distant_{n}'] = (normal_distant, post_distant)
        
        return methods
    
    @staticmethod
    def statistical_similarity_matching(series):
        """Method 3: Statistical similarity matching"""
        series_clean = np.array(series)[~np.isnan(series)]
        split_point = len(series_clean) // 2
        normal_full = series_clean[:split_point]
        post_full = series_clean[split_point:]
        
        methods = {}
        
        # Post segment statistics
        post_mean = np.mean(post_full)
        post_std = np.std(post_full, ddof=1)
        
        # Find most similar window in normal segment
        window_size = min(len(post_full), len(normal_full) // 2)
        if window_size >= 3:
            similarities = []
            
            for i in range(len(normal_full) - window_size + 1):
                window = normal_full[i:i+window_size]
                window_mean = np.mean(window)
                window_std = np.std(window, ddof=1)
                
                # Similarity score (lower = more similar)
                similarity = abs(window_mean - post_mean) + abs(window_std - post_std)
                similarities.append((similarity, window))
            
            # Most similar
            similarities.sort(key=lambda x: x[0])
            methods['most_similar'] = (similarities[0][1], post_full)
            
            # Most different
            methods['most_different'] = (similarities[-1][1], post_full)
            
            # Variance matched (closest std)
            similarities_std = sorted(similarities, key=lambda x: abs(np.std(x[1], ddof=1) - post_std))
            methods['variance_matched'] = (similarities_std[0][1], post_full)
        
        return methods
    
    @staticmethod
    def regime_aware_selection(series):
        """Method 4: Regime-aware selection"""
        series_clean = np.array(series)[~np.isnan(series)]
        split_point = len(series_clean) // 2
        normal_full = series_clean[:split_point]
        post_full = series_clean[split_point:]
        
        methods = {}
        
        if len(normal_full) >= 10:
            # Calculate rolling volatility
            window_size = max(5, len(normal_full) // 4)
            volatilities = []
            
            for i in range(len(normal_full) - window_size + 1):
                window = normal_full[i:i+window_size]
                vol = np.std(window, ddof=1)
                volatilities.append((vol, window))
            
            if volatilities:
                # Stable periods (low volatility)
                volatilities_sorted = sorted(volatilities, key=lambda x: x[0])
                methods['stable_period'] = (volatilities_sorted[0][1], post_full)
                
                # Active periods (high volatility)
                methods['active_period'] = (volatilities_sorted[-1][1], post_full)
                
                # Trend matching (similar slope)
                post_trend = np.polyfit(range(len(post_full)), post_full, 1)[0]
                trend_similarities = []
                
                for vol, window in volatilities:
                    window_trend = np.polyfit(range(len(window)), window, 1)[0]
                    trend_diff = abs(window_trend - post_trend)
                    trend_similarities.append((trend_diff, window))
                
                trend_similarities.sort(key=lambda x: x[0])
                methods['trend_matched'] = (trend_similarities[0][1], post_full)
        
        return methods
    
    @staticmethod
    def information_content_optimization(series):
        """Method 5: Information content optimization"""
        series_clean = np.array(series)[~np.isnan(series)]
        split_point = len(series_clean) // 2
        normal_full = series_clean[:split_point]
        post_full = series_clean[split_point:]
        
        methods = {}
        
        if len(normal_full) >= 10:
            window_size = max(5, len(normal_full) // 3)
            entropies = []
            
            for i in range(len(normal_full) - window_size + 1):
                window = normal_full[i:i+window_size]
                
                # Calculate entropy (using histogram)
                hist, _ = np.histogram(window, bins=min(10, len(window)//2), density=True)
                hist = hist[hist > 0]
                entropy_val = -np.sum(hist * np.log(hist)) if len(hist) > 0 else 0
                
                entropies.append((entropy_val, window))
            
            if entropies:
                entropies_sorted = sorted(entropies, key=lambda x: x[0])
                
                # High entropy (most informative)
                methods['high_entropy'] = (entropies_sorted[-1][1], post_full)
                
                # Low entropy (most stable)
                methods['low_entropy'] = (entropies_sorted[0][1], post_full)
                
                # Signal-to-noise optimization
                snr_scores = []
                for entropy_val, window in entropies:
                    signal = np.mean(np.abs(window))
                    noise = np.std(window, ddof=1)
                    snr = signal / noise if noise > 0 else 0
                    snr_scores.append((snr, window))
                
                snr_scores.sort(key=lambda x: x[0], reverse=True)
                methods['high_snr'] = (snr_scores[0][1], post_full)
        
        return methods
    
    @staticmethod
    def break_point_adaptive(series):
        """Method 6: Break-point adaptive"""
        series_clean = np.array(series)[~np.isnan(series)]
        split_point = len(series_clean) // 2
        normal_full = series_clean[:split_point]
        post_full = series_clean[split_point:]
        
        methods = {}
        
        # Pre-break proximity (closer to break point)
        proximity_sizes = [0.1, 0.2, 0.3]
        for size in proximity_sizes:
            window_len = max(3, int(len(normal_full) * size))
            normal_proximity = normal_full[-window_len:]  # Closest to break
            methods[f'proximity_{int(size*100)}pct'] = (normal_proximity, post_full)
        
        # Baseline establishment (representing true normal)
        baseline_sizes = [0.2, 0.3, 0.5]
        for size in baseline_sizes:
            window_len = max(3, int(len(normal_full) * size))
            normal_baseline = normal_full[:window_len]  # Early normal behavior
            methods[f'baseline_{int(size*100)}pct'] = (normal_baseline, post_full)
        
        return methods

def process_single_series(args):
    """Process a single time series for all temporal methods"""
    series, label = args
    series_clean = np.array(series)[~np.isnan(series)]
    
    if len(series_clean) < 20:
        return {}
    
    # Apply all temporal methods
    methods = {}
    try:
        methods.update(TemporalPositionMethods.sliding_window_positions(series))
        methods.update(TemporalPositionMethods.proximity_based_selection(series))
        methods.update(TemporalPositionMethods.statistical_similarity_matching(series))
        methods.update(TemporalPositionMethods.regime_aware_selection(series))
        methods.update(TemporalPositionMethods.information_content_optimization(series))
        methods.update(TemporalPositionMethods.break_point_adaptive(series))
    except Exception:
        return {}
    
    # Compute features for each method
    series_results = {}
    for method_name, (normal_seg, post_seg) in methods.items():
        try:
            features = compute_comparison_features(normal_seg, post_seg)
            features['label'] = label
            series_results[method_name] = features
        except Exception:
            continue
    
    return series_results

def evaluate_method_parallel(args):
    """Evaluate a single method with cross-validation"""
    method_name, method_features = args
    
    if len(method_features) < 20:
        return None
    
    try:
        # Convert to DataFrame
        features_df = pd.DataFrame(method_features)
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        
        # Prepare for classification
        feature_cols = [col for col in features_df.columns if col != 'label']
        X = features_df[feature_cols].fillna(features_df[feature_cols].median())
        y = features_df['label'].values
        
        if len(np.unique(y)) < 2:
            return None
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        model = RandomForestClassifier(n_estimators=50, random_state=42, class_weight='balanced', n_jobs=1)
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
        
        return {
            'method': method_name,
            'mean_auroc': cv_scores.mean(),
            'std_auroc': cv_scores.std(),
            'n_samples': len(features_df),
            'break_rate': y.mean()
        }
        
    except Exception:
        return None

def test_temporal_methods(time_series_list, labels, max_samples=100000):
    """Test all temporal position methods with parallel processing"""
    print("🔧 Testing temporal position methods...")
    
    # Limit samples for faster testing
    if len(time_series_list) > max_samples:
        indices = np.random.choice(len(time_series_list), max_samples, replace=False)
        time_series_list = [time_series_list[i] for i in indices]
        labels = labels[indices]
    
    print(f"   Processing {len(time_series_list)} series with {cpu_count()} cores...")
    
    # Parallel processing of time series
    n_processes = max(1, cpu_count() - 1)
    
    with Pool(processes=n_processes) as pool:
        # Process all series in parallel with progress bar
        series_args = [(series, label) for series, label in zip(time_series_list, labels)]
        
        results_list = list(tqdm(
            pool.imap(process_single_series, series_args),
            total=len(series_args),
            desc="   Processing series",
            ncols=80
        ))
    
    # Aggregate results by method
    all_method_results = {}
    for series_result in results_list:
        for method_name, features in series_result.items():
            if method_name not in all_method_results:
                all_method_results[method_name] = []
            all_method_results[method_name].append(features)
    
    print(f"   Collected results for {len(all_method_results)} methods")
    
    # Parallel evaluation of methods
    print("   Evaluating methods with cross-validation...")
    
    method_args = [(method_name, method_features) for method_name, method_features in all_method_results.items()]
    
    with Pool(processes=n_processes) as pool:
        evaluation_results = list(tqdm(
            pool.imap(evaluate_method_parallel, method_args),
            total=len(method_args),
            desc="   Evaluating methods",
            ncols=80
        ))
    
    # Filter out None results
    results = [result for result in evaluation_results if result is not None]
    
    return pd.DataFrame(results)

def analyze_temporal_results(results_df):
    """Analyze temporal position results"""
    print("\n📈 TEMPORAL POSITION ANALYSIS RESULTS")
    print("=" * 60)
    
    # Sort by AUROC performance
    results_sorted = results_df.sort_values('mean_auroc', ascending=False)
    
    print("🏆 Top 10 performing methods:")
    for i, (_, row) in enumerate(results_sorted.head(10).iterrows()):
        print(f"   {i+1:2d}. {row['method']:20}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f} (n={row['n_samples']})")
    
    print(f"\n📉 Bottom 5 performing methods:")
    for i, (_, row) in enumerate(results_sorted.tail(5).iterrows()):
        print(f"   {row['method']:20}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    # Analyze by method category
    print(f"\n📊 Analysis by method category:")
    
    categories = {
        'sliding_window': ['first_', 'last_'],
        'proximity': ['adjacent_', 'distant_'],
        'similarity': ['most_similar', 'most_different', 'variance_matched'],
        'regime': ['stable_period', 'active_period', 'trend_matched'],
        'information': ['high_entropy', 'low_entropy', 'high_snr'],
        'adaptive': ['proximity_', 'baseline_']
    }
    
    for category, keywords in categories.items():
        category_results = results_df[results_df['method'].str.contains('|'.join(keywords), na=False)]
        if len(category_results) > 0:
            best_method = category_results.loc[category_results['mean_auroc'].idxmax()]
            avg_auroc = category_results['mean_auroc'].mean()
            print(f"   {category:15}: Best = {best_method['mean_auroc']:.4f} ({best_method['method']}), Avg = {avg_auroc:.4f}")
    
    # Best overall method
    best_method = results_sorted.iloc[0]
    print(f"\n🎯 Best overall method: {best_method['method']}")
    print(f"   AUROC: {best_method['mean_auroc']:.4f} ± {best_method['std_auroc']:.4f}")
    print(f"   Samples: {best_method['n_samples']}")
    print(f"   Break rate: {best_method['break_rate']:.3f}")

def main():
    """Main pipeline execution"""
    print("🚀 TEMPORAL POSITION PROBING PIPELINE")
    print("=" * 50)
    
    # Load data
    time_series_list, labels = load_data()
    
    # Test temporal position methods
    results_df = test_temporal_methods(time_series_list, labels)
    
    # Analyze results
    analyze_temporal_results(results_df)
    
    # Save results
    results_df.to_csv('temporal_position_results.csv', index=False)
    
    print(f"\n💾 Results saved to 'temporal_position_results.csv'")
    
    return results_df

if __name__ == "__main__":
    results = main()