"""
Comprehensive Analysis: Extracting Meaningful Features from Truncation Mechanics
"""

import numpy as np
import pandas as pd
import json
from collections import Counter
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score
from sklearn.linear_model import LogisticRegression

def analyze_truncation_features():
    """Analyze truncation patterns and extract meaningful features"""
    
    print("🔬 TRUNCATION FEATURE ENGINEERING ANALYSIS")
    print("=" * 60)
    
    # Load data
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    # with open('revised_truncation_analysis_results.json', 'r') as f:
    #     truncation_results = json.load(f)
    
    print(f"📊 Dataset: {len(y_data)} samples")
    # print(f"📊 Suspicious samples: {truncation_results['total_suspicious']} ({truncation_results['total_suspicious']/len(y_data)*100:.1f}%)")
    
    # Extract features for all samples
    print(f"\n🔧 Extracting truncation features...")
    
    all_features = []
    all_labels = []
    
    for i, sample_id in enumerate(y_data.index):
        try:
            sample_X = X_data.loc[sample_id]
            sample_y = y_data.loc[sample_id].values[0]
            
            # Separate parts
            normal_mask = sample_X['period'] == 0
            candidate_mask = sample_X['period'] == 1
            
            normal_values = sample_X.loc[normal_mask, 'value'].values
            candidate_values = sample_X.loc[candidate_mask, 'value'].values
            
            if len(normal_values) < 10 or len(candidate_values) < 10:
                continue
            
            # Extract features
            features = extract_comprehensive_features(normal_values, candidate_values, sample_id)
            
            all_features.append(features)
            all_labels.append(int(sample_y))
            
            if (i + 1) % 1000 == 0:
                print(f"   Processed {i+1:,} samples...")
                
        except Exception as e:
            continue
    
    print(f"   ✅ Extracted features for {len(all_features)} samples")
    
    # Convert to DataFrame
    features_df = pd.DataFrame(all_features)
    labels_array = np.array(all_labels)
    
    print(f"   📊 Feature matrix: {features_df.shape}")
    print(f"   📊 Label distribution: {Counter(labels_array)}")
    
    # Analyze features
    analyze_feature_importance(features_df, labels_array)
    analyze_truncation_patterns(features_df, labels_array)
    evaluate_classifiers(features_df, labels_array)
    
    return features_df, labels_array

def extract_comprehensive_features(normal_values, candidate_values, sample_id):
    """Extract comprehensive truncation-based features"""
    
    features = {'sample_id': sample_id}
    
    # 1. DECIMAL PRECISION FEATURES
    for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
        # Count limited precision values
        precision_1 = sum(1 for v in values if len(f"{v:.10f}".split('.')[1].rstrip('0')) <= 1)
        precision_2 = sum(1 for v in values if len(f"{v:.10f}".split('.')[1].rstrip('0')) <= 2)
        precision_3 = sum(1 for v in values if len(f"{v:.10f}".split('.')[1].rstrip('0')) <= 3)
        
        total = len(values)
        features[f'{part_name}_precision_1_pct'] = precision_1 / total * 100
        features[f'{part_name}_precision_2_pct'] = precision_2 / total * 100
        features[f'{part_name}_precision_3_pct'] = precision_3 / total * 100
        features[f'{part_name}_decimal_truncation_score'] = (precision_1 + precision_2 + precision_3) / total * 100
    
    # 2. ROUND NUMBER FEATURES
    round_numbers = [0.0, 0.1, -0.1, 0.01, -0.01, 0.07, -0.07, 0.05, -0.05, 0.03, -0.03]
    
    for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
        min_val, max_val = np.min(values), np.max(values)
        
        # Check if extremes are round numbers
        min_is_round = any(abs(min_val - rn) < 1e-8 for rn in round_numbers)
        max_is_round = any(abs(max_val - rn) < 1e-8 for rn in round_numbers)
        
        features[f'{part_name}_min_is_round'] = int(min_is_round)
        features[f'{part_name}_max_is_round'] = int(max_is_round)
        
        # Count round number occurrences
        round_count = sum(sum(abs(val - rn) < 1e-8 for rn in round_numbers) for val in values)
        features[f'{part_name}_round_number_pct'] = round_count / len(values) * 100
        
        # Suspicious patterns (0.07, -0.07, etc.)
        suspicious = [0.070000, -0.070000, 0.050000, -0.050000, 0.030000, -0.030000]
        suspicious_count = sum(sum(abs(val - sp) < 1e-8 for sp in suspicious) for val in values)
        features[f'{part_name}_suspicious_patterns_pct'] = suspicious_count / len(values) * 100
    
    # 3. CLUSTERING FEATURES
    for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
        min_val, max_val = np.min(values), np.max(values)
        range_val = max_val - min_val
        
        if range_val > 0:
            # Clustering at different thresholds
            for threshold_pct in [0.5, 1.0, 2.0]:
                threshold = threshold_pct / 100.0 * range_val
                near_min = np.sum(np.abs(values - min_val) <= threshold)
                near_max = np.sum(np.abs(values - max_val) <= threshold)
                
                features[f'{part_name}_min_cluster_{threshold_pct}pct'] = near_min / len(values) * 100
                features[f'{part_name}_max_cluster_{threshold_pct}pct'] = near_max / len(values) * 100
        else:
            for threshold_pct in [0.5, 1.0, 2.0]:
                features[f'{part_name}_min_cluster_{threshold_pct}pct'] = 100.0
                features[f'{part_name}_max_cluster_{threshold_pct}pct'] = 100.0
    
    # 4. BOUNDARY FEATURES
    for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
        min_val, max_val = np.min(values), np.max(values)
        
        # Symmetric bounds
        features[f'{part_name}_symmetric_bounds'] = int(
            abs(abs(min_val) - abs(max_val)) < 1e-6 and abs(min_val) > 0.01
        )
        
        # Range features
        range_val = max_val - min_val
        features[f'{part_name}_range'] = range_val
        
        # Suspicious ranges
        suspicious_ranges = [0.1, 0.2, 0.5, 1.0, 2.0]
        features[f'{part_name}_suspicious_range'] = int(
            any(abs(range_val - sr) < 1e-6 for sr in suspicious_ranges)
        )
    
    # 5. TAIL FLATNESS FEATURES
    for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
        sorted_values = np.sort(values)
        n = len(values)
        
        # Count consecutive equal values at extremes
        left_flatness = 0
        right_flatness = 0
        
        for i in range(1, min(10, n)):
            if abs(sorted_values[i] - sorted_values[0]) < 1e-10:
                left_flatness += 1
            else:
                break
        
        for i in range(n-2, max(n-11, -1), -1):
            if abs(sorted_values[i] - sorted_values[-1]) < 1e-10:
                right_flatness += 1
            else:
                break
        
        features[f'{part_name}_left_tail_flatness'] = left_flatness
        features[f'{part_name}_right_tail_flatness'] = right_flatness
        features[f'{part_name}_total_tail_flatness'] = left_flatness + right_flatness
    
    # 6. CROSS-PART COMPARISON FEATURES
    normal_range = np.max(normal_values) - np.min(normal_values)
    candidate_range = np.max(candidate_values) - np.min(candidate_values)
    
    features['range_ratio'] = normal_range / (candidate_range + 1e-8)
    features['range_difference'] = abs(normal_range - candidate_range)
    
    # Extreme value alignment
    features['min_alignment'] = abs(np.min(normal_values) - np.min(candidate_values))
    features['max_alignment'] = abs(np.max(normal_values) - np.max(candidate_values))
    
    # Truncation asymmetry
    normal_truncation = features['normal_decimal_truncation_score'] + features['normal_suspicious_patterns_pct']
    candidate_truncation = features['candidate_decimal_truncation_score'] + features['candidate_suspicious_patterns_pct']
    features['truncation_asymmetry'] = abs(normal_truncation - candidate_truncation)
    
    # Consistent truncation
    features['consistent_round_truncation'] = int(
        (features['normal_min_is_round'] and features['candidate_min_is_round']) or
        (features['normal_max_is_round'] and features['candidate_max_is_round'])
    )
    
    return features

def analyze_feature_importance(features_df, labels_array):
    """Analyze feature importance using Random Forest"""
    
    print(f"\n🌳 FEATURE IMPORTANCE ANALYSIS")
    print("=" * 40)
    
    # Remove non-numeric columns
    numeric_features = features_df.select_dtypes(include=[np.number])
    numeric_features = numeric_features.fillna(0)
    
    print(f"📊 Using {numeric_features.shape[1]} numeric features")
    
    # Train Random Forest
    rf = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10)
    rf.fit(numeric_features, labels_array)
    
    # Get feature importances
    importances = rf.feature_importances_
    feature_names = numeric_features.columns
    
    # Sort by importance
    importance_indices = np.argsort(importances)[::-1]
    
    print(f"\n🏆 TOP 15 MOST IMPORTANT TRUNCATION FEATURES:")
    print("   Rank | Importance | Feature Name")
    print("   -----|------------|-------------")
    
    for i in range(min(15, len(importance_indices))):
        idx = importance_indices[i]
        print(f"   {i+1:4d} |   {importances[idx]:.6f} | {feature_names[idx]}")
    
    # Cross-validation score
    cv_scores = cross_val_score(rf, numeric_features, labels_array, cv=5, scoring='roc_auc')
    print(f"\n📊 Random Forest CV Performance:")
    print(f"   Mean AUC: {np.mean(cv_scores):.4f} ± {np.std(cv_scores):.4f}")
    
    return importances, feature_names

def analyze_truncation_patterns(features_df, labels_array):
    """Analyze how truncation patterns relate to structural breaks"""
    
    print(f"\n🔍 TRUNCATION PATTERN ANALYSIS")
    print("=" * 40)
    
    # Analyze truncation by label
    pos_samples = features_df[labels_array == 1]
    neg_samples = features_df[labels_array == 0]
    
    print(f"📊 Samples: {len(pos_samples)} positive, {len(neg_samples)} negative")
    
    # Key truncation indicators
    key_indicators = [
        'normal_decimal_truncation_score',
        'candidate_decimal_truncation_score', 
        'normal_suspicious_patterns_pct',
        'candidate_suspicious_patterns_pct',
        'truncation_asymmetry',
        'consistent_round_truncation',
        'normal_total_tail_flatness',
        'candidate_total_tail_flatness'
    ]
    
    print(f"\n📈 Key Truncation Indicators by Label:")
    print("   Indicator                        | Pos Mean | Neg Mean | Difference")
    print("   ---------------------------------|----------|----------|----------")
    
    for indicator in key_indicators:
        if indicator in features_df.columns:
            pos_mean = pos_samples[indicator].mean()
            neg_mean = neg_samples[indicator].mean()
            diff = pos_mean - neg_mean
            print(f"   {indicator:32s} | {pos_mean:8.3f} | {neg_mean:8.3f} | {diff:8.3f}")

def evaluate_classifiers(features_df, labels_array):
    """Evaluate classifiers on truncation features"""
    
    print(f"\n🎯 TRUNCATION-BASED CLASSIFIER EVALUATION")
    print("=" * 50)
    
    # Remove non-numeric columns
    numeric_features = features_df.select_dtypes(include=[np.number])
    numeric_features = numeric_features.fillna(0)
    
    # Train different classifiers
    from sklearn.ensemble import GradientBoostingClassifier
    
    classifiers = {
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42, max_depth=10),
        'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
        'Gradient Boosting': GradientBoostingClassifier(random_state=42, n_estimators=100)
    }
    
    print(f"🔬 Evaluating {len(classifiers)} classifiers on truncation features:")
    print("   Classifier         | CV AUC    | CV Accuracy")
    print("   -------------------|-----------|------------")
    
    results = {}
    
    for name, clf in classifiers.items():
        # Cross-validation AUC
        auc_scores = cross_val_score(clf, numeric_features, labels_array, cv=5, scoring='roc_auc')
        acc_scores = cross_val_score(clf, numeric_features, labels_array, cv=5, scoring='accuracy')
        
        results[name] = {
            'auc_mean': np.mean(auc_scores),
            'auc_std': np.std(auc_scores),
            'acc_mean': np.mean(acc_scores),
            'acc_std': np.std(acc_scores)
        }
        
        print(f"   {name:18s} | {results[name]['auc_mean']:.4f}±{results[name]['auc_std']:.3f} | {results[name]['acc_mean']:.4f}±{results[name]['acc_std']:.3f}")
    
    return results

def create_feature_insights():
    """Create insights about truncation feature engineering"""
    
    print(f"\n📋 TRUNCATION FEATURE ENGINEERING INSIGHTS")
    print("=" * 60)
    
    insights = {
        "decimal_precision_features": {
            "description": "Detect artificial precision limitations in data",
            "key_patterns": [
                "Values with 1-3 decimal places only",
                "Excessive trailing zeros",
                "Different precision between normal/candidate parts"
            ],
            "interpretation": "High scores suggest data preprocessing or constraints"
        },
        
        "round_number_features": {
            "description": "Identify suspicious round number patterns",
            "key_patterns": [
                "Extremes at exact values (0.0, 0.1, -0.1)",
                "Specific suspicious values (0.07, -0.07, 0.05)",
                "High percentage of round numbers"
            ],
            "interpretation": "Indicates potential data clipping or boundaries"
        },
        
        "clustering_features": {
            "description": "Detect value clustering near data extremes",
            "key_patterns": [
                "Multiple values clustered at min/max",
                "Asymmetric clustering patterns",
                "Different clustering between parts"
            ],
            "interpretation": "Strong clustering suggests truncation effects"
        },
        
        "boundary_features": {
            "description": "Identify artificial boundaries and constraints",
            "key_patterns": [
                "Symmetric bounds (±same value)",
                "Suspicious round ranges",
                "Constrained value ranges"
            ],
            "interpretation": "Reveals preprocessing with artificial limits"
        },
        
        "tail_flatness_features": {
            "description": "Detect flat tails from truncation",
            "key_patterns": [
                "Consecutive equal values at extremes",
                "Flat-line patterns in data",
                "Truncated distribution tails"
            ],
            "interpretation": "Flat tails indicate systematic truncation"
        },
        
        "cross_part_features": {
            "description": "Compare truncation between normal/candidate",
            "key_patterns": [
                "Consistent truncation across parts",
                "Differential truncation effects",
                "Aligned extreme values"
            ],
            "interpretation": "Reveals systematic vs. localized effects"
        }
    }
    
    for category, info in insights.items():
        print(f"\n🔍 {category.upper().replace('_', ' ')}")
        print(f"   Description: {info['description']}")
        print(f"   Key Patterns:")
        for pattern in info['key_patterns']:
            print(f"     • {pattern}")
        print(f"   Interpretation: {info['interpretation']}")
    
    return insights

if __name__ == "__main__":
    # Run analysis
    features_df, labels_array = analyze_truncation_features()
    
    # Create insights
    insights = create_feature_insights()
    
    # Save results
    results = {
        'feature_count': len(features_df.columns),
        'sample_count': len(features_df),
        'insights': insights
    }
    
    with open('truncation_feature_analysis_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n✅ Analysis complete! Results saved to truncation_feature_analysis_results.json")