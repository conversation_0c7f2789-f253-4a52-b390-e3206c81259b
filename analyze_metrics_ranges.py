#!/usr/bin/env python3
"""
Analyze Advanced Deviation Metrics for AUROC Improvement
Focus on mean-deviation patterns, persistence, and exceedance statistics
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from scipy import stats
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import cross_val_score, StratifiedKFold
from sklearn.feature_selection import SelectKBest, f_classif
import warnings

warnings.filterwarnings('ignore')

def load_time_series_data():
    """Load time series data with labels"""
    try:
        print("📊 Loading data...")
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        time_series_list = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            grouped = X_data.groupby(level='id')
            for id_, group in grouped:
                series_data = group.sort_index(level='time').values.flatten()
                time_series_list.append(series_data)
                label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                labels.append(label)
        else:
            for idx, row in X_data.iterrows():
                time_series_list.append(row.values)
                label = y_data.iloc[idx] if idx < len(y_data) else 0
                labels.append(label)
        
        print(f"   ✅ Loaded {len(time_series_list)} time series")
        return time_series_list, labels
        
    except Exception as e:
        print(f"   ❌ Error: {e}")
        return None, None

def compute_deviation_metrics(segment):
    """Compute comprehensive deviation metrics for a segment"""
    segment = np.array(segment)
    if len(segment) < 3:
        return {}
    
    mean_val = np.mean(segment)
    std_val = np.std(segment, ddof=1)
    
    # Deviations from mean
    deviations = segment - mean_val
    abs_deviations = np.abs(deviations)
    
    metrics = {}
    
    # 1. Z-score metrics
    if std_val > 0:
        z_scores = deviations / std_val
        metrics['z_score_mean'] = np.mean(np.abs(z_scores))
        metrics['z_score_max'] = np.max(np.abs(z_scores))
        metrics['z_score_std'] = np.std(z_scores)
        metrics['z_score_skew'] = stats.skew(z_scores)
    else:
        metrics['z_score_mean'] = metrics['z_score_max'] = metrics['z_score_std'] = metrics['z_score_skew'] = 0
    
    # 2. Relative size of deviation from mean
    if mean_val != 0:
        relative_deviations = abs_deviations / abs(mean_val)
        metrics['relative_dev_mean'] = np.mean(relative_deviations)
        metrics['relative_dev_max'] = np.max(relative_deviations)
        metrics['relative_dev_std'] = np.std(relative_deviations)
    else:
        metrics['relative_dev_mean'] = metrics['relative_dev_max'] = metrics['relative_dev_std'] = np.inf
    
    # 3. Skewness - asymmetry of extreme deviations
    metrics['skewness'] = stats.skew(segment)
    metrics['deviation_skewness'] = stats.skew(deviations)
    
    # Directional skewness
    positive_devs = deviations[deviations > 0]
    negative_devs = deviations[deviations < 0]
    metrics['positive_dev_mean'] = np.mean(positive_devs) if len(positive_devs) > 0 else 0
    metrics['negative_dev_mean'] = np.mean(negative_devs) if len(negative_devs) > 0 else 0
    metrics['asymmetry_ratio'] = abs(metrics['positive_dev_mean']) / abs(metrics['negative_dev_mean']) if metrics['negative_dev_mean'] != 0 else np.inf
    
    # 4. Proportion above/below mean
    above_mean = segment > mean_val
    below_mean = segment < mean_val
    
    metrics['prop_above_mean'] = np.sum(above_mean) / len(segment)
    metrics['prop_below_mean'] = np.sum(below_mean) / len(segment)
    metrics['prop_at_mean'] = np.sum(segment == mean_val) / len(segment)
    
    # 5. Mean/max deviation amplitudes
    if len(positive_devs) > 0:
        metrics['mean_positive_dev'] = np.mean(positive_devs)
        metrics['max_positive_dev'] = np.max(positive_devs)
        metrics['std_positive_dev'] = np.std(positive_devs)
    else:
        metrics['mean_positive_dev'] = metrics['max_positive_dev'] = metrics['std_positive_dev'] = 0
    
    if len(negative_devs) > 0:
        metrics['mean_negative_dev'] = np.mean(np.abs(negative_devs))
        metrics['max_negative_dev'] = np.max(np.abs(negative_devs))
        metrics['std_negative_dev'] = np.std(negative_devs)
    else:
        metrics['mean_negative_dev'] = metrics['max_negative_dev'] = metrics['std_negative_dev'] = 0
    
    # 6. Run-length/Persistence analysis
    # Above mean runs
    above_runs = []
    current_run = 0
    for val in above_mean:
        if val:
            current_run += 1
        else:
            if current_run > 0:
                above_runs.append(current_run)
                current_run = 0
    if current_run > 0:
        above_runs.append(current_run)
    
    # Below mean runs
    below_runs = []
    current_run = 0
    for val in below_mean:
        if val:
            current_run += 1
        else:
            if current_run > 0:
                below_runs.append(current_run)
                current_run = 0
    if current_run > 0:
        below_runs.append(current_run)
    
    metrics['above_mean_runs'] = len(above_runs)
    metrics['below_mean_runs'] = len(below_runs)
    metrics['mean_above_run_length'] = np.mean(above_runs) if above_runs else 0
    metrics['mean_below_run_length'] = np.mean(below_runs) if below_runs else 0
    metrics['max_above_run_length'] = np.max(above_runs) if above_runs else 0
    metrics['max_below_run_length'] = np.max(below_runs) if below_runs else 0
    
    # Persistence ratio
    total_runs = len(above_runs) + len(below_runs)
    metrics['run_persistence'] = (metrics['mean_above_run_length'] + metrics['mean_below_run_length']) / 2 if total_runs > 0 else 0
    
    # 7. Mean-crossing rate
    crossings = 0
    for i in range(1, len(segment)):
        if (segment[i-1] - mean_val) * (segment[i] - mean_val) < 0:
            crossings += 1
    
    metrics['mean_crossing_rate'] = crossings / (len(segment) - 1) if len(segment) > 1 else 0
    metrics['mean_crossings_total'] = crossings
    
    # 8. Probability of exceedance (various thresholds)
    thresholds = [0.5, 1.0, 1.5, 2.0]  # In terms of standard deviations
    
    for thresh in thresholds:
        if std_val > 0:
            exceedance_positive = np.sum(deviations > thresh * std_val) / len(segment)
            exceedance_negative = np.sum(deviations < -thresh * std_val) / len(segment)
            exceedance_either = np.sum(np.abs(deviations) > thresh * std_val) / len(segment)
        else:
            exceedance_positive = exceedance_negative = exceedance_either = 0
        
        metrics[f'prob_exceed_pos_{thresh}std'] = exceedance_positive
        metrics[f'prob_exceed_neg_{thresh}std'] = exceedance_negative
        metrics[f'prob_exceed_either_{thresh}std'] = exceedance_either
    
    # 9. Conditional expectations
    # Expected value given being above mean
    if len(positive_devs) > 0:
        metrics['conditional_exp_above'] = np.mean(segment[above_mean])
        metrics['conditional_dev_above'] = np.mean(positive_devs)
    else:
        metrics['conditional_exp_above'] = metrics['conditional_dev_above'] = mean_val
    
    # Expected value given being below mean
    if len(negative_devs) > 0:
        metrics['conditional_exp_below'] = np.mean(segment[below_mean])
        metrics['conditional_dev_below'] = np.mean(negative_devs)
    else:
        metrics['conditional_exp_below'] = metrics['conditional_dev_below'] = mean_val
    
    # 10. Occupancy time (% time spent above/below mean)
    metrics['occupancy_above'] = metrics['prop_above_mean'] * 100
    metrics['occupancy_below'] = metrics['prop_below_mean'] * 100
    
    # Weighted occupancy (time * magnitude)
    if len(segment) > 0:
        weighted_above = np.sum(positive_devs) / len(segment) if len(positive_devs) > 0 else 0
        weighted_below = np.sum(np.abs(negative_devs)) / len(segment) if len(negative_devs) > 0 else 0
        metrics['weighted_occupancy_above'] = weighted_above
        metrics['weighted_occupancy_below'] = weighted_below
        metrics['occupancy_asymmetry'] = weighted_above / weighted_below if weighted_below > 0 else np.inf
    
    # 11. Additional persistence metrics
    # Autocorrelation of deviations
    if len(deviations) > 10:
        try:
            autocorr_dev = np.corrcoef(deviations[:-1], deviations[1:])[0, 1]
            metrics['deviation_autocorr'] = autocorr_dev if not np.isnan(autocorr_dev) else 0
        except:
            metrics['deviation_autocorr'] = 0
    else:
        metrics['deviation_autocorr'] = 0
    
    # Variance of run lengths
    all_runs = above_runs + below_runs
    if len(all_runs) > 1:
        metrics['run_length_variance'] = np.var(all_runs)
        metrics['run_length_cv'] = np.std(all_runs) / np.mean(all_runs) if np.mean(all_runs) > 0 else 0
    else:
        metrics['run_length_variance'] = metrics['run_length_cv'] = 0
    
    return metrics

def compute_original_conditional_features(pre_segment, post_segment):
    """Compute original conditional expectation features (35 features)"""
    features = {}
    
    # Get segment means
    pre_mean = np.mean(pre_segment)
    post_mean = np.mean(post_segment)
    
    # Split segments by their respective means
    pre_above = pre_segment[pre_segment > pre_mean]
    pre_below = pre_segment[pre_segment < pre_mean]
    post_above = post_segment[post_segment > post_mean]
    post_below = post_segment[post_segment < post_mean]
    
    # 1. Conditional expectations
    features['pre_conditional_exp_above'] = np.mean(pre_above) if len(pre_above) > 0 else pre_mean
    features['pre_conditional_exp_below'] = np.mean(pre_below) if len(pre_below) > 0 else pre_mean
    features['post_conditional_exp_above'] = np.mean(post_above) if len(post_above) > 0 else post_mean
    features['post_conditional_exp_below'] = np.mean(post_below) if len(post_below) > 0 else post_mean
    
    # 2. Conditional deviations
    pre_dev_above = pre_above - pre_mean if len(pre_above) > 0 else np.array([0])
    pre_dev_below = pre_below - pre_mean if len(pre_below) > 0 else np.array([0])
    post_dev_above = post_above - post_mean if len(post_above) > 0 else np.array([0])
    post_dev_below = post_below - post_mean if len(post_below) > 0 else np.array([0])
    
    features['pre_conditional_dev_above'] = np.mean(pre_dev_above)
    features['pre_conditional_dev_below'] = np.mean(pre_dev_below)
    features['post_conditional_dev_above'] = np.mean(post_dev_above)
    features['post_conditional_dev_below'] = np.mean(post_dev_below)
    
    # 3. Conditional standard deviations
    features['pre_conditional_std_above'] = np.std(pre_above, ddof=1) if len(pre_above) > 1 else 0
    features['pre_conditional_std_below'] = np.std(pre_below, ddof=1) if len(pre_below) > 1 else 0
    features['post_conditional_std_above'] = np.std(post_above, ddof=1) if len(post_above) > 1 else 0
    features['post_conditional_std_below'] = np.std(post_below, ddof=1) if len(post_below) > 1 else 0
    
    # 4. Conditional proportions
    features['pre_prop_above'] = len(pre_above) / len(pre_segment)
    features['pre_prop_below'] = len(pre_below) / len(pre_segment)
    features['post_prop_above'] = len(post_above) / len(post_segment)
    features['post_prop_below'] = len(post_below) / len(post_segment)
    
    # 5. Changes in conditional expectations
    features['conditional_exp_above_shift'] = features['post_conditional_exp_above'] - features['pre_conditional_exp_above']
    features['conditional_exp_below_shift'] = features['post_conditional_exp_below'] - features['pre_conditional_exp_below']
    
    # 6. Changes in conditional deviations
    features['conditional_dev_above_shift'] = features['post_conditional_dev_above'] - features['pre_conditional_dev_above']
    features['conditional_dev_below_shift'] = features['post_conditional_dev_below'] - features['pre_conditional_dev_below']
    
    # 7. Changes in conditional volatility
    features['conditional_std_above_shift'] = features['post_conditional_std_above'] - features['pre_conditional_std_above']
    features['conditional_std_below_shift'] = features['post_conditional_std_below'] - features['pre_conditional_std_below']
    
    # 8. Changes in proportions
    features['prop_above_shift'] = features['post_prop_above'] - features['pre_prop_above']
    features['prop_below_shift'] = features['post_prop_below'] - features['pre_prop_below']
    
    # 9. Asymmetry measures
    features['pre_conditional_asymmetry'] = features['pre_conditional_exp_above'] - features['pre_conditional_exp_below']
    features['post_conditional_asymmetry'] = features['post_conditional_exp_above'] - features['post_conditional_exp_below']
    features['conditional_asymmetry_change'] = features['post_conditional_asymmetry'] - features['pre_conditional_asymmetry']
    
    # 10. Ratios (avoid division by zero)
    if abs(features['pre_conditional_exp_above']) > 1e-10:
        features['conditional_exp_above_ratio'] = features['post_conditional_exp_above'] / features['pre_conditional_exp_above']
    else:
        features['conditional_exp_above_ratio'] = 1.0
    
    if abs(features['pre_conditional_exp_below']) > 1e-10:
        features['conditional_exp_below_ratio'] = features['post_conditional_exp_below'] / features['pre_conditional_exp_below']
    else:
        features['conditional_exp_below_ratio'] = 1.0
    
    # 11. Normalized changes
    if abs(features['pre_conditional_exp_above']) > 1e-10:
        features['conditional_exp_above_norm_change'] = features['conditional_exp_above_shift'] / abs(features['pre_conditional_exp_above'])
    else:
        features['conditional_exp_above_norm_change'] = 0.0
    
    if abs(features['pre_conditional_exp_below']) > 1e-10:
        features['conditional_exp_below_norm_change'] = features['conditional_exp_below_shift'] / abs(features['pre_conditional_exp_below'])
    else:
        features['conditional_exp_below_norm_change'] = 0.0
    
    # 12. Cross-regime comparisons
    features['pre_above_vs_below_diff'] = features['pre_conditional_exp_above'] - features['pre_conditional_exp_below']
    features['post_above_vs_below_diff'] = features['post_conditional_exp_above'] - features['post_conditional_exp_below']
    features['above_vs_below_diff_change'] = features['post_above_vs_below_diff'] - features['pre_above_vs_below_diff']
    
    # 13. Regime balance
    features['pre_regime_balance'] = features['pre_prop_above'] - 0.5  # Centered around 0
    features['post_regime_balance'] = features['post_prop_above'] - 0.5
    features['regime_balance_shift'] = features['post_regime_balance'] - features['pre_regime_balance']
    
    return features
def compute_histogram_based_conditional_features(pre_segment, post_segment, n_bins=20):
    """Compute same conditional features but using histogram bins instead of raw values"""
    features = {}
    
    # Convert to histogram representation
    combined_data = np.concatenate([pre_segment, post_segment])
    hist_range = (np.min(combined_data), np.max(combined_data))
    
    pre_hist, bin_edges = np.histogram(pre_segment, bins=n_bins, range=hist_range)
    post_hist, _ = np.histogram(post_segment, bins=n_bins, range=hist_range)
    
    # Use bin centers as "values" and counts as "frequencies"
    bin_centers = (bin_edges[:-1] + bin_edges[1:]) / 2
    
    # Expand histogram back to pseudo-raw data (repeat bin_centers by counts)
    pre_pseudo = np.repeat(bin_centers, pre_hist)
    post_pseudo = np.repeat(bin_centers, post_hist)
    
    if len(pre_pseudo) == 0 or len(post_pseudo) == 0:
        return {f'hist_{k}': np.nan for k in ['conditional_exp_above_shift', 'conditional_exp_below_shift', 'prop_above_shift']}
    
    # Apply same logic as original function but on histogram data
    pre_mean = np.mean(pre_pseudo)
    post_mean = np.mean(post_pseudo)
    
    pre_above = pre_pseudo[pre_pseudo > pre_mean]
    pre_below = pre_pseudo[pre_pseudo < pre_mean]
    post_above = post_pseudo[post_pseudo > post_mean]
    post_below = post_pseudo[post_pseudo < post_mean]
    
    # Key features for comparison
    pre_cond_exp_above = np.mean(pre_above) if len(pre_above) > 0 else pre_mean
    pre_cond_exp_below = np.mean(pre_below) if len(pre_below) > 0 else pre_mean
    post_cond_exp_above = np.mean(post_above) if len(post_above) > 0 else post_mean
    post_cond_exp_below = np.mean(post_below) if len(post_below) > 0 else post_mean
    
    features['hist_conditional_exp_above_shift'] = post_cond_exp_above - pre_cond_exp_above
    features['hist_conditional_exp_below_shift'] = post_cond_exp_below - pre_cond_exp_below
    features['hist_prop_above_shift'] = (len(post_above)/len(post_pseudo)) - (len(pre_above)/len(pre_pseudo))
    
    return features

def compute_statistical_test_features(pre_segment, post_segment):
    """Compute statistical tests on conditional distributions"""
    from scipy.stats import anderson_ksamp
    
    features = {}
    
    # Get segment means
    pre_mean = np.mean(pre_segment)
    post_mean = np.mean(post_segment)
    
    # Split segments by their respective means
    pre_above = pre_segment[pre_segment > pre_mean]
    pre_below = pre_segment[pre_segment < pre_mean]
    post_above = post_segment[post_segment > post_mean]
    post_below = post_segment[post_segment < post_mean]
    
    min_samples = 3
    
    # Above-mean regime tests
    if len(pre_above) >= min_samples and len(post_above) >= min_samples:
        try:
            t_stat, t_pval = stats.ttest_ind(pre_above, post_above)
            features['above_ttest_stat'] = t_stat
            features['above_ttest_pval'] = t_pval
        except:
            features['above_ttest_stat'] = features['above_ttest_pval'] = np.nan
        
        try:
            ks_stat, ks_pval = stats.ks_2samp(pre_above, post_above)
            features['above_ks_stat'] = ks_stat
            features['above_ks_pval'] = ks_pval
        except:
            features['above_ks_stat'] = features['above_ks_pval'] = np.nan
        
        try:
            ad_stat, ad_crit, ad_pval = anderson_ksamp([pre_above, post_above])
            features['above_ad_stat'] = ad_stat
            features['above_ad_pval'] = ad_pval
        except:
            features['above_ad_stat'] = features['above_ad_pval'] = np.nan
    else:
        features.update({
            'above_ttest_stat': np.nan, 'above_ttest_pval': 1.0,
            'above_ks_stat': np.nan, 'above_ks_pval': 1.0,
            'above_ad_stat': np.nan, 'above_ad_pval': 1.0
        })
    
    # Below-mean regime tests
    if len(pre_below) >= min_samples and len(post_below) >= min_samples:
        try:
            t_stat, t_pval = stats.ttest_ind(pre_below, post_below)
            features['below_ttest_stat'] = t_stat
            features['below_ttest_pval'] = t_pval
        except:
            features['below_ttest_stat'] = features['below_ttest_pval'] = np.nan
        
        try:
            ks_stat, ks_pval = stats.ks_2samp(pre_below, post_below)
            features['below_ks_stat'] = ks_stat
            features['below_ks_pval'] = ks_pval
        except:
            features['below_ks_stat'] = features['below_ks_pval'] = np.nan
        
        try:
            ad_stat, ad_crit, ad_pval = anderson_ksamp([pre_below, post_below])
            features['below_ad_stat'] = ad_stat
            features['below_ad_pval'] = ad_pval
        except:
            features['below_ad_stat'] = features['below_ad_pval'] = np.nan
    else:
        features.update({
            'below_ttest_stat': np.nan, 'below_ttest_pval': 1.0,
            'below_ks_stat': np.nan, 'below_ks_pval': 1.0,
            'below_ad_stat': np.nan, 'below_ad_pval': 1.0
        })
    
    # Combined significance scores
    p_values = [features.get(f'{regime}_{test}_pval', 1.0) 
                for regime in ['above', 'below'] 
                for test in ['ttest', 'ks', 'ad']]
    p_values = [p for p in p_values if not np.isnan(p)]
    
    if p_values:
        features['min_pvalue'] = np.min(p_values)
        features['mean_pvalue'] = np.mean(p_values)
        features['significant_tests'] = np.sum(np.array(p_values) < 0.05)
    else:
        features['min_pvalue'] = features['mean_pvalue'] = 1.0
        features['significant_tests'] = 0
    
    return features

def process_single_series(args):
    """Process a single time series - for parallel processing"""
    i, series, label = args
    
    series = np.array(series)
    if len(series) < 20:
        return None
    
    # Split into pre/post segments
    mid = len(series) // 2
    pre_seg = series[:mid]
    post_seg = series[mid:]
    
    if len(pre_seg) < 10 or len(post_seg) < 10:
        return None
    
    try:
        # Compute original conditional features (35 features)
        original_conditional = compute_original_conditional_features(pre_seg, post_seg)
        
        # Compute statistical test features
        statistical_test_features = compute_statistical_test_features(pre_seg, post_seg)
        # Add after original_conditional = compute_original_conditional_features(pre_seg, post_seg)
        histogram_conditional = compute_histogram_based_conditional_features(pre_seg, post_seg)

        # Update features dict
        features = {
            'series_id': i,
            'label': label,
            **original_conditional,
            **histogram_conditional,  # Add this line
            **statistical_test_features
        }

        # # Combine all features
        # features = {
        #     'series_id': i,
        #     'label': label,
        #     **original_conditional,
        #     **statistical_test_features
        # }
        
        return features
    except Exception as e:
        print(f"   ⚠️  Error processing series {i}: {e}")
        return None

def extract_all_features(time_series_list, labels):
    """Extract all deviation-based features from time series with parallel processing"""
    from multiprocessing import Pool, cpu_count
    import time
    
    print("\n🔧 Extracting deviation-based features with parallel processing...")
    print(f"   Using {cpu_count()} CPU cores")
    
    # Prepare arguments for parallel processing
    args_list = [(i, series, label) for i, (series, label) in enumerate(zip(time_series_list, labels))]
    
    all_features = []
    batch_size = 1000
    total_batches = (len(args_list) + batch_size - 1) // batch_size
    
    start_time = time.time()
    
    # Process in batches to show progress
    for batch_idx in range(total_batches):
        batch_start = batch_idx * batch_size
        batch_end = min((batch_idx + 1) * batch_size, len(args_list))
        batch_args = args_list[batch_start:batch_end]
        
        print(f"   📊 Processing batch {batch_idx + 1}/{total_batches} ({batch_start+1}-{batch_end}/{len(args_list)})...")
        
        # Process batch in parallel
        with Pool(processes=cpu_count()) as pool:
            batch_results = pool.map(process_single_series, batch_args)
        
        # Filter out None results and add to main list
        valid_results = [result for result in batch_results if result is not None]
        all_features.extend(valid_results)
        
        # Progress update
        elapsed_time = time.time() - start_time
        processed_count = batch_end
        if processed_count > 0:
            estimated_total_time = elapsed_time * len(args_list) / processed_count
            remaining_time = estimated_total_time - elapsed_time
            print(f"      ⏱️  Processed: {len(valid_results)}/{batch_end-batch_start} valid | "
                  f"Elapsed: {elapsed_time:.1f}s | ETA: {remaining_time:.1f}s")
    
    df = pd.DataFrame(all_features)
    total_time = time.time() - start_time
    
    print(f"   ✅ Extracted {len(df.columns)-2} features for {len(df)} series in {total_time:.1f}s")
    print(f"   📈 Processing rate: {len(df)/total_time:.1f} series/second")
    
    return df

def evaluate_feature_importance(features_df):
    """Evaluate feature importance for AUROC improvement"""
    print("\n🎯 Evaluating feature importance...")
    
    # Prepare data
    y = features_df['label'].values
    feature_cols = [col for col in features_df.columns if col not in ['series_id', 'label']]
    X = features_df[feature_cols].copy()
    
    # Handle infinite values and NaNs
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    
    print(f"   📊 Dataset: {len(y)} samples, {X.shape[1]} features")
    print(f"   📊 Positive class: {np.sum(y)} ({np.sum(y)/len(y)*100:.1f}%)")
    
    # Feature selection using univariate statistics
    selector = SelectKBest(score_func=f_classif, k='all')
    X_selected = selector.fit_transform(X, y)
    
    # Get feature scores
    feature_scores = selector.scores_
    feature_pvalues = selector.pvalues_
    
    # Create feature importance dataframe
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'f_score': feature_scores,
        'p_value': feature_pvalues,
        'log_p_value': -np.log10(feature_pvalues + 1e-10)
    })
    
    importance_df = importance_df.sort_values('f_score', ascending=False)
    
    print(f"\n🔝 TOP 15 MOST IMPORTANT FEATURES:")
    for i, (_, row) in enumerate(importance_df.head(15).iterrows()):
        significance = "***" if row['p_value'] < 0.001 else "**" if row['p_value'] < 0.01 else "*" if row['p_value'] < 0.05 else ""
        print(f"   {i+1:2d}. {row['feature']:35} F={row['f_score']:8.2f} {significance}")
    
    return importance_df

def evaluate_conditional_features(features_df):
    """Compare raw vs histogram-based features"""
    print("\n📊 Comparing raw vs histogram-based features...")
    
    y = features_df['label'].values
    
    # Raw features (original)
    raw_features = [col for col in features_df.columns if not col.startswith('hist_') and col not in ['series_id', 'label']]
    
    # Histogram features
    hist_features = [col for col in features_df.columns if col.startswith('hist_')]
    
    results = {}
    
    for name, feature_list in [('raw', raw_features), ('histogram', hist_features)]:
        if not feature_list:
            continue
            
        print(f"   Testing {name} ({len(feature_list)} features)...")
        
        X = features_df[feature_list].copy()
        X = X.replace([np.inf, -np.inf], np.nan).fillna(X.median())
        
        model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
        
        results[name] = {
            'mean_auroc': cv_scores.mean(),
            'std_auroc': cv_scores.std(),
            'n_features': len(feature_list)
        }
        
        print(f"     AUROC: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
    
    return results


def create_comprehensive_visualization(features_df, importance_df, category_results):
    """Create comprehensive visualization of results"""
    print("\n📊 Creating visualization...")
    
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    
    # Plot 1: Top feature importance
    top_features = importance_df.head(15)
    axes[0, 0].barh(range(len(top_features)), top_features['f_score'])
    axes[0, 0].set_yticks(range(len(top_features)))
    axes[0, 0].set_yticklabels([f.replace('_', ' ')[:20] for f in top_features['feature']], fontsize=8)
    axes[0, 0].set_xlabel('F-Score')
    axes[0, 0].set_title('Top 15 Feature Importance')
    axes[0, 0].invert_yaxis()
    
    # Plot 2: Feature category performance
    if category_results:
        categories = list(category_results.keys())
        auroc_scores = [category_results[cat]['mean_auroc'] for cat in categories]
        
        axes[0, 1].bar(range(len(categories)), auroc_scores)
        axes[0, 1].set_xticks(range(len(categories)))
        axes[0, 1].set_xticklabels(categories, rotation=45, ha='right')
        axes[0, 1].set_ylabel('AUROC Score')
        axes[0, 1].set_title('Performance by Feature Category')
        axes[0, 1].grid(True, alpha=0.3)
    
    # Plot 3: P-value distribution
    axes[0, 2].hist(-np.log10(importance_df['p_value'] + 1e-10), bins=30, alpha=0.7, edgecolor='black')
    axes[0, 2].axvline(-np.log10(0.05), color='red', linestyle='--', label='p=0.05')
    axes[0, 2].axvline(-np.log10(0.001), color='orange', linestyle='--', label='p=0.001')
    axes[0, 2].set_xlabel('-log10(p-value)')
    axes[0, 2].set_ylabel('Frequency')
    axes[0, 2].set_title('Feature Significance Distribution')
    axes[0, 2].legend()
    
    # Plot 4: Feature count by category
    if category_results:
        feature_counts = [category_results[cat]['n_features'] for cat in categories]
        axes[0, 3].bar(range(len(categories)), feature_counts)
        axes[0, 3].set_xticks(range(len(categories)))
        axes[0, 3].set_xticklabels(categories, rotation=45, ha='right')
        axes[0, 3].set_ylabel('Number of Features')
        axes[0, 3].set_title('Features per Category')
    
    # Plot 5-8: Distribution comparisons for top features
    top_4_features = importance_df.head(4)['feature'].tolist()
    
    for i, feature in enumerate(top_4_features):
        if i < 4 and feature in features_df.columns:
            row, col = 1 + i // 2, i % 2
            
            normal_data = features_df[features_df['label'] == 0][feature].replace([np.inf, -np.inf], np.nan).dropna()
            break_data = features_df[features_df['label'] == 1][feature].replace([np.inf, -np.inf], np.nan).dropna()
            
            if len(normal_data) > 0 and len(break_data) > 0:
                axes[row, col].hist([normal_data, break_data], bins=30, alpha=0.7, 
                                   label=['Normal', 'Break'], edgecolor='black')
                axes[row, col].set_xlabel(feature.replace('_', ' '))
                axes[row, col].set_ylabel('Frequency')
                axes[row, col].set_title(f'Distribution: {feature[:25]}')
                axes[row, col].legend()
    
    # Plot 9: Performance vs number of features
    if category_results:
        n_features = [category_results[cat]['n_features'] for cat in categories]
        performance = [category_results[cat]['mean_auroc'] for cat in categories]
        
        axes[2, 0].scatter(n_features, performance, alpha=0.7)
        for i, cat in enumerate(categories):
            axes[2, 0].annotate(cat[:8], (n_features[i], performance[i]), 
                               xytext=(5, 5), textcoords='offset points', fontsize=8)
        axes[2, 0].set_xlabel('Number of Features')
        axes[2, 0].set_ylabel('AUROC Score')
        axes[2, 0].set_title('Performance vs Feature Count')
        axes[2, 0].grid(True, alpha=0.3)
    
    # Plot 10-12: Summary statistics
    for i in range(3):
        axes[2, i+1].axis('off')
    
    # Summary text
    summary_text = "DEVIATION METRICS ANALYSIS\n\n"
    
    if category_results:
        best_category = max(category_results.keys(), key=lambda x: category_results[x]['mean_auroc'])
        best_score = category_results[best_category]['mean_auroc']
        
        summary_text += f"Best Category: {best_category}\n"
        summary_text += f"Best AUROC: {best_score:.4f}\n"
        summary_text += f"Features: {category_results[best_category]['n_features']}\n\n"
    
    # Top features summary
    summary_text += "Top 5 Features:\n"
    for i, (_, row) in enumerate(importance_df.head(5).iterrows()):
        summary_text += f"{i+1}. {row['feature'][:25]}\n"
    
    # Significant features
    significant_features = len(importance_df[importance_df['p_value'] < 0.05])
    summary_text += f"\nSignificant features: {significant_features}/{len(importance_df)}\n"
    
    axes[2, 1].text(0.1, 0.9, summary_text, transform=axes[2, 1].transAxes, 
                   fontsize=10, verticalalignment='top', fontfamily='monospace')
    
    plt.tight_layout()
    plt.savefig('deviation_metrics_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print("   ✅ Saved visualization to 'deviation_metrics_analysis.png'")

def main():
    """Main analysis function"""
    print("📊 DEVIATION METRICS ANALYSIS FOR AUROC IMPROVEMENT")
    print("=" * 60)
    
    # Load data
    time_series_list, labels = load_time_series_data()
    if time_series_list is None:
        return
    
    # Extract features
    features_df = extract_all_features(time_series_list, labels)
    
    # Evaluate feature importance
    importance_df = evaluate_feature_importance(features_df)
    
    # Evaluate conditional features
    category_results = evaluate_conditional_features(features_df)
    
    # Create visualization
    create_comprehensive_visualization(features_df, importance_df, category_results)
    
    # Save results
    features_df.to_csv('deviation_metrics_features.csv', index=False)
    importance_df.to_csv('feature_importance_analysis.csv', index=False)
    
    # Save category results
    category_summary = []
    for category, results in category_results.items():
        category_summary.append({
            'category': category,
            'mean_auroc': results['mean_auroc'],
            'std_auroc': results['std_auroc'],
            'n_features': results['n_features']
        })
    
    category_df = pd.DataFrame(category_summary)
    category_df.to_csv('category_performance_analysis.csv', index=False)
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Features: 'deviation_metrics_features.csv'")
    print(f"   📊 Importance: 'feature_importance_analysis.csv'")
    print(f"   📊 Categories: 'category_performance_analysis.csv'")
    print(f"   📈 Visualization: 'deviation_metrics_analysis.png'")
    
    return features_df, importance_df, category_results

if __name__ == "__main__":
    results = main()