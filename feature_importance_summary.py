"""
Feature Importance Analysis Summary for 1280 TSFRESHplusCatch22 Features
"""

import joblib
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# Load results
results = joblib.load('feature_importance_1280_results.joblib')

feature_importance = results['feature_importance']
overlapping_features = results['overlapping_features']
feature_names = results['feature_names']

print("="*80)
print("FEATURE IMPORTANCE ANALYSIS - 1280 TSFRESHplusCatch22 FEATURES")
print("="*80)

print(f"\nModels analyzed: {list(feature_importance.keys())}")
print(f"Total features: {len(feature_names)}")

print(f"\nTOP 20 MOST CONSISTENTLY IMPORTANT FEATURES:")
print("-" * 60)
for i, (feature, count) in enumerate(overlapping_features[:20]):
    print(f"{i+1:2d}. {feature[:55]:<55} ({count}/5 models)")

# Create detailed comparison
print(f"\nTOP 10 FEATURES PER MODEL:")
print("-" * 60)

for model_name, importance in feature_importance.items():
    top_10_idx = np.argsort(importance)[-10:][::-1]
    top_10_features = feature_names[top_10_idx]
    top_10_scores = importance[top_10_idx]
    
    print(f"\n{model_name.upper()}:")
    for i, (feat, score) in enumerate(zip(top_10_features, top_10_scores)):
        print(f"  {i+1:2d}. {feat[:50]:<50} ({score:.4f})")

# Feature categories analysis
print(f"\nFEATURE CATEGORY ANALYSIS:")
print("-" * 60)

categories = {
    'Statistical': ['mean_', 'std_', 'var_', 'skew_', 'kurt_'],
    'TSFRESH': ['energy_ratio_', 'fft_agg_', 'index_mass_', 'binned_entropy_', 'lempel_ziv_'],
    'Catch22': ['DN_', 'CO_', 'IN_', 'MD_', 'SB_', 'SP_', 'FC_', 'PD_', 'SC_'],
    'SSA': ['ssa_'],
    'Difference': ['diff_'],
    'Weighted': ['_weighted'],
    'Normalized': ['_norm_zscore'],
    'Binned': ['time_bin', 'value_bin'],
    'Statistical Tests': ['ad_stat', 'p_ad', 'cvm_stat', 'jsd', 'wass_dist']
}

category_counts = {}
top_50_features = [feat for feat, _ in overlapping_features[:50]]

for category, patterns in categories.items():
    count = sum(1 for feat in top_50_features 
                if any(pattern in feat for pattern in patterns))
    category_counts[category] = count

print("Top 50 features by category:")
for category, count in sorted(category_counts.items(), key=lambda x: x[1], reverse=True):
    print(f"  {category:<20}: {count:2d} features")

# Key insights
print(f"\nKEY INSIGHTS:")
print("-" * 60)
print("1. Most consistent features across all models:")
top_5_consistent = [feat for feat, count in overlapping_features[:5] if count == 5]
for feat in top_5_consistent:
    print(f"   • {feat}")

print("\n2. Feature types that perform well:")
print("   • Normalized features (_norm_zscore)")
print("   • Difference features (diff_)")
print("   • Statistical test features (jsd, ad_stat)")
print("   • SSA reconstruction error features")
print("   • Reoccurring pattern features")

print("\n3. Model preferences:")
print("   • LGB/CatBoost: Prefer GARCH, SSA, and difference features")
print("   • XGB: Prefers binned mean features and ratios")
print("   • RF/DT: Strong preference for statistical test features (jsd, ad_stat)")

print(f"\nAnalysis complete. Results saved in feature_importance_1280_results.joblib")