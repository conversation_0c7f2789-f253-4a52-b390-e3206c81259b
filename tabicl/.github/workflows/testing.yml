name: 'test'

on:
  push:
    branches:
      - "main"
      - "dev"
  pull_request:
    branches:
      - '*'

jobs:
  test:
    strategy:
      fail-fast: false
      matrix:
        os: [windows-latest, ubuntu-latest, macos-latest]
        python-version: ['3.9', '3.10', '3.11', '3.12', '3.13']
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          # Install a specific version of uv.
          version: "0.5.4"
      - name: Install hatch
        run: uv pip install --system hatch
      - name: Install swig
        run: uv pip install --system swig
      - name: Run tests
        run: hatch test
      - name: Upload coverage reports to Codecov
        uses: codecov/codecov-action@v4.5.0
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          slug: soda-inria/tabicl
