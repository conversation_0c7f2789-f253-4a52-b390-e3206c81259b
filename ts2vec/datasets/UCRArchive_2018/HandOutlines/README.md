# HandOutlines

This classification problem was created as part of <PERSON>'s PhD (see [1]). Data are outlines of hand derived from images used by <PERSON> et al. (see [2]). They are designed to test the efficacy of hand and bone outline detection and whether these outlines could be helpful in bone age prediction. A range of algorithms were applied to automatically extract the hand outlines from over 1300 images, and three human evaluators labelled the output of the image outlinings as correct or incorrect. The data is described in detail in [3].

Train size: 1000

Test size: 370

Missing value: No

Number of classses: 2

Time series length: 2709

Data donated by <PERSON> and <PERSON> (see [4], [5]).

[1] <PERSON>, <PERSON>. Predictive modelling of bone ageing. Diss. University of East Anglia, 2013.

[2] <PERSON>, <PERSON><PERSON>, et al. "Digital hand atlas and web-based bone age assessment: system design and implementation." Computerized medical imaging and graphics 24.5 (2000): 297-307.

[3] <PERSON>, <PERSON>, et al. "On the segmentation and classification of hand radiographs." International journal of neural systems 22.05 (2012): 1250020.

[4] https://www.uea.ac.uk/computing/machine-learning/predictive-modelling-of-bone-ageing

[5] http://www.timeseriesclassification.com/description.php?Dataset=HandOutlines