# BME

BME is a synthetic data set with three classes:  one class is characterized by a small positive bell arising at the initial period (Begin), one does not have any bell (Middle), one has a positive bell arising at the final period (End).

All series are constituted by a central plate. The central plates may be positive or negative. The discriminant is the presence or absence of a positive peak, at the beginning of series or at the end.

- Class 1: Begin
- Class 2: Middle
- Class 3: End

Train size: 30

Test size: 150

Missing value: No

Number of classses: 3

Time series length: 128

Data source: Université Joseph Fourier (Grenoble I), Laboratoire d’informatique de Grenoble(LIG), Equipe AMA