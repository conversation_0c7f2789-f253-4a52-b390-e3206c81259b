# CricketX

The series Cricket X, Y and Z are accelerometer data (in three dimensions) taken from actors performing cricket gestures. The twelve classes are different umpire signals: Cancel Call, Dead Ball, Four, Last Hour Leg Bye, No Ball, One Short, Out, Penalty Runs, Six, TV Replay, and Wide. Two accelerometers are mounted orthogonal to each other, thus acceleration is measured in 3D space. The accelerometers are housed in a small wrist watch sized enclosure worn in the form of a wrist band. The data were edited by donating authors to have the same length by appending low variance white noise (see [1]). The series are no longer aligned in their original dimensions, therefore they cannot be used for multivariate time series classification problem as in [4] and [5].

Train size: 390

Test size: 390

Missing value: No

Number of classses: 12

Time series length: 300

Data donated by <PERSON> and <PERSON><PERSON><PERSON> (see [1], [2], [3]).

[1] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. "Logical-shapelets: an expressive primitive for time series classification." Proceedings of the 17th ACM SIGKDD international conference on Knowledge discovery and data mining. ACM, 2011.

[2] http://alumni.cs.ucr.edu/~mueen/LogicalShapelet/

[3] http://www.timeseriesclassification.com/description.php?Dataset=CricketX

[4] <PERSON>, <PERSON>, et al. "Segmentation of intentional human gestures for sports video annotation." Proceedings of the 10th International Conference on Multimedia Modelling. IEEE, 2004. 

[5] Ko, Ming Hsiao, et al. "Online context recognition in multisensor systems using dynamic time warping." Proceedings of the 2005 International Conference on Intelligent Sensors, Sensor Networks and Information Processing. IEEE, 2005.