# Yoga

The data set was obtained by capturing two actors transiting between yoga poses in front of a green screen. The problem is to discriminate between one actor (male) and another (female). 

Each image was converted to a one dimensional series by finding the outline and measuring the distance of the outline to the centre. 

Train size: 300

Test size: 3000

Missing value: No

Number of classses: 2

Time series length: 426

Data donated by <PERSON> and <PERSON><PERSON><PERSON> (see [1]).

[1] http://www.timeseriesclassification.com/description.php?Dataset=Yoga