# InlineSkate

The InlineSkate data were collected from tests in professional inline speed skating by Dr<PERSON> <PERSON> (Department of Sports Medicine, University Marburg) and used in this context by <PERSON> in his PhD thesis "Time Series Knowledge Mining" [1] at the University of Marlberg, Germany. 

An athlete performed a standardized indoor test at a speed of 7.89 m/s on a large motor driven treadmill. EMG and kinematic data of the right body side were measured for 30 seconds, corresponding to 19 complete movement cycles. The EMG data of 7 major leg extensor and flexor muscles of the right body side were collected using bipolar surface electrodes attached to the relevant muscles. 

Each time series represents an angular measurement of the ankle during one movement cycle. Data have been truncated to equal length. We assume the prediction problem is to classify one of seven individuals, although this is not yet confirmed.

Train size: 100

Test size: 550

Missing value: No

Number of classses: 7

Time series length: 1882

Data donated by <PERSON> and <PERSON> (see [1], [2]).

[1] <PERSON><PERSON>, Fabian. Time series knowledge mining. Görich & Weiershäuser, 2006.

[2] http://www.timeseriesclassification.com/description.php?Dataset=InlineSkate