# Beef

Food spectrographs are used in chemometrics to classify food types, a task that has obvious applications in food safety and quality assurance. The beef data set consists of four classes of beef spectrograms, from pure beef and beef adulterated with varying degrees of offal. Further information can be found in the original paper by <PERSON><PERSON><PERSON> et al. [1]. The data were first used in the time series classification literature by <PERSON><PERSON><PERSON> et al. [2].

Train size: 30

Test size: 30

Missing value: No

Number of classses: 5

Time series length: 470

Data donated by <PERSON> and <PERSON> (see [1], [2], [3]).

[1] <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON>, and <PERSON>. "Detection of adulteration in cooked meat products by mid-infrared spectroscopy." Journal of agricultural and food chemistry 50.6 (2002): 1325-1329.

[2] <PERSON><PERSON>, <PERSON>, et al. "Transformation based ensembles for time series classification." Proceedings of the 2012 SIAM international conference on data mining. Society for Industrial and Applied Mathematics, 2012.

[3] http://www.timeseriesclassification.com/description.php?Dataset=Beef