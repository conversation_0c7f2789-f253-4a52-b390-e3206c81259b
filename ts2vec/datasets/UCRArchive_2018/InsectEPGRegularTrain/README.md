# InsectEPG data sets

The electrical penetration graph or EPG is a system used by biologists to study the interaction of insects with plants. EPG data capture voltage changes of the electrical circuit that connects insects and their food source.  

We make two data sets out of these data, InsectEPGRegularTrain and InsectEPGSmallTrain. As the names suggest, the two data sets share a same test set and only differ in the number of training instances.

## InsectEPGRegularTrain

Train size: 62

Test size: 249

Missing value: No

Number of classes: 3

Time series length: 601

## InsectEPGSmallTrain

Train size: 17

Test size: 249

Missing value: No

Number of classes: 3

Time series length: 601

There is nothing to infer from the order of examples in the train and test set.

Original data created by <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>, Lapointe <PERSON> (see [1], [2]. Edited by <PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON><PERSON>.

[1] <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Lapointe <PERSON> (2016) Machine Learning for Characterization of Insect Vector Feeding. PLOS Computational Biology 12(11): e1005158. 

[2] https://datadryad.org//resource/doi:10.5061/dryad.4931c
