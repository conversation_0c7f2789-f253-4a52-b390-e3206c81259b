# SwedishLeaf

*SwedishLeaf* is a data set of leaf outlines used by <PERSON><PERSON> in his MSc thesis [1]. 

The 15 tree classes are:
- 1. <PERSON>lmus carpinifolia 
- 2. Acer 
- 3. Salix aurita 
- 4. Quercus 
- 5. <PERSON><PERSON> incana 
- 6. <PERSON><PERSON> pubescens 
- 7. <PERSON><PERSON> alba 'Sericea' 
- 8. Populus tremula 
- 9. <PERSON><PERSON><PERSON> glabra 
- 10. Sorbus aucuparia 
- 11. Salix sinerea 
- 12. Populus 
- 13. Tilia 
- 14. Sorbus intermedia 
- 15. Fagus silvatica

Train size: 500

Test size: 625

Missing value: No

Number of classses: 15

Time series length: 128

Data donated by <PERSON><PERSON> (see [1], [2]).

[1] <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>. "Computer vision classification of leaves from swedish trees." (2001).

[2] http://www.timeseriesclassification.com/description.php?Dataset=SwedishLeaf