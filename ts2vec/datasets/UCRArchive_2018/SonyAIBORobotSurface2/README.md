# SonyAIBORobotSurface2

The task is to detect the type of surface the robot is walking on, that is cement or carpet for *SonyAIBORobotSurface1* and cement or carpet/field for *SonyAIBORobotSurface2*. Data are x-axis readings of the robot's built-in accelerometers. More details about this dataset can be found in [1].

Train size: 27

Test size: 953

Missing value: No

Number of classses: 2

Time series length: 65

Data donated by <PERSON><PERSON> and <PERSON> (see [1], [2], [3]).

[1] <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, and <PERSON>. "Logical-shapelets: an expressive primitive for time series classification." Proceedings of the 17th ACM SIGKDD international conference on Knowledge discovery and data mining. ACM, 2011.

[2] http://alumni.cs.ucr.edu/~mueen/LogicalShapelet/

[3] http://www.timeseriesclassification.com/description.php?Dataset=SonyAIBORobotSurface2