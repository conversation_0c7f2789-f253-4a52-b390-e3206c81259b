# MoteStrain

This sensor data were originally from <PERSON>. It was subsequently used in [1] and later formatted for classification by <PERSON><PERSON><PERSON>. The task is to distinguish between sensor *q8calibHumid* (a humidity measure) and sensor *q8calibHumTemp* (a temperature measure). The data have dropouts. In the original data the dropouts have value 0. This means they are difficult to detect with normalized data.

Train size: 20

Test size: 1252

Missing value: No

Number of classses: 2

Time series length: 84

Data donated by <PERSON>, <PERSON><PERSON>. Data edited by <PERSON><PERSON><PERSON>. (see [1], [2])

[1] <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, and <PERSON><PERSON>. "Online latent variable detection in sensor networks." Data Engineering, 2005. ICDE 2005. Proceedings. 21st International Conference on. IEEE, 2005.

[2] http://www.timeseriesclassification.com/description.php?Dataset=MoteStrain