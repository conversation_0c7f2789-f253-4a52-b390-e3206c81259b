# FordB

These data were originally used in a competition in the 2008 IEEE World Congress on Computational Intelligence. The classification problem is to diagnose whether a certain symptom exists or not in an automotive subsystem. Each case consists of 500 measurements of engine noise. 

There are two related problems, *FordA* and *FordB*. For *FordA*, both the train and test data were collected in typical operating conditions with minimal noise contamination. For *FordB*, the train data were collected in typical operating conditions, but the test data were collected under noisy conditions.

Train size: 3636

Test size: 810

Missing value: No

Number of classses: 2

Time series length: 500

Data donated by <PERSON> (see [1]).

[1] http://www.timeseriesclassification.com/description.php?Dataset=FordB