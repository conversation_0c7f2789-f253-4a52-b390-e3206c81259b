# ArrowHead

The ArrowHead data consist of outlines of the images of arrowheads. The shapes of the projectile points are converted into a time series using the angle-based method. The classification of projectile points is an important topic in anthropology. The classes are based on shape distinctions such as the presence and location of a notch in the arrow. The problem in the repository is a length normalised version of that used by <PERSON> and <PERSON><PERSON><PERSON> [1]. The three classes are "Avonlea", "<PERSON>lovis" and "Mix"."

Train size: 36

Test size: 175

Missing value: No

Number of classses: 3

Time series length: 251

Data donated by <PERSON><PERSON> and <PERSON><PERSON><PERSON> (see [1], [2], [3]).

[1] <PERSON>, <PERSON><PERSON>, and <PERSON><PERSON><PERSON>. "Time series shapelets: a new primitive for data mining." Proceedings of the 15th ACM SIGKDD international conference on Knowledge discovery and data mining. ACM, 2009.

[2] http://alumni.cs.ucr.edu/~lexiangy/shapelet.html

[3] http://www.timeseriesclassification.com/description.php?Dataset=ArrowHead