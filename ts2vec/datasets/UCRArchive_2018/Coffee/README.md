# Coffee

Food spectrographs are used in chemometrics to classify food types, a task that has obvious applications in food safety and quality assurance. The coffee dataset is a two-class problem to distinguish between Robusta and Aribica coffee beans. Further information can be found in the original paper (see [1]). The data were first used in the time series classification literature by <PERSON><PERSON><PERSON> et al. (see [2]).

Train size: 28

Test size: 28

Missing value: No

Number of classses: 2

Time series length: 286

Data donated by <PERSON> and <PERSON> (see [1], [2], [3]).

[1] <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, and <PERSON>. "Discrimination of Arabica and Robusta in instant coffee by Fourier transform infrared spectroscopy and chemometrics." Journal of agricultural and food chemistry 44.1 (1996): 170-174.

[2] <PERSON><PERSON><PERSON>, <PERSON>, et al. "Transformation based ensembles for time series classification." Proceedings of the 2012 SIAM international conference on data mining. Society for Industrial and Applied Mathematics, 2012.

[3] http://www.timeseriesclassification.com/description.php?Dataset=Coffee