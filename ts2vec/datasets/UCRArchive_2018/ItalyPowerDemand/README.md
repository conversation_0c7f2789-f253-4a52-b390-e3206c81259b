# ItalyPowerDemand

The data were derived from twelve monthly electrical power demand time series of Italy and first used in [1]. The classification task is to distinguish days from Oct to March (inclusive) and from April to September.

Train size: 67

Test size: 1029

Missing value: No

Number of classses: 2

Time series length: 24

Data donated by <PERSON><PERSON><PERSON> and <PERSON> (see [1], [2]).

[1] <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, et al. "Intelligent icons: Integrating lite-weight data mining and visualization into GUI operating systems." Data Mining, 2006. ICDM'06. Sixth International Conference on. IEEE, 2006.

[2] http://www.timeseriesclassification.com/description.php?Dataset=ItalyPowerDemand