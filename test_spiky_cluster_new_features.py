#!/usr/bin/env python3
"""
Test Spiky Cluster New Features
Evaluate directional movement features on volatile, calm, and spiky clusters
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and volatility patterns"""
    print("📊 Loading data...")
    
    # Load time series
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    # Load volatility patterns
    try:
        vol_df = pd.read_csv('volatility_pattern_analysis.csv')
        print(f"   ✅ Loaded {len(vol_df)} volatility patterns")
    except:
        print("   ⚠️ volatility_pattern_analysis.csv not found, creating patterns...")
        vol_df = create_volatility_patterns(time_series_list, labels)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels), vol_df

def create_volatility_patterns(time_series_list, labels):
    """Create basic volatility patterns if file doesn't exist"""
    patterns = []
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        series_clean = np.array(series)[~np.isnan(series)]
        if len(series_clean) < 10:
            continue
        
        # Basic volatility metrics
        std_val = np.std(series_clean, ddof=1)
        mean_val = np.mean(series_clean)
        cv = std_val / abs(mean_val) if mean_val != 0 else np.inf
        
        # Spike detection
        z_scores = np.abs((series_clean - mean_val) / std_val) if std_val > 0 else np.zeros_like(series_clean)
        spike_rate = np.sum(z_scores > 3) / len(series_clean)
        
        # Simple pattern classification
        if spike_rate > 0.05:
            pattern_type = 'spiky'
        elif cv > 5:
            pattern_type = 'volatile'
        elif cv < 1:
            pattern_type = 'calm'
        else:
            pattern_type = 'moderate'
        
        patterns.append({
            'series_id': i,
            'label': label,
            'pattern_type': pattern_type,
            'cv': cv,
            'spike_rate': spike_rate
        })
    
    return pd.DataFrame(patterns)

def compute_directional_features(series):
    """Compute directional movement features"""
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 3:
        return {}
    
    # Calculate returns/changes
    returns = np.diff(series_clean)
    
    # 1. Up/Down Ratio (Positive/Negative Deviations Ratio)
    pos_changes = returns[returns > 0]
    neg_changes = returns[returns < 0]
    
    pos_sum = np.sum(pos_changes) if len(pos_changes) > 0 else 0
    neg_sum = np.sum(np.abs(neg_changes)) if len(neg_changes) > 0 else 0
    
    up_down_ratio = pos_sum / neg_sum if neg_sum > 0 else np.inf
    
    # 2. Net Directional Strength Index (NDSI)
    ndsi = (pos_sum - neg_sum) / (pos_sum + neg_sum + 1e-9)
    
    # 3. Directional Frequency Ratio
    pos_count = len(pos_changes)
    neg_count = len(neg_changes)
    freq_ratio = pos_count / neg_count if neg_count > 0 else np.inf
    
    # 4. Average Velocity Imbalance
    avg_pos = np.mean(pos_changes) if len(pos_changes) > 0 else 0
    avg_neg = np.mean(np.abs(neg_changes)) if len(neg_changes) > 0 else 0
    velocity_imbalance = avg_pos / avg_neg if avg_neg > 0 else np.inf
    
    # 5. Peak-to-Trough Acceleration Ratio
    max_pos = np.max(pos_changes) if len(pos_changes) > 0 else 0
    max_neg = np.max(np.abs(neg_changes)) if len(neg_changes) > 0 else 0
    peak_trough_ratio = max_pos / max_neg if max_neg > 0 else np.inf
    
    # 6. Consecutive Streak Dominance
    def longest_streak(condition_array):
        if len(condition_array) == 0:
            return 0
        streaks = []
        current_streak = 0
        for val in condition_array:
            if val:
                current_streak += 1
            else:
                if current_streak > 0:
                    streaks.append(current_streak)
                current_streak = 0
        if current_streak > 0:
            streaks.append(current_streak)
        return max(streaks) if streaks else 0
    
    pos_streak = longest_streak(returns > 0)
    neg_streak = longest_streak(returns < 0)
    streak_dominance = pos_streak / neg_streak if neg_streak > 0 else np.inf
    
    # 7. Directional Consistency Index (Std of positive vs negative changes)
    pos_std = np.std(pos_changes) if len(pos_changes) > 1 else 0
    neg_std = np.std(np.abs(neg_changes)) if len(neg_changes) > 1 else 0
    consistency_index = pos_std / neg_std if neg_std > 0 else np.inf
    
    # 8. Reversal Propensity Ratio
    up_after_down = 0
    down_after_up = 0
    for i in range(1, len(returns)):
        if returns[i] > 0 and returns[i-1] < 0:
            up_after_down += 1
        elif returns[i] < 0 and returns[i-1] > 0:
            down_after_up += 1
    
    reversal_ratio = up_after_down / down_after_up if down_after_up > 0 else np.inf
    
    # 9. Kurtosis of Returns (Spikiness measure)
    returns_kurtosis = stats.kurtosis(returns) if len(returns) > 3 else 0
    
    # 10. Mean Crossings (Oscillation measure)
    mean_val = np.mean(series_clean)
    crossings = np.sum(np.diff(np.sign(series_clean - mean_val)) != 0)
    normalized_crossings = crossings / (len(series_clean) - 1) if len(series_clean) > 1 else 0
    
    # 11. Average Absolute Step Size
    avg_abs_step = np.mean(np.abs(returns)) if len(returns) > 0 else 0
    
    # 12. Longest Calm Streak
    calm_threshold = 0.01 * np.std(series_clean) if np.std(series_clean) > 0 else 0.01
    calm_periods = np.abs(returns) < calm_threshold
    longest_calm = longest_streak(calm_periods)
    normalized_calm_streak = longest_calm / len(returns) if len(returns) > 0 else 0
    
    return {
        'up_down_ratio': up_down_ratio,
        'ndsi': ndsi,
        'freq_ratio': freq_ratio,
        'velocity_imbalance': velocity_imbalance,
        'peak_trough_ratio': peak_trough_ratio,
        'streak_dominance': streak_dominance,
        'consistency_index': consistency_index,
        'reversal_ratio': reversal_ratio,
        'returns_kurtosis': returns_kurtosis,
        'normalized_crossings': normalized_crossings,
        'avg_abs_step': avg_abs_step,
        'normalized_calm_streak': normalized_calm_streak
    }

def extract_features_by_cluster(time_series_list, labels, vol_df, target_patterns=['volatile', 'calm', 'spiky']):
    """Extract directional features for specific volatility clusters"""
    print(f"🔧 Extracting features for patterns: {target_patterns}")
    
    # Filter for target patterns
    target_indices = vol_df[vol_df['pattern_type'].isin(target_patterns)]['series_id'].values
    
    all_features = []
    
    for i, series_id in enumerate(target_indices):
        if i % 100 == 0:
            print(f"   Processing {i+1}/{len(target_indices)}...")
        
        if series_id >= len(time_series_list):
            continue
        
        series = time_series_list[series_id]
        label = labels[series_id]
        pattern_type = vol_df[vol_df['series_id'] == series_id]['pattern_type'].iloc[0]
        
        # Compute directional features
        dir_features = compute_directional_features(series)
        
        # Add metadata
        dir_features['label'] = label
        dir_features['series_id'] = series_id
        dir_features['pattern_type'] = pattern_type
        
        all_features.append(dir_features)
    
    features_df = pd.DataFrame(all_features)
    features_df = features_df.replace([np.inf, -np.inf], np.nan)
    
    print(f"   ✅ Extracted {features_df.shape[1]-3} features for {len(features_df)} series")
    print(f"   📊 Pattern distribution:")
    for pattern in target_patterns:
        count = (features_df['pattern_type'] == pattern).sum()
        print(f"      {pattern}: {count} ({count/len(features_df)*100:.1f}%)")
    
    return features_df

def evaluate_features_by_pattern(features_df):
    """Evaluate directional features using cross-validation by pattern"""
    print("📈 Evaluating features by volatility pattern...")
    
    # Feature columns (exclude metadata)
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]
    
    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
    
    results = {}
    
    print(f"\n🔬 Cross-validation results by pattern:")
    print("=" * 60)
    
    # Evaluate each pattern separately
    for pattern in features_df['pattern_type'].unique():
        pattern_data = features_df[features_df['pattern_type'] == pattern]
        
        if len(pattern_data) < 20 or pattern_data['label'].nunique() < 2:
            print(f"{pattern:10}: Insufficient data (n={len(pattern_data)})")
            continue
        
        # Prepare data
        X = pattern_data[feature_cols].copy()
        X = X.fillna(X.median())
        y = pattern_data['label'].values
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Cross-validation
        try:
            cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
            
            results[pattern] = {
                'mean_auroc': cv_scores.mean(),
                'std_auroc': cv_scores.std(),
                'n_samples': len(pattern_data),
                'break_rate': y.mean(),
                'cv_scores': cv_scores
            }
            
            print(f"{pattern:10}: AUROC = {cv_scores.mean():.4f} ± {cv_scores.std():.4f} (n={len(pattern_data)}, break_rate={y.mean():.3f})")
            
        except Exception as e:
            print(f"{pattern:10}: Error - {e}")
            results[pattern] = {'mean_auroc': np.nan, 'std_auroc': np.nan, 'n_samples': len(pattern_data)}
    
    # Evaluate all patterns combined
    print(f"\n📊 Combined evaluation:")
    X_all = features_df[feature_cols].copy()
    X_all = X_all.fillna(X_all.median())
    y_all = features_df['label'].values
    
    scaler_all = StandardScaler()
    X_all_scaled = scaler_all.fit_transform(X_all)
    
    try:
        cv_scores_all = cross_val_score(model, X_all_scaled, y_all, cv=cv, scoring='roc_auc')
        results['combined'] = {
            'mean_auroc': cv_scores_all.mean(),
            'std_auroc': cv_scores_all.std(),
            'n_samples': len(features_df),
            'break_rate': y_all.mean(),
            'cv_scores': cv_scores_all
        }
        
        print(f"{'combined':10}: AUROC = {cv_scores_all.mean():.4f} ± {cv_scores_all.std():.4f} (n={len(features_df)}, break_rate={y_all.mean():.3f})")
        
    except Exception as e:
        print(f"{'combined':10}: Error - {e}")
    
    return results

def analyze_feature_importance(features_df):
    """Analyze which directional features are most important"""
    print("\n🔍 Analyzing feature importance...")
    
    # Feature columns
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]
    
    # Prepare data
    X = features_df[feature_cols].copy()
    X = X.fillna(X.median())
    y = features_df['label'].values
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # Train model and get feature importance
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
    model.fit(X_scaled, y)
    
    # Get feature importance
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)
    
    print(f"\n🔝 Top 10 most important directional features:")
    for _, row in importance_df.head(10).iterrows():
        print(f"   {row['feature']:25}: {row['importance']:.4f}")
    
    return importance_df

def analyze_pattern_differences(features_df):
    """Analyze differences between volatility patterns"""
    print("\n📊 Analyzing pattern differences...")
    
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]
    
    print(f"\n🔍 Feature means by pattern:")
    print("=" * 80)
    
    for feature in feature_cols:
        print(f"\n{feature.upper()}:")
        for pattern in features_df['pattern_type'].unique():
            pattern_data = features_df[features_df['pattern_type'] == pattern][feature]
            mean_val = pattern_data.mean()
            std_val = pattern_data.std()
            print(f"   {pattern:10}: {mean_val:8.4f} ± {std_val:6.4f}")
    
    # Statistical tests between patterns
    print(f"\n📈 Statistical significance tests (p-values):")
    print("=" * 60)
    
    patterns = features_df['pattern_type'].unique()
    
    for feature in feature_cols[:5]:  # Top 5 features only
        print(f"\n{feature}:")
        for i, pattern1 in enumerate(patterns):
            for pattern2 in patterns[i+1:]:
                data1 = features_df[features_df['pattern_type'] == pattern1][feature].dropna()
                data2 = features_df[features_df['pattern_type'] == pattern2][feature].dropna()
                
                if len(data1) > 5 and len(data2) > 5:
                    try:
                        _, p_val = stats.ttest_ind(data1, data2)
                        significance = "***" if p_val < 0.001 else "**" if p_val < 0.01 else "*" if p_val < 0.05 else ""
                        print(f"   {pattern1} vs {pattern2}: p={p_val:.4f} {significance}")
                    except:
                        pass

def create_summary_report(results, importance_df, features_df):
    """Create comprehensive summary report"""
    print(f"\n📋 DIRECTIONAL FEATURES SUMMARY REPORT")
    print("=" * 50)
    
    print(f"Dataset overview:")
    print(f"   Total series analyzed: {len(features_df)}")
    print(f"   Overall break rate: {features_df['label'].mean():.3f}")
    print(f"   Features extracted: {len([col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']])}")
    
    print(f"\nPattern performance ranking:")
    pattern_results = [(pattern, data['mean_auroc']) for pattern, data in results.items() 
                      if pattern != 'combined' and not np.isnan(data['mean_auroc'])]
    pattern_results.sort(key=lambda x: x[1], reverse=True)
    
    for i, (pattern, auroc) in enumerate(pattern_results):
        print(f"   {i+1}. {pattern}: AUROC = {auroc:.4f}")
    
    print(f"\nTop 5 most important features:")
    for i, (_, row) in enumerate(importance_df.head(5).iterrows()):
        print(f"   {i+1}. {row['feature']}: {row['importance']:.4f}")
    
    # Best performing directional features
    print(f"\nKey insights:")
    if 'up_down_ratio' in importance_df['feature'].values:
        up_down_importance = importance_df[importance_df['feature'] == 'up_down_ratio']['importance'].iloc[0]
        print(f"   • Up/Down Ratio importance: {up_down_importance:.4f}")
    
    if 'ndsi' in importance_df['feature'].values:
        ndsi_importance = importance_df[importance_df['feature'] == 'ndsi']['importance'].iloc[0]
        print(f"   • Net Directional Strength importance: {ndsi_importance:.4f}")
    
    if 'returns_kurtosis' in importance_df['feature'].values:
        kurtosis_importance = importance_df[importance_df['feature'] == 'returns_kurtosis']['importance'].iloc[0]
        print(f"   • Returns Kurtosis importance: {kurtosis_importance:.4f}")

def main():
    """Main pipeline execution"""
    print("🚀 SPIKY CLUSTER DIRECTIONAL FEATURES EVALUATION")
    print("=" * 60)
    
    # Load data
    time_series_list, labels, vol_df = load_data()
    
    # Extract directional features for target patterns
    features_df = extract_features_by_cluster(time_series_list, labels, vol_df)
    
    # Evaluate features by pattern
    results = evaluate_features_by_pattern(features_df)
    
    # Analyze feature importance
    importance_df = analyze_feature_importance(features_df)
    
    # Analyze pattern differences
    analyze_pattern_differences(features_df)
    
    # Create summary report
    create_summary_report(results, importance_df, features_df)
    
    # Save results
    features_df.to_csv('directional_features_by_pattern.csv', index=False)
    importance_df.to_csv('directional_feature_importance.csv', index=False)
    
    results_df = pd.DataFrame([
        {
            'pattern': pattern,
            'mean_auroc': data['mean_auroc'],
            'std_auroc': data['std_auroc'],
            'n_samples': data['n_samples'],
            'break_rate': data.get('break_rate', np.nan)
        }
        for pattern, data in results.items()
    ])
    results_df.to_csv('pattern_auroc_results.csv', index=False)
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Features by pattern: 'directional_features_by_pattern.csv'")
    print(f"   📊 Feature importance: 'directional_feature_importance.csv'")
    print(f"   📊 AUROC results: 'pattern_auroc_results.csv'")
    
    return features_df, results, importance_df

if __name__ == "__main__":
    results = main()