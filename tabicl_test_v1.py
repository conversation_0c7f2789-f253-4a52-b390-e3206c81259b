#!/usr/bin/env python3
"""
TabICL Test for Structural Break Detection
In-Context Learning with Tabular Transformer
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
import logging
from typing import Dict, List, Tuple
import warnings
from tqdm import tqdm
import random

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device - use CPU to avoid CUDA assertion errors
device = torch.device('cuda:0')  # Force CPU for stability
logger.info(f"Using device: {device}")

class TabularTransformer(nn.Module):
    """Simplified Tabular Transformer for In-Context Learning"""
    
    def __init__(self, feature_dim, d_model=256, nhead=8, num_layers=4, max_context_len=32):
        super(TabularTransformer, self).__init__()
        
        self.feature_dim = feature_dim
        self.d_model = d_model
        self.max_context_len = max_context_len
        
        # Feature projection
        self.feature_proj = nn.Linear(feature_dim, d_model)
        self.label_embedding = nn.Embedding(3, d_model)  # 0, 1, 2 (2 for query)
        
        # Positional encoding
        self.pos_embedding = nn.Parameter(torch.randn(max_context_len + 1, d_model))
        
        # Transformer
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model, 
            nhead=nhead, 
            dim_feedforward=d_model * 4,
            dropout=0.1,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=num_layers)
        
        # Output head
        self.output_head = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, 1),
            nn.Sigmoid()
        )
        
    def forward(self, features, labels, query_features):
        """
        Args:
            features: [batch_size, context_len, feature_dim]
            labels: [batch_size, context_len] 
            query_features: [batch_size, feature_dim]
        """
        batch_size, context_len = features.shape[:2]
        
        # Project features
        context_embeds = self.feature_proj(features)  # [batch, context_len, d_model]
        query_embed = self.feature_proj(query_features).unsqueeze(1)  # [batch, 1, d_model]
        
        # Add label embeddings to context
        label_embeds = self.label_embedding(labels)  # [batch, context_len, d_model]
        context_embeds = context_embeds + label_embeds
        
        # Add query with special token (label=2)
        query_label = torch.full((batch_size, 1), 2, device=features.device)
        query_label_embed = self.label_embedding(query_label)
        query_embed = query_embed + query_label_embed
        
        # Concatenate context and query
        sequence = torch.cat([context_embeds, query_embed], dim=1)  # [batch, context_len+1, d_model]
        
        # Add positional encoding
        seq_len = sequence.shape[1]
        sequence = sequence + self.pos_embedding[:seq_len].unsqueeze(0)
        
        # Apply transformer
        output = self.transformer(sequence)  # [batch, context_len+1, d_model]
        
        # Extract query prediction (last token)
        query_output = output[:, -1, :]  # [batch, d_model]
        
        # Predict
        prediction = self.output_head(query_output)  # [batch, 1]
        
        return prediction.squeeze(-1)  # [batch]

class TabICLPredictor:
    """TabICL predictor with context selection and training"""
    
    def __init__(self, feature_dim, context_size=16, d_model=256, nhead=8, num_layers=4):
        self.context_size = context_size
        self.model = TabularTransformer(
            feature_dim=feature_dim,
            d_model=d_model,
            nhead=nhead,
            num_layers=num_layers,
            max_context_len=context_size
        ).to(device)
        
    def select_context(self, X_train, y_train, X_query, strategy='balanced'):
        """Select context examples for in-context learning"""
        if strategy == 'balanced':
            # Select balanced examples from both classes
            pos_indices = np.where(y_train == 1)[0]
            neg_indices = np.where(y_train == 0)[0]
            
            n_pos = min(self.context_size // 2, len(pos_indices))
            n_neg = min(self.context_size - n_pos, len(neg_indices))
            
            selected_pos = np.random.choice(pos_indices, n_pos, replace=False)
            selected_neg = np.random.choice(neg_indices, n_neg, replace=False)
            
            context_indices = np.concatenate([selected_pos, selected_neg])
            np.random.shuffle(context_indices)
            
        elif strategy == 'random':
            # Random selection
            context_indices = np.random.choice(len(X_train), 
                                             min(self.context_size, len(X_train)), 
                                             replace=False)
        
        return context_indices
    
    def create_batch(self, X_train, y_train, X_queries, y_queries=None):
        """Create batch for training/inference"""
        batch_size = len(X_queries)
        
        batch_features = []
        batch_labels = []
        batch_queries = []
        
        for i in range(batch_size):
            # Select context for this query
            context_indices = self.select_context(X_train, y_train, X_queries[i])
            
            # Get context features and labels
            context_features = X_train[context_indices]
            context_labels = y_train[context_indices]
            
            # Pad if necessary
            if len(context_features) < self.context_size:
                pad_size = self.context_size - len(context_features)
                context_features = np.vstack([
                    context_features,
                    np.zeros((pad_size, context_features.shape[1]))
                ])
                context_labels = np.concatenate([context_labels, np.zeros(pad_size)])
            
            batch_features.append(context_features)
            batch_labels.append(context_labels)
            batch_queries.append(X_queries[i])
        
        return (
            torch.FloatTensor(batch_features).to(device),
            torch.LongTensor(batch_labels).to(device),
            torch.FloatTensor(batch_queries).to(device)
        )
    
    def train_epoch(self, X_train, y_train, X_val, y_val, optimizer, batch_size=512):
        """Train for one epoch"""
        self.model.train()
        total_loss = 0
        n_batches = 0
        
        # Create training batches
        indices = np.arange(len(X_val))
        np.random.shuffle(indices)
        
        for i in range(0, len(indices), batch_size):
            batch_indices = indices[i:i+batch_size]
            X_batch = X_val[batch_indices]
            y_batch = y_val[batch_indices]
            
            # Create ICL batch
            features, labels, queries = self.create_batch(X_train, y_train, X_batch, y_batch)
            
            # Forward pass
            predictions = self.model(features, labels, queries)
            
            # Check for NaN/Inf and handle them
            if torch.isnan(predictions).any() or torch.isinf(predictions).any():
                predictions = torch.where(torch.isnan(predictions) | torch.isinf(predictions), 
                                        torch.tensor(0.5, device=predictions.device), predictions)
            
            # Loss - clamp predictions to valid range and add gradient clipping
            predictions_clamped = torch.clamp(predictions, 1e-7, 1-1e-7)
            loss = nn.BCELoss()(predictions_clamped, torch.FloatTensor(y_batch).to(device))
            
            # Skip batch if loss is invalid
            if torch.isnan(loss) or torch.isinf(loss):
                continue
            
            # Backward pass with gradient clipping
            optimizer.zero_grad()
            loss.backward()
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
            optimizer.step()
            
            total_loss += loss.item()
            n_batches += 1
        
        return total_loss / n_batches if n_batches > 0 else 0
    
    def predict(self, X_train, y_train, X_test, batch_size=32):
        """Make predictions using in-context learning"""
        self.model.eval()
        predictions = []
        
        with torch.no_grad():
            for i in range(0, len(X_test), batch_size):
                X_batch = X_test[i:i+batch_size]
                
                # Create ICL batch
                features, labels, queries = self.create_batch(X_train, y_train, X_batch)
                
                # Predict
                batch_preds = self.model(features, labels, queries)
                predictions.extend(batch_preds.cpu().numpy())
        
        return np.array(predictions)

def load_data():
    """Load prepared features and labels"""
    logger.info("📊 Loading prepared features and labels...")
    
    try:
        # Load features
        features_path = "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet"
        X = pd.read_parquet(features_path)
        logger.info(f"   ✅ Features loaded: {X.shape}")
        
        # Load labels
        y_data = pd.read_parquet('y_train.parquet')
        y = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
        y = y.astype(int)
        logger.info(f"   ✅ Labels loaded: {len(y)}")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X.values, y
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def evaluate_tabicl_cv(X, y, n_folds=5, context_size=16, n_epochs=20):
    """5-fold cross-validation evaluation of TabICL"""
    logger.info(f"📊 TabICL 5-fold CV evaluation (context_size={context_size})...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # Create TabICL predictor
        predictor = TabICLPredictor(
            feature_dim=X.shape[1],
            context_size=context_size,
            d_model=256,
            nhead=8,
            num_layers=4
        )
        
        # Train
        optimizer = torch.optim.Adam(predictor.model.parameters(), lr=1e-4, weight_decay=1e-5)
        
        for epoch in range(n_epochs):
            loss = predictor.train_epoch(X_train_scaled, y_train, X_val_scaled, y_val, optimizer)
            if epoch % 5 == 0:
                logger.info(f"     Epoch {epoch}: Loss = {loss:.4f}")
        
        # Predict
        y_pred_proba = predictor.predict(X_train_scaled, y_train, X_val_scaled)
        y_pred_binary = (y_pred_proba >= 0.5).astype(int)
        
        # Calculate metrics
        auc = roc_auc_score(y_val, y_pred_proba)
        f1 = f1_score(y_val, y_pred_binary)
        precision = precision_score(y_val, y_pred_binary, zero_division=0)
        recall = recall_score(y_val, y_pred_binary)
        accuracy = accuracy_score(y_val, y_pred_binary)
        
        # Confusion matrix
        tn, fp, fn, tp = confusion_matrix(y_val, y_pred_binary).ravel()
        
        # Store metrics
        all_metrics['auc'].append(auc)
        all_metrics['f1'].append(f1)
        all_metrics['precision'].append(precision)
        all_metrics['recall'].append(recall)
        all_metrics['accuracy'].append(accuracy)
        all_metrics['tp'].append(tp)
        all_metrics['tn'].append(tn)
        all_metrics['fp'].append(fp)
        all_metrics['fn'].append(fn)
        
        # Store for overall analysis
        all_y_true.extend(y_val)
        all_y_pred_proba.extend(y_pred_proba)
        
        logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
        
        # Clean up GPU memory
        del predictor
        torch.cuda.empty_cache()
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
    overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba),
        'fold_metrics': all_metrics
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting TabICL Structural Break Detection...")
    
    # Load data
    X, y = load_data()
    
    # Run 5-fold CV evaluation
    results = evaluate_tabicl_cv(X, y, n_folds=5, context_size=128, n_epochs=10)
    
    # Print final results
    logger.info("\n🎉 TABICL EVALUATION COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Calculate rates
    total_positives = cm[1,0] + cm[1,1]
    total_negatives = cm[0,0] + cm[0,1]
    
    logger.info(f"\n📈 DETAILED METRICS:")
    logger.info(f"   True Positive Rate (Recall):  {cm[1,1]/total_positives:.4f}")
    logger.info(f"   True Negative Rate:           {cm[0,0]/total_negatives:.4f}")
    logger.info(f"   False Positive Rate:          {cm[0,1]/total_negatives:.4f}")
    logger.info(f"   False Negative Rate:          {cm[1,0]/total_positives:.4f}")
    
    # Save results
    import joblib
    joblib.dump(results, 'tabicl_results.joblib')
    logger.info("💾 Results saved to 'tabicl_results.joblib'")
    
    return results

if __name__ == "__main__":
    results = main()