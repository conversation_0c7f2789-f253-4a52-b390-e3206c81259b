#!/usr/bin/env python3
"""
Test Length-Context Enhancement Methods V2
Tests hypothesis that temporal position from start has statistical meaning for structural break detection.
Implements time-weighted density features with incremental logic for pre-break segments.
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
from scipy.stats import entropy
from scipy.spatial.distance import jensenshannon
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and labels"""
    print("📊 Loading data...")
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels)

def compute_time_weighted_density_features(pre_segment, post_segment, bins=10, weight_factor=0.01):
    """
    Compute time-weighted density features testing temporal position hypothesis.
    
    Hypothesis: Earlier positions in time series have different structural break probability.
    Method: Add incremental weights to pre-segment values based on temporal position.
    
    Args:
        pre_segment: Pre-break segment
        post_segment: Post-break segment  
        bins: Number of histogram bins
        weight_factor: Small incremental weight factor (e.g., 0.01)
    """
    features = {}
    
    if len(pre_segment) < 3 or len(post_segment) < 3:
        return {f'tw_js_bins_{bins}': np.nan, f'tw_kl_bins_{bins}': np.nan, 
                f'tw_entropy_ratio': np.nan, f'tw_density_shift': np.nan}
    
    try:
        # Create time-weighted pre-segment
        # Earlier positions get smaller weights, later positions get larger weights
        time_weights = np.linspace(0, weight_factor, len(pre_segment))
        pre_weighted = pre_segment + time_weights
        
        # Compute combined range for consistent binning
        all_values = np.concatenate([pre_weighted, post_segment])
        value_range = (np.min(all_values), np.max(all_values))
        
        # Create histograms
        hist_pre_weighted, _ = np.histogram(pre_weighted, bins=bins, range=value_range, density=True)
        hist_post, _ = np.histogram(post_segment, bins=bins, range=value_range, density=True)
        
        # Normalize to probability distributions
        hist_pre_weighted = hist_pre_weighted / np.sum(hist_pre_weighted) if np.sum(hist_pre_weighted) > 0 else hist_pre_weighted
        hist_post = hist_post / np.sum(hist_post) if np.sum(hist_post) > 0 else hist_post
        
        # Add epsilon to avoid log(0)
        eps = 1e-10
        hist_pre_weighted = hist_pre_weighted + eps
        hist_post = hist_post + eps
        
        # Renormalize
        hist_pre_weighted = hist_pre_weighted / np.sum(hist_pre_weighted)
        hist_post = hist_post / np.sum(hist_post)
        
        # 1. Time-weighted Jensen-Shannon divergence
        tw_js_div = jensenshannon(hist_pre_weighted, hist_post)
        features[f'tw_js_bins_{bins}'] = tw_js_div
        
        # 2. Time-weighted KL divergence (pre->post)
        tw_kl_div = entropy(hist_pre_weighted, hist_post)
        features[f'tw_kl_bins_{bins}'] = tw_kl_div
        
        # 3. Time-weighted entropy ratio
        entropy_pre_weighted = entropy(hist_pre_weighted)
        entropy_post = entropy(hist_post)
        tw_entropy_ratio = entropy_post / (entropy_pre_weighted + eps)
        features['tw_entropy_ratio'] = tw_entropy_ratio
        
        # 4. Time-weighted density shift (center of mass difference)
        bin_centers = np.linspace(value_range[0], value_range[1], bins)
        com_pre_weighted = np.sum(bin_centers * hist_pre_weighted)
        com_post = np.sum(bin_centers * hist_post)
        tw_density_shift = abs(com_post - com_pre_weighted)
        features['tw_density_shift'] = tw_density_shift
        
        # 5. Time-weighted variance ratio
        var_pre_weighted = np.var(pre_weighted, ddof=1)
        var_post = np.var(post_segment, ddof=1)
        tw_var_ratio = var_post / (var_pre_weighted + eps)
        features['tw_var_ratio'] = tw_var_ratio
        
        # 6. Time-weighted mean shift
        mean_pre_weighted = np.mean(pre_weighted)
        mean_post = np.mean(post_segment)
        tw_mean_shift = abs(mean_post - mean_pre_weighted)
        features['tw_mean_shift'] = tw_mean_shift
        
    except Exception as e:
        # Return NaN features on error
        features = {f'tw_js_bins_{bins}': np.nan, f'tw_kl_bins_{bins}': np.nan, 
                   'tw_entropy_ratio': np.nan, 'tw_density_shift': np.nan,
                   'tw_var_ratio': np.nan, 'tw_mean_shift': np.nan}
    
    return features

def compute_baseline_density_features(pre_segment, post_segment, bins=10):
    """Compute baseline density features without time weighting for comparison"""
    features = {}
    
    if len(pre_segment) < 3 or len(post_segment) < 3:
        return {f'baseline_js_bins_{bins}': np.nan, f'baseline_kl_bins_{bins}': np.nan,
                'baseline_entropy_ratio': np.nan, 'baseline_density_shift': np.nan}
    
    try:
        # Compute combined range
        all_values = np.concatenate([pre_segment, post_segment])
        value_range = (np.min(all_values), np.max(all_values))
        
        # Create histograms
        hist_pre, _ = np.histogram(pre_segment, bins=bins, range=value_range, density=True)
        hist_post, _ = np.histogram(post_segment, bins=bins, range=value_range, density=True)
        
        # Normalize
        hist_pre = hist_pre / np.sum(hist_pre) if np.sum(hist_pre) > 0 else hist_pre
        hist_post = hist_post / np.sum(hist_post) if np.sum(hist_post) > 0 else hist_post
        
        # Add epsilon
        eps = 1e-10
        hist_pre = hist_pre + eps
        hist_post = hist_post + eps
        
        # Renormalize
        hist_pre = hist_pre / np.sum(hist_pre)
        hist_post = hist_post / np.sum(hist_post)
        
        # Baseline features
        features[f'baseline_js_bins_{bins}'] = jensenshannon(hist_pre, hist_post)
        features[f'baseline_kl_bins_{bins}'] = entropy(hist_pre, hist_post)
        features['baseline_entropy_ratio'] = entropy(hist_post) / (entropy(hist_pre) + eps)
        
        bin_centers = np.linspace(value_range[0], value_range[1], bins)
        com_pre = np.sum(bin_centers * hist_pre)
        com_post = np.sum(bin_centers * hist_post)
        features['baseline_density_shift'] = abs(com_post - com_pre)
        
        features['baseline_var_ratio'] = np.var(post_segment, ddof=1) / (np.var(pre_segment, ddof=1) + eps)
        features['baseline_mean_shift'] = abs(np.mean(post_segment) - np.mean(pre_segment))
        
    except Exception:
        features = {f'baseline_js_bins_{bins}': np.nan, f'baseline_kl_bins_{bins}': np.nan,
                   'baseline_entropy_ratio': np.nan, 'baseline_density_shift': np.nan,
                   'baseline_var_ratio': np.nan, 'baseline_mean_shift': np.nan}
    
    return features

def compute_multi_weight_features(pre_segment, post_segment, weight_factors=[0.005, 0.01, 0.02, 0.05], bins=10):
    """Test multiple weight factors to find optimal temporal weighting"""
    all_features = {}
    
    for weight_factor in weight_factors:
        weight_features = compute_time_weighted_density_features(pre_segment, post_segment, bins, weight_factor)
        
        # Rename features to include weight factor
        for feat_name, feat_val in weight_features.items():
            new_name = feat_name.replace('tw_', f'tw_{weight_factor}_')
            all_features[new_name] = feat_val
    
    return all_features

def extract_temporal_hypothesis_features(time_series_list, labels, max_samples=2000):
    """Extract features testing temporal position hypothesis"""
    print("🔧 Extracting temporal position hypothesis features...")
    
    # Limit samples for faster testing
    if len(time_series_list) > max_samples:
        indices = np.random.choice(len(time_series_list), max_samples, replace=False)
        time_series_list = [time_series_list[i] for i in indices]
        labels = labels[indices]
    
    all_features = []
    bins_to_test = [10, 15, 20]  # Test different bin counts
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        if i % 500 == 0:
            print(f"   Processing {i+1}/{len(time_series_list)}...")
        
        series_clean = np.array(series)[~np.isnan(series)]
        if len(series_clean) < 10:
            continue
        
        # Split at midpoint
        split_point = len(series_clean) // 2
        pre = series_clean[:split_point]
        post = series_clean[split_point:]
        
        if len(pre) < 3 or len(post) < 3:
            continue
        
        series_features = {'label': label, 'series_id': i}
        
        # Test multiple bin counts
        for bins in bins_to_test:
            # Time-weighted features
            tw_features = compute_time_weighted_density_features(pre, post, bins)
            series_features.update(tw_features)
            
            # Baseline features for comparison
            baseline_features = compute_baseline_density_features(pre, post, bins)
            series_features.update(baseline_features)
        
        # Multi-weight features (only for bins=10 to avoid explosion)
        multi_weight_features = compute_multi_weight_features(pre, post, bins=10)
        series_features.update(multi_weight_features)
        
        # Additional temporal position features
        series_features['pre_length'] = len(pre)
        series_features['post_length'] = len(post)
        series_features['total_length'] = len(series_clean)
        series_features['pre_to_total_ratio'] = len(pre) / len(series_clean)
        
        all_features.append(series_features)
    
    features_df = pd.DataFrame(all_features)
    features_df = features_df.replace([np.inf, -np.inf], np.nan)
    
    print(f"   ✅ Extracted {features_df.shape[1]-2} features for {len(features_df)} series")
    return features_df

def evaluate_temporal_hypothesis(features_df):
    """Evaluate temporal position hypothesis using cross-validation"""
    print("📈 Evaluating temporal position hypothesis...")

    # Separate feature groups
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id']]

    # Group features by type
    time_weighted_features = [col for col in feature_cols if col.startswith('tw_')]
    baseline_features = [col for col in feature_cols if col.startswith('baseline_')]
    multi_weight_features = [col for col in feature_cols if 'tw_0.' in col]
    length_features = [col for col in feature_cols if 'length' in col or 'ratio' in col]

    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')

    results = {}

    # Prepare data
    X_all = features_df[feature_cols].copy()
    X_all = X_all.fillna(X_all.median())
    y = features_df['label'].values

    scaler = StandardScaler()

    # Test different feature groups
    feature_groups = {
        'time_weighted_only': time_weighted_features,
        'baseline_only': baseline_features,
        'multi_weight_only': multi_weight_features,
        'length_features_only': length_features,
        'time_weighted_plus_length': time_weighted_features + length_features,
        'baseline_plus_length': baseline_features + length_features,
        'all_features': feature_cols
    }

    print(f"\n🎯 TEMPORAL HYPOTHESIS EVALUATION:")
    print("=" * 60)

    for group_name, group_features in feature_groups.items():
        if not group_features:
            continue

        # Filter features that exist in dataframe
        existing_features = [f for f in group_features if f in X_all.columns]
        if not existing_features:
            continue

        X_group = X_all[existing_features].copy()

        try:
            X_group_scaled = scaler.fit_transform(X_group)
            scores = cross_val_score(model, X_group_scaled, y, cv=cv, scoring='roc_auc')

            results[group_name] = {
                'mean_auroc': scores.mean(),
                'std_auroc': scores.std(),
                'n_features': len(existing_features),
                'features': existing_features
            }

            print(f"   {group_name.upper()}: AUROC {scores.mean():.4f} ± {scores.std():.4f} "
                  f"(n_features={len(existing_features)})")

        except Exception as e:
            print(f"   {group_name.upper()}: Error - {str(e)}")

    return results

def analyze_weight_factor_impact(features_df):
    """Analyze impact of different weight factors"""
    print("\n🔍 Analyzing weight factor impact...")

    weight_factors = [0.005, 0.01, 0.02, 0.05]
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')

    y = features_df['label'].values
    scaler = StandardScaler()

    weight_results = {}

    for weight_factor in weight_factors:
        # Get features for this weight factor
        weight_features = [col for col in features_df.columns if f'tw_{weight_factor}_' in col]

        if not weight_features:
            continue

        X_weight = features_df[weight_features].copy()
        X_weight = X_weight.fillna(X_weight.median())

        try:
            X_weight_scaled = scaler.fit_transform(X_weight)
            scores = cross_val_score(model, X_weight_scaled, y, cv=cv, scoring='roc_auc')

            weight_results[weight_factor] = {
                'mean_auroc': scores.mean(),
                'std_auroc': scores.std(),
                'n_features': len(weight_features)
            }

            print(f"   Weight {weight_factor}: AUROC {scores.mean():.4f} ± {scores.std():.4f}")

        except Exception as e:
            print(f"   Weight {weight_factor}: Error - {str(e)}")

    # Find optimal weight factor
    if weight_results:
        best_weight = max(weight_results.items(), key=lambda x: x[1]['mean_auroc'])
        print(f"\n🏆 Best weight factor: {best_weight[0]} (AUROC: {best_weight[1]['mean_auroc']:.4f})")

    return weight_results

def compare_hypothesis_vs_baseline(results):
    """Compare temporal hypothesis features vs baseline"""
    print("\n📊 TEMPORAL HYPOTHESIS vs BASELINE COMPARISON:")
    print("=" * 60)

    if 'time_weighted_only' in results and 'baseline_only' in results:
        tw_auroc = results['time_weighted_only']['mean_auroc']
        baseline_auroc = results['baseline_only']['mean_auroc']

        improvement = tw_auroc - baseline_auroc
        improvement_pct = (improvement / baseline_auroc) * 100

        print(f"Time-weighted features: {tw_auroc:.4f}")
        print(f"Baseline features:      {baseline_auroc:.4f}")
        print(f"Improvement:           {improvement:+.4f} ({improvement_pct:+.2f}%)")

        if improvement > 0:
            print("✅ HYPOTHESIS SUPPORTED: Time-weighted features outperform baseline")
        else:
            print("❌ HYPOTHESIS NOT SUPPORTED: Baseline features outperform time-weighted")

    # Compare with length features
    if 'time_weighted_plus_length' in results and 'baseline_plus_length' in results:
        tw_length_auroc = results['time_weighted_plus_length']['mean_auroc']
        baseline_length_auroc = results['baseline_plus_length']['mean_auroc']

        improvement_with_length = tw_length_auroc - baseline_length_auroc
        improvement_pct_with_length = (improvement_with_length / baseline_length_auroc) * 100

        print(f"\nWith length features:")
        print(f"Time-weighted + length: {tw_length_auroc:.4f}")
        print(f"Baseline + length:      {baseline_length_auroc:.4f}")
        print(f"Improvement:           {improvement_with_length:+.4f} ({improvement_pct_with_length:+.2f}%)")

def create_hypothesis_summary(results, weight_results, features_df):
    """Create comprehensive summary of temporal hypothesis testing"""
    print(f"\n📋 TEMPORAL POSITION HYPOTHESIS SUMMARY")
    print("=" * 50)

    print(f"Hypothesis: Earlier temporal positions have different structural break probability")
    print(f"Method: Add incremental weights to pre-segment values based on position")
    print(f"Dataset: {len(features_df)} time series, break rate: {features_df['label'].mean():.3f}")

    # Best performing approach
    if results:
        best_approach = max(results.items(), key=lambda x: x[1]['mean_auroc'])
        print(f"\n🏆 Best approach: {best_approach[0]}")
        print(f"   AUROC: {best_approach[1]['mean_auroc']:.4f} ± {best_approach[1]['std_auroc']:.4f}")
        print(f"   Features: {best_approach[1]['n_features']}")

    # Weight factor analysis
    if weight_results:
        best_weight = max(weight_results.items(), key=lambda x: x[1]['mean_auroc'])
        print(f"\n🎯 Optimal weight factor: {best_weight[0]}")
        print(f"   AUROC: {best_weight[1]['mean_auroc']:.4f}")

    # Feature importance insights
    print(f"\n💡 Key insights:")
    print(f"   • Time-weighted density features test temporal position hypothesis")
    print(f"   • Multiple weight factors tested: {list(weight_results.keys()) if weight_results else 'None'}")
    print(f"   • Density measures: JS divergence, KL divergence, entropy ratio, density shift")
    print(f"   • Comparison with baseline non-weighted features provided")

def main():
    """Main execution function"""
    print("🚀 TEMPORAL POSITION HYPOTHESIS TESTING")
    print("=" * 50)
    print("Hypothesis: Time sequence position affects structural break probability")
    print("Method: Incremental weighting of density features by temporal position")
    print("=" * 50)

    # Load data
    time_series_list, labels = load_data()

    # Extract features
    features_df = extract_temporal_hypothesis_features(time_series_list, labels)

    # Evaluate hypothesis
    results = evaluate_temporal_hypothesis(features_df)

    # Analyze weight factors
    weight_results = analyze_weight_factor_impact(features_df)

    # Compare hypothesis vs baseline
    compare_hypothesis_vs_baseline(results)

    # Create summary
    create_hypothesis_summary(results, weight_results, features_df)

    # Save results
    features_df.to_csv('temporal_hypothesis_features.csv', index=False)

    results_summary = pd.DataFrame([
        {
            'approach': approach,
            'mean_auroc': data['mean_auroc'],
            'std_auroc': data['std_auroc'],
            'n_features': data['n_features']
        }
        for approach, data in results.items()
    ])
    results_summary.to_csv('temporal_hypothesis_results.csv', index=False)

    print(f"\n💾 Results saved:")
    print(f"   • temporal_hypothesis_features.csv")
    print(f"   • temporal_hypothesis_results.csv")

    return results, weight_results, features_df

if __name__ == "__main__":
    results = main()
