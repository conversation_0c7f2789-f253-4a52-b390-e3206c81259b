"""
Distributional Branch Classifier
Uses distributional tests and incremental statistics for branch classification
Robust to CV shuffling and order-independent
"""

import numpy as np
import torch
import torch.nn as nn
from collections import deque, defaultdict
from typing import Dict, List, Tuple, Optional
from scipy import stats
# from scipy.spatial.distance import wasserstein_distance  # Not available in this scipy version
import warnings
warnings.filterwarnings('ignore')

class IncrementalStatistics:
    """Incremental statistics computation for online processing"""
    
    def __init__(self, max_history: int = 1000):
        self.max_history = max_history
        self.reset()
    
    def reset(self):
        """Reset all statistics"""
        self.count = 0
        self.mean = 0.0
        self.m2 = 0.0  # For variance calculation
        self.m3 = 0.0  # For skewness
        self.m4 = 0.0  # For kurtosis
        self.min_val = float('inf')
        self.max_val = float('-inf')
        self.history = deque(maxlen=self.max_history)
        
        # Distribution approximation
        self.histogram_bins = 50
        self.histogram_range = None
        self.histogram_counts = None
    
    def update(self, values: np.ndarray):
        """Update statistics with new values"""
        for value in values.flatten():
            self._update_single(value)
        
        # Update histogram for distribution estimation
        self._update_histogram(values)
    
    def _update_single(self, value: float):
        """Update statistics with single value using Welford's algorithm"""
        self.count += 1
        self.history.append(value)
        
        # Update min/max
        self.min_val = min(self.min_val, value)
        self.max_val = max(self.max_val, value)
        
        # Welford's online algorithm for moments
        delta = value - self.mean
        self.mean += delta / self.count
        delta2 = value - self.mean
        self.m2 += delta * delta2
        
        if self.count > 1:
            self.m3 += delta * delta2 * (delta * (self.count - 2) - delta2) / (self.count - 1)
            self.m4 += (delta * delta2 * 
                       (delta**2 * (self.count**2 - 3*self.count + 3) + 
                        6*delta2*self.mean - 4*delta2**2)) / (self.count - 1)
    
    def _update_histogram(self, values: np.ndarray):
        """Update histogram for distribution estimation"""
        if self.histogram_range is None:
            self.histogram_range = (self.min_val, self.max_val)
        else:
            # Expand range if needed
            self.histogram_range = (
                min(self.histogram_range[0], np.min(values)),
                max(self.histogram_range[1], np.max(values))
            )
        
        # Compute histogram
        if self.histogram_range[1] > self.histogram_range[0]:
            hist, _ = np.histogram(values, bins=self.histogram_bins, 
                                 range=self.histogram_range, density=True)
            
            if self.histogram_counts is None:
                self.histogram_counts = hist
            else:
                # Exponential moving average for histogram
                alpha = 0.1
                self.histogram_counts = (1 - alpha) * self.histogram_counts + alpha * hist
    
    @property
    def variance(self) -> float:
        return self.m2 / self.count if self.count > 1 else 0.0
    
    @property
    def std(self) -> float:
        return np.sqrt(self.variance)
    
    @property
    def skewness(self) -> float:
        if self.count < 3 or self.variance == 0:
            return 0.0
        return (self.m3 / self.count) / (self.variance ** 1.5)
    
    @property
    def kurtosis(self) -> float:
        if self.count < 4 or self.variance == 0:
            return 0.0
        return (self.m4 / self.count) / (self.variance ** 2) - 3
    
    def get_distribution_features(self) -> Dict:
        """Get comprehensive distributional features"""
        return {
            'mean': self.mean,
            'std': self.std,
            'variance': self.variance,
            'skewness': self.skewness,
            'kurtosis': self.kurtosis,
            'min': self.min_val,
            'max': self.max_val,
            'range': self.max_val - self.min_val,
            'count': self.count,
            'histogram': self.histogram_counts.copy() if self.histogram_counts is not None else None
        }

class MutualInformationEstimator:
    """Neural Mutual Information Estimator (MINE)"""
    
    def __init__(self, input_dim: int = 100, hidden_dim: int = 64):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # Neural network for MI estimation
        self.mi_net = nn.Sequential(
            nn.Linear(input_dim * 2, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, hidden_dim),
            nn.ReLU(),
            nn.Linear(hidden_dim, 1)
        ).to(self.device)
        
        self.optimizer = torch.optim.Adam(self.mi_net.parameters(), lr=0.001)
        
    def estimate_mi(self, x: np.ndarray, y: np.ndarray, n_epochs: int = 100) -> float:
        """Estimate mutual information between x and y using MINE"""
        
        # Convert to tensors
        x_tensor = torch.FloatTensor(x).to(self.device)
        y_tensor = torch.FloatTensor(y).to(self.device)
        
        # Ensure same length
        min_len = min(len(x_tensor), len(y_tensor))
        x_tensor = x_tensor[:min_len]
        y_tensor = y_tensor[:min_len]
        
        # Training loop
        for epoch in range(n_epochs):
            self.optimizer.zero_grad()
            
            # Joint samples
            joint = torch.cat([x_tensor, y_tensor], dim=-1)
            
            # Marginal samples (shuffle y)
            y_shuffled = y_tensor[torch.randperm(len(y_tensor))]
            marginal = torch.cat([x_tensor, y_shuffled], dim=-1)
            
            # MINE objective
            joint_scores = self.mi_net(joint)
            marginal_scores = self.mi_net(marginal)
            
            # MI estimate using Donsker-Varadhan representation
            mi_estimate = torch.mean(joint_scores) - torch.log(torch.mean(torch.exp(marginal_scores)))
            
            # Maximize MI (minimize negative MI)
            loss = -mi_estimate
            loss.backward()
            self.optimizer.step()
        
        # Return final MI estimate
        with torch.no_grad():
            joint = torch.cat([x_tensor, y_tensor], dim=-1)
            y_shuffled = y_tensor[torch.randperm(len(y_tensor))]
            marginal = torch.cat([x_tensor, y_shuffled], dim=-1)
            
            joint_scores = self.mi_net(joint)
            marginal_scores = self.mi_net(marginal)
            
            mi_final = torch.mean(joint_scores) - torch.log(torch.mean(torch.exp(marginal_scores)))
            
        return mi_final.item()

class DistributionalBranchClassifier:
    """
    Branch classifier using distributional tests and incremental statistics
    Robust to CV shuffling and order-independent
    """
    
    def __init__(self, 
                 single_branch: bool = False,
                 max_branches: int = 10,
                 mi_threshold: float = 0.5,
                 kl_threshold: float = 0.3):
        
        self.single_branch = single_branch
        self.max_branches = max_branches
        self.mi_threshold = mi_threshold
        self.kl_threshold = kl_threshold
        
        # Branch statistics
        if single_branch:
            self.global_stats = IncrementalStatistics()
            self.segment_metadata = []
        else:
            self.branch_stats = {}  # branch_id -> IncrementalStatistics
        
        # MI estimator
        self.mi_estimator = MutualInformationEstimator()
        
        # Processing state
        self.processed_segments = 0
        self.decision_history = []
        
    def process_new_segment(self, segment: np.ndarray, segment_id: Optional[str] = None) -> Dict:
        """
        Process new segment using distributional approach
        
        Args:
            segment: Time series segment
            segment_id: Optional identifier for the segment
        
        Returns:
            Decision information
        """
        
        if self.single_branch:
            return self._process_single_branch(segment, segment_id)
        else:
            return self._process_multiple_branches(segment, segment_id)
    
    def _process_single_branch(self, segment: np.ndarray, segment_id: Optional[str] = None) -> Dict:
        """Process segment in single branch mode"""
        
        # Always add to global statistics
        self.global_stats.update(segment)
        
        # Store segment metadata
        segment_features = self._extract_distributional_features(segment)
        
        self.segment_metadata.append({
            'segment_id': segment_id or f'seg_{self.processed_segments}',
            'features': segment_features,
            'position': self.processed_segments
        })
        
        # Decision is always "continue" in single branch mode
        decision = {
            'action': 'CONTINUE_GLOBAL_BRANCH',
            'branch_id': 'global_branch',
            'confidence': 1.0,
            'distributional_features': segment_features,
            'global_stats': self.global_stats.get_distribution_features(),
            'segment_count': self.processed_segments + 1
        }
        
        self.processed_segments += 1
        self.decision_history.append(decision)
        
        return decision
    
    def _process_multiple_branches(self, segment: np.ndarray, segment_id: Optional[str] = None) -> Dict:
        """Process segment in multiple branches mode"""
        
        segment_features = self._extract_distributional_features(segment)
        
        if not self.branch_stats:
            # First segment - create first branch
            branch_id = 'branch_0'
            self.branch_stats[branch_id] = IncrementalStatistics()
            self.branch_stats[branch_id].update(segment)
            
            decision = {
                'action': 'NEW_BRANCH',
                'branch_id': branch_id,
                'confidence': 1.0,
                'distributional_features': segment_features,
                'reason': 'first_segment'
            }
        else:
            # Compare with existing branches using distributional tests
            branch_similarities = self._compute_distributional_similarities(segment, segment_features)
            
            # Make decision based on similarities
            decision = self._make_distributional_decision(segment, segment_features, branch_similarities)
            
            # Update chosen branch statistics
            if decision['action'] in ['CONTINUE_BRANCH', 'STRUCTURAL_BREAK']:
                self.branch_stats[decision['branch_id']].update(segment)
            elif decision['action'] == 'NEW_BRANCH':
                # Create new branch
                new_branch_id = f"branch_{len(self.branch_stats)}"
                self.branch_stats[new_branch_id] = IncrementalStatistics()
                self.branch_stats[new_branch_id].update(segment)
                decision['branch_id'] = new_branch_id
        
        self.processed_segments += 1
        self.decision_history.append(decision)
        
        return decision
    
    def _extract_distributional_features(self, segment: np.ndarray) -> Dict:
        """Extract distributional features from segment"""
        
        # Basic statistical features
        features = {
            'mean': np.mean(segment),
            'std': np.std(segment),
            'skewness': stats.skew(segment),
            'kurtosis': stats.kurtosis(segment),
            'min': np.min(segment),
            'max': np.max(segment),
            'median': np.median(segment),
            'iqr': np.percentile(segment, 75) - np.percentile(segment, 25)
        }
        
        # Distribution shape features
        try:
            # Histogram-based features
            hist, bin_edges = np.histogram(segment, bins=20, density=True)
            features['histogram_entropy'] = -np.sum(hist * np.log(hist + 1e-10))
            features['histogram_peak'] = np.max(hist)
            features['histogram_std'] = np.std(hist)
            
            # Normality tests
            _, p_shapiro = stats.shapiro(segment[:min(len(segment), 5000)])  # Limit for performance
            features['shapiro_p'] = p_shapiro
            
            # Distribution fitting
            try:
                # Fit normal distribution
                mu, sigma = stats.norm.fit(segment)
                features['normal_mu'] = mu
                features['normal_sigma'] = sigma
                
                # KS test against fitted normal
                _, p_ks = stats.kstest(segment, lambda x: stats.norm.cdf(x, mu, sigma))
                features['ks_normal_p'] = p_ks
                
            except:
                features['normal_mu'] = features['mean']
                features['normal_sigma'] = features['std']
                features['ks_normal_p'] = 0.0
                
        except:
            # Fallback values
            features['histogram_entropy'] = 0.0
            features['histogram_peak'] = 0.0
            features['histogram_std'] = 0.0
            features['shapiro_p'] = 0.0
            features['normal_mu'] = features['mean']
            features['normal_sigma'] = features['std']
            features['ks_normal_p'] = 0.0
        
        return features
    
    def _compute_distributional_similarities(self, segment: np.ndarray, segment_features: Dict) -> Dict:
        """Compute distributional similarities with existing branches"""
        
        similarities = {}
        
        for branch_id, branch_stats in self.branch_stats.items():
            branch_features = branch_stats.get_distribution_features()
            
            # KL divergence approximation
            kl_div = self._approximate_kl_divergence(segment_features, branch_features)
            
            # Simple Wasserstein distance approximation (since scipy version doesn't have it)
            if len(branch_stats.history) > 10:
                branch_sample = np.array(list(branch_stats.history)[-min(len(segment), len(branch_stats.history)):])
                wasserstein_dist = self._approximate_wasserstein_distance(segment, branch_sample)
            else:
                wasserstein_dist = 1.0
            
            # Statistical distance
            stat_distance = self._compute_statistical_distance(segment_features, branch_features)
            
            # Mutual information estimate (simplified)
            if len(branch_stats.history) > 50:
                try:
                    # Use subset for MI estimation (performance)
                    branch_sample = np.array(list(branch_stats.history)[-100:])
                    segment_sample = segment[:min(len(segment), 100)]
                    
                    # Reshape for MI estimation
                    if len(branch_sample) >= len(segment_sample):
                        branch_sample = branch_sample[:len(segment_sample)]
                    else:
                        segment_sample = segment_sample[:len(branch_sample)]
                    
                    # Simple MI approximation (faster than neural)
                    mi_estimate = self._approximate_mutual_information(segment_sample, branch_sample)
                except:
                    mi_estimate = 0.0
            else:
                mi_estimate = 0.0
            
            # Combine similarities
            similarities[branch_id] = {
                'kl_divergence': kl_div,
                'wasserstein_distance': wasserstein_dist,
                'statistical_distance': stat_distance,
                'mutual_information': mi_estimate,
                'combined_similarity': self._combine_similarities(kl_div, wasserstein_dist, stat_distance, mi_estimate)
            }
        
        return similarities
    
    def _approximate_kl_divergence(self, features1: Dict, features2: Dict) -> float:
        """Approximate KL divergence using distributional features"""
        
        # Use normal approximation for KL divergence
        mu1, sigma1 = features1.get('normal_mu', 0), features1.get('normal_sigma', 1)
        mu2, sigma2 = features2.get('mean', 0), features2.get('std', 1)
        
        if sigma1 <= 0 or sigma2 <= 0:
            return 1.0
        
        # KL divergence between two normal distributions
        kl_div = np.log(sigma2 / sigma1) + (sigma1**2 + (mu1 - mu2)**2) / (2 * sigma2**2) - 0.5
        
        return max(0, kl_div)
    
    def _compute_statistical_distance(self, features1: Dict, features2: Dict) -> float:
        """Compute distance between statistical features"""
        
        # Normalize and compare key statistical features
        feature_keys = ['mean', 'std', 'skewness', 'kurtosis']
        
        distances = []
        for key in feature_keys:
            val1 = features1.get(key, 0)
            val2 = features2.get(key, 0)
            
            # Normalized absolute difference
            if abs(val1) + abs(val2) > 0:
                dist = abs(val1 - val2) / (abs(val1) + abs(val2))
            else:
                dist = 0
            
            distances.append(dist)
        
        return np.mean(distances)
    
    def _approximate_mutual_information(self, x: np.ndarray, y: np.ndarray) -> float:
        """Fast approximation of mutual information"""
        
        try:
            # Bin the data for discrete MI estimation
            n_bins = min(20, len(x) // 5)
            
            # Create bins
            x_binned = np.digitize(x, np.histogram_bin_edges(x, bins=n_bins))
            y_binned = np.digitize(y, np.histogram_bin_edges(y, bins=n_bins))
            
            # Compute joint and marginal probabilities
            joint_hist, _, _ = np.histogram2d(x_binned, y_binned, bins=n_bins)
            joint_prob = joint_hist / np.sum(joint_hist)
            
            x_prob = np.sum(joint_prob, axis=1)
            y_prob = np.sum(joint_prob, axis=0)
            
            # Compute MI
            mi = 0.0
            for i in range(n_bins):
                for j in range(n_bins):
                    if joint_prob[i, j] > 0 and x_prob[i] > 0 and y_prob[j] > 0:
                        mi += joint_prob[i, j] * np.log(joint_prob[i, j] / (x_prob[i] * y_prob[j]))
            
            return max(0, mi)
            
        except:
            return 0.0
    
    def _approximate_wasserstein_distance(self, x: np.ndarray, y: np.ndarray) -> float:
        """Approximate Wasserstein distance using sorted samples"""
        try:
            # Sort both arrays
            x_sorted = np.sort(x.flatten())
            y_sorted = np.sort(y.flatten())
            
            # Align lengths
            min_len = min(len(x_sorted), len(y_sorted))
            x_aligned = x_sorted[:min_len]
            y_aligned = y_sorted[:min_len]
            
            # Compute L1 distance between sorted arrays (approximates Wasserstein-1)
            wasserstein_approx = np.mean(np.abs(x_aligned - y_aligned))
            
            return wasserstein_approx
        except:
            return 1.0
    
    def _combine_similarities(self, kl_div: float, wasserstein: float, stat_dist: float, mi: float) -> float:
        """Combine different similarity measures"""
        
        # Normalize measures to [0, 1] where 1 = most similar
        kl_sim = 1 / (1 + kl_div)
        wasserstein_sim = 1 / (1 + wasserstein)
        stat_sim = 1 - stat_dist
        mi_sim = mi  # Already in [0, inf], clip to [0, 1]
        mi_sim = min(1.0, mi_sim)
        
        # Weighted combination
        combined = (0.3 * kl_sim + 0.3 * wasserstein_sim + 0.2 * stat_sim + 0.2 * mi_sim)
        
        return combined
    
    def _make_distributional_decision(self, segment: np.ndarray, segment_features: Dict, similarities: Dict) -> Dict:
        """Make decision based on distributional similarities"""
        
        if not similarities:
            return {
                'action': 'NEW_BRANCH',
                'branch_id': f'branch_{len(self.branch_stats)}',
                'confidence': 1.0,
                'reason': 'no_existing_branches'
            }
        
        # Find best matching branch
        best_branch = max(similarities.keys(), key=lambda k: similarities[k]['combined_similarity'])
        best_similarity = similarities[best_branch]['combined_similarity']
        best_kl = similarities[best_branch]['kl_divergence']
        
        # Decision thresholds
        high_similarity_threshold = 0.7
        low_similarity_threshold = 0.3
        high_kl_threshold = 0.5
        
        # Decision logic
        if best_similarity > high_similarity_threshold and best_kl < high_kl_threshold:
            # High similarity, low divergence -> Continue branch
            action = 'CONTINUE_BRANCH'
            confidence = best_similarity
            reason = f'high_similarity_{best_similarity:.3f}'
            
        elif best_similarity > low_similarity_threshold and best_kl > high_kl_threshold:
            # Moderate similarity but high divergence -> Structural break
            action = 'STRUCTURAL_BREAK'
            confidence = 0.8
            reason = f'structural_break_kl_{best_kl:.3f}'
            
        elif len(self.branch_stats) < self.max_branches:
            # Low similarity and room for new branches -> New branch
            action = 'NEW_BRANCH'
            confidence = 1.0 - best_similarity
            reason = f'low_similarity_{best_similarity:.3f}'
            
        else:
            # Low similarity but max branches reached -> Force continue with best match
            action = 'CONTINUE_BRANCH'
            confidence = best_similarity * 0.5  # Lower confidence
            reason = f'forced_continue_max_branches'
        
        return {
            'action': action,
            'branch_id': best_branch,
            'confidence': confidence,
            'reason': reason,
            'similarities': similarities,
            'distributional_features': segment_features
        }
    
    def get_branch_summary(self) -> Dict:
        """Get comprehensive branch summary"""
        
        if self.single_branch:
            return {
                'mode': 'single_branch',
                'total_segments': self.processed_segments,
                'global_statistics': self.global_stats.get_distribution_features(),
                'segment_metadata_count': len(self.segment_metadata)
            }
        else:
            branch_summaries = {}
            for branch_id, stats in self.branch_stats.items():
                branch_summaries[branch_id] = {
                    'segment_count': stats.count,
                    'statistics': stats.get_distribution_features()
                }
            
            return {
                'mode': 'multiple_branches',
                'total_segments': self.processed_segments,
                'branch_count': len(self.branch_stats),
                'branches': branch_summaries,
                'decision_distribution': self._get_decision_distribution()
            }
    
    def _get_decision_distribution(self) -> Dict:
        """Get distribution of decisions made"""
        
        decision_counts = defaultdict(int)
        for decision in self.decision_history:
            decision_counts[decision['action']] += 1
        
        return dict(decision_counts)

# Example usage and testing
if __name__ == "__main__":
    print("🧠 Testing Distributional Branch Classifier")
    print("=" * 50)
    
    # Test both modes
    for mode in ['single', 'multiple']:
        print(f"\n📊 Testing {mode} branch mode:")
        
        classifier = DistributionalBranchClassifier(
            single_branch=(mode == 'single'),
            max_branches=5
        )
        
        # Generate test segments with different distributions
        np.random.seed(42)
        
        for i in range(10):
            if i < 3:
                # Normal distribution
                segment = np.random.normal(0, 1, 100)
            elif i < 6:
                # Different normal distribution
                segment = np.random.normal(2, 0.5, 100)
            else:
                # Uniform distribution
                segment = np.random.uniform(-1, 1, 100)
            
            result = classifier.process_new_segment(segment, f'seg_{i}')
            
            print(f"   Segment {i}: {result['action']:20s} -> {result['branch_id']:15s} "
                  f"(conf: {result['confidence']:.3f})")
        
        # Print summary
        summary = classifier.get_branch_summary()
        print(f"   📈 Summary: {summary['total_segments']} segments processed")
        
        if mode == 'multiple':
            print(f"   🌳 Branches created: {summary['branch_count']}")
            for action, count in summary['decision_distribution'].items():
                print(f"      {action}: {count}")