#!/usr/bin/env python3
"""
Two-Stage Contrastive Learning with TS2Vec and Hard Triplet Mining
Stage 1: Learn contrastive embeddings for labels 0/1 using TS2Vec + Hard Triplet Mining
Stage 2: Use frozen encoder features for probabilistic classification
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
from sklearn.preprocessing import StandardScaler
import logging
from typing import Dict, Tuple, List
import warnings
from tqdm import tqdm
import sys
import os

# Add ts2vec to path
sys.path.append('/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/ts2vec')
from ts2vec import TSEncoder
from ts2vec import hierarchical_contrastive_loss

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

class TimeSeriesContrastiveDataset(Dataset):
    """Dataset for contrastive learning on time series"""
    
    def __init__(self, time_series_list, labels, max_length=None):
        self.time_series_list = time_series_list
        self.labels = np.array(labels)
        self.max_length = max_length
        
        # Group indices by label for efficient triplet mining
        self.label_to_indices = {}
        for idx, label in enumerate(self.labels):
            if label not in self.label_to_indices:
                self.label_to_indices[label] = []
            self.label_to_indices[label].append(idx)
    
    def __len__(self):
        return len(self.time_series_list)
    
    def __getitem__(self, idx):
        series, break_point = self.time_series_list[idx]
        label = self.labels[idx]
        
        # Clean and pad/truncate series
        series_clean = series[~np.isnan(series)]
        if self.max_length and len(series_clean) > self.max_length:
            series_clean = series_clean[:self.max_length]
        
        # Convert to tensor and add feature dimension
        series_tensor = torch.FloatTensor(series_clean).unsqueeze(-1)  # T x 1
        
        return series_tensor, label, idx
    
    def get_triplet(self, anchor_idx):
        """Get hard triplet: anchor, positive, negative"""
        anchor_label = self.labels[anchor_idx]
        
        # Get positive (same label, different sample)
        pos_candidates = [i for i in self.label_to_indices[anchor_label] if i != anchor_idx]
        if len(pos_candidates) == 0:
            pos_idx = anchor_idx  # Fallback
        else:
            pos_idx = np.random.choice(pos_candidates)
        
        # Get negative (different label)
        neg_label = 1 - anchor_label  # Binary classification
        if neg_label in self.label_to_indices and len(self.label_to_indices[neg_label]) > 0:
            neg_idx = np.random.choice(self.label_to_indices[neg_label])
        else:
            # Fallback: random different sample
            neg_candidates = [i for i in range(len(self.labels)) if self.labels[i] != anchor_label]
            neg_idx = np.random.choice(neg_candidates) if neg_candidates else anchor_idx
        
        return anchor_idx, pos_idx, neg_idx

def collate_variable_length(batch):
    """Collate function for variable length sequences"""
    sequences, labels, indices = zip(*batch)
    
    # Find max length in batch
    max_len = max(seq.size(0) for seq in sequences)
    
    # Pad sequences
    padded_sequences = []
    masks = []
    
    for seq in sequences:
        seq_len = seq.size(0)
        if seq_len < max_len:
            # Pad with NaN
            padding = torch.full((max_len - seq_len, 1), float('nan'))
            padded_seq = torch.cat([seq, padding], dim=0)
        else:
            padded_seq = seq
        
        padded_sequences.append(padded_seq)
        
        # Create mask (True for valid positions)
        mask = torch.ones(max_len, dtype=torch.bool)
        if seq_len < max_len:
            mask[seq_len:] = False
        masks.append(mask)
    
    # Stack into batch: B x T x 1
    batch_sequences = torch.stack(padded_sequences)
    batch_masks = torch.stack(masks)
    batch_labels = torch.LongTensor(labels)
    
    return batch_sequences, batch_labels, batch_masks, indices

class ContrastiveTS2Vec(nn.Module):
    """TS2Vec encoder with contrastive learning capabilities"""
    
    def __init__(self, input_dims=1, output_dims=320, hidden_dims=64, depth=10):
        super().__init__()
        self.encoder = TSEncoder(
            input_dims=input_dims,
            output_dims=output_dims,
            hidden_dims=hidden_dims,
            depth=depth,
            mask_mode='binomial'
        )
        self.output_dims = output_dims
        
        # Projection head for contrastive learning
        self.projection_head = nn.Sequential(
            nn.Linear(output_dims, 256),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Linear(128, 64)
        )
    
    def forward(self, x, mask=None, return_projection=True):
        # x: B x T x 1
        # Get encoder representations
        encoder_out = self.encoder(x, mask=mask)  # B x T x output_dims
        
        # Global average pooling to get fixed-size representation
        if mask is not None:
            # Masked average pooling
            mask_expanded = mask.unsqueeze(-1).expand_as(encoder_out)
            masked_out = encoder_out * mask_expanded.float()
            pooled = masked_out.sum(dim=1) / mask.sum(dim=1, keepdim=True).float()
        else:
            pooled = encoder_out.mean(dim=1)  # B x output_dims
        
        if return_projection:
            # Project to contrastive space
            projected = self.projection_head(pooled)
            projected = F.normalize(projected, dim=-1)  # L2 normalize
            return pooled, projected
        else:
            return pooled

class HardTripletMiner:
    """Hard triplet mining for contrastive learning"""
    
    def __init__(self, margin=0.5):
        self.margin = margin
    
    def mine_hard_triplets(self, embeddings, labels, return_indices=False):
        """Mine hard triplets from batch embeddings"""
        batch_size = embeddings.size(0)
        
        # Compute pairwise distances
        distances = torch.cdist(embeddings, embeddings, p=2)
        
        triplets = []
        triplet_indices = []
        
        for i in range(batch_size):
            anchor_label = labels[i]
            
            # Find hardest positive (same label, maximum distance)
            pos_mask = (labels == anchor_label) & (torch.arange(batch_size, device=labels.device) != i)
            if pos_mask.any():
                pos_distances = distances[i][pos_mask]
                hardest_pos_idx = torch.argmax(pos_distances)
                pos_idx = torch.arange(batch_size, device=labels.device)[pos_mask][hardest_pos_idx]
            else:
                continue  # Skip if no positive samples
            
            # Find hardest negative (different label, minimum distance)
            neg_mask = (labels != anchor_label)
            if neg_mask.any():
                neg_distances = distances[i][neg_mask]
                hardest_neg_idx = torch.argmin(neg_distances)
                neg_idx = torch.arange(batch_size, device=labels.device)[neg_mask][hardest_neg_idx]
            else:
                continue  # Skip if no negative samples
            
            # Check if this is a valid hard triplet
            pos_dist = distances[i, pos_idx]
            neg_dist = distances[i, neg_idx]
            
            if pos_dist + self.margin > neg_dist:  # Hard triplet condition
                triplets.append((embeddings[i], embeddings[pos_idx], embeddings[neg_idx]))
                if return_indices:
                    triplet_indices.append((i, pos_idx.item(), neg_idx.item()))
        
        if return_indices:
            return triplets, triplet_indices
        return triplets


# from pytorch_metric_learning import losses

def triplet_loss(anchor, positive, negative, margin=0.5):
    """Compute triplet loss"""
    pos_dist = F.pairwise_distance(anchor, positive, p=2)
    neg_dist = F.pairwise_distance(anchor, negative, p=2)
    loss = F.relu(pos_dist - neg_dist + margin)
    return loss.mean()


class SupConLossTriplet:
    """
    Callable Supervised Contrastive Loss for triplet learning paradigm.
    
    Implements the core InfoNCE objective adapted for explicit triplet mining,
    providing a standalone alternative to matrix-based implementations.
    
    Mathematical Foundation:
    L = -log(exp(sim(a,p)/τ) / Σ exp(sim(a,x)/τ))
    
    where x ∈ {positive, negative} samples and τ is the temperature parameter.
    """
    def __init__(self, temperature=0.1):
        self.temperature = temperature
    
    def __call__(self, anchor, positive, negative):
        return supcon_loss_triplet(anchor, positive, negative, self.temperature)


def supcon_loss_triplet(anchor, positive, negative, temperature=0.1):
    """
    Standalone Supervised Contrastive Loss for explicit triplet configurations.
    
    This implementation provides direct triplet-based SupCon Loss computation,
    circumventing matrix-based approaches while maintaining mathematical equivalence
    to the InfoNCE objective in contrastive representation learning.
    
    Theoretical Framework:
    SupCon loss extends the InfoNCE objective by incorporating supervised signals,
    maximizing mutual information between anchor and positive while minimizing
    correlation with negative samples through softmax-normalized similarities.
    
    The loss formulation:
    L = -log(exp(s(a,p)/τ) / (exp(s(a,p)/τ) + exp(s(a,n)/τ)))
    
    where:
    - s(·,·) represents cosine similarity
    - τ is the temperature hyperparameter controlling concentration
    - The denominator includes both positive and negative similarities
    
    Args:
        anchor: Tensor [batch_size, embed_dim] - anchor embeddings
        positive: Tensor [batch_size, embed_dim] - positive sample embeddings
        negative: Tensor [batch_size, embed_dim] - negative sample embeddings  
        temperature: Float, temperature parameter for softmax scaling (default: 0.1)
        
    Returns:
        Tensor: SupCon loss value for the triplet batch
        
    References:
        Khosla et al. "Supervised Contrastive Learning" NeurIPS 2020.
        Chen et al. "A Simple Framework for Contrastive Learning of Visual Representations" ICML 2020.
    """
    # L2 normalization for cosine similarity computation - avoid in-place operations
    anchor_norm = F.normalize(anchor.clone(), p=2, dim=-1)
    positive_norm = F.normalize(positive.clone(), p=2, dim=-1)
    negative_norm = F.normalize(negative.clone(), p=2, dim=-1)
    
    # Cosine similarities
    sim_pos = torch.sum(anchor_norm * positive_norm, dim=-1) / temperature  # [batch_size]
    sim_neg = torch.sum(anchor_norm * negative_norm, dim=-1) / temperature  # [batch_size]
    
    # Numerical stability: subtract maximum for numerical precision
    max_sim = torch.max(sim_pos, sim_neg).detach()
    sim_pos_stable = sim_pos - max_sim
    sim_neg_stable = sim_neg - max_sim
    
    # Log-softmax computation for InfoNCE objective
    # log P(positive | anchor) = log(exp(sim_pos) / (exp(sim_pos) + exp(sim_neg)))
    log_prob_pos = sim_pos_stable - torch.log(
        torch.exp(sim_pos_stable) + torch.exp(sim_neg_stable) + 1e-8
    )
    
    # SupCon loss: negative log-likelihood of positive pairs
    loss = -log_prob_pos
    
    return loss.mean()
class CircleLossTriplet:
    """
    Callable Circle Loss class for consistent API interface.
    Enables functional programming paradigm: loss_fn = CircleLossTriplet(m=0.4, gamma=80)
    """
    def __init__(self, m=0.4, gamma=80):
        self.m = m
        self.gamma = gamma
    
    def __call__(self, anchor, positive, negative, margin=None):
        # Override m with margin if provided (for API compatibility)
        m_val = margin if margin is not None else self.m
        return circle_loss_triplet(anchor, positive, negative, m=m_val, gamma=self.gamma)


def circle_loss_triplet(anchor, positive, negative, m=0.4, gamma=80):
    """
    Standalone Circle Loss implementation for triplet learning.
    
    This implementation provides a direct triplet-based Circle Loss without 
    requiring matrix computations or external dependencies, optimized for 
    scenarios where triplets are explicitly provided.
    
    Mathematical Foundation:
    Circle Loss reformulates the softmax-based classification boundary into a 
    more flexible circular decision boundary in the similarity space:
    
    L = log(1 + Σ exp(γ·αₙ·(sₙ - Δₙ)) · Σ exp(-γ·αₚ·(sₚ - Δₚ)))
    
    where:
    - sₚ, sₙ are cosine similarities for positive and negative pairs
    - αₚ = max(0, 1+m-sₚ), αₙ = max(0, sₙ+m) are adaptive weights
    - Δₚ = 1-m, Δₙ = m are adaptive margins
    
    Args:
        anchor: Tensor of shape [batch_size, embedding_dim] - anchor embeddings
        positive: Tensor of shape [batch_size, embedding_dim] - positive embeddings  
        negative: Tensor of shape [batch_size, embedding_dim] - negative embeddings
        m: Float, relaxation factor controlling decision boundary radius (default: 0.4)
        gamma: Float, scale factor for similarity scores (default: 80)
        
    Returns:
        Tensor: Circle loss value for the triplet batch
        
    References:
        Sun et al. "Circle Loss: A Unified Perspective for Deep Metric Learning"
        CVPR 2020. https://arxiv.org/abs/2002.10857
    """
    # Ensure embeddings are L2 normalized for cosine similarity computation - avoid in-place operations
    anchor_norm = F.normalize(anchor.clone(), p=2, dim=-1)
    positive_norm = F.normalize(positive.clone(), p=2, dim=-1)  
    negative_norm = F.normalize(negative.clone(), p=2, dim=-1)
    
    # Compute cosine similarities
    s_p = torch.sum(anchor_norm * positive_norm, dim=-1)  # [batch_size]
    s_n = torch.sum(anchor_norm * negative_norm, dim=-1)  # [batch_size]
    
    # Circle Loss adaptive parameters
    delta_p = 1 - m  # Positive margin
    delta_n = m      # Negative margin
    
    # Adaptive weights (gradient re-weighting mechanism) - use detach to avoid in-place issues
    alpha_p = torch.relu(1 + m - s_p.detach())  # [batch_size]
    alpha_n = torch.relu(s_n.detach() + m)      # [batch_size]
    
    # Circle Loss logits computation
    # Positive term: encourages anchor-positive similarity to exceed delta_p
    logits_p = -gamma * alpha_p * (s_p - delta_p)  # [batch_size]
    
    # Negative term: penalizes anchor-negative similarity exceeding delta_n  
    logits_n = gamma * alpha_n * (s_n - delta_n)   # [batch_size]
    
    # Circle Loss: log(1 + exp(logits_p) * exp(logits_n))
    # Numerically stable computation using logsumexp identity
    loss = torch.log1p(torch.exp(logits_p + logits_n))
    
    return loss.mean()

def safe_hierarchical_contrastive_loss(z1, z2, alpha=0.5, temporal_unit=0):
    """
    Safe implementation of hierarchical contrastive loss without in-place operations.
    
    This version clones tensors to avoid modifying the original computation graph,
    preventing the "modified by an inplace operation" error.
    
    Args:
        z1, z2: Input tensors [B x T x C]
        alpha: Weight for instance vs temporal loss
        temporal_unit: Minimum unit for temporal contrast
    
    Returns:
        Hierarchical contrastive loss value
    """
    # Clone inputs to avoid modifying original tensors
    z1_work = z1.clone()
    z2_work = z2.clone()
    
    loss = torch.tensor(0., device=z1.device)
    d = 0
    
    while z1_work.size(1) > 1:
        if alpha != 0:
            loss = loss + alpha * safe_instance_contrastive_loss(z1_work, z2_work)
        if d >= temporal_unit:
            if 1 - alpha != 0:
                loss = loss + (1 - alpha) * safe_temporal_contrastive_loss(z1_work, z2_work)
        d += 1
        
        # Create new tensors instead of modifying existing ones
        z1_work = F.max_pool1d(z1_work.transpose(1, 2), kernel_size=2).transpose(1, 2)
        z2_work = F.max_pool1d(z2_work.transpose(1, 2), kernel_size=2).transpose(1, 2)
    
    if z1_work.size(1) == 1:
        if alpha != 0:
            loss = loss + alpha * safe_instance_contrastive_loss(z1_work, z2_work)
        d += 1
    
    return loss / d

def safe_instance_contrastive_loss(z1, z2):
    """Safe instance contrastive loss without in-place operations"""
    B, T = z1.size(0), z1.size(1)
    if B == 1:
        return z1.new_tensor(0.)
    
    z = torch.cat([z1, z2], dim=0)  # 2B x T x C
    z = z.transpose(0, 1)  # T x 2B x C
    sim = torch.matmul(z, z.transpose(1, 2))  # T x 2B x 2B
    
    # Avoid in-place operations
    logits_lower = torch.tril(sim, diagonal=-1)[:, :, :-1]
    logits_upper = torch.triu(sim, diagonal=1)[:, :, 1:]
    logits = logits_lower + logits_upper  # Addition instead of +=
    logits = -F.log_softmax(logits, dim=-1)
    
    i = torch.arange(B, device=z1.device)
    loss = (logits[:, i, B + i - 1].mean() + logits[:, B + i, i].mean()) / 2
    return loss

def safe_temporal_contrastive_loss(z1, z2):
    """Safe temporal contrastive loss without in-place operations"""
    B, T = z1.size(0), z1.size(1)
    if T == 1:
        return z1.new_tensor(0.)
    
    z = torch.cat([z1, z2], dim=1)  # B x 2T x C
    sim = torch.matmul(z, z.transpose(1, 2))  # B x 2T x 2T
    
    # Avoid in-place operations
    logits_lower = torch.tril(sim, diagonal=-1)[:, :, :-1]
    logits_upper = torch.triu(sim, diagonal=1)[:, :, 1:]
    logits = logits_lower + logits_upper  # Addition instead of +=
    logits = -F.log_softmax(logits, dim=-1)
    
    t = torch.arange(T, device=z1.device)
    loss = (logits[:, t, T + t - 1].mean() + logits[:, T + t, t].mean()) / 2
    return loss

def train_contrastive_stage(model, train_loader, params, n_epochs=4):
    """Stage 1: Train contrastive representations"""
    logger.info("🎯 Stage 1: Training contrastive representations...")
    
    optimizer = torch.optim.AdamW(model.parameters(), lr=params['lr'], weight_decay=params['weight_decay'])
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    triplet_miner = HardTripletMiner(margin=params['margin'])
    
    best_loss = float('inf')
    patience_counter = 0
    patience = 15
    
    for epoch in range(n_epochs):
        model.train()
        epoch_loss = 0
        n_batches = 0
        n_triplets = 0
        
        for batch_sequences, batch_labels, batch_masks, _ in train_loader:#tqdm(train_loader, desc=f"Epoch {epoch+1}"):
            batch_sequences = batch_sequences.to(device)
            batch_labels = batch_labels.to(device)
            batch_masks = batch_masks.to(device)
            
            optimizer.zero_grad()
            
            # Get embeddings
            _, projected_embeddings = model(batch_sequences, mask=batch_masks, return_projection=True)
            
            # Mine hard triplets using detached embeddings for mining only
            with torch.no_grad():
                triplet_indices = []
                batch_size = projected_embeddings.size(0)
                distances = torch.cdist(projected_embeddings, projected_embeddings, p=2)
                
                for i in range(batch_size):
                    anchor_label = batch_labels[i]
                    
                    # Find hardest positive (same label, maximum distance)
                    pos_mask = (batch_labels == anchor_label) & (torch.arange(batch_size, device=batch_labels.device) != i)
                    if pos_mask.any():
                        pos_distances = distances[i][pos_mask]
                        hardest_pos_idx = torch.argmax(pos_distances)
                        pos_idx = torch.arange(batch_size, device=batch_labels.device)[pos_mask][hardest_pos_idx]
                    else:
                        continue
                    
                    # Find hardest negative (different label, minimum distance)
                    neg_mask = (batch_labels != anchor_label)
                    if neg_mask.any():
                        neg_distances = distances[i][neg_mask]
                        hardest_neg_idx = torch.argmin(neg_distances)
                        neg_idx = torch.arange(batch_size, device=batch_labels.device)[neg_mask][hardest_neg_idx]
                    else:
                        continue
                    
                    # Check if this is a valid hard triplet
                    pos_dist = distances[i, pos_idx]
                    neg_dist = distances[i, neg_idx]
                    
                    if pos_dist + params['margin'] > neg_dist:
                        triplet_indices.append((i, pos_idx.item(), neg_idx.item()))
            
            if len(triplet_indices) == 0:
                continue  # Skip batch if no valid triplets
            
            # Compute triplet loss using original embeddings (not detached)
            total_triplet_loss = 0
            circle_loss = CircleLossTriplet(m=0.4, gamma=80)
            
            for anchor_idx, pos_idx, neg_idx in triplet_indices:
                anchor_emb = projected_embeddings[anchor_idx].unsqueeze(0)
                pos_emb = projected_embeddings[pos_idx].unsqueeze(0)
                neg_emb = projected_embeddings[neg_idx].unsqueeze(0)
                
                total_triplet_loss += circle_loss(anchor_emb, pos_emb, neg_emb)
            # for anchor, positive, negative in triplets:
            #     total_triplet_loss += triplet_loss(anchor.unsqueeze(0), positive.unsqueeze(0), negative.unsqueeze(0), margin=params['margin'])
            
            avg_triplet_loss = total_triplet_loss / len(triplet_indices)
            
            # Optional: Add TS2Vec hierarchical contrastive loss for better representations
            if params.get('use_ts2vec_loss', False):
                # Create augmented views for TS2Vec loss using safe implementation
                aug1 = model.encoder(batch_sequences, mask='binomial')
                aug2 = model.encoder(batch_sequences, mask='continuous')
                ts2vec_loss = safe_hierarchical_contrastive_loss(aug1, aug2, alpha=0.5)
                
                total_loss = avg_triplet_loss + params.get('ts2vec_weight', 0.1) * ts2vec_loss
            else:
                total_loss = avg_triplet_loss
            
            total_loss.backward()
            optimizer.step()
            
            epoch_loss += total_loss.item()
            n_batches += 1
            n_triplets += len(triplet_indices)
        
        avg_epoch_loss = epoch_loss / n_batches if n_batches > 0 else float('inf')
        scheduler.step(avg_epoch_loss)
        
        if epoch % 1 == 0:
            logger.info(f"   Epoch {epoch+1}: Loss = {avg_epoch_loss:.4f}, Triplets = {n_triplets}")
        
        # Early stopping
        if avg_epoch_loss < best_loss:
            best_loss = avg_epoch_loss
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            logger.info(f"   Early stopping at epoch {epoch+1}")
            break
    
    logger.info(f"   ✅ Contrastive training completed. Best loss: {best_loss:.4f}")
    return best_loss

class FrozenContrastiveClassifier(nn.Module):
    """Stage 2: Frozen encoder + probabilistic classifier"""
    
    def __init__(self, frozen_encoder, encoder_dim=320):
        super().__init__()
        self.frozen_encoder = frozen_encoder
        
        # Freeze encoder parameters
        for param in self.frozen_encoder.parameters():
            param.requires_grad = False
        
        # Probabilistic classifier
        self.classifier = nn.Sequential(
            nn.Linear(encoder_dim, 128),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(64, 32),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(32, 1)  # Logits for BCEWithLogitsLoss
        )
    
    def forward(self, x, mask=None):
        with torch.no_grad():
            # Get frozen encoder features
            encoder_features = self.frozen_encoder(x, mask=mask, return_projection=False)
        
        # Classify
        logits = self.classifier(encoder_features)
        return logits

def train_classification_stage(model, train_loader, val_loader, params, n_epochs=10):
    """Stage 2: Train probabilistic classifier"""
    logger.info("🎯 Stage 2: Training probabilistic classifier...")
    
    optimizer = torch.optim.Adam(model.classifier.parameters(), lr=params['cls_lr'], weight_decay=params['cls_weight_decay'])
    scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    criterion = nn.BCEWithLogitsLoss()
    
    best_val_auc = 0
    patience_counter = 0
    patience = 15
    
    for epoch in range(n_epochs):
        # Training
        model.train()
        train_loss = 0
        n_batches = 0
        
        for batch_sequences, batch_labels, batch_masks, _ in train_loader:
            batch_sequences = batch_sequences.to(device)
            batch_labels = batch_labels.to(device).float()
            batch_masks = batch_masks.to(device)
            
            optimizer.zero_grad()
            
            logits = model(batch_sequences, mask=batch_masks)
            loss = criterion(logits.squeeze(), batch_labels)
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            n_batches += 1
        
        # Validation
        model.eval()
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for batch_sequences, batch_labels, batch_masks, _ in val_loader:
                batch_sequences = batch_sequences.to(device)
                batch_labels = batch_labels.to(device)
                batch_masks = batch_masks.to(device)
                
                logits = model(batch_sequences, mask=batch_masks)
                probs = torch.sigmoid(logits)
                
                val_preds.extend(probs.cpu().numpy())
                val_targets.extend(batch_labels.cpu().numpy())
        
        # Calculate metrics
        try:
            val_auc = roc_auc_score(val_targets, val_preds)
        except:
            val_auc = 0.5
        
        avg_train_loss = train_loss / n_batches
        scheduler.step(avg_train_loss)
        
        if epoch % 10 == 0:
            logger.info(f"   Epoch {epoch+1}: Train Loss = {avg_train_loss:.4f}, Val AUC = {val_auc:.4f}")
        
        # Early stopping
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            logger.info(f"   Early stopping at epoch {epoch+1}")
            break
    
    logger.info(f"   ✅ Classification training completed. Best Val AUC: {best_val_auc:.4f}")
    return best_val_auc

def load_and_prepare_data():
    """Load raw time series data"""
    logger.info("📊 Loading raw time series data...")
    
    try:
        # Load raw data
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        logger.info(f"   ✅ Raw data loaded: {X_data.shape}")
        
        # Extract time series and labels
        time_series_list = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            grouped = X_data.groupby(level='id')
            for id_, group in grouped:
                series_data = group.sort_index(level='time')
                values = series_data['value'].values
                periods = series_data['period'].values
                
                # Find break point
                period_changes = np.where(np.diff(periods) != 0)[0]
                break_point = period_changes[0] + 1 if len(period_changes) > 0 else len(values) // 2
                
                time_series_list.append((values, break_point))
                
                label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                labels.append(int(label))
        
        logger.info(f"   ✅ Extracted {len(time_series_list)} time series")
        logger.info(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        
        return time_series_list, labels
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def optimize_contrastive_hyperparameters(time_series_list, labels, n_trials=1, n_folds=5):
    """Step 1: Optimize contrastive hyperparameters separately"""
    logger.info("🔧 Step 1: Optimizing contrastive hyperparameters...")
    
    def objective(trial):
        # Suggest contrastive hyperparameters only
        params = {
            'lr': trial.suggest_float('lr', 1e-4, 1e-2, log=True),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
            'margin': trial.suggest_float('margin', 0.2, 1.0),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64]),
            'hidden_dims': trial.suggest_categorical('hidden_dims', [32, 64, 128]),
            'output_dims': trial.suggest_categorical('output_dims', [128, 256, 320]),
            'depth': trial.suggest_int('depth', 6, 12),
            'use_ts2vec_loss': False,  # Disable to avoid in-place issues
            'ts2vec_weight': trial.suggest_float('ts2vec_weight', 0.01, 0.5)
        }
        
        # Cross-validation for contrastive loss only
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        cv_losses = []
        
        for train_idx, val_idx in skf.split(time_series_list, labels):
            train_series = [time_series_list[i] for i in train_idx]
            train_labels = [labels[i] for i in train_idx]
            
            # Create dataset
            train_dataset = TimeSeriesContrastiveDataset(train_series, train_labels, max_length=1000)
            train_loader = DataLoader(train_dataset, batch_size=params['batch_size'], shuffle=True, 
                                    collate_fn=collate_variable_length, drop_last=True)
            
            # Train contrastive model
            contrastive_model = ContrastiveTS2Vec(
                input_dims=1,
                output_dims=params['output_dims'],
                hidden_dims=params['hidden_dims'],
                depth=params['depth']
            ).to(device)
            
            final_loss = train_contrastive_stage(contrastive_model, train_loader, params, n_epochs=5)
            cv_losses.append(final_loss)
            
            # Clean up GPU memory
            del contrastive_model
            torch.cuda.empty_cache()
        
        # Return negative loss (since Optuna maximizes)
        return -np.mean(cv_losses)
    
    # Create and run study
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best contrastive loss: {-study.best_value:.4f}")
    logger.info(f"   📊 Best contrastive params: {study.best_params}")
    
    return study.best_params

def train_final_contrastive_model(time_series_list, labels, contrastive_params):
    """Step 2: Train final contrastive model with best params"""
    logger.info("🎯 Step 2: Training final contrastive model...")
    
    # Create full dataset
    full_dataset = TimeSeriesContrastiveDataset(time_series_list, labels, max_length=1000)
    full_loader = DataLoader(full_dataset, batch_size=contrastive_params['batch_size'], shuffle=True, 
                           collate_fn=collate_variable_length, drop_last=True)
    
    # Create and train final contrastive model
    contrastive_model = ContrastiveTS2Vec(
        input_dims=1,
        output_dims=contrastive_params['output_dims'],
        hidden_dims=contrastive_params['hidden_dims'],
        depth=contrastive_params['depth']
    ).to(device)
    
    train_contrastive_stage(contrastive_model, full_loader, contrastive_params, n_epochs=5)
    
    logger.info("   ✅ Final contrastive model trained")
    return contrastive_model

def optimize_classifier_hyperparameters(contrastive_model, time_series_list, labels, n_trials=4, n_folds=3):
    """Step 3: Optimize classifier hyperparameters using frozen encoder"""
    logger.info("🔧 Step 3: Optimizing classifier hyperparameters...")
    
    def objective(trial):
        # Suggest classifier hyperparameters only
        params = {
            'cls_lr': trial.suggest_float('cls_lr', 1e-4, 1e-2, log=True),
            'cls_weight_decay': trial.suggest_float('cls_weight_decay', 1e-6, 1e-3, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [16, 32, 64])
        }
        
        # Cross-validation for classifier only
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        cv_aucs = []
        
        for train_idx, val_idx in skf.split(time_series_list, labels):
            train_series = [time_series_list[i] for i in train_idx]
            val_series = [time_series_list[i] for i in val_idx]
            train_labels = [labels[i] for i in train_idx]
            val_labels = [labels[i] for i in val_idx]
            
            # Create datasets
            train_dataset = TimeSeriesContrastiveDataset(train_series, train_labels, max_length=1000)
            val_dataset = TimeSeriesContrastiveDataset(val_series, val_labels, max_length=1000)
            
            train_loader = DataLoader(train_dataset, batch_size=params['batch_size'], shuffle=True, 
                                    collate_fn=collate_variable_length, drop_last=True)
            val_loader = DataLoader(val_dataset, batch_size=params['batch_size'], shuffle=False, 
                                  collate_fn=collate_variable_length)
            
            # Create classifier with frozen encoder
            classifier_model = FrozenContrastiveClassifier(
                contrastive_model, 
                encoder_dim=contrastive_model.output_dims
            ).to(device)
            
            val_auc = train_classification_stage(classifier_model, train_loader, val_loader, params, n_epochs=30)
            cv_aucs.append(val_auc)
            
            # Clean up GPU memory
            del classifier_model
            torch.cuda.empty_cache()
        
        return np.mean(cv_aucs)
    
    # Create and run study
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best classifier AUC: {study.best_value:.4f}")
    logger.info(f"   📊 Best classifier params: {study.best_params}")
    
    return study.best_params

def evaluate_comprehensive(contrastive_model, time_series_list, labels, classifier_params, n_folds=5):
    """Step 4: Comprehensive evaluation using frozen contrastive model"""
    logger.info("📊 Step 4: Comprehensive evaluation...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(time_series_list, labels)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        train_series = [time_series_list[i] for i in train_idx]
        val_series = [time_series_list[i] for i in val_idx]
        train_labels = [labels[i] for i in train_idx]
        val_labels = [labels[i] for i in val_idx]
        
        # Create datasets
        train_dataset = TimeSeriesContrastiveDataset(train_series, train_labels, max_length=1000)
        val_dataset = TimeSeriesContrastiveDataset(val_series, val_labels, max_length=1000)
        
        train_loader = DataLoader(train_dataset, batch_size=classifier_params['batch_size'], shuffle=True, 
                                collate_fn=collate_variable_length, drop_last=True)
        val_loader = DataLoader(val_dataset, batch_size=classifier_params['batch_size'], shuffle=False, 
                              collate_fn=collate_variable_length)
        
        # Create classifier with frozen contrastive encoder
        classifier_model = FrozenContrastiveClassifier(
            contrastive_model, 
            encoder_dim=contrastive_model.output_dims
        ).to(device)
        
        # Train classifier only
        train_classification_stage(classifier_model, train_loader, val_loader, classifier_params, n_epochs=100)
        
        # Evaluate
        classifier_model.eval()
        fold_preds = []
        fold_targets = []
        
        with torch.no_grad():
            for batch_sequences, batch_labels, batch_masks, _ in val_loader:
                batch_sequences = batch_sequences.to(device)
                batch_labels = batch_labels.to(device)
                batch_masks = batch_masks.to(device)
                
                logits = classifier_model(batch_sequences, mask=batch_masks)
                probs = torch.sigmoid(logits)
                
                fold_preds.extend(probs.cpu().numpy())
                fold_targets.extend(batch_labels.cpu().numpy())
        
        # Calculate metrics
        fold_preds_array = np.array(fold_preds).flatten()
        y_pred_binary = (fold_preds_array >= 0.5).astype(int)
        
        auc = roc_auc_score(fold_targets, fold_preds_array)
        f1 = f1_score(fold_targets, y_pred_binary)
        precision = precision_score(fold_targets, y_pred_binary, zero_division=0)
        recall = recall_score(fold_targets, y_pred_binary)
        accuracy = accuracy_score(fold_targets, y_pred_binary)
        
        # Confusion matrix
        tn, fp, fn, tp = confusion_matrix(fold_targets, y_pred_binary).ravel()
        
        # Store metrics
        all_metrics['auc'].append(auc)
        all_metrics['f1'].append(f1)
        all_metrics['precision'].append(precision)
        all_metrics['recall'].append(recall)
        all_metrics['accuracy'].append(accuracy)
        all_metrics['tp'].append(tp)
        all_metrics['tn'].append(tn)
        all_metrics['fp'].append(fp)
        all_metrics['fn'].append(fn)
        
        # Store for overall analysis
        all_y_true.extend(fold_targets)
        all_y_pred_proba.extend(fold_preds_array)
        
        logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
        
        # Clean up classifier only (keep contrastive model)
        del classifier_model
        torch.cuda.empty_cache()
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
    overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba)
    }

def main():
    """Main execution function - Refactored with separate optimizers"""
    logger.info("🚀 Starting Two-Stage Contrastive TS2Vec Classification...")
    
    # Load and prepare data once
    time_series_list, labels = load_and_prepare_data()
    
    # Step 1: Optimize contrastive hyperparameters separately
    contrastive_params = optimize_contrastive_hyperparameters(time_series_list, labels, n_trials=1, n_folds=5)
    
    # Step 2: Train final contrastive model once with best params
    contrastive_model = train_final_contrastive_model(time_series_list, labels, contrastive_params)
    
    # Step 3: Optimize classifier hyperparameters using frozen encoder
    classifier_params = optimize_classifier_hyperparameters(contrastive_model, time_series_list, labels, n_trials=1, n_folds=5)
    
    # Step 4: Final comprehensive evaluation
    results = evaluate_comprehensive(contrastive_model, time_series_list, labels, classifier_params, n_folds=5)
    
    # Print final results
    logger.info("\n🎉 TWO-STAGE CONTRASTIVE ANALYSIS COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Save results
    import joblib
    final_results = {
        'contrastive_params': contrastive_params,
        'classifier_params': classifier_params,
        'evaluation_results': results,
        'approach': 'two_stage_separate_optimizers'
    }
    joblib.dump(final_results, 'contrastive_ts2vec_results.joblib')
    logger.info("💾 Results saved to 'contrastive_ts2vec_results.joblib'")
    
    return final_results

if __name__ == "__main__":
    results = main()