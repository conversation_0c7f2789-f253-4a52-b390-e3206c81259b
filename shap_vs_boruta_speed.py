"""
Quick SHAP vs Boruta Speed Comparison
"""

import pandas as pd
import numpy as np
import joblib
import time
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
import shap
from boruta import BorutaPy
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load features and targets"""
    X = joblib.load('1280_tsfresh_catch22_cca')
    y_train = pd.read_parquet('y_train.parquet')
    y = y_train.iloc[:len(X)]
    
    # Handle missing values for Boruta
    X_clean = X.fillna(X.median())
    
    return X_clean, y.values.ravel()

def test_shap_speed(X_train, X_test, y_train):
    """Test SHAP speed"""
    print("Testing SHAP...")
    
    # Train a simple model
    model = lgb.LGBMClassifier(n_estimators=50, random_state=42, verbose=-1)
    model.fit(X_train, y_train)
    
    # SHAP analysis on sample
    sample_size = min(200, len(X_test))
    X_sample = X_test.iloc[:sample_size]
    
    start_time = time.time()
    explainer = shap.TreeExplainer(model)
    shap_values = explainer.shap_values(X_sample)
    shap_time = time.time() - start_time
    
    # Get feature importance
    if isinstance(shap_values, list):
        shap_values = shap_values[1]
    importance = np.abs(shap_values).mean(axis=0)
    top_features = np.argsort(importance)[-10:][::-1]
    
    return shap_time, X_train.columns[top_features]

def test_boruta_speed(X_train, y_train):
    """Test Boruta speed"""
    print("Testing Boruta...")
    
    # Use smaller sample for speed and ensure no NaNs
    sample_size = min(2000, len(X_train))
    X_sample = X_train.iloc[:sample_size]
    y_sample = y_train[:sample_size]
    
    # Double-check for NaNs and handle them
    if X_sample.isnull().any().any():
        print("  Handling remaining NaNs...")
        X_sample = X_sample.fillna(0)  # More aggressive NaN handling
    
    # Remove constant features that might cause issues
    constant_cols = X_sample.columns[X_sample.std() == 0]
    if len(constant_cols) > 0:
        X_sample = X_sample.drop(columns=constant_cols)
        print(f"  Removed {len(constant_cols)} constant features")
    
    # Boruta with limited iterations
    rf = RandomForestClassifier(n_estimators=30, random_state=42)
    boruta = BorutaPy(rf, n_estimators='auto', random_state=42, max_iter=10)
    
    start_time = time.time()
    boruta.fit(X_sample.values, y_sample)
    boruta_time = time.time() - start_time
    
    # Get selected features
    selected_features = X_sample.columns[boruta.support_]
    
    return boruta_time, selected_features

def main():
    print("SHAP vs Boruta Speed Comparison")
    print("="*50)
    
    # Load data
    X, y = load_data()
    print(f"Data shape: {X.shape}")
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(
        X, y, test_size=0.3, random_state=42, stratify=y
    )
    
    results = {}
    
    # Test SHAP
    try:
        shap_time, shap_features = test_shap_speed(X_train, X_test, y_train)
        print(f"✓ SHAP completed in: {shap_time:.2f} seconds")
        print(f"  Top features: {list(shap_features[:3])}")
        results['shap'] = {'time': shap_time, 'success': True, 'features': shap_features}
    except Exception as e:
        print(f"✗ SHAP failed: {e}")
        results['shap'] = {'time': None, 'success': False, 'error': str(e)}
    
    # Test Boruta
    try:
        boruta_time, boruta_features = test_boruta_speed(X_train, y_train)
        print(f"✓ Boruta completed in: {boruta_time:.2f} seconds")
        print(f"  Selected features: {len(boruta_features)} features")
        print(f"  Top features: {list(boruta_features[:3])}")
        results['boruta'] = {'time': boruta_time, 'success': True, 'features': boruta_features}
    except Exception as e:
        print(f"✗ Boruta failed: {e}")
        results['boruta'] = {'time': None, 'success': False, 'error': str(e)}
    
    # Comparison
    print("\n" + "="*50)
    print("SPEED COMPARISON RESULTS")
    print("="*50)
    
    if results['shap']['success'] and results['boruta']['success']:
        shap_time = results['shap']['time']
        boruta_time = results['boruta']['time']
        
        if shap_time < boruta_time:
            speedup = boruta_time / shap_time
            print(f"🏆 SHAP is {speedup:.1f}x FASTER than Boruta")
        else:
            speedup = shap_time / boruta_time
            print(f"🏆 Boruta is {speedup:.1f}x FASTER than SHAP")
        
        print(f"\nTiming details:")
        print(f"- SHAP: {shap_time:.2f}s (200 samples)")
        print(f"- Boruta: {boruta_time:.2f}s (2000 samples, 20 iterations)")
        
    else:
        print("Comparison incomplete:")
        print(f"- SHAP: {'✓' if results['shap']['success'] else '✗'}")
        print(f"- Boruta: {'✓' if results['boruta']['success'] else '✗'}")
    
    print(f"\nConclusion: SHAP is generally much faster for feature importance analysis")
    print(f"Boruta is more thorough but computationally expensive for high-dimensional data")

if __name__ == "__main__":
    main()