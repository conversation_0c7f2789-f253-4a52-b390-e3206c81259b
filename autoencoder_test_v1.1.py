#!/usr/bin/env python3
"""
VAE Autoencoder V1.1 with Truncation-Aware Thermodynamic Features
PyTorch CUDA implementation with Optuna optimization and thermodynamic feature integration
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
from sklearn.preprocessing import StandardScaler
import logging
from typing import Dict, Tuple
import warnings
from tqdm import tqdm
from scipy import stats, signal
try:
    from scipy.integrate import trapz
except ImportError:
    from scipy.integrate import trapezoid as trapz

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

class AdaptiveTruncationDetector:
    """Adaptive truncation detection that learns patterns from actual data"""
    
    def __init__(self, safe_threshold: float = 1e-12):
        self.safe_threshold = safe_threshold
        self.suspicious_extremes = []
        self.suspicious_ranges = []
        self.precision_patterns = {}
        self.clustering_thresholds = []
        self.boundary_sensitivity = 1e-8
        self.is_calibrated = False
        
        # Statistical thresholds for pattern detection
        # self.extreme_frequency_threshold = 0.005  # 0.5% of samples
        # self.range_frequency_threshold = 0.01     # 1% of samples
        # self.precision_frequency_threshold = 0.15  # 15% of values
        self.clustering_significance = 0.02        # 2% clustering threshold
        self.extreme_frequency_threshold = 0.0005
        self.range_frequency_threshold = 0.001
        self.precision_frequency_threshold = 0.15
    def calibrate_on_dataset(self, dataset_samples: list, verbose: bool = True):
        """Calibrate detector on actual dataset to learn truncation patterns"""
        if verbose:
            logger.info("🔧 Calibrating adaptive truncation detector on dataset...")
        
        self.suspicious_extremes = self._discover_suspicious_extremes(dataset_samples)
        self.suspicious_ranges = self._discover_suspicious_ranges(dataset_samples)
        self.precision_patterns = self._analyze_precision_patterns(dataset_samples)
        self._set_adaptive_clustering_thresholds(dataset_samples)
        self._set_boundary_sensitivity(dataset_samples)
        self._calculate_dataset_statistics(dataset_samples)
        
        self.is_calibrated = True
        
        if verbose:
            logger.info(f"   ✅ Found {len(self.suspicious_extremes)} suspicious extreme values")
            logger.info(f"   ✅ Found {len(self.suspicious_ranges)} suspicious ranges")
            logger.info(f"   ✅ Identified {len(self.precision_patterns)} precision patterns")

    def _discover_suspicious_extremes(self, dataset_samples: list) -> list:
        """Discover frequently occurring extreme values across the dataset"""
        from collections import Counter
        
        all_mins = []
        all_maxs = []
        
        for sample in dataset_samples:
            if len(sample) > 0:
                all_mins.append(np.min(sample))
                all_maxs.append(np.max(sample))
        
        rounded_mins = [round(x, 10) for x in all_mins]
        rounded_maxs = [round(x, 10) for x in all_maxs]
        
        min_counter = Counter(rounded_mins)
        max_counter = Counter(rounded_maxs)
        
        total_samples = len(dataset_samples)
        threshold_count = max(1, int(total_samples * self.extreme_frequency_threshold))
        
        suspicious_extremes = []
        
        for value, count in min_counter.most_common():
            if count >= threshold_count:
                suspicious_extremes.append(value)
        
        for value, count in max_counter.most_common():
            if count >= threshold_count and value not in suspicious_extremes:
                suspicious_extremes.append(value)
        
        return sorted(set(suspicious_extremes))

    def _discover_suspicious_ranges(self, dataset_samples: list) -> list:
        """Discover frequently occurring ranges that suggest truncation"""
        from collections import Counter
        
        all_ranges = []
        for sample in dataset_samples:
            if len(sample) > 1:
                range_val = np.max(sample) - np.min(sample)
                all_ranges.append(range_val)
        
        rounded_ranges = [round(x, 10) for x in all_ranges if x > 0]
        range_counter = Counter(rounded_ranges)
        
        total_samples = len([r for r in all_ranges if r > 0])
        threshold_count = max(1, int(total_samples * self.range_frequency_threshold))
        
        suspicious_ranges = []
        for range_val, count in range_counter.most_common():
            if count >= threshold_count:
                suspicious_ranges.append(range_val)
        
        return sorted(suspicious_ranges)

    def _analyze_precision_patterns(self, dataset_samples: list) -> dict:
        """Analyze decimal precision patterns across the dataset"""
        from collections import Counter
        
        precision_counts = Counter()
        total_values = 0
        
        for sample in dataset_samples:
            for value in sample:
                if not np.isnan(value):
                    val_str = f"{value:.15f}"
                    if '.' in val_str:
                        decimal_part = val_str.split('.')[1]
                        non_zero_decimals = len(decimal_part.rstrip('0'))
                        precision_counts[non_zero_decimals] += 1
                        total_values += 1
        
        precision_patterns = {}
        for precision, count in precision_counts.items():
            frequency = count / total_values if total_values > 0 else 0
            if frequency > self.precision_frequency_threshold:
                precision_patterns[precision] = frequency
        
        return precision_patterns

    def _set_adaptive_clustering_thresholds(self, dataset_samples: list):
        """Set clustering thresholds based on actual data characteristics"""
        all_ranges = []
        for sample in dataset_samples:
            if len(sample) > 1:
                range_val = np.max(sample) - np.min(sample)
                if range_val > 0:
                    all_ranges.append(range_val)
        
        if len(all_ranges) > 0:
            median_range = np.median(all_ranges)
            self.clustering_thresholds = [
                0.001 * median_range,
                0.005 * median_range,
                0.01 * median_range,
                0.02 * median_range,
                0.05 * median_range
            ]
        else:
            self.clustering_thresholds = [1e-6, 5e-6, 1e-5, 2e-5, 5e-5]

    def _set_boundary_sensitivity(self, dataset_samples: list):
        """Set boundary detection sensitivity based on data scale"""
        all_values = []
        for sample in dataset_samples:
            all_values.extend(sample)
        
        if len(all_values) > 0:
            data_range = np.ptp(all_values)
            self.boundary_sensitivity = max(1e-12, data_range * 1e-6)
        else:
            self.boundary_sensitivity = 1e-8

    def _calculate_dataset_statistics(self, dataset_samples: list):
        """Calculate dataset-wide statistics for adaptive truncation detection"""
        from collections import Counter
        
        all_values = []
        for sample in dataset_samples:
            if len(sample) > 0:
                all_values.extend(sample)
        
        if len(all_values) > 0:
            self.global_value_range = (np.min(all_values), np.max(all_values))
        else:
            self.global_value_range = (0.0, 1.0)

    def compute_truncation_confidence(self, ts: np.ndarray) -> dict:
        """Compute overall truncation confidence metrics"""
        ts_clean = ts[~np.isnan(ts)]
        if len(ts_clean) < 2:
            return {
                'truncation_confidence': 1.0,
                'precision_quality_score': 1.0,
                'boundary_constraint_factor': 0.0,
                'clustering_artifact_score': 0.0,
                'flat_segment_ratio': 0.0
            }
        
        # Basic truncation detection
        precision_quality = self._detect_precision_truncation(ts_clean)
        boundary_factor = self._detect_boundary_truncation(ts_clean)
        clustering_score = self._detect_clustering_artifacts(ts_clean)
        flat_ratio = self._detect_flat_segments(ts_clean)
        
        # Overall confidence
        truncation_indicators = [
            1 - precision_quality,
            boundary_factor,
            clustering_score,
            flat_ratio
        ]
        
        truncation_confidence = 1 - np.mean(truncation_indicators)
        
        return {
            'truncation_confidence': max(0, min(1, truncation_confidence)),
            'precision_quality_score': precision_quality,
            'boundary_constraint_factor': boundary_factor,
            'clustering_artifact_score': clustering_score,
            'flat_segment_ratio': flat_ratio
        }

    def _detect_precision_truncation(self, ts_clean: np.ndarray) -> float:
        """Detect decimal precision limitations"""
        from collections import Counter
        
        precision_counts = Counter()
        total_values = len(ts_clean)
        
        for value in ts_clean:
            val_str = f"{value:.15f}"
            if '.' in val_str:
                decimal_part = val_str.split('.')[1]
                non_zero_decimals = len(decimal_part.rstrip('0'))
                precision_counts[non_zero_decimals] += 1
        
        # Penalize low precision
        precision_1 = precision_counts.get(0, 0) + precision_counts.get(1, 0)
        precision_2 = precision_counts.get(2, 0)
        
        truncation_score = (precision_1 * 2 + precision_2) / total_values * 100
        precision_quality_score = max(0, 1 - truncation_score / 100)
        
        return precision_quality_score

    def _detect_boundary_truncation(self, ts_clean: np.ndarray) -> float:
        """Detect artificial boundaries and constraints"""
        min_val, max_val = np.min(ts_clean), np.max(ts_clean)
        
        # Check for suspicious values
        rounded_min = round(min_val, 10)
        rounded_max = round(max_val, 10)
        
        basic_suspicious = [0.0, 0.1, -0.1, 0.01, -0.01, 0.05, -0.05, 0.07, -0.07]
        min_is_suspicious = any(abs(rounded_min - sv) < 1e-10 for sv in basic_suspicious)
        max_is_suspicious = any(abs(rounded_max - sv) < 1e-10 for sv in basic_suspicious)
        
        symmetric_bounds = abs(abs(min_val) - abs(max_val)) < 1e-8 and abs(min_val) > 0.01
        
        boundary_indicators = sum([min_is_suspicious, max_is_suspicious, symmetric_bounds])
        return boundary_indicators / 3.0

    def _detect_clustering_artifacts(self, ts_clean: np.ndarray) -> float:
        """Detect value clustering that indicates truncation"""
        if len(ts_clean) < 10:
            return 0.0
        
        min_val, max_val = np.min(ts_clean), np.max(ts_clean)
        range_val = max_val - min_val
        
        if range_val == 0:
            return 1.0
        
        # Check clustering at boundaries
        threshold = 0.01 * range_val
        near_min = np.sum(np.abs(ts_clean - min_val) <= threshold)
        near_max = np.sum(np.abs(ts_clean - max_val) <= threshold)
        
        clustering_score = (near_min + near_max) / len(ts_clean)
        return clustering_score

    def _detect_flat_segments(self, ts_clean: np.ndarray) -> float:
        """Detect flat segments from truncation"""
        if len(ts_clean) < 3:
            return 0.0
        
        velocity = np.diff(ts_clean)
        flat_points = np.sum(np.abs(velocity) < 1e-10)
        flat_segment_ratio = flat_points / len(velocity)
        
        return flat_segment_ratio

class TruncationAwareThermodynamicAnalyzer:
    """Thermodynamic analyzer with integrated truncation awareness"""
    
    def __init__(self, temperature: float = 1.0, damping: float = 0.1):
        self.temperature = temperature
        self.damping = damping
        self.truncation_detector = AdaptiveTruncationDetector()

    def analyze_time_series(self, ts: np.ndarray, tstar: int = None) -> dict:
        """Comprehensive truncation-aware thermodynamic analysis"""
        ts = np.array(ts)
        N = len(ts)
        
        # Detect truncation patterns
        truncation_metrics = self.truncation_detector.compute_truncation_confidence(ts)
        
        # Compute basic thermodynamic metrics
        ts_clean = ts[~np.isnan(ts)]
        if len(ts_clean) < 2:
            return {
                'truncation_confidence': 0.0,
                'quality_weighted_total_energy': 0.0,
                'robust_dissipation_rate': 0.0,
                'validated_ergodic_measure': 0.0,
                'clustering_corrected_entropy': 0.0,
                'unconstrained_phase_volume': 1.0
            }
        
        confidence = truncation_metrics['truncation_confidence']
        
        # Potential energy (quality-weighted)
        potential_energy = np.sum(ts_clean**2) / (len(ts_clean) + 1e-12) * confidence
        
        # Kinetic energy (corrected for flat segments)
        velocity = np.diff(ts_clean)
        if len(velocity) > 0:
            flat_ratio = truncation_metrics['flat_segment_ratio']
            significant_velocity = velocity[np.abs(velocity) > 1e-10]
            if len(significant_velocity) > 0:
                kinetic_energy = np.mean(significant_velocity**2) * (1 - flat_ratio * 0.3)
            else:
                kinetic_energy = 0.0
        else:
            kinetic_energy = 0.0
        
        # Total energy
        total_energy = potential_energy + kinetic_energy
        quality_weighted_total_energy = total_energy * confidence
        
        # Dissipation rate
        if len(velocity) > 1:
            dissipation_rate = self.damping * np.var(velocity) * confidence
        else:
            dissipation_rate = 0.0
        
        # Entropy (corrected for clustering)
        clustering_score = truncation_metrics['clustering_artifact_score']
        base_bins = max(10, int(np.sqrt(len(ts_clean))))
        adjusted_bins = max(5, int(base_bins * (1 - clustering_score * 0.5)))
        
        hist, bin_edges = np.histogram(ts_clean, bins=adjusted_bins, density=True)
        p_i = hist[hist > 0] * (bin_edges[1] - bin_edges[0])
        entropy = -np.sum(p_i * np.log(p_i + 1e-12))
        clustering_corrected_entropy = entropy * (1 + clustering_score * 0.3) * confidence
        
        # Phase volume (corrected for boundaries)
        if len(ts_clean) > 1:
            x = ts_clean[:-1]
            v = velocity
            boundary_factor = truncation_metrics['boundary_constraint_factor']
            
            x_range = np.ptp(x)
            v_range = np.ptp(v)
            boundary_expansion = 1 + boundary_factor * 0.5
            unconstrained_phase_volume = x_range * v_range * np.pi * boundary_expansion * confidence
        else:
            unconstrained_phase_volume = 1.0
        
        # Ergodic measure
        if len(ts_clean) >= 10:
            n_windows = 10
            window_size = len(ts_clean) // n_windows
            time_avg = np.mean(ts_clean)
            ensemble_avgs = []
            
            for i in range(0, len(ts_clean)-window_size+1, window_size):
                window_data = ts_clean[i:i+window_size]
                if len(window_data) > 0:
                    ensemble_avgs.append(np.mean(window_data))
            
            if len(ensemble_avgs) > 1:
                ensemble_avg = np.mean(ensemble_avgs)
                ensemble_std = np.std(ensemble_avgs)
                if ensemble_std > 0:
                    ergodic_measure = np.abs(time_avg - ensemble_avg) / ensemble_std
                    validated_ergodic_measure = ergodic_measure * confidence
                else:
                    validated_ergodic_measure = 0.0
            else:
                validated_ergodic_measure = 0.0
        else:
            validated_ergodic_measure = 0.0
        
        return {
            'truncation_confidence': confidence,
            'quality_weighted_total_energy': quality_weighted_total_energy,
            'robust_dissipation_rate': dissipation_rate,
            'validated_ergodic_measure': validated_ergodic_measure,
            'clustering_corrected_entropy': clustering_corrected_entropy,
            'unconstrained_phase_volume': unconstrained_phase_volume
        }

class TimeSeriesDistributionDataset(Dataset):
    """Dataset for time series distribution pairs"""
    
    def __init__(self, X_features, y_labels):
        self.X = torch.FloatTensor(X_features)
        self.y = torch.LongTensor(y_labels)
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]

class DistributionVAE(nn.Module):
    """Variational Autoencoder for time series distribution analysis"""
    
    def __init__(self, input_dim, latent_dim=32, hidden_dims=[128, 64]):
        super(DistributionVAE, self).__init__()
        
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        
        # Encoder
        encoder_layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Latent space
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_logvar = nn.Linear(hidden_dims[-1], latent_dim)
        
        # Decoder
        decoder_layers = []
        prev_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        decoder_layers.append(nn.Linear(hidden_dims[0], input_dim))
        self.decoder = nn.Sequential(*decoder_layers)
        
        # Classifier head - output raw logits (no sigmoid)
        self.classifier = nn.Sequential(
            nn.Linear(latent_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(16, 1)
            # No sigmoid - BCEWithLogitsLoss will handle it
        )
    
    def encode(self, x):
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        return self.decoder(z)
    
    def forward(self, x):
        mu, logvar = self.encode(x)
        z = self.reparameterize(mu, logvar)
        recon_x = self.decode(z)
        class_pred = self.classifier(z)
        return recon_x, class_pred, mu, logvar

def load_and_prepare_data():
    """Load raw time series data and extract distribution features"""
    logger.info("📊 Loading raw time series data...")
    
    try:
        # Load raw data
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        logger.info(f"   ✅ Raw data loaded: {X_data.shape}")
        
        # Extract time series and labels
        time_series_list = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            grouped = X_data.groupby(level='id')
            for id_, group in grouped:
                series_data = group.sort_index(level='time')
                values = series_data['value'].values
                periods = series_data['period'].values
                
                # Find break point
                period_changes = np.where(np.diff(periods) != 0)[0]
                break_point = period_changes[0] + 1 if len(period_changes) > 0 else len(values) // 2
                
                time_series_list.append((values, break_point))
                
                label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                labels.append(int(label))
        
        logger.info(f"   ✅ Extracted {len(time_series_list)} time series")
        logger.info(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        
        return time_series_list, labels
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def extract_truncation_aware_features(time_series_list, n_bins=50):
    """Extract distribution features enhanced with truncation-aware thermodynamic metrics"""
    logger.info(f"🔧 Extracting truncation-aware features (n_bins={n_bins})...")
    
    # Initialize truncation detector and calibrate on dataset
    detector = AdaptiveTruncationDetector()
    series_for_calibration = [ts[0] for ts in time_series_list if len(ts[0]) > 2]
    if len(series_for_calibration) > 0:
        detector.calibrate_on_dataset(series_for_calibration, verbose=True)
    
    # Initialize thermodynamic analyzer
    analyzer = TruncationAwareThermodynamicAnalyzer()
    
    features_list = []
    
    for i, (series, break_point) in enumerate(tqdm(time_series_list, desc="Processing series")):
        if i % 1000 == 0:
            logger.info(f"   Processing {i+1}/{len(time_series_list)}...")
        
        series_clean = series[~np.isnan(series)]
        if len(series_clean) < 20:
            continue
        
        # Split into pre and post segments
        pre_segment = series_clean[:break_point]
        post_segment = series_clean[break_point:]
        
        if len(pre_segment) < 5 or len(post_segment) < 5:
            continue
        
        features = []
        
        # Original distribution features
        pre_hist, _ = np.histogram(pre_segment, bins=n_bins, density=True)
        features.extend(pre_hist)
        
        post_hist, _ = np.histogram(post_segment, bins=n_bins, density=True)
        features.extend(post_hist)
        
        # Original comparative statistics
        features.extend([
            np.mean(pre_segment), np.std(pre_segment), np.median(pre_segment),
            np.mean(post_segment), np.std(post_segment), np.median(post_segment),
            np.mean(post_segment) - np.mean(pre_segment),
            np.std(post_segment) / (np.std(pre_segment) + 1e-8),
            np.percentile(pre_segment, 25), np.percentile(pre_segment, 75),
            np.percentile(post_segment, 25), np.percentile(post_segment, 75)
        ])
        
        # Original KL divergence and Wasserstein distance
        pre_hist_smooth = pre_hist + 1e-8
        post_hist_smooth = post_hist + 1e-8
        kl_div = np.sum(pre_hist_smooth * np.log(pre_hist_smooth / post_hist_smooth))
        features.append(kl_div)
        
        wasserstein_approx = np.sum(np.abs(np.cumsum(pre_hist) - np.cumsum(post_hist)))
        features.append(wasserstein_approx)
        
        # NEW: Truncation-aware thermodynamic features for full series
        thermo_metrics = analyzer.analyze_time_series(series_clean, tstar=break_point)
        thermodynamic_features = [
            thermo_metrics['truncation_confidence'],
            thermo_metrics['quality_weighted_total_energy'],
            thermo_metrics['robust_dissipation_rate'],
            thermo_metrics['validated_ergodic_measure'],
            thermo_metrics['clustering_corrected_entropy'],
            thermo_metrics['unconstrained_phase_volume']
        ]
        features.extend(thermodynamic_features)
        
        # NEW: Segment-specific thermodynamic features
        pre_thermo = analyzer.analyze_time_series(pre_segment)
        post_thermo = analyzer.analyze_time_series(post_segment)
        
        segment_features = [
            pre_thermo['truncation_confidence'],
            post_thermo['truncation_confidence'],
            pre_thermo['quality_weighted_total_energy'],
            post_thermo['quality_weighted_total_energy'],
            pre_thermo['clustering_corrected_entropy'],
            post_thermo['clustering_corrected_entropy'],
            # Thermodynamic ratios
            post_thermo['quality_weighted_total_energy'] / (pre_thermo['quality_weighted_total_energy'] + 1e-8),
            post_thermo['clustering_corrected_entropy'] / (pre_thermo['clustering_corrected_entropy'] + 1e-8),
            abs(pre_thermo['truncation_confidence'] - post_thermo['truncation_confidence'])  # Truncation asymmetry
        ]
        features.extend(segment_features)
        
        features_list.append(features)
    
    features_array = np.array(features_list)
    logger.info(f"   ✅ Extracted truncation-aware features shape: {features_array.shape}")
    logger.info(f"   📊 Added {len(thermodynamic_features) + len(segment_features)} thermodynamic features")
    
    return features_array

def vae_loss_function(recon_x, x, mu, logvar, class_pred, y_true, beta=1.0, alpha=1.0):
    """Combined VAE and classification loss"""
    # Reconstruction loss
    recon_loss = nn.MSELoss()(recon_x, x)
    
    # KL divergence loss
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    kl_loss /= x.size(0) * x.size(1)  # Normalize by batch size and feature dim
    
    # Classification loss - use BCEWithLogitsLoss for numerical stability
    class_pred_flat = class_pred.view(-1)  # Flatten to 1D
    y_true_flat = y_true.float().view(-1)  # Flatten to 1D
    class_loss = nn.BCEWithLogitsLoss()(class_pred_flat, y_true_flat)
    
    # Combined loss
    total_loss = recon_loss + beta * kl_loss + alpha * class_loss
    
    return total_loss, recon_loss, kl_loss, class_loss

def train_vae_model(model, train_loader, val_loader, params, n_epochs=100):
    """Train VAE model with early stopping"""
    optimizer = optim.Adam(model.parameters(), lr=params['lr'], weight_decay=params['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    best_val_auc = 0
    patience_counter = 0
    patience = 15
    
    for epoch in range(n_epochs):
        # Training
        model.train()
        train_loss = 0
        train_preds = []
        train_targets = []
        
        for batch_x, batch_y in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            
            optimizer.zero_grad()
            recon_x, class_pred, mu, logvar = model(batch_x)
            
            loss, recon_loss, kl_loss, class_loss = vae_loss_function(
                recon_x, batch_x, mu, logvar, class_pred, batch_y,
                beta=params['beta'], alpha=params['alpha']
            )
            
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
            train_preds.extend(class_pred.cpu().detach().numpy())
            train_targets.extend(batch_y.cpu().numpy())
        
        # Validation
        model.eval()
        val_loss = 0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                
                recon_x, class_pred, mu, logvar = model(batch_x)
                loss, _, _, _ = vae_loss_function(
                    recon_x, batch_x, mu, logvar, class_pred, batch_y,
                    beta=params['beta'], alpha=params['alpha']
                )
                
                val_loss += loss.item()
                val_preds.extend(class_pred.cpu().numpy())
                val_targets.extend(batch_y.cpu().numpy())
        
        # Calculate metrics - convert logits to probabilities
        try:
            val_preds_proba = torch.sigmoid(torch.tensor(val_preds)).numpy()
            val_auc = roc_auc_score(val_targets, val_preds_proba)
        except:
            val_auc = 0.5
        scheduler.step(val_loss)
        
        # Early stopping
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            break
    
    return best_val_auc

def optimize_vae_hyperparameters(X_features, y_labels, n_trials=50, n_folds=5):
    """Optimize VAE hyperparameters using Optuna"""
    logger.info("🔧 Optimizing VAE hyperparameters...")
    
    def objective(trial):
        # Suggest hyperparameters
        params = {
            'latent_dim': trial.suggest_int('latent_dim', 16, 64),
            'hidden_dim1': trial.suggest_int('hidden_dim1', 64, 256),
            'hidden_dim2': trial.suggest_int('hidden_dim2', 32, 128),
            'lr': trial.suggest_float('lr', 1e-4, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [1024, 2048, 3072]),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
            'beta': trial.suggest_float('beta', 0.1, 2.0),
            'alpha': trial.suggest_float('alpha', 0.5, 3.0)
        }
        
        # Cross-validation
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        cv_scores = []
        
        for train_idx, val_idx in skf.split(X_features, y_labels):
            X_train, X_val = X_features[train_idx], X_features[val_idx]
            y_train, y_val = y_labels[train_idx], y_labels[val_idx]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Create datasets
            train_dataset = TimeSeriesDistributionDataset(X_train_scaled, y_train)
            val_dataset = TimeSeriesDistributionDataset(X_val_scaled, y_val)
            
            train_loader = DataLoader(train_dataset, batch_size=params['batch_size'], shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=params['batch_size'], shuffle=False)
            
            # Create model
            model = DistributionVAE(
                input_dim=X_features.shape[1],
                latent_dim=params['latent_dim'],
                hidden_dims=[params['hidden_dim1'], params['hidden_dim2']]
            ).to(device)
            
            # Train model
            val_auc = train_vae_model(model, train_loader, val_loader, params, n_epochs=50)
            cv_scores.append(val_auc)
            
            # Clean up GPU memory
            del model
            torch.cuda.empty_cache()
        
        return np.mean(cv_scores)
    
    # Create and run study
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best AUC: {study.best_value:.4f}")
    logger.info(f"   📊 Best params: {study.best_params}")
    
    return study.best_params, study.best_value

def evaluate_vae_comprehensive(X_features, y_labels, best_params, n_folds=5):
    """Comprehensive evaluation of VAE model"""
    logger.info("📊 Comprehensive VAE evaluation...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_features, y_labels)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X_features[train_idx], X_features[val_idx]
        y_train, y_val = y_labels[train_idx], y_labels[val_idx]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # Create datasets
        train_dataset = TimeSeriesDistributionDataset(X_train_scaled, y_train)
        val_dataset = TimeSeriesDistributionDataset(X_val_scaled, y_val)
        
        train_loader = DataLoader(train_dataset, batch_size=best_params['batch_size'], shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=best_params['batch_size'], shuffle=False)
        
        # Create and train model
        model = DistributionVAE(
            input_dim=X_features.shape[1],
            latent_dim=best_params['latent_dim'],
            hidden_dims=[best_params['hidden_dim1'], best_params['hidden_dim2']]
        ).to(device)
        
        # Train model
        train_vae_model(model, train_loader, val_loader, best_params, n_epochs=1000)
        
        # Evaluate
        model.eval()
        fold_preds = []
        fold_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                _, class_pred, _, _ = model(batch_x)
                
                fold_preds.extend(class_pred.cpu().numpy())
                fold_targets.extend(batch_y.cpu().numpy())
        
        # Calculate metrics - convert logits to probabilities
        fold_preds_proba = torch.sigmoid(torch.tensor(fold_preds)).numpy()
        y_pred_binary = (fold_preds_proba >= 0.5).astype(int)
        
        auc = roc_auc_score(fold_targets, fold_preds_proba)
        f1 = f1_score(fold_targets, y_pred_binary)
        precision = precision_score(fold_targets, y_pred_binary, zero_division=0)
        recall = recall_score(fold_targets, y_pred_binary)
        accuracy = accuracy_score(fold_targets, y_pred_binary)
        
        # Confusion matrix
        tn, fp, fn, tp = confusion_matrix(fold_targets, y_pred_binary).ravel()
        
        # Store metrics
        all_metrics['auc'].append(auc)
        all_metrics['f1'].append(f1)
        all_metrics['precision'].append(precision)
        all_metrics['recall'].append(recall)
        all_metrics['accuracy'].append(accuracy)
        all_metrics['tp'].append(tp)
        all_metrics['tn'].append(tn)
        all_metrics['fp'].append(fp)
        all_metrics['fn'].append(fn)
        
        # Store for overall analysis
        all_y_true.extend(fold_targets)
        all_y_pred_proba.extend(fold_preds_proba)
        
        logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
        
        # Clean up
        del model
        torch.cuda.empty_cache()
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
    overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba)
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting VAE with Truncation-Aware Thermodynamic Features...")
    
    # Load and prepare data
    time_series_list, labels = load_and_prepare_data()
    
    # Extract truncation-aware features (NEW)
    X_features = extract_truncation_aware_features(time_series_list, n_bins=50)
    y_labels = np.array(labels[:len(X_features)])  # Match lengths
    
    logger.info(f"📊 Final dataset: {X_features.shape[0]} samples, {X_features.shape[1]} features")
    logger.info(f"   📈 Feature breakdown: ~{2*50} distribution + ~14 statistical + 15 thermodynamic features")
    
    # Optimize hyperparameters
    best_params, best_auc = optimize_vae_hyperparameters(X_features, y_labels, n_trials=30, n_folds=5)
    
    # Comprehensive evaluation
    results = evaluate_vae_comprehensive(X_features, y_labels, best_params, n_folds=5)
    
    # Print final results
    logger.info("\n🎉 TRUNCATION-AWARE VAE ANALYSIS COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Save results
    import joblib
    final_results = {
        'best_params': best_params,
        'evaluation_results': results,
        'feature_shape': X_features.shape,
        'thermodynamic_features_added': 15
    }
    joblib.dump(final_results, 'truncation_aware_vae_results.joblib')
    logger.info("💾 Results saved to 'truncation_aware_vae_results.joblib'")
    
    return final_results

if __name__ == "__main__":
    results = main()