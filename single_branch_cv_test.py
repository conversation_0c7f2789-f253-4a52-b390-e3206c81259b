"""
Kernel Methods with Information Density Features
Walk-Forward CV vs Stratified K-Fold comparison for sequential learning benefits
"""

import numpy as np
import pandas as pd
from sklearn.metrics import roc_auc_score, accuracy_score, classification_report
from sklearn.model_selection import TimeSeriesSplit, StratifiedKFold
from sklearn.svm import SVC
from sklearn.preprocessing import StandardScaler
from sklearn.pipeline import Pipeline
import matplotlib.pyplot as plt
import json
from collections import Counter
from scipy.stats import wasserstein_distance, energy_distance, ks_2samp, mannwhitneyu, ttest_ind
import warnings
warnings.filterwarnings('ignore')

def compute_information_volume_differential(normal_part, candidate_part):
    """Compute Information Volume Differential - key metric for structural breaks"""
    features = {}
    
    # Multiple information volume measures
    def compute_entropy(data):
        """Compute Shannon entropy"""
        try:
            hist, _ = np.histogram(data, bins=15, density=True)
            hist = hist / (np.sum(hist) + 1e-8)
            return -np.sum(hist * np.log(hist + 1e-8))
        except:
            return 0.0
    
    def compute_emc(data):
        """Compute Effective Measure Complexity"""
        try:
            hist, _ = np.histogram(data, bins=20, density=True)
            hist = hist / (np.sum(hist) + 1e-8)
            H = -np.sum(hist * np.log(hist + 1e-8))
            uniform_prob = 1.0 / len(hist)
            D = np.sum((hist - uniform_prob) ** 2)
            return H * D
        except:
            return 0.0
    
    def compression_ratio(data):
        """Compute compression ratio"""
        try:
            import zlib
            data_bytes = np.array(data * 1000, dtype=np.int16).tobytes()
            compressed = zlib.compress(data_bytes)
            return len(compressed) / len(data_bytes) if len(data_bytes) > 0 else 0.0
        except:
            return 0.0
    
    # Compute IVD for different measures
    normal_entropy = compute_entropy(normal_part)
    candidate_entropy = compute_entropy(candidate_part)
    features['entropy_ivd'] = abs(normal_entropy - candidate_entropy)
    
    normal_emc = compute_emc(normal_part)
    candidate_emc = compute_emc(candidate_part)
    features['emc_ivd'] = abs(normal_emc - candidate_emc)
    
    normal_compression = compression_ratio(normal_part)
    candidate_compression = compression_ratio(candidate_part)
    features['compression_ivd'] = abs(normal_compression - candidate_compression)
    
    # Combined IVD score
    features['combined_ivd'] = (features['entropy_ivd'] + features['emc_ivd'] + features['compression_ivd']) / 3
    
    return features

def compute_compression_features(normal_part, candidate_part):
    """Compute compression-based features"""
    features = {}
    
    try:
        import zlib
        
        # Individual compression ratios
        normal_bytes = np.array(normal_part * 1000, dtype=np.int16).tobytes()
        candidate_bytes = np.array(candidate_part * 1000, dtype=np.int16).tobytes()
        combined_bytes = normal_bytes + candidate_bytes
        
        normal_compressed = len(zlib.compress(normal_bytes))
        candidate_compressed = len(zlib.compress(candidate_bytes))
        combined_compressed = len(zlib.compress(combined_bytes))
        
        features['normal_compression_ratio'] = normal_compressed / len(normal_bytes) if len(normal_bytes) > 0 else 0.0
        features['candidate_compression_ratio'] = candidate_compressed / len(candidate_bytes) if len(candidate_bytes) > 0 else 0.0
        features['compression_ratio_diff'] = abs(features['normal_compression_ratio'] - features['candidate_compression_ratio'])
        
        # Normalized Compression Distance (NCD)
        min_compression = min(normal_compressed, candidate_compressed)
        max_compression = max(normal_compressed, candidate_compressed)
        
        if max_compression > 0:
            features['normalized_compression_distance'] = (combined_compressed - min_compression) / max_compression
        else:
            features['normalized_compression_distance'] = 0.0
        
        # Joint compression efficiency
        expected_combined = normal_compressed + candidate_compressed
        if expected_combined > 0:
            features['joint_compression_efficiency'] = combined_compressed / expected_combined
        else:
            features['joint_compression_efficiency'] = 1.0
            
    except:
        features['normal_compression_ratio'] = 0.0
        features['candidate_compression_ratio'] = 0.0
        features['compression_ratio_diff'] = 0.0
        features['normalized_compression_distance'] = 0.0
        features['joint_compression_efficiency'] = 1.0
    
    return features

def compute_all_information_features(normal_part, candidate_part):
    """Compute comprehensive information density features"""
    features = {}
    
    # 1. Information Volume Differential
    features.update(compute_information_volume_differential(normal_part, candidate_part))
    
    # 2. Compression features
    features.update(compute_compression_features(normal_part, candidate_part))
    
    # 3. Effective Measure Complexity (EMC)
    def compute_emc(data):
        try:
            hist, _ = np.histogram(data, bins=20, density=True)
            hist = hist / (np.sum(hist) + 1e-8)
            H = -np.sum(hist * np.log(hist + 1e-8))
            uniform_prob = 1.0 / len(hist)
            D = np.sum((hist - uniform_prob) ** 2)
            return H * D
        except:
            return 0.0
    
    features['emc_normal'] = compute_emc(normal_part)
    features['emc_candidate'] = compute_emc(candidate_part)
    features['emc_difference'] = abs(features['emc_normal'] - features['emc_candidate'])
    
    # 4. Multiscale Sample Entropy
    def sample_entropy_approx(data, m=2):
        try:
            if len(data) < m + 1:
                return 0.0
            patterns = set()
            for i in range(len(data) - m + 1):
                pattern = tuple(np.round(data[i:i+m], 2))
                patterns.add(pattern)
            return np.log(len(patterns) + 1) / np.log(len(data) - m + 2)
        except:
            return 0.0
    
    def multiscale_entropy(data, max_scale=3):
        mse_values = []
        for scale in range(1, max_scale + 1):
            if len(data) >= scale * 10:
                coarse_data = []
                for i in range(0, len(data) - scale + 1, scale):
                    coarse_data.append(np.mean(data[i:i+scale]))
                mse = sample_entropy_approx(coarse_data)
                mse_values.append(mse)
            else:
                mse_values.append(0.0)
        return mse_values
    
    normal_mse = multiscale_entropy(normal_part)
    candidate_mse = multiscale_entropy(candidate_part)
    
    features['mse_complexity_normal'] = np.sum(normal_mse)
    features['mse_complexity_candidate'] = np.sum(candidate_mse)
    features['mse_complexity_diff'] = abs(features['mse_complexity_normal'] - features['mse_complexity_candidate'])
    
    for i, (n_mse, c_mse) in enumerate(zip(normal_mse, candidate_mse)):
        features[f'mse_scale_{i+1}_diff'] = abs(n_mse - c_mse)
    
    # 5. Entropy measures
    def compute_entropy(data):
        try:
            hist, _ = np.histogram(data, bins=15, density=True)
            hist = hist / (np.sum(hist) + 1e-8)
            return -np.sum(hist * np.log(hist + 1e-8))
        except:
            return 0.0
    
    features['entropy_normal'] = compute_entropy(normal_part)
    features['entropy_candidate'] = compute_entropy(candidate_part)
    features['entropy_difference'] = abs(features['entropy_normal'] - features['entropy_candidate'])
    
    # 6. Permutation Entropy
    def permutation_entropy(data, order=3):
        try:
            if len(data) < order:
                return 0.0
            patterns = {}
            for i in range(len(data) - order + 1):
                segment = data[i:i+order]
                pattern = tuple(np.argsort(segment))
                patterns[pattern] = patterns.get(pattern, 0) + 1
            
            total = sum(patterns.values())
            if total == 0:
                return 0.0
            
            entropy = 0.0
            for count in patterns.values():
                p = count / total
                entropy -= p * np.log(p + 1e-8)
            return entropy
        except:
            return 0.0
    
    features['permutation_entropy_normal'] = permutation_entropy(normal_part)
    features['permutation_entropy_candidate'] = permutation_entropy(candidate_part)
    features['permutation_entropy_diff'] = abs(features['permutation_entropy_normal'] - features['permutation_entropy_candidate'])
    
    # 7. Lempel-Ziv Complexity
    def lempel_ziv_complexity(data):
        try:
            median_val = np.median(data)
            binary_string = ''.join(['1' if x > median_val else '0' for x in data])
            substrings = set()
            for i in range(len(binary_string)):
                for j in range(i+1, len(binary_string)+1):
                    substrings.add(binary_string[i:j])
            return len(substrings) / len(binary_string) if len(binary_string) > 0 else 0.0
        except:
            return 0.0
    
    features['lz_complexity_normal'] = lempel_ziv_complexity(normal_part)
    features['lz_complexity_candidate'] = lempel_ziv_complexity(candidate_part)
    features['lz_complexity_diff'] = abs(features['lz_complexity_normal'] - features['lz_complexity_candidate'])
    
    # 8. Traditional distributional features
    try:
        features['wasserstein_distance'] = wasserstein_distance(normal_part, candidate_part)
        features['energy_distance'] = energy_distance(normal_part, candidate_part)
        
        # Multi-scale Wasserstein
        for n_bins in [10, 20, 50]:
            try:
                range_min = min(np.min(normal_part), np.min(candidate_part))
                range_max = max(np.max(normal_part), np.max(candidate_part))
                bins = np.linspace(range_min, range_max, n_bins + 1)
                
                normal_hist, _ = np.histogram(normal_part, bins=bins, density=True)
                candidate_hist, _ = np.histogram(candidate_part, bins=bins, density=True)
                
                normal_hist = normal_hist / (np.sum(normal_hist) + 1e-8)
                candidate_hist = candidate_hist / (np.sum(candidate_hist) + 1e-8)
                
                cumsum_normal = np.cumsum(normal_hist)
                cumsum_candidate = np.cumsum(candidate_hist)
                features[f'wasserstein_bins_{n_bins}'] = np.mean(np.abs(cumsum_normal - cumsum_candidate))
            except:
                features[f'wasserstein_bins_{n_bins}'] = 0.0
                
    except:
        features['wasserstein_distance'] = 0.0
        features['energy_distance'] = 0.0
    
    # 9. Statistical tests
    try:
        t_stat, t_pval = ttest_ind(normal_part, candidate_part)
        features['t_statistic'] = abs(t_stat) if not np.isnan(t_stat) else 0.0
        features['t_pvalue'] = t_pval if not np.isnan(t_pval) else 1.0
        
        u_stat, u_pval = mannwhitneyu(normal_part, candidate_part, alternative='two-sided')
        features['mannwhitney_pvalue'] = u_pval
        
        ks_stat, ks_pval = ks_2samp(normal_part, candidate_part)
        features['ks_statistic'] = ks_stat
        features['ks_pvalue'] = ks_pval
    except:
        features['t_statistic'] = 0.0
        features['t_pvalue'] = 1.0
        features['mannwhitney_pvalue'] = 1.0
        features['ks_statistic'] = 0.0
        features['ks_pvalue'] = 1.0
    
    # 10. Basic statistical features
    features['mean_diff'] = abs(np.mean(normal_part) - np.mean(candidate_part))
    features['std_diff'] = abs(np.std(normal_part) - np.std(candidate_part))
    features['range_diff'] = abs((np.max(normal_part) - np.min(normal_part)) - 
                                (np.max(candidate_part) - np.min(candidate_part)))
    
    # Quantile features
    try:
        normal_q25, normal_q50, normal_q75 = np.percentile(normal_part, [25, 50, 75])
        candidate_q25, candidate_q50, candidate_q75 = np.percentile(candidate_part, [25, 50, 75])
        
        features['median_diff'] = abs(normal_q50 - candidate_q50)
        features['iqr_diff'] = abs((normal_q75 - normal_q25) - (candidate_q75 - candidate_q25))
        features['q25_diff'] = abs(normal_q25 - candidate_q25)
        features['q75_diff'] = abs(normal_q75 - candidate_q75)
    except:
        features['median_diff'] = 0.0
        features['iqr_diff'] = 0.0
        features['q25_diff'] = 0.0
        features['q75_diff'] = 0.0
    
    # Moment features
    try:
        from scipy import stats as scipy_stats
        features['skewness_diff'] = abs(scipy_stats.skew(normal_part) - scipy_stats.skew(candidate_part))
        features['kurtosis_diff'] = abs(scipy_stats.kurtosis(normal_part) - scipy_stats.kurtosis(candidate_part))
    except:
        features['skewness_diff'] = 0.0
        features['kurtosis_diff'] = 0.0
    
    # Distributional overlap
    try:
        range_min = min(np.min(normal_part), np.min(candidate_part))
        range_max = max(np.max(normal_part), np.max(candidate_part))
        bins = np.linspace(range_min, range_max, 21)
        
        normal_hist, _ = np.histogram(normal_part, bins=bins, density=True)
        candidate_hist, _ = np.histogram(candidate_part, bins=bins, density=True)
        
        normal_hist = normal_hist / (np.sum(normal_hist) + 1e-8)
        candidate_hist = candidate_hist / (np.sum(candidate_hist) + 1e-8)
        
        features['histogram_overlap'] = np.sum(np.minimum(normal_hist, candidate_hist))
        features['hellinger_distance'] = np.sqrt(np.sum((np.sqrt(normal_hist) - np.sqrt(candidate_hist))**2)) / np.sqrt(2)
    except:
        features['histogram_overlap'] = 0.0
        features['hellinger_distance'] = 0.0
    
    # Handle NaN/inf values
    for key, value in features.items():
        if np.isnan(value) or np.isinf(value):
            features[key] = 0.0
    
    return features

def load_and_prepare_data():
    """Load and prepare dataset"""
    print("🚀 KERNEL METHODS WITH INFORMATION DENSITY FEATURES")
    print("=" * 70)
    print("📊 Loading dataset...")
    
    try:
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        print(f"   ✅ X_data: {X_data.shape} (columns: {list(X_data.columns)})")
        print(f"   ✅ y_data: {y_data.shape} (columns: {list(y_data.columns)})")
        
        if 'structural_breakpoint' in y_data.columns:
            labels = y_data['structural_breakpoint'].astype(int).values
        else:
            labels = y_data.iloc[:, 0].astype(int).values
        
        print(f"   📊 Label distribution: {Counter(labels)}")
        print(f"   📊 Label ratio: {np.mean(labels):.4f}")
        
        return X_data, labels
        
    except Exception as e:
        print(f"   ❌ Error loading data: {e}")
        return None, None

def create_samples_with_features(X_data, labels, max_samples=2000):
    """Create samples with information density features"""
    print(f"\n🔧 Creating samples with information density features...")
    
    samples = []
    window_size = len(X_data) // len(labels)
    
    print(f"   📊 Window size per label: {window_size:,} time steps")
    print(f"   📊 Creating up to {max_samples} samples...")
    
    n_samples = min(max_samples, len(labels))
    
    for i in range(n_samples):
        try:
            start_idx = i * window_size
            end_idx = min((i + 1) * window_size, len(X_data))
            
            if end_idx - start_idx < 100:
                continue
            
            window_data = X_data.iloc[start_idx:end_idx]
            
            # Extract normal and candidate parts
            normal_mask = window_data['period'] == 0
            candidate_mask = window_data['period'] == 1
            
            normal_values = window_data.loc[normal_mask, 'value'].values
            candidate_values = window_data.loc[candidate_mask, 'value'].values
            
            if len(normal_values) < 10 or len(candidate_values) < 10:
                continue
            
            # Limit size for computational efficiency
            normal_part = normal_values[:200] if len(normal_values) > 200 else normal_values
            candidate_part = candidate_values[:200] if len(candidate_values) > 200 else candidate_values
            
            # Compute all information density features
            features = compute_all_information_features(normal_part, candidate_part)
            
            samples.append({
                'features': features,
                'label': labels[i],
                'normal_candidate_ratio': len(candidate_values) / (len(normal_values) + len(candidate_values))
            })
            
            if len(samples) % 200 == 0:
                print(f"   📈 Created {len(samples)} samples...")
                
        except Exception as e:
            continue
    
    print(f"   ✅ Created {len(samples)} valid samples")
    
    if samples:
        sample_labels = [s['label'] for s in samples]
        print(f"   📊 Sample label distribution: {Counter(sample_labels)}")
        print(f"   📊 Sample label ratio: {np.mean(sample_labels):.4f}")
        print(f"   📊 Feature count: {len(samples[0]['features'])}")
    
    return samples

def kernel_cv_comparison(samples, n_splits=5):
    """Compare Walk-Forward CV vs Stratified K-Fold with kernel methods"""
    print(f"\n🎯 KERNEL METHODS CV COMPARISON")
    print("=" * 60)
    
    # Prepare data
    X = np.array([list(s['features'].values()) for s in samples])
    y = np.array([s['label'] for s in samples])
    
    print(f"   📊 Feature matrix: {X.shape}")
    print(f"   📊 Labels: {y.shape}, ratio: {np.mean(y):.4f}")
    
    # Define kernel SVM pipeline
    kernel_pipeline = Pipeline([
        ('scaler', StandardScaler()),
        ('svm', SVC(kernel='rbf', C=1.0, gamma='scale', probability=True, random_state=42))
    ])
    
    results = {}
    
    # 1. Sequential K-Fold CV (preserving temporal order, no shuffling)
    print(f"\n📈 Sequential K-Fold CV (preserving order, no shuffle)...")
    
    # Create 5 equal-sized folds in sequential order
    fold_size = len(X) // n_splits
    
    seq_aucs = []
    seq_accuracies = []
    seq_predictions = []
    seq_probabilities = []
    seq_true_labels = []
    
    for fold_idx in range(n_splits):
        # Define validation fold indices (sequential blocks)
        val_start = fold_idx * fold_size
        val_end = (fold_idx + 1) * fold_size if fold_idx < n_splits - 1 else len(X)
        
        val_idx = np.arange(val_start, val_end)
        train_idx = np.concatenate([np.arange(0, val_start), np.arange(val_end, len(X))])
        
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        print(f"   Fold {fold_idx+1}: Train={len(X_train)}, Val={len(X_val)} (sequential block)")
        
        # Train kernel SVM
        kernel_pipeline.fit(X_train, y_train)
        
        # Predict
        val_pred = kernel_pipeline.predict(X_val)
        val_prob = kernel_pipeline.predict_proba(X_val)[:, 1]
        
        # Metrics
        accuracy = accuracy_score(y_val, val_pred)
        auc = roc_auc_score(y_val, val_prob) if len(np.unique(y_val)) > 1 else 0.5
        
        seq_aucs.append(auc)
        seq_accuracies.append(accuracy)
        seq_predictions.extend(val_pred)
        seq_probabilities.extend(val_prob)
        seq_true_labels.extend(y_val)
        
        print(f"   Fold {fold_idx+1}: AUC={auc:.4f}, Accuracy={accuracy:.4f}")
    
    seq_overall_auc = roc_auc_score(seq_true_labels, seq_probabilities)
    seq_overall_accuracy = accuracy_score(seq_true_labels, seq_predictions)
    
    results['sequential_kfold'] = {
        'fold_aucs': seq_aucs,
        'fold_accuracies': seq_accuracies,
        'mean_auc': np.mean(seq_aucs),
        'std_auc': np.std(seq_aucs),
        'mean_accuracy': np.mean(seq_accuracies),
        'std_accuracy': np.std(seq_accuracies),
        'overall_auc': seq_overall_auc,
        'overall_accuracy': seq_overall_accuracy
    }
    
    # 2. Stratified K-Fold CV
    print(f"\n📊 Stratified K-Fold CV...")
    skf = StratifiedKFold(n_splits=n_splits, shuffle=True, random_state=42)
    
    skf_aucs = []
    skf_accuracies = []
    skf_predictions = []
    skf_probabilities = []
    skf_true_labels = []
    
    for fold_idx, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # Train kernel SVM
        kernel_pipeline.fit(X_train, y_train)
        
        # Predict
        val_pred = kernel_pipeline.predict(X_val)
        val_prob = kernel_pipeline.predict_proba(X_val)[:, 1]
        
        # Metrics
        accuracy = accuracy_score(y_val, val_pred)
        auc = roc_auc_score(y_val, val_prob) if len(np.unique(y_val)) > 1 else 0.5
        
        skf_aucs.append(auc)
        skf_accuracies.append(accuracy)
        skf_predictions.extend(val_pred)
        skf_probabilities.extend(val_prob)
        skf_true_labels.extend(y_val)
        
        print(f"   Fold {fold_idx+1}: AUC={auc:.4f}, Accuracy={accuracy:.4f}")
    
    skf_overall_auc = roc_auc_score(skf_true_labels, skf_probabilities)
    skf_overall_accuracy = accuracy_score(skf_true_labels, skf_predictions)
    
    results['stratified_kfold'] = {
        'fold_aucs': skf_aucs,
        'fold_accuracies': skf_accuracies,
        'mean_auc': np.mean(skf_aucs),
        'std_auc': np.std(skf_aucs),
        'mean_accuracy': np.mean(skf_accuracies),
        'std_accuracy': np.std(skf_accuracies),
        'overall_auc': skf_overall_auc,
        'overall_accuracy': skf_overall_accuracy
    }
    
    return results

def print_comparison_results(results):
    """Print comprehensive comparison results"""
    print(f"\n🏆 KERNEL METHODS CV COMPARISON RESULTS")
    print("=" * 70)
    
    seq = results['sequential_kfold']
    skf = results['stratified_kfold']
    
    print(f"📊 SEQUENTIAL K-FOLD CV (preserving order):")
    print(f"   Mean AUC: {seq['mean_auc']:.4f} ± {seq['std_auc']:.4f}")
    print(f"   Mean Accuracy: {seq['mean_accuracy']:.4f} ± {seq['std_accuracy']:.4f}")
    print(f"   Overall AUC: {seq['overall_auc']:.4f}")
    print(f"   Overall Accuracy: {seq['overall_accuracy']:.4f}")
    
    print(f"\n📊 STRATIFIED K-FOLD CV (shuffled):")
    print(f"   Mean AUC: {skf['mean_auc']:.4f} ± {skf['std_auc']:.4f}")
    print(f"   Mean Accuracy: {skf['mean_accuracy']:.4f} ± {skf['std_accuracy']:.4f}")
    print(f"   Overall AUC: {skf['overall_auc']:.4f}")
    print(f"   Overall Accuracy: {skf['overall_accuracy']:.4f}")
    
    print(f"\n🔍 COMPARISON ANALYSIS:")
    auc_diff = seq['overall_auc'] - skf['overall_auc']
    acc_diff = seq['overall_accuracy'] - skf['overall_accuracy']
    
    print(f"   AUC Difference (Sequential - Stratified): {auc_diff:+.4f}")
    print(f"   Accuracy Difference (Sequential - Stratified): {acc_diff:+.4f}")
    
    if abs(auc_diff) > 0.02:
        if auc_diff > 0:
            print(f"   ✅ Sequential K-Fold shows BETTER performance")
            print(f"   💡 Preserving temporal order provides benefits!")
        else:
            print(f"   ✅ Stratified K-Fold shows BETTER performance")
            print(f"   💡 Random shuffling is more effective than preserving order")
    else:
        print(f"   ⚖️  Both methods show SIMILAR performance")
        print(f"   💡 Temporal order has minimal impact")
    
    # Performance assessment
    best_auc = max(seq['overall_auc'], skf['overall_auc'])
    if best_auc > 0.7:
        print(f"\n✅ EXCELLENT performance achieved!")
    elif best_auc > 0.6:
        print(f"\n✅ GOOD performance achieved!")
    elif best_auc > 0.55:
        print(f"\n⚠️  MODERATE performance")
    else:
        print(f"\n❌ POOR performance - needs improvement")

def main():
    """Main execution function"""
    
    # Load data
    X_data, labels = load_and_prepare_data()
    if X_data is None:
        return
    
    # Create samples with features
    samples = create_samples_with_features(X_data, labels, max_samples=2000)
    if len(samples) < 100:
        print("❌ Not enough valid samples created")
        return
    
    # Run CV comparison
    results = kernel_cv_comparison(samples, n_splits=5)
    
    # Print results
    print_comparison_results(results)
    
    # Save results
    results_summary = {
        'sequential_kfold': {
            'mean_auc': float(results['sequential_kfold']['mean_auc']),
            'std_auc': float(results['sequential_kfold']['std_auc']),
            'overall_auc': float(results['sequential_kfold']['overall_auc']),
            'overall_accuracy': float(results['sequential_kfold']['overall_accuracy'])
        },
        'stratified_kfold': {
            'mean_auc': float(results['stratified_kfold']['mean_auc']),
            'std_auc': float(results['stratified_kfold']['std_auc']),
            'overall_auc': float(results['stratified_kfold']['overall_auc']),
            'overall_accuracy': float(results['stratified_kfold']['overall_accuracy'])
        },
        'n_samples': len(samples),
        'n_features': len(samples[0]['features'])
    }
    
    with open('kernel_cv_comparison_results.json', 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\n🎉 Kernel Methods CV Comparison Complete!")
    print(f"   💾 Results saved to 'kernel_cv_comparison_results.json'")

if __name__ == "__main__":
    main()