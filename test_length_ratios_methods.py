#!/usr/bin/env python3
"""
Test Length-Context Enhancement Methods
Evaluate how different length-aware feature enhancement methods affect AUROC performance
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
from scipy.stats import entropy
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and labels"""
    print("📊 Loading data...")
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels)

def compute_base_features(pre, post, whole):
    """Compute diverse base features from Ratios_updated_v4.py"""
    features = {}
    
    # Basic statistical features
    features['mean_pre'] = np.nanmean(pre)
    features['mean_post'] = np.nanmean(post)
    features['std_pre'] = np.nanstd(pre, ddof=1)
    features['std_post'] = np.nanstd(post, ddof=1)
    features['skew_pre'] = np.nan if len(pre) < 3 else float(pd.Series(pre).skew())
    features['skew_post'] = np.nan if len(post) < 3 else float(pd.Series(post).skew())
    
    # Range and volatility
    features['range_pre'] = np.nanmax(pre) - np.nanmin(pre) if len(pre) > 0 else np.nan
    features['range_post'] = np.nanmax(post) - np.nanmin(post) if len(post) > 0 else np.nan
    features['vol_pre'] = np.nanstd(np.diff(pre)) if len(pre) > 1 else np.nan
    features['vol_post'] = np.nanstd(np.diff(post)) if len(post) > 1 else np.nan
    
    # Quantiles
    if len(pre) > 0 and len(post) > 0:
        features['q25_pre'] = np.nanquantile(pre, 0.25)
        features['q75_pre'] = np.nanquantile(pre, 0.75)
        features['q25_post'] = np.nanquantile(post, 0.25)
        features['q75_post'] = np.nanquantile(post, 0.75)
    
    # Autocorrelation
    if len(pre) > 1:
        pre_centered = pre - np.nanmean(pre)
        features['ac_pre'] = np.corrcoef(pre_centered[:-1], pre_centered[1:])[0, 1] if len(pre_centered) > 1 else np.nan
    else:
        features['ac_pre'] = np.nan
        
    if len(post) > 1:
        post_centered = post - np.nanmean(post)
        features['ac_post'] = np.corrcoef(post_centered[:-1], post_centered[1:])[0, 1] if len(post_centered) > 1 else np.nan
    else:
        features['ac_post'] = np.nan
    
    # Cross-segment features
    features['mean_diff'] = features['mean_post'] - features['mean_pre']
    features['std_ratio'] = features['std_post'] / features['std_pre'] if features['std_pre'] != 0 else np.nan
    
    # Statistical tests
    try:
        from scipy.stats import ks_2samp, mannwhitneyu
        _, features['ks_pvalue'] = ks_2samp(pre, post)
        _, features['mw_pvalue'] = mannwhitneyu(pre, post, alternative='two-sided')
    except:
        features['ks_pvalue'] = np.nan
        features['mw_pvalue'] = np.nan
    
    return features

def apply_length_enhancement_methods(features, len_pre, len_post, total_length):
    """Apply different length-context enhancement methods"""
    enhanced_features = {}
    
    # Length ratios
    pre_to_total = len_pre / total_length
    post_to_total = len_post / total_length
    pre_to_post = len_pre / len_post if len_post > 0 else np.nan
    length_asymmetry = abs(pre_to_total - 0.5)
    
    # Method 1: Weighted Features by Length Proportions
    for feat_name, feat_val in features.items():
        if not np.isnan(feat_val):
            if 'pre' in feat_name:
                enhanced_features[f'{feat_name}_weighted'] = pre_to_total * feat_val
            elif 'post' in feat_name:
                enhanced_features[f'{feat_name}_weighted'] = post_to_total * feat_val
            else:
                enhanced_features[f'{feat_name}_weighted'] = 0.5 * feat_val
    
    # Method 2: Length-Normalized Ratios
    expected_ratio = 0.5
    actual_ratio = pre_to_total
    length_bias = actual_ratio / expected_ratio if expected_ratio != 0 else 1
    
    for feat_name, feat_val in features.items():
        if not np.isnan(feat_val) and length_bias != 0:
            enhanced_features[f'{feat_name}_length_norm'] = feat_val / length_bias
    
    # Method 3: Gaussian Kernel Length Weighting
    optimal_length = total_length * 0.5
    sigma = total_length * 0.2  # 20% of total length as sigma
    
    pre_kernel = np.exp(-((len_pre - optimal_length)**2) / (2 * sigma**2)) if sigma > 0 else 1
    post_kernel = np.exp(-((len_post - optimal_length)**2) / (2 * sigma**2)) if sigma > 0 else 1
    
    for feat_name, feat_val in features.items():
        if not np.isnan(feat_val):
            if 'pre' in feat_name:
                enhanced_features[f'{feat_name}_kernel'] = pre_kernel * feat_val
            elif 'post' in feat_name:
                enhanced_features[f'{feat_name}_kernel'] = post_kernel * feat_val
            else:
                enhanced_features[f'{feat_name}_kernel'] = 0.5 * (pre_kernel + post_kernel) * feat_val
    
    # Method 4: Three-Way Length Ratios as Features
    enhanced_features['pre_to_total_ratio'] = pre_to_total
    enhanced_features['post_to_total_ratio'] = post_to_total
    enhanced_features['pre_to_post_ratio'] = pre_to_post
    enhanced_features['length_asymmetry'] = length_asymmetry
    
    # Method 5: Length-Scaled Feature Variants
    for feat_name, feat_val in features.items():
        if not np.isnan(feat_val) and total_length > 0:
            enhanced_features[f'{feat_name}_sqrt_scaled'] = feat_val / np.sqrt(total_length)
            enhanced_features[f'{feat_name}_log_scaled'] = feat_val / np.log(total_length + 1)
            enhanced_features[f'{feat_name}_linear_scaled'] = feat_val / total_length
    
    # Method 6: Segment Quality Weighting (using entropy as proxy for information content)
    try:
        pre_entropy = entropy(np.histogram(features.get('mean_pre', [0]), bins=10)[0] + 1e-10)
        post_entropy = entropy(np.histogram(features.get('mean_post', [0]), bins=10)[0] + 1e-10)
        
        pre_info = pre_entropy * len_pre
        post_info = post_entropy * len_post
        total_info = pre_info + post_info
        
        if total_info > 0:
            for feat_name, feat_val in features.items():
                if not np.isnan(feat_val):
                    if 'pre' in feat_name:
                        enhanced_features[f'{feat_name}_info_weighted'] = (pre_info / total_info) * feat_val
                    elif 'post' in feat_name:
                        enhanced_features[f'{feat_name}_info_weighted'] = (post_info / total_info) * feat_val
                    else:
                        enhanced_features[f'{feat_name}_info_weighted'] = feat_val
    except:
        pass
    
    # Method 7: Length-Conditional Features (regime-based)
    length_regime = 'short' if total_length < 100 else 'long' if total_length > 1000 else 'medium'
    
    for feat_name, feat_val in features.items():
        if not np.isnan(feat_val):
            if length_regime == 'short':
                enhanced_features[f'{feat_name}_regime'] = feat_val * 1.2  # Boost short series features
            elif length_regime == 'long':
                enhanced_features[f'{feat_name}_regime'] = feat_val * 0.8  # Dampen long series features
            else:
                enhanced_features[f'{feat_name}_regime'] = feat_val
    
    return enhanced_features

def extract_features_with_methods(time_series_list, labels, max_samples=2000):
    """Extract features using different length enhancement methods"""
    print("🔧 Extracting features with length enhancement methods...")
    
    # Limit samples for faster testing
    if len(time_series_list) > max_samples:
        indices = np.random.choice(len(time_series_list), max_samples, replace=False)
        time_series_list = [time_series_list[i] for i in indices]
        labels = labels[indices]
    
    all_features = []
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        if i % 500 == 0:
            print(f"   Processing {i+1}/{len(time_series_list)}...")
        
        series_clean = np.array(series)[~np.isnan(series)]
        if len(series_clean) < 10:
            continue
        
        # Split series (assume break at midpoint for structural break series)
        split_point = len(series_clean) // 2
        pre = series_clean[:split_point]
        post = series_clean[split_point:]
        
        if len(pre) < 5 or len(post) < 5:
            continue
        
        # Compute base features
        base_features = compute_base_features(pre, post, series_clean)
        
        # Apply length enhancement methods
        enhanced_features = apply_length_enhancement_methods(
            base_features, len(pre), len(post), len(series_clean)
        )
        
        # Combine all features
        all_features_dict = {**base_features, **enhanced_features}
        all_features_dict['label'] = label
        all_features_dict['series_id'] = i
        
        all_features.append(all_features_dict)
    
    features_df = pd.DataFrame(all_features)
    features_df = features_df.replace([np.inf, -np.inf], np.nan)
    
    print(f"   ✅ Extracted {features_df.shape[1]-2} features for {len(features_df)} series")
    return features_df

def evaluate_feature_groups(features_df):
    """Evaluate different feature groups using cross-validation"""
    print("📈 Evaluating feature groups with 5-fold CV...")
    
    # Prepare data
    y = features_df['label'].values
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id']]
    
    # Define feature groups
    feature_groups = {
        'baseline': [col for col in feature_cols if not any(suffix in col for suffix in 
                    ['_weighted', '_length_norm', '_kernel', '_sqrt_scaled', '_log_scaled', 
                     '_linear_scaled', '_info_weighted', '_regime']) and 'ratio' not in col],
        
        'weighted': [col for col in feature_cols if '_weighted' in col],
        
        'length_normalized': [col for col in feature_cols if '_length_norm' in col],
        
        'kernel_weighted': [col for col in feature_cols if '_kernel' in col],
        
        'length_ratios': [col for col in feature_cols if 'ratio' in col or 'asymmetry' in col],
        
        'scaled_variants': [col for col in feature_cols if any(suffix in col for suffix in 
                           ['_sqrt_scaled', '_log_scaled', '_linear_scaled'])],
        
        'info_weighted': [col for col in feature_cols if '_info_weighted' in col],
        
        'regime_based': [col for col in feature_cols if '_regime' in col],
        
        'all_enhanced': [col for col in feature_cols if any(suffix in col for suffix in 
                        ['_weighted', '_length_norm', '_kernel', '_sqrt_scaled', '_log_scaled', 
                         '_linear_scaled', '_info_weighted', '_regime']) or 'ratio' in col]
    }
    
    # Filter available features
    for group_name in feature_groups:
        available_features = [f for f in feature_groups[group_name] if f in features_df.columns]
        feature_groups[group_name] = available_features
        print(f"   {group_name}: {len(available_features)} features")
    
    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
    
    results = {}
    
    print(f"\n🔬 Cross-validation results:")
    print("=" * 60)
    
    for group_name, group_features in feature_groups.items():
        if not group_features:
            continue
        
        # Prepare feature matrix
        X = features_df[group_features].copy()
        X = X.fillna(X.median())
        
        if X.shape[1] == 0:
            continue
        
        # Standardize features
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # Cross-validation
        try:
            cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
            
            results[group_name] = {
                'mean_auroc': cv_scores.mean(),
                'std_auroc': cv_scores.std(),
                'n_features': len(group_features),
                'cv_scores': cv_scores
            }
            
            print(f"{group_name:20}: AUROC = {cv_scores.mean():.4f} ± {cv_scores.std():.4f} ({len(group_features):3d} features)")
            
        except Exception as e:
            print(f"{group_name:20}: Error - {e}")
            results[group_name] = {'mean_auroc': np.nan, 'std_auroc': np.nan, 'n_features': len(group_features)}
    
    return results

def analyze_method_effectiveness(results):
    """Analyze which methods are most effective"""
    print(f"\n📊 METHOD EFFECTIVENESS ANALYSIS:")
    print("=" * 50)
    
    if 'baseline' not in results or np.isnan(results['baseline']['mean_auroc']):
        print("   ⚠️ No baseline results available")
        return
    
    baseline_auroc = results['baseline']['mean_auroc']
    print(f"Baseline AUROC: {baseline_auroc:.4f}")
    
    improvements = []
    
    for method_name, method_results in results.items():
        if method_name == 'baseline' or np.isnan(method_results['mean_auroc']):
            continue
        
        improvement = method_results['mean_auroc'] - baseline_auroc
        improvement_pct = (improvement / baseline_auroc) * 100
        
        improvements.append({
            'method': method_name,
            'auroc': method_results['mean_auroc'],
            'improvement': improvement,
            'improvement_pct': improvement_pct,
            'n_features': method_results['n_features']
        })
        
        status = "📈" if improvement > 0 else "📉"
        print(f"{status} {method_name:20}: {improvement:+.4f} ({improvement_pct:+.2f}%) - {method_results['n_features']} features")
    
    # Sort by improvement
    improvements.sort(key=lambda x: x['improvement'], reverse=True)
    
    print(f"\n🏆 TOP PERFORMING METHODS:")
    for i, method in enumerate(improvements[:3]):
        print(f"   {i+1}. {method['method']}: +{method['improvement']:.4f} ({method['improvement_pct']:+.2f}%)")
    
    print(f"\n📉 LEAST EFFECTIVE METHODS:")
    for i, method in enumerate(improvements[-3:]):
        print(f"   {len(improvements)-i}. {method['method']}: {method['improvement']:+.4f} ({method['improvement_pct']:+.2f}%)")
    
    return improvements

def create_summary_report(results, improvements):
    """Create summary report"""
    print(f"\n📋 SUMMARY REPORT")
    print("=" * 40)
    
    total_methods = len([r for r in results.values() if not np.isnan(r['mean_auroc'])])
    positive_improvements = len([imp for imp in improvements if imp['improvement'] > 0])
    
    print(f"Total methods tested: {total_methods}")
    print(f"Methods with positive improvement: {positive_improvements}")
    print(f"Success rate: {positive_improvements/total_methods*100:.1f}%")
    
    if improvements:
        best_method = improvements[0]
        worst_method = improvements[-1]
        
        print(f"\nBest method: {best_method['method']}")
        print(f"  AUROC: {best_method['auroc']:.4f}")
        print(f"  Improvement: +{best_method['improvement']:.4f} ({best_method['improvement_pct']:+.2f}%)")
        
        print(f"\nWorst method: {worst_method['method']}")
        print(f"  AUROC: {worst_method['auroc']:.4f}")
        print(f"  Improvement: {worst_method['improvement']:+.4f} ({worst_method['improvement_pct']:+.2f}%)")

def main():
    """Main pipeline execution"""
    print("🚀 LENGTH-CONTEXT ENHANCEMENT METHODS EVALUATION")
    print("=" * 60)
    
    # Load data
    time_series_list, labels = load_data()
    
    # Extract features with different methods
    features_df = extract_features_with_methods(time_series_list, labels)
    
    # Evaluate feature groups
    results = evaluate_feature_groups(features_df)
    
    # Analyze effectiveness
    improvements = analyze_method_effectiveness(results)
    
    # Create summary report
    create_summary_report(results, improvements)
    
    # Save results
    results_df = pd.DataFrame([
        {
            'method': method,
            'mean_auroc': data['mean_auroc'],
            'std_auroc': data['std_auroc'],
            'n_features': data['n_features']
        }
        for method, data in results.items()
    ])
    
    results_df.to_csv('length_enhancement_methods_results.csv', index=False)
    features_df.to_csv('enhanced_features_sample.csv', index=False)
    
    print(f"\n💾 Results saved:")
    print(f"   📊 Method comparison: 'length_enhancement_methods_results.csv'")
    print(f"   📊 Feature sample: 'enhanced_features_sample.csv'")
    
    return results, improvements, features_df

if __name__ == "__main__":
    results = main()