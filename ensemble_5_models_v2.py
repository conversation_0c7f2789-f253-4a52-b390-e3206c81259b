#!/usr/bin/env python3
"""
5-Model Ensemble V2 - Models created inside objective function
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
from sklearn.preprocessing import StandardScaler
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostClassifier
from sklearn.ensemble import RandomForestClassifier, ExtraTreesClassifier
import optuna
import logging
import warnings

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_prepared_features():
    """Load prepared features and labels separately"""
    logger.info("📊 Loading prepared_features.parquet and y_train.parquet...")
    
    try:
        X = pd.read_parquet('resources/processed_data/prepared_features.parquet')
        y = pd.read_parquet('y_train.parquet')
        
        if isinstance(y.index, pd.MultiIndex):
            y = y.reset_index(drop=True).iloc[:, 0] if y.shape[1] == 1 else y.reset_index(drop=True)
        else:
            y = y.iloc[:, 0] if y.shape[1] == 1 else y.squeeze()
        
        min_len = min(len(X), len(y))
        X = X.iloc[:min_len]
        y = y.iloc[:min_len] if hasattr(y, 'iloc') else y[:min_len]
        
        logger.info(f"   ✅ Features: {X.shape[0]} samples, {X.shape[1]} features")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def optimize_model_params(model_name, X, y, n_trials=5):
    """Optimize hyperparameters with model creation inside objective"""
    logger.info(f"🔧 Optimizing {model_name} hyperparameters...")
    
    def objective(trial):
        early_stopping_count = []
        
        if model_name == 'xgb':
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                'random_state': 42,
                'eval_metric': 'logloss',
                'verbosity': 0
            }
        elif model_name == 'lgb':
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 1000),
                'max_depth': trial.suggest_int('max_depth', 3, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.01, 0.3),
                'subsample': trial.suggest_float('subsample', 0.6, 1.0),
                'colsample_bytree': trial.suggest_float('colsample_bytree', 0.6, 1.0),
                'reg_alpha': trial.suggest_float('reg_alpha', 0, 10),
                'reg_lambda': trial.suggest_float('reg_lambda', 0, 10),
                'num_leaves': trial.suggest_int('num_leaves', 10, 300),
                'random_state': 42,
                'verbosity': -1,
                'force_col_wise': True
            }
        elif model_name == 'cat':
            bootstrap_type = 'Bayesian'# trial.suggest_categorical('bootstrap_type', ['Bayesian', 'Bernoulli'])
            params = {
                'depth': trial.suggest_int('depth', 5, 10),
                'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.07),
                'iterations': trial.suggest_int('iterations', 500, 3000),
                'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 0.01, 1.0),
                'early_stopping_rounds': 50,
                'bootstrap_type': 'Bayesian',
                'verbose': 0,
                'task_type': 'GPU',
                'random_state': 42,
            }
            
            # Add subsample for Bernoulli
            if bootstrap_type == 'Bernoulli':
                params['subsample'] = trial.suggest_float('subsample', 0.7, 1.0)
            
            # Add langevin for Bayesian (boolean, not string)
            if bootstrap_type == 'Bayesian':
                params['langevin'] = True#trial.suggest_categorical('langevin', [True, False])
        elif model_name == 'rf':
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 5, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                'random_state': 42,
                'n_jobs': -1
            }
        elif model_name == 'et':
            params = {
                'n_estimators': trial.suggest_int('n_estimators', 100, 500),
                'max_depth': trial.suggest_int('max_depth', 5, 20),
                'min_samples_split': trial.suggest_int('min_samples_split', 2, 20),
                'min_samples_leaf': trial.suggest_int('min_samples_leaf', 1, 10),
                'max_features': trial.suggest_categorical('max_features', ['sqrt', 'log2', None]),
                'random_state': 42,
                'n_jobs': -1
            }
        
        # 5-fold CV
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        cv_scores = []
        
        for train_idx, val_idx in skf.split(X, y):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # Create model inside objective
            if model_name == 'xgb':
                model = xgb.XGBClassifier(**params)
            elif model_name == 'lgb':
                model = lgb.LGBMClassifier(**params)
            elif model_name == 'cat':
                model = CatBoostClassifier(**params)
            elif model_name == 'rf':
                model = RandomForestClassifier(**params)
            elif model_name == 'et':
                model = ExtraTreesClassifier(**params)
            
            # Train with early stopping tracking for CatBoost
            if model_name == 'cat':
                model.fit(
                    X_train, y_train,
                    eval_set=(X_val, y_val),
                    use_best_model=True
                )
                best_iteration = 1 if model.best_iteration_ == 0 else model.best_iteration_
                early_stopping_count.append(best_iteration)
            else:
                model.fit(X_train, y_train)
            
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            auc = roc_auc_score(y_val, y_pred_proba)
            cv_scores.append(auc)
        
        # Set user attribute for CatBoost optimal iterations
        if model_name == 'cat' and early_stopping_count:
            trial.set_user_attr('c', np.max(early_stopping_count))
        
        return np.mean(cv_scores)
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best {model_name} AUC: {study.best_value:.4f}")
    
    # Get best params and add optimal iterations for CatBoost
    best_params = study.best_params.copy()
    if model_name == 'cat' and 'c' in study.best_trial.user_attrs:
        best_params['c'] = study.best_trial.user_attrs['c']
        logger.info(f"   📊 Optimal CatBoost iterations: {best_params['c']}")
    
    return best_params

def create_model_from_params(model_name, params):
    """Create model from optimized parameters"""
    if model_name == 'xgb':
        return xgb.XGBClassifier(**params)
    elif model_name == 'lgb':
        return lgb.LGBMClassifier(**params)
    elif model_name == 'cat':
        return CatBoostClassifier(**params)
    elif model_name == 'rf':
        return RandomForestClassifier(**params)
    elif model_name == 'et':
        return ExtraTreesClassifier(**params)

def train_ensemble_cv(model_names, optimized_params, X, y, n_folds=5):
    """Train ensemble with cross-validation using optimal iterations"""
    logger.info("🎯 Training ensemble with 5-fold CV...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    oof_predictions = np.zeros((len(X), len(model_names)))
    individual_aucs = {name: [] for name in model_names}
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        for i, name in enumerate(model_names):
            # Create model with optimized params
            if name == 'cat' and 'c' in optimized_params[name]:
                # Use exact iterations for CatBoost
                cat_params = optimized_params[name].copy()
                cat_params['iterations'] = cat_params.pop('c')
                cat_params.pop('early_stopping_rounds', None)
                model = create_model_from_params(name, cat_params)
            else:
                model = create_model_from_params(name, optimized_params[name])
            
            model.fit(X_train, y_train)
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            oof_predictions[val_idx, i] = y_pred_proba
            
            auc = roc_auc_score(y_val, y_pred_proba)
            individual_aucs[name].append(auc)
            logger.info(f"     {name}: AUC = {auc:.4f}")
    
    # Calculate ensemble predictions
    ensemble_predictions = np.mean(oof_predictions, axis=1)
    ensemble_auc = roc_auc_score(y, ensemble_predictions)
    
    logger.info("\n📊 Individual Model Performance:")
    for name, aucs in individual_aucs.items():
        mean_auc = np.mean(aucs)
        std_auc = np.std(aucs)
        logger.info(f"   {name:>3}: {mean_auc:.4f} ± {std_auc:.4f}")
    
    logger.info(f"\n🎉 Ensemble AUC: {ensemble_auc:.4f}")
    
    return {
        'ensemble_auc': ensemble_auc,
        'individual_aucs': individual_aucs,
        'oof_predictions': oof_predictions,
        'ensemble_predictions': ensemble_predictions
    }

def optimize_ensemble_weights(oof_predictions, y, model_names, n_trials=50):
    """Optimize ensemble weights using Optuna"""
    logger.info("🔧 Optimizing ensemble weights...")
    
    def objective(trial):
        weights = []
        for i in range(oof_predictions.shape[1]):
            weight = trial.suggest_float(f'weight_{i}', 0.0, 1.0)
            weights.append(weight)
        
        weights = np.array(weights)
        weights = weights / (weights.sum() + 1e-8)
        
        weighted_predictions = np.average(oof_predictions, axis=1, weights=weights)
        auc = roc_auc_score(y, weighted_predictions)
        return auc
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    best_weights = []
    for i in range(oof_predictions.shape[1]):
        best_weights.append(study.best_params[f'weight_{i}'])
    
    best_weights = np.array(best_weights)
    best_weights = best_weights / (best_weights.sum() + 1e-8)
    
    final_predictions = np.average(oof_predictions, axis=1, weights=best_weights)
    final_auc = roc_auc_score(y, final_predictions)
    
    logger.info(f"   ✅ Optimized Ensemble AUC: {final_auc:.4f}")
    logger.info(f"   📊 Best weights: {dict(zip(model_names, best_weights))}")
    
    return best_weights, final_auc, final_predictions

def calculate_comprehensive_metrics(y_true, y_pred_proba, threshold=0.5):
    """Calculate comprehensive classification metrics"""
    y_pred_binary = (y_pred_proba >= threshold).astype(int)
    
    return {
        'auc': roc_auc_score(y_true, y_pred_proba),
        'f1': f1_score(y_true, y_pred_binary),
        'precision': precision_score(y_true, y_pred_binary, zero_division=0),
        'recall': recall_score(y_true, y_pred_binary),
        'accuracy': accuracy_score(y_true, y_pred_binary)
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting 5-Model Ensemble V2...")
    
    # Load data
    X, y = load_prepared_features()
    
    # Define models to use
    # model_names = ['xgb', 'lgb', 'cat', 'rf', 'et']
    model_names = ['cat']

    # Optimize each model
    optimized_params = {}
    for name in model_names:
        best_params = optimize_model_params(name, X, y, n_trials=20)
        optimized_params[name] = best_params
    
    # Train ensemble
    ensemble_results = train_ensemble_cv(model_names, optimized_params, X, y, n_folds=5)
    
    # Optimize ensemble weights
    best_weights, optimized_auc, optimized_predictions = optimize_ensemble_weights(
        ensemble_results['oof_predictions'], y, model_names, n_trials=50
    )
    
    # Calculate metrics
    simple_metrics = calculate_comprehensive_metrics(y, ensemble_results['ensemble_predictions'])
    optimized_metrics = calculate_comprehensive_metrics(y, optimized_predictions)
    
    # Print results
    logger.info("\n🎉 ENSEMBLE V2 ANALYSIS COMPLETED!")
    logger.info("=" * 60)
    logger.info("📊 RESULTS:")
    logger.info(f"   Simple Ensemble:      {simple_metrics['auc']:.4f} AUC")
    logger.info(f"   Optimized Ensemble:   {optimized_metrics['auc']:.4f} AUC")
    
    logger.info(f"\n📈 OPTIMIZED ENSEMBLE METRICS:")
    logger.info(f"   AUC:        {optimized_metrics['auc']:.4f}")
    logger.info(f"   F1 Score:   {optimized_metrics['f1']:.4f}")
    logger.info(f"   Precision:  {optimized_metrics['precision']:.4f}")
    logger.info(f"   Recall:     {optimized_metrics['recall']:.4f}")
    logger.info(f"   Accuracy:   {optimized_metrics['accuracy']:.4f}")
    
    # Save results
    import joblib
    final_results = {
        'optimized_params': optimized_params,
        'ensemble_results': ensemble_results,
        'best_weights': best_weights,
        'simple_metrics': simple_metrics,
        'optimized_metrics': optimized_metrics
    }
    joblib.dump(final_results, 'ensemble_5_models_v2_results.joblib')
    logger.info("💾 Results saved to 'ensemble_5_models_v2_results.joblib'")
    
    return final_results

if __name__ == "__main__":
    results = main()