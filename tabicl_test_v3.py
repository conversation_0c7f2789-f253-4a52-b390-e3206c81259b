#!/usr/bin/env python3
"""
TabICL Test V3 - Using Official TabICL Library
Based on https://github.com/soda-inria/tabicl
"""

import numpy as np
import pandas as pd
import logging
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
import warnings

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Import official TabICL
try:
    from tabicl import TabICLClassifier
    TABICL_AVAILABLE = True
    logger.info("✅ Official TabICL imported successfully")
except ImportError as e:
    TABICL_AVAILABLE = False
    logger.error(f"❌ TabICL library not available: {e}")
    logger.error("Install with: pip install tabicl")

def test_tabicl_installation():
    """Test if TabICL is properly installed and working"""
    logger.info("🧪 Testing TabICL installation...")
    
    if not TABICL_AVAILABLE:
        logger.error("❌ TabICL not available")
        logger.info("💡 Install with: pip install tabicl")
        return False
    
    try:
        # Create small test data
        X_train = np.random.randn(50, 10)
        y_train = np.random.randint(0, 2, 50)
        X_test = np.random.randn(10, 10)
        
        # Test TabICL with official API
        clf = TabICLClassifier(
            n_estimators=4,  # Small for testing
            batch_size=2,
            device='cpu',
            verbose=False
        )
        
        # Fit and predict
        clf.fit(X_train, y_train)
        predictions = clf.predict_proba(X_test)
        
        logger.info("✅ TabICL installation test passed!")
        logger.info(f"   Test predictions shape: {predictions.shape}")
        return True
        
    except Exception as e:
        logger.error(f"❌ TabICL test failed: {e}")
        return False

def load_data():
    """Load prepared features and labels"""
    logger.info("📊 Loading data...")
    
    try:
        # Try loading from resources first
        try:
            features_path = "resources/processed_data/prepared_features.parquet"
            X = pd.read_parquet(features_path)
        except:
            # Fallback to current directory
            X = pd.read_parquet('X_train.parquet')
        
        y_data = pd.read_parquet('y_train.parquet')
        y = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
        y = y.astype(int)
        
        logger.info(f"   ✅ Data loaded: X={X.shape}, y={len(y)}")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X.values, y
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def evaluate_official_tabicl_cv(X, y, n_folds=3):
    """Cross-validation evaluation using official TabICL"""
    logger.info(f"📊 Official TabICL {n_folds}-fold CV evaluation...")
    
    if not TABICL_AVAILABLE:
        logger.error("❌ TabICL not available. Cannot proceed.")
        return None
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': []}
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        try:
            # Initialize memory-optimized TabICL classifier
            clf = TabICLClassifier(
                n_estimators=4,                                         # ↓ Reduced from 16 to 4 for memory
                norm_methods=["none"],                                  # ↓ Single method instead of 2
                feat_shuffle_method="none",                             # ↓ No shuffling to save memory
                class_shift=False,                                      # ↓ No class shifts
                outlier_threshold=4.0,                                  # z-score threshold for outlier detection
                softmax_temperature=0.9,                                # controls prediction confidence
                average_logits=True,                                    # whether ensemble averaging is done on logits or probabilities
                use_hierarchical=False,                                 # ↓ Disable hierarchical for memory
                batch_size=1,                                           # ↓ Sequential processing (1 at a time)
                use_amp=True,                                           # ✅ Mixed precision for 50% memory savings
                device='cpu',                                           # CPU to avoid GPU memory limits
                random_state=42,                                        # random seed
                verbose=False                                           # print detailed information
            )
            
            # Fit (cheap operation - just stores data)
            logger.info("     Fitting TabICL (cheap operation)...")
            clf.fit(X_train_scaled, y_train)
            
            # Predict probabilities (in-context learning happens here)
            logger.info("     Making TabICL predictions (in-context learning)...")
            y_pred_proba = clf.predict_proba(X_val_scaled)
            
            # Extract probabilities for positive class
            if y_pred_proba.shape[1] > 1:
                y_pred_proba_pos = y_pred_proba[:, 1]
            else:
                y_pred_proba_pos = y_pred_proba.flatten()
            
            y_pred = clf.predict(X_val_scaled)
            
            # Calculate metrics
            auc = roc_auc_score(y_val, y_pred_proba_pos)
            f1 = f1_score(y_val, y_pred)
            precision = precision_score(y_val, y_pred, zero_division=0)
            recall = recall_score(y_val, y_pred, zero_division=0)
            accuracy = accuracy_score(y_val, y_pred)
            
            all_metrics['auc'].append(auc)
            all_metrics['f1'].append(f1)
            all_metrics['precision'].append(precision)
            all_metrics['recall'].append(recall)
            all_metrics['accuracy'].append(accuracy)
            
            logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
            
        except Exception as e:
            logger.warning(f"     Fold {fold + 1} failed: {e}")
            # Add default values for failed fold
            all_metrics['auc'].append(0.5)
            all_metrics['f1'].append(0.0)
            all_metrics['precision'].append(0.0)
            all_metrics['recall'].append(0.0)
            all_metrics['accuracy'].append(0.5)
    
    # Summary
    logger.info("🎯 Official TabICL CV Results:")
    for metric, values in all_metrics.items():
        mean_val = np.mean(values)
        std_val = np.std(values)
        logger.info(f"   {metric.upper()}: {mean_val:.4f} ± {std_val:.4f}")
    
    return all_metrics

def compare_with_baseline(X, y, n_folds=5):
    """Compare TabICL with simple baseline"""
    logger.info("📊 Comparing TabICL with XGBoost baseline...")
    
    try:
        from xgboost import XGBClassifier
        
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        
        tabicl_aucs = []
        xgb_aucs = []
        
        for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
            logger.info(f"   Fold {fold + 1}/{n_folds}...")
            
            X_train, X_val = X[train_idx], X[val_idx]
            y_train, y_val = y[train_idx], y[val_idx]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # TabICL
            try:
                clf_tabicl = TabICLClassifier(
                    n_estimators=8,
                    batch_size=4,
                    device='cpu',
                    verbose=False,
                    random_state=42
                )
                clf_tabicl.fit(X_train_scaled, y_train)
                y_pred_tabicl = clf_tabicl.predict_proba(X_val_scaled)[:, 1]
                auc_tabicl = roc_auc_score(y_val, y_pred_tabicl)
                tabicl_aucs.append(auc_tabicl)
            except Exception as e:
                logger.warning(f"     TabICL failed: {e}")
                tabicl_aucs.append(0.5)
            
            # XGBoost baseline
            try:
                clf_xgb = XGBClassifier(
                    n_estimators=100,
                    max_depth=6,
                    learning_rate=0.1,
                    random_state=42,
                    verbosity=0
                )
                clf_xgb.fit(X_train_scaled, y_train)
                y_pred_xgb = clf_xgb.predict_proba(X_val_scaled)[:, 1]
                auc_xgb = roc_auc_score(y_val, y_pred_xgb)
                xgb_aucs.append(auc_xgb)
            except Exception as e:
                logger.warning(f"     XGBoost failed: {e}")
                xgb_aucs.append(0.5)
            
            logger.info(f"     TabICL AUC: {tabicl_aucs[-1]:.4f}, XGBoost AUC: {xgb_aucs[-1]:.4f}")
        
        # Summary comparison
        logger.info("🎯 Comparison Results:")
        logger.info(f"   TabICL AUC: {np.mean(tabicl_aucs):.4f} ± {np.std(tabicl_aucs):.4f}")
        logger.info(f"   XGBoost AUC: {np.mean(xgb_aucs):.4f} ± {np.std(xgb_aucs):.4f}")
        
        if np.mean(tabicl_aucs) > np.mean(xgb_aucs):
            logger.info("   🏆 TabICL wins!")
        else:
            logger.info("   🏆 XGBoost wins!")
            
    except ImportError:
        logger.warning("XGBoost not available for comparison")

def main():
    """Main execution"""
    logger.info("🚀 Official TabICL Test V3")
    
    # Test installation
    if not test_tabicl_installation():
        logger.error("❌ TabICL installation test failed. Cannot proceed.")
        return
    
    # Load data
    X, y = load_data()
    
    # Limit data size for faster testing
    if len(X) > 3000:
        indices = np.random.choice(len(X), 3000, replace=False)
        X, y = X[indices], y[indices]
        logger.info(f"   📊 Limited to {len(X)} samples for faster testing")
    
    # Run official TabICL evaluation
    results = evaluate_official_tabicl_cv(X, y, n_folds=3)
    
    # Compare with baseline
    if results is not None:
        compare_with_baseline(X, y, n_folds=3)
    
    logger.info("✅ Official TabICL evaluation completed!")

if __name__ == "__main__":
    main()