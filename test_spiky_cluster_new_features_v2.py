#!/usr/bin/env python3
"""
Test Spiky Cluster New Features V2
Implements 5 advanced frequency-energy-volatility features for structural break detection
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from scipy import stats
from scipy.signal import welch, periodogram
import pywt
from scipy.stats import pearsonr
import warnings
warnings.filterwarnings('ignore')

def load_data():
    """Load time series data and volatility patterns"""
    print("📊 Loading data...")
    
    # Load time series
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    # Load volatility patterns
    try:
        vol_df = pd.read_csv('volatility_pattern_analysis.csv')
        print(f"   ✅ Loaded {len(vol_df)} volatility patterns")
    except:
        print("   ⚠️ volatility_pattern_analysis.csv not found, creating patterns...")
        vol_df = create_volatility_patterns(time_series_list, labels)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels), vol_df

def create_volatility_patterns(time_series_list, labels):
    """Create basic volatility patterns if file doesn't exist"""
    patterns = []
    for i, series in enumerate(time_series_list):
        series_clean = np.array(series)[~np.isnan(series)]
        if len(series_clean) < 10:
            pattern_type = 'unknown'
        else:
            volatility = np.std(series_clean)
            if volatility > np.percentile([np.std(s) for s in time_series_list], 80):
                pattern_type = 'spiky'
            elif volatility > np.percentile([np.std(s) for s in time_series_list], 60):
                pattern_type = 'volatile'
            else:
                pattern_type = 'calm'
        
        patterns.append({
            'series_id': i,
            'pattern_type': pattern_type,
            'volatility': volatility if len(series_clean) >= 10 else 0
        })
    
    return pd.DataFrame(patterns)

def compute_spectral_energy_volatility_ratio(series, k=5):
    """
    1. Spectral Energy-to-Volatility Ratio (SEVR)
    Measures balance between spectral energy and volatility
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 10:
        return np.nan
    
    try:
        # Compute power spectral density
        freqs, psd = welch(series_clean, nperseg=min(len(series_clean)//2, 256))
        
        # Get top k frequencies by energy
        top_k_indices = np.argsort(psd)[-k:]
        spectral_energy = np.sum(psd[top_k_indices])
        
        # Calculate volatility
        volatility = np.std(series_clean)
        
        # SEVR feature
        sevr = spectral_energy / (volatility + 1e-10)
        return sevr
    except:
        return np.nan

def compute_wavelet_energy_volatility_index(series, wavelet='db4', scales=[2, 4, 8, 16], window_size=20):
    """
    2. Wavelet Energy Volatility Index (WEVI)
    Captures energy of frequency bands normalized by local volatility
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 20:
        return [np.nan] * len(scales)
    
    try:
        # Continuous wavelet transform
        coeffs, _ = pywt.cwt(series_clean, scales, wavelet)
        
        # Energy per scale
        energies = [np.sum(np.abs(c)**2) for c in coeffs]
        
        # Rolling volatility
        ts_series = pd.Series(series_clean)
        rolling_std = ts_series.rolling(window=window_size, min_periods=1).std().values
        mean_volatility = np.nanmean(rolling_std)
        
        # WEVI for each scale
        wevi = [energy / (mean_volatility + 1e-10) for energy in energies]
        return wevi
    except:
        return [np.nan] * len(scales)

def compute_frequency_weighted_volatility(series, k=5):
    """
    3. Frequency-Weighted Volatility (FWV)
    Weights volatility by strength of dominant frequencies
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 10:
        return np.nan
    
    try:
        # FFT for dominant frequencies
        freqs, psd = welch(series_clean, nperseg=min(len(series_clean)//2, 256))
        
        # Top k frequency amplitudes
        top_k_indices = np.argsort(psd)[-k:]
        top_amplitudes = psd[top_k_indices]
        
        # Volatility of first differences
        diff_volatility = np.std(np.diff(series_clean))
        
        # Weight by normalized amplitudes
        total_amplitude = np.sum(psd)
        weight = np.sum(top_amplitudes) / (total_amplitude + 1e-10)
        
        fwv = diff_volatility * weight
        return fwv
    except:
        return np.nan

def compute_periodicity_stabilized_volatility_ratio(series, max_lag=12):
    """
    4. Periodicity-Stabilized Volatility Ratio (PSVR)
    Measures volatility relative to periodicity strength
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < max_lag * 2:
        return np.nan
    
    try:
        # Autocorrelation for different lags
        autocorrs = []
        for lag in range(2, min(max_lag + 1, len(series_clean)//2)):
            if lag < len(series_clean):
                autocorr = np.corrcoef(series_clean[:-lag], series_clean[lag:])[0, 1]
                if not np.isnan(autocorr):
                    autocorrs.append(abs(autocorr))
        
        if not autocorrs:
            return np.nan
        
        # Maximum autocorrelation as periodicity strength
        max_autocorr = max(autocorrs)
        
        # Median absolute deviation as robust volatility measure
        median_val = np.median(series_clean)
        mad = np.median(np.abs(series_clean - median_val))
        
        # PSVR
        psvr = mad / (max_autocorr + 1e-10)
        return psvr
    except:
        return np.nan

def compute_cross_scale_energy_volatility_correlation(series, scales=[2, 4, 8, 16], window_size=20):
    """
    5. Cross-Scale Energy-Volatility Correlation (CEVC)
    Correlation between frequency energy and time-domain volatility
    """
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < window_size * 2:
        return np.nan
    
    try:
        # Continuous wavelet transform
        coeffs, _ = pywt.cwt(series_clean, scales, 'db4')
        
        # Rolling volatility
        ts_series = pd.Series(series_clean)
        rolling_volatility = ts_series.rolling(window=window_size, min_periods=1).std().values
        
        # Correlations between energy and volatility for each scale
        correlations = []
        for coeff in coeffs:
            # Energy at each time point (squared magnitude)
            energy_series = np.abs(coeff)**2
            
            # Ensure same length
            min_len = min(len(energy_series), len(rolling_volatility))
            energy_truncated = energy_series[:min_len]
            volatility_truncated = rolling_volatility[:min_len]
            
            # Remove NaN values
            valid_mask = ~(np.isnan(energy_truncated) | np.isnan(volatility_truncated))
            if np.sum(valid_mask) > 5:
                corr, _ = pearsonr(energy_truncated[valid_mask], volatility_truncated[valid_mask])
                if not np.isnan(corr):
                    correlations.append(corr)
        
        # Return mean correlation across scales
        if correlations:
            return np.mean(correlations)
        else:
            return np.nan
    except:
        return np.nan

def compute_advanced_frequency_features(series):
    """Compute all 5 advanced frequency-energy-volatility features"""
    features = {}
    
    # 1. Spectral Energy-to-Volatility Ratio
    features['sevr'] = compute_spectral_energy_volatility_ratio(series)
    
    # 2. Wavelet Energy Volatility Index (4 scales)
    wevi_values = compute_wavelet_energy_volatility_index(series)
    for i, wevi in enumerate(wevi_values):
        features[f'wevi_scale_{i+1}'] = wevi
    
    # 3. Frequency-Weighted Volatility
    features['fwv'] = compute_frequency_weighted_volatility(series)
    
    # 4. Periodicity-Stabilized Volatility Ratio
    features['psvr'] = compute_periodicity_stabilized_volatility_ratio(series)
    
    # 5. Cross-Scale Energy-Volatility Correlation
    features['cevc'] = compute_cross_scale_energy_volatility_correlation(series)
    
    return features

def extract_features_by_cluster(time_series_list, labels, vol_df, target_patterns=['volatile', 'calm', 'spiky']):
    """Extract advanced frequency features for specific volatility clusters"""
    print(f"🔧 Extracting advanced frequency features for patterns: {target_patterns}")
    
    # Filter for target patterns
    target_indices = vol_df[vol_df['pattern_type'].isin(target_patterns)]['series_id'].values
    
    all_features = []
    
    for i, series_id in enumerate(target_indices):
        if i % 100 == 0:
            print(f"   Processing {i}/{len(target_indices)} series...")
        
        if series_id >= len(time_series_list):
            continue
            
        series = time_series_list[series_id]
        label = labels[series_id]
        pattern_type = vol_df[vol_df['series_id'] == series_id]['pattern_type'].iloc[0]
        
        # Compute advanced frequency features
        freq_features = compute_advanced_frequency_features(series)
        
        # Add metadata
        freq_features['label'] = label
        freq_features['series_id'] = series_id
        freq_features['pattern_type'] = pattern_type
        
        all_features.append(freq_features)
    
    features_df = pd.DataFrame(all_features)
    features_df = features_df.replace([np.inf, -np.inf], np.nan)
    
    # Fill NaN with median for each pattern
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]
    for pattern in target_patterns:
        pattern_mask = features_df['pattern_type'] == pattern
        if pattern_mask.sum() > 0:
            features_df.loc[pattern_mask, feature_cols] = features_df.loc[pattern_mask, feature_cols].fillna(
                features_df.loc[pattern_mask, feature_cols].median()
            )
    
    return features_df

def evaluate_features_by_pattern(features_df):
    """Evaluate advanced frequency features using cross-validation by pattern"""
    print("📈 Evaluating advanced frequency features by volatility pattern...")

    # Feature columns (exclude metadata)
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]

    # Cross-validation setup
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')

    results = {}

    # Overall performance
    print(f"\n🎯 OVERALL PERFORMANCE:")
    X_all = features_df[feature_cols].copy()
    X_all = X_all.fillna(X_all.median())
    y_all = features_df['label'].values

    scaler = StandardScaler()
    X_all_scaled = scaler.fit_transform(X_all)

    scores = cross_val_score(model, X_all_scaled, y_all, cv=cv, scoring='roc_auc')
    results['overall'] = {
        'mean_auroc': scores.mean(),
        'std_auroc': scores.std(),
        'n_samples': len(features_df),
        'break_rate': y_all.mean()
    }
    print(f"   AUROC: {scores.mean():.4f} ± {scores.std():.4f} (n={len(features_df)})")

    # Performance by pattern
    print(f"\n📊 PERFORMANCE BY PATTERN:")
    for pattern in features_df['pattern_type'].unique():
        pattern_data = features_df[features_df['pattern_type'] == pattern]

        if len(pattern_data) < 20:
            print(f"   {pattern.upper()}: Insufficient data (n={len(pattern_data)})")
            continue

        X_pattern = pattern_data[feature_cols].copy()
        X_pattern = X_pattern.fillna(X_pattern.median())
        y_pattern = pattern_data['label'].values

        if len(np.unique(y_pattern)) < 2:
            print(f"   {pattern.upper()}: No class variation")
            continue

        try:
            scaler_pattern = StandardScaler()
            X_pattern_scaled = scaler_pattern.fit_transform(X_pattern)

            scores_pattern = cross_val_score(model, X_pattern_scaled, y_pattern, cv=cv, scoring='roc_auc')

            results[pattern] = {
                'mean_auroc': scores_pattern.mean(),
                'std_auroc': scores_pattern.std(),
                'n_samples': len(pattern_data),
                'break_rate': y_pattern.mean()
            }

            print(f"   {pattern.upper()}: AUROC {scores_pattern.mean():.4f} ± {scores_pattern.std():.4f} "
                  f"(n={len(pattern_data)}, break_rate={y_pattern.mean():.3f})")
        except Exception as e:
            print(f"   {pattern.upper()}: Error - {str(e)}")

    return results

def analyze_feature_importance(features_df):
    """Analyze which advanced frequency features are most important"""
    print("\n🔍 Analyzing advanced frequency feature importance...")

    # Feature columns
    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]

    # Prepare data
    X = features_df[feature_cols].copy()
    X = X.fillna(X.median())
    y = features_df['label'].values

    # Fit model
    model = RandomForestClassifier(n_estimators=200, random_state=42, class_weight='balanced')
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    model.fit(X_scaled, y)

    # Feature importance
    importance_df = pd.DataFrame({
        'feature': feature_cols,
        'importance': model.feature_importances_
    }).sort_values('importance', ascending=False)

    print(f"\n🏆 TOP 10 MOST IMPORTANT FEATURES:")
    for i, row in importance_df.head(10).iterrows():
        print(f"   {row['feature']}: {row['importance']:.4f}")

    return importance_df

def analyze_pattern_differences(features_df):
    """Analyze differences between volatility patterns for advanced features"""
    print("\n📊 Analyzing pattern differences for advanced frequency features...")

    feature_cols = [col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']]

    print(f"\n🔍 Feature means by pattern:")
    print("=" * 80)

    for feature in feature_cols:
        print(f"\n{feature.upper()}:")
        for pattern in features_df['pattern_type'].unique():
            pattern_data = features_df[features_df['pattern_type'] == pattern][feature]
            pattern_data_clean = pattern_data.dropna()
            if len(pattern_data_clean) > 0:
                mean_val = pattern_data_clean.mean()
                std_val = pattern_data_clean.std()
                print(f"   {pattern}: {mean_val:.4f} ± {std_val:.4f}")

        # Statistical tests between patterns
        patterns = features_df['pattern_type'].unique()
        if len(patterns) >= 2:
            for i, pattern1 in enumerate(patterns):
                for pattern2 in patterns[i+1:]:
                    data1 = features_df[features_df['pattern_type'] == pattern1][feature].dropna()
                    data2 = features_df[features_df['pattern_type'] == pattern2][feature].dropna()

                    if len(data1) > 5 and len(data2) > 5:
                        try:
                            stat, p_value = stats.mannwhitneyu(data1, data2, alternative='two-sided')
                            if p_value < 0.05:
                                print(f"   📈 {pattern1} vs {pattern2}: p={p_value:.4f} (significant)")
                        except:
                            pass

def create_summary_report(results, importance_df, features_df):
    """Create comprehensive summary report for advanced frequency features"""
    print(f"\n📋 ADVANCED FREQUENCY FEATURES SUMMARY REPORT")
    print("=" * 60)

    print(f"Dataset overview:")
    print(f"   Total series analyzed: {len(features_df)}")
    print(f"   Overall break rate: {features_df['label'].mean():.3f}")
    print(f"   Advanced features extracted: {len([col for col in features_df.columns if col not in ['label', 'series_id', 'pattern_type']])}")

    print(f"\nPattern performance ranking:")
    pattern_results = [(k, v['mean_auroc']) for k, v in results.items() if k != 'overall']
    pattern_results.sort(key=lambda x: x[1], reverse=True)

    for i, (pattern, auroc) in enumerate(pattern_results, 1):
        print(f"   {i}. {pattern.upper()}: {auroc:.4f}")

    print(f"\nTop 5 most important features:")
    for i, row in importance_df.head(5).iterrows():
        print(f"   {row['feature']}: {row['importance']:.4f}")

    print(f"\nKey insights:")
    print(f"   • SEVR (Spectral Energy-to-Volatility Ratio) measures periodic vs random behavior")
    print(f"   • WEVI (Wavelet Energy Volatility Index) captures multi-scale frequency patterns")
    print(f"   • FWV (Frequency-Weighted Volatility) emphasizes volatility in dominant frequencies")
    print(f"   • PSVR (Periodicity-Stabilized Volatility Ratio) balances volatility with periodicity")
    print(f"   • CEVC (Cross-Scale Energy-Volatility Correlation) shows frequency-volatility coupling")

def main():
    """Main execution function"""
    print("🚀 Advanced Frequency-Energy-Volatility Features Analysis")
    print("=" * 60)

    # Load data
    time_series_list, labels, vol_df = load_data()

    # Extract features
    features_df = extract_features_by_cluster(time_series_list, labels, vol_df)

    # Evaluate features
    results = evaluate_features_by_pattern(features_df)

    # Analyze importance
    importance_df = analyze_feature_importance(features_df)

    # Analyze pattern differences
    analyze_pattern_differences(features_df)

    # Create summary
    create_summary_report(results, importance_df, features_df)

    # Save results
    features_df.to_csv('advanced_frequency_features_results.csv', index=False)
    importance_df.to_csv('advanced_frequency_feature_importance.csv', index=False)

    print(f"\n💾 Results saved:")
    print(f"   • advanced_frequency_features_results.csv")
    print(f"   • advanced_frequency_feature_importance.csv")

if __name__ == "__main__":
    main()
