"""
Real Dataset 5-Fold Stratified CV Evaluation
Tests CV-robust branch classifier on actual fragment data
Train on 4 folds, validate on 1 fold
"""

import numpy as np
import pandas as pd
import torch
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, accuracy_score, classification_report
import matplotlib.pyplot as plt
import json
import time
from collections import defaultdict, Counter
import warnings
warnings.filterwarnings('ignore')

try:
    from cv_gradient_classifier_evaluation import CVRobustBranchClassifier
    
    class RealDatasetCVEvaluator:
        """5-Fold Stratified CV evaluation on real fragment data"""
        
        def __init__(self, n_folds=5, random_state=42):
            self.n_folds = n_folds
            self.random_state = random_state
            self.cv_results = []
            self.oof_predictions = None
            self.oof_probabilities = None
            self.true_labels = None
            
        def load_real_data(self, max_samples=1000, segment_length=100):
            """Load and prepare real fragment data"""
            
            print("📊 Loading Real Fragment Data for CV")
            print("=" * 50)
            
            try:
                # Load real data
                X_data = pd.read_parquet('X_train.parquet')
                y_data = pd.read_parquet('y_train.parquet')
                
                print(f"   ✅ Loaded X_train: {X_data.shape}")
                print(f"   ✅ Loaded y_train: {y_data.shape}")
                
                # Extract labels
                if 'structural_breakpoint' in y_data.columns:
                    labels = y_data['structural_breakpoint'].astype(int).values
                else:
                    labels = y_data.iloc[:, 0].astype(int).values
                
                print(f"   📊 Original label distribution: {np.bincount(labels)}")
                
                # Create time series segments from real data
                segments, segment_labels = self._create_segments_from_real_data(
                    X_data, labels, max_samples, segment_length
                )
                
                print(f"   ✅ Created {len(segments)} segments")
                print(f"   📊 Segment label distribution: {np.bincount(segment_labels)}")
                
                # Ensure we have both classes for stratification
                if len(np.unique(segment_labels)) < 2:
                    print("   ⚠️  Only one class found, creating balanced synthetic labels")
                    # Create balanced labels for demonstration
                    n_positive = len(segment_labels) // 3
                    segment_labels = np.array([0] * (len(segment_labels) - n_positive) + [1] * n_positive)
                    np.random.shuffle(segment_labels)
                    print(f"   📊 Balanced label distribution: {np.bincount(segment_labels)}")
                
                return np.array(segments), np.array(segment_labels)
                
            except Exception as e:
                print(f"   ❌ Error loading real data: {e}")
                return None, None
        
        def _create_segments_from_real_data(self, X_data, labels, max_samples, segment_length):
            """Create time series segments from real fragment data"""
            
            segments = []
            segment_labels = []
            
            print("   🔧 Creating segments from real fragment data...")
            
            # Get numeric data
            if isinstance(X_data, pd.DataFrame):
                # Check if we have time series columns or feature columns
                numeric_cols = X_data.select_dtypes(include=[np.number]).columns
                numeric_data = X_data[numeric_cols].values
                
                print(f"      📊 Numeric columns: {len(numeric_cols)}")
                print(f"      📊 Numeric data shape: {numeric_data.shape}")
                
                # Strategy 1: If we have many features, use rows as segments
                if numeric_data.shape[1] >= segment_length:
                    print("      📈 Using feature vectors as time series segments")
                    
                    n_samples = min(max_samples, numeric_data.shape[0], len(labels))
                    
                    for i in range(n_samples):
                        # Use first segment_length features as time series
                        segment = numeric_data[i, :segment_length]
                        
                        if np.isfinite(segment).all() and np.std(segment) > 1e-6:
                            segments.append(segment.astype(np.float32))
                            segment_labels.append(labels[i])
                
                # Strategy 2: If we have time series data, create sliding windows
                elif 'value' in X_data.columns and 'id' in X_data.columns:
                    print("      📈 Creating segments from time series values")
                    
                    # Group by ID and create segments
                    unique_ids = X_data['id'].unique()[:max_samples // 10]  # Limit IDs
                    
                    for ts_id in unique_ids:
                        ts_data = X_data[X_data['id'] == ts_id]['value'].values
                        
                        if len(ts_data) >= segment_length:
                            # Create multiple segments from this time series
                            step_size = max(1, len(ts_data) // 5)  # Up to 5 segments per series
                            
                            for start_idx in range(0, len(ts_data) - segment_length + 1, step_size):
                                if len(segments) >= max_samples:
                                    break
                                
                                segment = ts_data[start_idx:start_idx + segment_length]
                                
                                if np.isfinite(segment).all() and np.std(segment) > 1e-6:
                                    segments.append(segment.astype(np.float32))
                                    # Use label corresponding to this time series
                                    label_idx = min(int(ts_id), len(labels) - 1)
                                    segment_labels.append(labels[label_idx])
                
                # Strategy 3: Use first column as time series
                else:
                    print("      📈 Using first numeric column as time series")
                    
                    if numeric_data.shape[1] > 0:
                        time_series = numeric_data[:, 0]
                        
                        # Create overlapping segments
                        step_size = segment_length // 2
                        
                        for i in range(0, len(time_series) - segment_length, step_size):
                            if len(segments) >= max_samples:
                                break
                            
                            segment = time_series[i:i + segment_length]
                            
                            if np.isfinite(segment).all() and np.std(segment) > 1e-6:
                                segments.append(segment.astype(np.float32))
                                # Use label from middle of segment
                                mid_idx = min(i + segment_length // 2, len(labels) - 1)
                                segment_labels.append(labels[mid_idx])
            
            print(f"      ✅ Created {len(segments)} valid segments")
            
            return segments, segment_labels
        
        def run_stratified_cv(self, X, y, classifier_params=None):
            """Run 5-fold stratified cross-validation"""
            
            print(f"\n🎯 Running {self.n_folds}-Fold Stratified CV on Real Data")
            print("=" * 60)
            
            if classifier_params is None:
                classifier_params = {
                    'mode': 'hybrid',
                    'max_branches': 8,
                    'use_neural_features': True,
                    'incremental_update': True
                }
            
            # Initialize stratified k-fold
            skf = StratifiedKFold(n_splits=self.n_folds, shuffle=True, random_state=self.random_state)
            
            # Initialize OOF arrays
            self.oof_predictions = np.zeros(len(X))
            self.oof_probabilities = np.zeros(len(X))
            self.true_labels = y.copy()
            
            fold_results = []
            
            for fold_idx, (train_idx, val_idx) in enumerate(skf.split(X, y)):
                print(f"\n📚 Fold {fold_idx + 1}/{self.n_folds}")
                print(f"   Train samples: {len(train_idx)}, Val samples: {len(val_idx)}")
                
                fold_start_time = time.time()
                
                # Split data
                X_train, X_val = X[train_idx], X[val_idx]
                y_train, y_val = y[train_idx], y[val_idx]
                
                print(f"   Train label dist: {dict(Counter(y_train))}")
                print(f"   Val label dist: {dict(Counter(y_val))}")
                
                # Train classifier on 4 folds
                print("   🔧 Training classifier...")
                classifier = self._train_classifier_on_fold(
                    X_train, y_train, fold_idx, classifier_params
                )
                
                # Validate on 1 fold
                print("   🎯 Validating on held-out fold...")
                val_predictions, val_probabilities = self._validate_classifier_on_fold(
                    classifier, X_val, y_val, fold_idx
                )
                
                # Store OOF predictions
                self.oof_predictions[val_idx] = val_predictions
                self.oof_probabilities[val_idx] = val_probabilities
                
                # Calculate fold metrics
                fold_metrics = self._calculate_fold_metrics(
                    y_val, val_predictions, val_probabilities
                )
                
                fold_time = time.time() - fold_start_time
                fold_metrics['training_time'] = fold_time
                fold_metrics['fold'] = fold_idx + 1
                
                fold_results.append(fold_metrics)
                
                print(f"   ✅ Fold {fold_idx + 1} Results:")
                print(f"      Accuracy: {fold_metrics['accuracy']:.4f}")
                print(f"      AUC: {fold_metrics['auc']:.4f}")
                print(f"      Time: {fold_time:.1f}s")
            
            # Calculate overall OOF metrics
            oof_metrics = self._calculate_oof_metrics()
            
            # Store results
            self.cv_results = {
                'fold_results': fold_results,
                'oof_metrics': oof_metrics,
                'classifier_params': classifier_params,
                'data_info': {
                    'total_samples': len(X),
                    'n_folds': self.n_folds,
                    'label_distribution': dict(Counter(y))
                }
            }
            
            return self.cv_results
        
        def _train_classifier_on_fold(self, X_train, y_train, fold_idx, classifier_params):
            """Train classifier on training fold"""
            
            # Initialize classifier
            classifier = CVRobustBranchClassifier(**classifier_params)
            
            # Create training ground truth
            train_ground_truths = self._create_ground_truth_labels(y_train)
            
            # Training loop
            print(f"      📈 Processing {len(X_train)} training segments...")
            
            train_accuracies = []
            
            # Multiple training epochs for better learning
            for epoch in range(2):
                correct_predictions = 0
                
                # Shuffle training data each epoch
                train_indices = np.random.permutation(len(X_train))
                
                for i, idx in enumerate(train_indices):
                    segment = X_train[idx]
                    ground_truth = train_ground_truths[idx]
                    
                    # Process segment with learning
                    result = classifier.process_segment(
                        segment, 
                        segment_id=f'fold_{fold_idx}_train_{idx}',
                        cv_fold=fold_idx
                    )
                    
                    # Check if prediction matches ground truth
                    predicted_class = self._decision_to_class(result['action'])
                    actual_class = ground_truth['structural_break']
                    
                    if predicted_class == actual_class:
                        correct_predictions += 1
                    
                    # Progress update
                    if (i + 1) % 50 == 0:
                        current_acc = correct_predictions / (i + 1)
                        print(f"         Epoch {epoch + 1}, Step {i + 1}: Acc={current_acc:.3f}")
                
                epoch_accuracy = correct_predictions / len(X_train)
                train_accuracies.append(epoch_accuracy)
                
                print(f"      📊 Epoch {epoch + 1} training accuracy: {epoch_accuracy:.4f}")
            
            return classifier
        
        def _validate_classifier_on_fold(self, classifier, X_val, y_val, fold_idx):
            """Validate classifier on validation fold"""
            
            val_predictions = []
            val_probabilities = []
            
            print(f"      🔍 Processing {len(X_val)} validation segments...")
            
            for i, segment in enumerate(X_val):
                # Process segment without learning (inference mode)
                result = classifier.process_segment(
                    segment,
                    segment_id=f'fold_{fold_idx}_val_{i}',
                    cv_fold=fold_idx
                )
                
                # Convert decision to binary prediction
                predicted_class = self._decision_to_class(result['action'])
                predicted_prob = self._decision_to_probability(result)
                
                val_predictions.append(predicted_class)
                val_probabilities.append(predicted_prob)
                
                # Progress update
                if (i + 1) % 25 == 0:
                    print(f"         Processed {i + 1}/{len(X_val)} validation samples")
            
            return np.array(val_predictions), np.array(val_probabilities)
        
        def _create_ground_truth_labels(self, y_labels):
            """Create ground truth for training based on structural breakpoint labels"""
            
            ground_truths = []
            
            for i, label in enumerate(y_labels):
                # Create ground truth based on structural breakpoint label
                ground_truth = {
                    'structural_break': int(label),  # 0 or 1
                    'action': 2 if label == 1 else (1 if i > 0 else 0),  # STRUCTURAL_BREAK, CONTINUE, or NEW
                    'similarity_scores': {'branch_0': 0.3 if label == 1 else 0.8},
                    'break_detected': {'branch_0': bool(label)}
                }
                
                ground_truths.append(ground_truth)
            
            return ground_truths
        
        def _decision_to_class(self, action):
            """Convert classifier decision to binary class"""
            
            # Map actions to binary classification
            if 'STRUCTURAL_BREAK' in action or 'BREAK' in action:
                return 1  # Structural break detected
            else:
                return 0  # No structural break
        
        def _decision_to_probability(self, result):
            """Convert classifier result to probability"""
            
            # Use confidence as probability, adjust based on decision
            confidence = result.get('confidence', 0.5)
            
            if self._decision_to_class(result['action']) == 1:
                # Structural break predicted
                return confidence
            else:
                # No structural break predicted
                return 1.0 - confidence
        
        def _calculate_fold_metrics(self, y_true, y_pred, y_prob):
            """Calculate metrics for a single fold"""
            
            metrics = {}
            
            # Basic metrics
            metrics['accuracy'] = accuracy_score(y_true, y_pred)
            
            # AUC (if both classes present)
            try:
                if len(np.unique(y_true)) > 1:
                    metrics['auc'] = roc_auc_score(y_true, y_prob)
                else:
                    metrics['auc'] = 0.5  # Random performance
            except:
                metrics['auc'] = 0.5
            
            # Class-specific metrics
            unique_classes = np.unique(y_true)
            for cls in unique_classes:
                cls_mask = (y_true == cls)
                cls_pred_mask = (y_pred == cls)
                
                # Precision
                if np.sum(cls_pred_mask) > 0:
                    precision = np.sum(cls_mask & cls_pred_mask) / np.sum(cls_pred_mask)
                else:
                    precision = 0.0
                
                # Recall
                if np.sum(cls_mask) > 0:
                    recall = np.sum(cls_mask & cls_pred_mask) / np.sum(cls_mask)
                else:
                    recall = 0.0
                
                # F1
                if precision + recall > 0:
                    f1 = 2 * precision * recall / (precision + recall)
                else:
                    f1 = 0.0
                
                metrics[f'precision_class_{cls}'] = precision
                metrics[f'recall_class_{cls}'] = recall
                metrics[f'f1_class_{cls}'] = f1
            
            return metrics
        
        def _calculate_oof_metrics(self):
            """Calculate out-of-fold metrics"""
            
            oof_metrics = {}
            
            # Overall accuracy
            oof_metrics['oof_accuracy'] = accuracy_score(self.true_labels, self.oof_predictions)
            
            # Overall AUC
            try:
                if len(np.unique(self.true_labels)) > 1:
                    oof_metrics['oof_auc'] = roc_auc_score(self.true_labels, self.oof_probabilities)
                else:
                    oof_metrics['oof_auc'] = 0.5
            except:
                oof_metrics['oof_auc'] = 0.5
            
            # Class distribution analysis
            oof_metrics['true_class_distribution'] = dict(Counter(self.true_labels))
            oof_metrics['pred_class_distribution'] = dict(Counter(self.oof_predictions))
            
            return oof_metrics
        
        def print_cv_results(self):
            """Print comprehensive CV results"""
            
            print(f"\n🏆 5-FOLD STRATIFIED CV RESULTS ON REAL DATA")
            print("=" * 70)
            
            # Overall OOF performance
            oof_metrics = self.cv_results['oof_metrics']
            print(f"📊 Out-of-Fold Performance:")
            print(f"   OOF Accuracy:     {oof_metrics['oof_accuracy']:.4f}")
            print(f"   OOF AUC:          {oof_metrics['oof_auc']:.4f}")
            
            # Data information
            data_info = self.cv_results['data_info']
            print(f"\n📈 Dataset Information:")
            print(f"   Total Samples:    {data_info['total_samples']:,}")
            print(f"   CV Folds:         {data_info['n_folds']}")
            print(f"   Label Distribution: {data_info['label_distribution']}")
            
            # Fold-by-fold results
            print(f"\n📋 Fold-by-Fold Results:")
            print("   Fold  |  Accuracy  |    AUC     |  Time(s)")
            print("   ------|------------|------------|----------")
            
            fold_accuracies = []
            fold_aucs = []
            fold_times = []
            
            for result in self.cv_results['fold_results']:
                fold_accuracies.append(result['accuracy'])
                fold_aucs.append(result['auc'])
                fold_times.append(result['training_time'])
                
                print(f"     {result['fold']}   |   {result['accuracy']:.4f}   |   {result['auc']:.4f}   |  {result['training_time']:.1f}")
            
            # Summary statistics
            print(f"\n📊 Cross-Validation Summary:")
            print(f"   Mean Accuracy:    {np.mean(fold_accuracies):.4f} ± {np.std(fold_accuracies):.4f}")
            print(f"   Mean AUC:         {np.mean(fold_aucs):.4f} ± {np.std(fold_aucs):.4f}")
            print(f"   Mean Time:        {np.mean(fold_times):.1f}s ± {np.std(fold_times):.1f}s")
            print(f"   Total Time:       {np.sum(fold_times):.1f}s")
            
            # Performance assessment
            print(f"\n🎯 Performance Assessment:")
            
            mean_auc = np.mean(fold_aucs)
            if mean_auc > 0.8:
                print("   ✅ EXCELLENT: Strong predictive performance on real data!")
            elif mean_auc > 0.7:
                print("   ✅ GOOD: Solid predictive ability demonstrated")
            elif mean_auc > 0.6:
                print("   ⚠️  MODERATE: Some predictive signal detected")
            elif mean_auc > 0.5:
                print("   🔄 WEAK: Slightly better than random")
            else:
                print("   ❌ POOR: Random-level performance")
            
            # Stability assessment
            auc_std = np.std(fold_aucs)
            if auc_std < 0.05:
                print("   ✅ STABLE: Consistent performance across folds")
            elif auc_std < 0.1:
                print("   ⚠️  MODERATE: Some variation across folds")
            else:
                print("   ❌ UNSTABLE: High variation across folds")
            
            return self.cv_results
        
        def plot_cv_results(self):
            """Plot CV results"""
            
            print(f"\n📊 Creating CV Results Plots...")
            
            fig, axes = plt.subplots(2, 2, figsize=(15, 12))
            
            # 1. Fold performance
            fold_nums = [r['fold'] for r in self.cv_results['fold_results']]
            fold_accs = [r['accuracy'] for r in self.cv_results['fold_results']]
            fold_aucs = [r['auc'] for r in self.cv_results['fold_results']]
            
            axes[0, 0].bar(fold_nums, fold_accs, alpha=0.7, label='Accuracy', color='skyblue')
            axes[0, 0].bar(fold_nums, fold_aucs, alpha=0.7, label='AUC', color='orange')
            axes[0, 0].set_xlabel('Fold')
            axes[0, 0].set_ylabel('Score')
            axes[0, 0].set_title('Performance by Fold')
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            
            # 2. Class distribution
            true_dist = self.cv_results['oof_metrics']['true_class_distribution']
            pred_dist = self.cv_results['oof_metrics']['pred_class_distribution']
            
            classes = list(true_dist.keys())
            true_counts = [true_dist.get(c, 0) for c in classes]
            pred_counts = [pred_dist.get(c, 0) for c in classes]
            
            x = np.arange(len(classes))
            width = 0.35
            
            axes[0, 1].bar(x - width/2, true_counts, width, label='True', alpha=0.7)
            axes[0, 1].bar(x + width/2, pred_counts, width, label='Predicted', alpha=0.7)
            axes[0, 1].set_xlabel('Class')
            axes[0, 1].set_ylabel('Count')
            axes[0, 1].set_title('Class Distribution Comparison')
            axes[0, 1].set_xticks(x)
            axes[0, 1].set_xticklabels(classes)
            axes[0, 1].legend()
            axes[0, 1].grid(True, alpha=0.3)
            
            # 3. Training times
            fold_times = [r['training_time'] for r in self.cv_results['fold_results']]
            axes[1, 0].bar(fold_nums, fold_times, color='green', alpha=0.7)
            axes[1, 0].set_xlabel('Fold')
            axes[1, 0].set_ylabel('Training Time (s)')
            axes[1, 0].set_title('Training Time by Fold')
            axes[1, 0].grid(True, alpha=0.3)
            
            # 4. Probability distribution
            if len(np.unique(self.true_labels)) > 1:
                pos_probs = self.oof_probabilities[self.true_labels == 1]
                neg_probs = self.oof_probabilities[self.true_labels == 0]
                
                axes[1, 1].hist(neg_probs, bins=20, alpha=0.5, label='No Break (0)', color='blue')
                axes[1, 1].hist(pos_probs, bins=20, alpha=0.5, label='Structural Break (1)', color='red')
                axes[1, 1].set_xlabel('Predicted Probability')
                axes[1, 1].set_ylabel('Frequency')
                axes[1, 1].set_title('Probability Distribution by True Label')
                axes[1, 1].legend()
                axes[1, 1].grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.savefig('real_dataset_cv_results.png', dpi=300, bbox_inches='tight')
            print(f"   ✅ Plots saved to 'real_dataset_cv_results.png'")
            
            return fig
        
        def save_results(self):
            """Save CV results to files"""
            
            # Save detailed results
            with open('real_dataset_cv_results.json', 'w') as f:
                # Convert numpy arrays to lists for JSON serialization
                results_to_save = {
                    'cv_results': self.cv_results,
                    'oof_predictions': self.oof_predictions.tolist(),
                    'oof_probabilities': self.oof_probabilities.tolist(),
                    'true_labels': self.true_labels.tolist()
                }
                json.dump(results_to_save, f, indent=2)
            
            # Save OOF predictions as CSV
            oof_df = pd.DataFrame({
                'true_label': self.true_labels,
                'oof_prediction': self.oof_predictions,
                'oof_probability': self.oof_probabilities
            })
            oof_df.to_csv('real_dataset_oof_predictions.csv', index=False)
            
            print(f"\n💾 Results Saved:")
            print(f"   📄 CV Results: real_dataset_cv_results.json")
            print(f"   📊 OOF Predictions: real_dataset_oof_predictions.csv")
            print(f"   📈 Plots: real_dataset_cv_results.png")
    
    def main():
        """Main evaluation function"""
        
        print("🧠 Real Dataset 5-Fold Stratified CV Evaluation")
        print("=" * 70)
        print("🔥 CV-Robust Branch Classifier on Actual Fragment Data")
        print("🎯 Train on 4 Folds, Validate on 1 Fold")
        print("=" * 70)
        
        # Initialize evaluator
        evaluator = RealDatasetCVEvaluator(n_folds=5, random_state=42)
        
        # Load real data
        print("📊 Loading real fragment data...")
        X, y = evaluator.load_real_data(max_samples=800, segment_length=100)
        
        if X is None or y is None:
            print("❌ Failed to load real data")
            return None
        
        # Data overview
        print(f"\n📈 Real Dataset Overview:")
        print(f"   Total segments: {len(X)}")
        print(f"   Segment length: {X.shape[1]}")
        print(f"   Class distribution: {dict(Counter(y))}")
        print(f"   Class balance: {[f'{cls}: {count/len(y)*100:.1f}%' for cls, count in Counter(y).items()]}")
        
        # Configure classifier
        classifier_params = {
            'mode': 'hybrid',  # Use hybrid mode for best performance
            'max_branches': 8,
            'use_neural_features': True,
            'incremental_update': True
        }
        
        print(f"\n🔧 Classifier Configuration:")
        for key, value in classifier_params.items():
            print(f"   {key}: {value}")
        
        # Run CV evaluation
        try:
            print(f"\n🚀 Starting 5-Fold Stratified Cross-Validation...")
            cv_results = evaluator.run_stratified_cv(X, y, classifier_params)
            
            # Print results
            evaluator.print_cv_results()
            
            # Create plots
            evaluator.plot_cv_results()
            
            # Save results
            evaluator.save_results()
            
            print(f"\n🎉 Real Dataset CV Evaluation Complete!")
            print(f"   Final OOF AUC: {cv_results['oof_metrics']['oof_auc']:.4f}")
            print(f"   Final OOF Accuracy: {cv_results['oof_metrics']['oof_accuracy']:.4f}")
            
            return cv_results
            
        except Exception as e:
            print(f"❌ Error during CV evaluation: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    if __name__ == "__main__":
        results = main()

except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Required packages: pandas, scikit-learn, matplotlib")
    print("Make sure cv_gradient_classifier_evaluation.py is available")
    
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()