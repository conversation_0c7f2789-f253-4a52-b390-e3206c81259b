#!/usr/bin/env python3
"""
Truncation-Aware Thermodynamic Time Series Analysis
Enhanced version that integrates truncation detection into thermodynamic calculations
"""

import numpy as np
import pandas as pd
from scipy import stats, signal
try:
    from scipy.integrate import trapz
except ImportError:
    from scipy.integrate import trapezoid as trapz
from joblib import Parallel, delayed
from tqdm import tqdm
from dataclasses import dataclass
import warnings

warnings.filterwarnings('ignore')

@dataclass
class TruncationAwareThermodynamicMetrics:
    """Container for truncation-aware thermodynamic metrics."""
    # Truncation Quality Metrics
    truncation_confidence: float
    precision_quality_score: float
    boundary_constraint_factor: float
    clustering_artifact_score: float
    flat_segment_ratio: float
    
    # Enhanced Thermodynamic Metrics (truncation-corrected)
    robust_potential_energy_length_weighted: float
    robust_potential_energy_curvature: float
    robust_potential_energy_gradient: float
    robust_potential_energy_harmonic: float
    robust_kinetic_energy_variance_based: float
    robust_kinetic_energy_velocity: float
    robust_kinetic_energy_amplitude: float
    robust_kinetic_energy_spectral: float
    
    # Truncation-Validated Spike Metrics
    validated_spike_count_2sigma_pos: float
    validated_spike_count_2sigma_neg: float
    validated_spike_ratio_2sigma_pos: float
    validated_spike_ratio_2sigma_neg: float
    validated_spike_count_3sigma_pos: float
    validated_spike_count_3sigma_neg: float
    validated_spike_ratio_3sigma_pos: float
    validated_spike_ratio_3sigma_neg: float
    boundary_spike_ratio: float
    corrected_spike_energy_2sigma: float
    corrected_spike_energy_3sigma: float
    
    # Quality-Weighted Composite Metrics
    quality_weighted_total_energy: float
    artifact_corrected_energy_efficiency: float
    clustering_corrected_entropy: float
    boundary_corrected_free_energy: float
    unconstrained_phase_volume: float
    robust_dissipation_rate: float
    validated_ergodic_measure: float

class AdaptiveTruncationDetector:
    """
    Adaptive truncation detection that learns patterns from actual data
    """
    
    def __init__(self, safe_threshold: float = 1e-12):
        self.safe_threshold = safe_threshold
        
        # Adaptive patterns (learned from data)
        self.suspicious_extremes = []
        self.suspicious_ranges = []
        self.precision_patterns = {}
        self.clustering_thresholds = []
        self.boundary_sensitivity = 1e-8
        self.is_calibrated = False
        
        # Statistical thresholds for pattern detection
        self.extreme_frequency_threshold = 0.005  # 0.5% of samples
        self.range_frequency_threshold = 0.01     # 1% of samples
        self.precision_frequency_threshold = 0.15  # 15% of values
        self.clustering_significance = 0.02        # 2% clustering threshold

    def calibrate_on_dataset(self, dataset_samples: list, verbose: bool = True):
        """Calibrate detector on actual dataset to learn truncation patterns"""
        if verbose:
            print("🔧 Calibrating adaptive truncation detector on dataset...")
        
        # Discover suspicious extreme values
        self.suspicious_extremes = self._discover_suspicious_extremes(dataset_samples)
        
        # Discover suspicious ranges
        self.suspicious_ranges = self._discover_suspicious_ranges(dataset_samples)
        
        # Analyze precision patterns
        self.precision_patterns = self._analyze_precision_patterns(dataset_samples)
        
        # Set adaptive clustering thresholds
        self._set_adaptive_clustering_thresholds(dataset_samples)
        
        # Set boundary sensitivity
        self._set_boundary_sensitivity(dataset_samples)
        
        # Calculate dataset-wide statistics for adaptive methods
        self._calculate_dataset_statistics(dataset_samples)
        
        self.is_calibrated = True
        
        if verbose:
            print(f"   ✅ Found {len(self.suspicious_extremes)} suspicious extreme values")
            print(f"   ✅ Found {len(self.suspicious_ranges)} suspicious ranges")
            print(f"   ✅ Identified {len(self.precision_patterns)} precision patterns")
            print(f"   ✅ Set boundary sensitivity: {self.boundary_sensitivity:.2e}")

    def _discover_suspicious_extremes(self, dataset_samples: list) -> list:
        """Discover frequently occurring extreme values across the dataset"""
        from collections import Counter
        
        all_mins = []
        all_maxs = []
        
        for sample in dataset_samples:
            if len(sample) > 0:
                all_mins.append(np.min(sample))
                all_maxs.append(np.max(sample))
        
        # Round to reasonable precision for frequency analysis
        rounded_mins = [round(x, 10) for x in all_mins]
        rounded_maxs = [round(x, 10) for x in all_maxs]
        
        # Count frequencies
        min_counter = Counter(rounded_mins)
        max_counter = Counter(rounded_maxs)
        
        # Find values that appear suspiciously often
        total_samples = len(dataset_samples)
        threshold_count = max(1, int(total_samples * self.extreme_frequency_threshold))
        
        suspicious_extremes = []
        
        # Add frequently occurring minimums
        for value, count in min_counter.most_common():
            if count >= threshold_count:
                suspicious_extremes.append(value)
        
        # Add frequently occurring maximums
        for value, count in max_counter.most_common():
            if count >= threshold_count and value not in suspicious_extremes:
                suspicious_extremes.append(value)
        
        return sorted(set(suspicious_extremes))

    def _discover_suspicious_ranges(self, dataset_samples: list) -> list:
        """Discover frequently occurring ranges that suggest truncation"""
        from collections import Counter
        
        all_ranges = []
        
        for sample in dataset_samples:
            if len(sample) > 1:
                range_val = np.max(sample) - np.min(sample)
                all_ranges.append(range_val)
        
        # Round ranges for frequency analysis
        rounded_ranges = [round(x, 10) for x in all_ranges if x > 0]
        
        # Count frequencies
        range_counter = Counter(rounded_ranges)
        
        # Find ranges that appear suspiciously often
        total_samples = len([r for r in all_ranges if r > 0])
        threshold_count = max(1, int(total_samples * self.range_frequency_threshold))
        
        suspicious_ranges = []
        for range_val, count in range_counter.most_common():
            if count >= threshold_count:
                suspicious_ranges.append(range_val)
        
        return sorted(suspicious_ranges)

    def _analyze_precision_patterns(self, dataset_samples: list) -> dict:
        """Analyze decimal precision patterns across the dataset"""
        from collections import Counter
        
        precision_counts = Counter()
        total_values = 0
        
        for sample in dataset_samples:
            for value in sample:
                if not np.isnan(value):
                    # Analyze decimal precision
                    val_str = f"{value:.15f}"
                    if '.' in val_str:
                        decimal_part = val_str.split('.')[1]
                        non_zero_decimals = len(decimal_part.rstrip('0'))
                        precision_counts[non_zero_decimals] += 1
                        total_values += 1
        
        # Find precision patterns that are over-represented
        precision_patterns = {}
        for precision, count in precision_counts.items():
            frequency = count / total_values if total_values > 0 else 0
            if frequency > self.precision_frequency_threshold:
                precision_patterns[precision] = frequency
        
        return precision_patterns

    def _set_adaptive_clustering_thresholds(self, dataset_samples: list):
        """Set clustering thresholds based on actual data characteristics"""
        all_ranges = []
        
        for sample in dataset_samples:
            if len(sample) > 1:
                range_val = np.max(sample) - np.min(sample)
                if range_val > 0:
                    all_ranges.append(range_val)
        
        if len(all_ranges) > 0:
            # Set thresholds as percentages of typical data ranges
            median_range = np.median(all_ranges)
            self.clustering_thresholds = [
                0.001 * median_range,  # Very tight clustering
                0.005 * median_range,  # Tight clustering
                0.01 * median_range,   # Moderate clustering
                0.02 * median_range,   # Loose clustering
                0.05 * median_range    # Very loose clustering
            ]
        else:
            # Fallback thresholds
            self.clustering_thresholds = [1e-6, 5e-6, 1e-5, 2e-5, 5e-5]

    def _set_boundary_sensitivity(self, dataset_samples: list):
        """Set boundary detection sensitivity based on data scale"""
        all_values = []
        
        for sample in dataset_samples:
            all_values.extend(sample)
        
        if len(all_values) > 0:
            data_range = np.ptp(all_values)
            # Set sensitivity relative to data scale
            self.boundary_sensitivity = max(1e-12, data_range * 1e-6)
        else:
            self.boundary_sensitivity = 1e-8

    def _calculate_dataset_statistics(self, dataset_samples: list):
        """Calculate dataset-wide statistics for adaptive truncation detection"""
        from collections import Counter
        
        all_values = []
        all_uniqueness_ratios = []
        
        for sample in dataset_samples:
            if len(sample) > 0:
                all_values.extend(sample)
                
                # Calculate uniqueness ratio for this sample
                unique_count = len(set(sample))
                uniqueness_ratio = unique_count / len(sample)
                all_uniqueness_ratios.append(uniqueness_ratio)
        
        # Store global value range for precision boundary detection
        if len(all_values) > 0:
            self.global_value_range = (np.min(all_values), np.max(all_values))
        else:
            self.global_value_range = (0.0, 1.0)
        
        # Store expected uniqueness for comparison
        if len(all_uniqueness_ratios) > 0:
            self.expected_uniqueness = np.median(all_uniqueness_ratios)
        else:
            self.expected_uniqueness = 0.5
        
        # Calculate dataset-wide precision distribution
        precision_distribution = Counter()
        total_dataset_values = 0
        
        for sample in dataset_samples:
            for value in sample:
                if not np.isnan(value):
                    val_str = f"{value:.15f}"
                    if '.' in val_str:
                        decimal_part = val_str.split('.')[1]
                        non_zero_decimals = len(decimal_part.rstrip('0'))
                        precision_distribution[non_zero_decimals] += 1
                        total_dataset_values += 1
        
        # Store precision distribution for adaptive comparison
        self.dataset_precision_distribution = {}
        for precision, count in precision_distribution.items():
            self.dataset_precision_distribution[precision] = count / total_dataset_values if total_dataset_values > 0 else 0

    def detect_precision_truncation(self, ts: np.ndarray) -> dict:
        """Detect decimal precision limitations using adaptive patterns"""
        ts_clean = ts[~np.isnan(ts)]
        if len(ts_clean) < 2:
            return {'precision_quality_score': 1.0, 'decimal_truncation_score': 0.0}
        
        from collections import Counter
        
        # Analyze precision distribution in this sample
        precision_counts = Counter()
        total_values = len(ts_clean)
        
        for value in ts_clean:
            val_str = f"{value:.15f}"
            if '.' in val_str:
                decimal_part = val_str.split('.')[1]
                non_zero_decimals = len(decimal_part.rstrip('0'))
                precision_counts[non_zero_decimals] += 1
        
        # Calculate truncation score based on adaptive patterns
        if self.is_calibrated and hasattr(self, 'dataset_precision_distribution'):
            # Use learned precision patterns
            truncation_score = 0.0
            
            for precision, count in precision_counts.items():
                frequency = count / total_values
                
                # Check if this precision appears more often than expected
                expected_frequency = self.dataset_precision_distribution.get(precision, 0.01)
                if frequency > expected_frequency * 1.5:  # 50% more than dataset average
                    truncation_score += frequency * 100
                
                # Heavily penalize very low precision (0-2 decimal places)
                if precision <= 2:
                    truncation_score += frequency * 50
        else:
            # Fallback to basic precision analysis
            precision_1 = precision_counts.get(0, 0) + precision_counts.get(1, 0)
            precision_2 = precision_counts.get(2, 0)
            precision_3 = precision_counts.get(3, 0)
            
            truncation_score = (precision_1 * 2 + precision_2 + precision_3 * 0.5) / total_values * 100
        
        # Check for excessive trailing zeros
        trailing_zero_score = 0.0
        for value in ts_clean:
            val_str = f"{value:.10f}"
            if '.' in val_str:
                decimal_part = val_str.split('.')[1]
                trailing_zeros = len(decimal_part) - len(decimal_part.rstrip('0'))
                if trailing_zeros >= 5:  # 5 or more trailing zeros
                    trailing_zero_score += 1
        
        trailing_zero_score = (trailing_zero_score / total_values) * 30  # Up to 30 points
        
        # Check for quantization patterns
        quantization_score = 0.0
        potential_quanta = [0.01, 0.001, 0.0001, 0.1, 0.05, 0.02]
        
        for quantum in potential_quanta:
            quantized_count = sum(1 for v in ts_clean if abs(v % quantum) < 1e-12 or abs((v % quantum) - quantum) < 1e-12)
            if quantized_count / total_values > 0.3:  # More than 30% are quantized
                quantization_score += (quantized_count / total_values) * 20
        
        # Combine all truncation indicators
        total_truncation_score = min(100, truncation_score + trailing_zero_score + quantization_score)
        precision_quality_score = max(0, 1 - total_truncation_score / 100)
        
        return {
            'precision_quality_score': precision_quality_score,
            'decimal_truncation_score': total_truncation_score
        }
    
    def detect_boundary_truncation(self, ts: np.ndarray) -> dict:
        """Detect artificial boundaries and constraints using adaptive patterns"""
        ts_clean = ts[~np.isnan(ts)]
        if len(ts_clean) < 2:
            return {'boundary_constraint_factor': 0.0, 'boundary_spike_ratio': 0.0}
        
        min_val, max_val = np.min(ts_clean), np.max(ts_clean)
        range_val = max_val - min_val
        
        boundary_indicators = 0
        
        if self.is_calibrated:
            # Use learned patterns for detection
            # Check if extremes match suspicious values
            min_is_suspicious = any(abs(min_val - sv) < self.boundary_sensitivity for sv in self.suspicious_extremes)
            max_is_suspicious = any(abs(max_val - sv) < self.boundary_sensitivity for sv in self.suspicious_extremes)
            
            # Check if range matches suspicious ranges
            range_is_suspicious = any(abs(range_val - sr) < self.boundary_sensitivity for sr in self.suspicious_ranges)
            
            # Check for symmetric bounds
            symmetric_bounds = abs(abs(min_val) - abs(max_val)) < self.boundary_sensitivity and abs(min_val) > 0.01
            
            boundary_indicators = sum([min_is_suspicious, max_is_suspicious, range_is_suspicious, symmetric_bounds])
        else:
            # Fallback to basic detection if not calibrated
            rounded_min = round(min_val, 10)
            rounded_max = round(max_val, 10)
            rounded_range = round(range_val, 10)
            
            # Basic suspicious patterns
            basic_suspicious = [0.0, 0.1, -0.1, 0.01, -0.01, 0.05, -0.05, 0.07, -0.07]
            min_is_suspicious = any(abs(rounded_min - sv) < 1e-10 for sv in basic_suspicious)
            max_is_suspicious = any(abs(rounded_max - sv) < 1e-10 for sv in basic_suspicious)
            
            # Basic suspicious ranges
            basic_ranges = [0.1, 0.2, 0.14, 1.0, 2.0]
            range_is_suspicious = any(abs(rounded_range - sr) < 1e-10 for sr in basic_ranges)
            
            symmetric_bounds = abs(abs(min_val) - abs(max_val)) < 1e-8 and abs(min_val) > 0.01
            
            boundary_indicators = sum([min_is_suspicious, max_is_suspicious, range_is_suspicious, symmetric_bounds])
        
        boundary_constraint_factor = boundary_indicators / 4.0
        
        # Calculate boundary spike ratio using adaptive thresholds
        if range_val > 0:
            if self.is_calibrated and len(self.clustering_thresholds) > 0:
                boundary_threshold = self.clustering_thresholds[2]  # Use moderate clustering threshold
            else:
                boundary_threshold = 0.01 * range_val
            
            near_boundaries = np.sum((np.abs(ts_clean - min_val) <= boundary_threshold) | 
                                   (np.abs(ts_clean - max_val) <= boundary_threshold))
            boundary_spike_ratio = near_boundaries / len(ts_clean)
        else:
            boundary_spike_ratio = 1.0  # All values are the same
        
        return {
            'boundary_constraint_factor': boundary_constraint_factor,
            'boundary_spike_ratio': boundary_spike_ratio
        }
    
    def detect_clustering_artifacts(self, ts: np.ndarray) -> dict:
        """Detect value clustering that indicates truncation"""
        ts_clean = ts[~np.isnan(ts)]
        if len(ts_clean) < 10:
            return {'clustering_artifact_score': 0.0}
        
        min_val, max_val = np.min(ts_clean), np.max(ts_clean)
        range_val = max_val - min_val
        
        if range_val == 0:
            return {'clustering_artifact_score': 1.0}
        
        # Check clustering at different thresholds
        clustering_scores = []
        for threshold_pct in [0.5, 1.0, 2.0]:
            threshold = threshold_pct / 100.0 * range_val
            near_min = np.sum(np.abs(ts_clean - min_val) <= threshold)
            near_max = np.sum(np.abs(ts_clean - max_val) <= threshold)
            
            clustering_score = (near_min + near_max) / len(ts_clean)
            clustering_scores.append(clustering_score)
        
        # High clustering suggests truncation
        clustering_artifact_score = np.mean(clustering_scores)
        
        return {'clustering_artifact_score': clustering_artifact_score}
    
    def detect_flat_segments(self, ts: np.ndarray) -> dict:
        """Detect flat segments from truncation"""
        ts_clean = ts[~np.isnan(ts)]
        if len(ts_clean) < 3:
            return {'flat_segment_ratio': 0.0}
        
        velocity = np.diff(ts_clean)
        flat_points = np.sum(np.abs(velocity) < 1e-10)
        flat_segment_ratio = flat_points / len(velocity)
        
        return {'flat_segment_ratio': flat_segment_ratio}
    
    def _is_round_number(self, value: float) -> bool:
        """Check if a number is 'round' (ends in multiple zeros or simple fractions)"""
        # Check for integers
        if abs(value - round(value)) < 1e-10:
            return True
        
        # Check for simple fractions (0.5, 0.25, 0.1, etc.)
        simple_fractions = [0.1, 0.2, 0.25, 0.5, 0.75]
        for frac in simple_fractions:
            if abs(value % 1 - frac) < 1e-10:
                return True
        
        # Check for values ending in multiple zeros
        val_str = f"{value:.10f}".rstrip('0')
        if val_str.endswith('.'):
            return True
        
        return False
    
    def compute_truncation_confidence(self, ts: np.ndarray) -> dict:
        """Compute overall truncation confidence metrics"""
        precision_metrics = self.detect_precision_truncation(ts)
        boundary_metrics = self.detect_boundary_truncation(ts)
        clustering_metrics = self.detect_clustering_artifacts(ts)
        flat_metrics = self.detect_flat_segments(ts)
        
        # Simple averaging (no adaptive weighting)
        truncation_indicators = [
            1 - precision_metrics['precision_quality_score'],
            boundary_metrics['boundary_constraint_factor'],
            clustering_metrics['clustering_artifact_score'],
            flat_metrics['flat_segment_ratio']
        ]
        
        # Overall truncation confidence (1 = no truncation, 0 = heavy truncation)
        truncation_confidence = 1 - np.mean(truncation_indicators)
        
        return {
            'truncation_confidence': max(0, min(1, truncation_confidence)),
            **precision_metrics,
            **boundary_metrics,
            **clustering_metrics,
            **flat_metrics
        }

class TruncationAwareThermodynamicAnalyzer:
    """Enhanced thermodynamic analyzer with integrated truncation awareness"""
    
    def __init__(self, temperature: float = 1.0, damping: float = 0.1):
        self.temperature = temperature
        self.damping = damping
        self.truncation_detector = AdaptiveTruncationDetector()  # Fixed class name
    
    def calibrate_parameters(self, ts: np.ndarray, truncation_metrics: dict) -> None:
        """Calibrate parameters with truncation awareness"""
        if len(ts) < 2:
            return
        
        # Adjust calibration based on truncation confidence
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        
        # More conservative calibration for truncated data
        self.temperature = (np.nanvar(ts) + 1e-12) * (0.5 + 0.5 * confidence)
        
        velocity = np.diff(ts[~np.isnan(ts)])
        if len(velocity) > 1:
            # Filter out flat segments for autocorrelation
            flat_ratio = truncation_metrics.get('flat_segment_ratio', 0.0)
            if flat_ratio < 0.5:  # Only if not too many flat segments
                autocorr = np.correlate(velocity, velocity, mode='full')[len(velocity)-1:]
                autocorr = autocorr / (autocorr[0] + 1e-12)
                self.damping = max(0, -np.log(np.abs(autocorr[1]) + 1e-12) if autocorr[1] != 0 else 0.1)
            else:
                self.damping = 0.1  # Default for heavily truncated data    
    def compute_robust_potential_energy(self, ts: np.ndarray, truncation_metrics: dict) -> dict:
        """Compute truncation-corrected potential energy"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        results = {
            'length_weighted': 0.0,
            'curvature': 0.0,
            'gradient': 0.0,
            'harmonic': 0.0
        }
        
        if N < 2:
            return results
        
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        flat_ratio = truncation_metrics.get('flat_segment_ratio', 0.0)
        
        # Length-weighted (less affected by truncation)
        results['length_weighted'] = N * np.log(N + 1)
        
        # Curvature potential (corrected for flat segments)
        d2x_dt2 = np.diff(ts_clean, n=2)
        if len(d2x_dt2) > 0:
            # Exclude near-zero curvatures from flat segments
            significant_curvature = d2x_dt2[np.abs(d2x_dt2) > 1e-10]
            if len(significant_curvature) > 0:
                results['curvature'] = np.sum(np.abs(significant_curvature)) * (1 + flat_ratio)
            else:
                results['curvature'] = 0.0
        
        # Gradient potential (corrected for flat segments)
        dx_dt = np.diff(ts_clean)
        if len(dx_dt) > 0:
            # Weight by non-flat segments
            results['gradient'] = np.sum(dx_dt**2) * (1 - flat_ratio * 0.5)
        
        # Harmonic potential (quality-weighted)
        results['harmonic'] = np.sum(ts_clean**2) / (N + 1e-12) * confidence
        
        return results
    
    def compute_robust_kinetic_energy(self, ts: np.ndarray, truncation_metrics: dict) -> dict:
        """Compute truncation-corrected kinetic energy"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        results = {
            'variance_based': 0.0,
            'velocity': 0.0,
            'amplitude': 0.0,
            'spectral': 0.0
        }
        
        if N < 2:
            return results
        
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        flat_ratio = truncation_metrics.get('flat_segment_ratio', 0.0)
        boundary_factor = truncation_metrics.get('boundary_constraint_factor', 0.0)
        
        # Variance-based kinetic energy (quality-weighted)
        results['variance_based'] = np.var(ts_clean) * confidence
        
        # Velocity-based kinetic energy (corrected for flat segments)
        velocity = np.diff(ts_clean)
        if len(velocity) > 0:
            # Exclude near-zero velocities from flat segments
            significant_velocity = velocity[np.abs(velocity) > 1e-10]
            if len(significant_velocity) > 0:
                results['velocity'] = np.mean(significant_velocity**2) * (1 - flat_ratio * 0.3)
            else:
                results['velocity'] = 0.0
        
        # Amplitude kinetic energy (corrected for boundary constraints)
        amplitude_range = np.ptp(ts_clean)
        results['amplitude'] = (amplitude_range**2 / 4) * (1 + boundary_factor * 0.5)
        
        # Spectral kinetic energy (truncation-aware)
        if N >= 4:
            try:
                freqs, psd = signal.welch(ts_clean, nperseg=min(N//8, 128))
                # Weight by confidence to account for artificial periodicities
                results['spectral'] = trapz(freqs**2 * psd, freqs) * confidence
            except:
                results['spectral'] = results['variance_based']
        else:
            results['spectral'] = results['variance_based']
        
        return results
    
    def compute_validated_spike_metrics(self, ts: np.ndarray, truncation_metrics: dict) -> dict:
        """Compute truncation-validated spike metrics"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        default_results = {
            'validated_spike_count_2sigma_pos': 0.0,
            'validated_spike_count_2sigma_neg': 0.0,
            'validated_spike_ratio_2sigma_pos': 0.0,
            'validated_spike_ratio_2sigma_neg': 0.0,
            'validated_spike_count_3sigma_pos': 0.0,
            'validated_spike_count_3sigma_neg': 0.0,
            'validated_spike_ratio_3sigma_pos': 0.0,
            'validated_spike_ratio_3sigma_neg': 0.0,
            'corrected_spike_energy_2sigma': 0.0,
            'corrected_spike_energy_3sigma': 0.0
        }
        
        if N < 2:
            return default_results
        
        mean_ts = np.mean(ts_clean)
        std_ts = np.std(ts_clean)
        
        if std_ts == 0:
            return default_results
        
        boundary_spike_ratio = truncation_metrics.get('boundary_spike_ratio', 0.0)
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        
        # Detect spikes with boundary awareness
        dev = ts_clean - mean_ts
        
        # 2-sigma spikes
        spikes_2sigma_pos = np.sum(dev > 2 * std_ts)
        spikes_2sigma_neg = np.sum(dev < -2 * std_ts)
        
        # 3-sigma spikes  
        spikes_3sigma_pos = np.sum(dev > 3 * std_ts)
        spikes_3sigma_neg = np.sum(dev < -3 * std_ts)
        
        # Correct for boundary artifacts
        boundary_correction = max(0, 1 - boundary_spike_ratio)
        
        # Validated spike counts (reduced by boundary artifacts)
        validated_2sigma_pos = spikes_2sigma_pos * boundary_correction
        validated_2sigma_neg = spikes_2sigma_neg * boundary_correction
        validated_3sigma_pos = spikes_3sigma_pos * boundary_correction
        validated_3sigma_neg = spikes_3sigma_neg * boundary_correction
        
        # Spike energies (confidence-weighted)
        spikes_2sigma = ts_clean[np.abs(dev) > 2 * std_ts]
        spikes_3sigma = ts_clean[np.abs(dev) > 3 * std_ts]
        
        spike_energy_2sigma = np.sum((spikes_2sigma - mean_ts)**2) / (N + 1e-12) * confidence
        spike_energy_3sigma = np.sum((spikes_3sigma - mean_ts)**2) / (N + 1e-12) * confidence
        
        return {
            'validated_spike_count_2sigma_pos': validated_2sigma_pos,
            'validated_spike_count_2sigma_neg': validated_2sigma_neg,
            'validated_spike_ratio_2sigma_pos': validated_2sigma_pos / N,
            'validated_spike_ratio_2sigma_neg': validated_2sigma_neg / N,
            'validated_spike_count_3sigma_pos': validated_3sigma_pos,
            'validated_spike_count_3sigma_neg': validated_3sigma_neg,
            'validated_spike_ratio_3sigma_pos': validated_3sigma_pos / N,
            'validated_spike_ratio_3sigma_neg': validated_3sigma_neg / N,
            'corrected_spike_energy_2sigma': spike_energy_2sigma,
            'corrected_spike_energy_3sigma': spike_energy_3sigma
        }
    
    def compute_clustering_corrected_entropy(self, ts: np.ndarray, truncation_metrics: dict) -> float:
        """Compute entropy corrected for artificial clustering"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        if N < 2:
            return 0.0
        
        clustering_score = truncation_metrics.get('clustering_artifact_score', 0.0)
        precision_quality = truncation_metrics.get('precision_quality_score', 1.0)
        
        # Adaptive binning based on data quality
        base_bins = max(10, int(np.sqrt(N)))
        # Reduce bins for heavily clustered data to avoid artificial entropy
        adjusted_bins = max(5, int(base_bins * (1 - clustering_score * 0.5)))
        
        hist, bin_edges = np.histogram(ts_clean, bins=adjusted_bins, density=True)
        p_i = hist[hist > 0] * (bin_edges[1] - bin_edges[0])
        
        # Base entropy
        entropy = -np.sum(p_i * np.log(p_i + 1e-12))
        
        # Correct for artificial clustering
        clustering_correction = 1 + clustering_score * 0.3
        entropy_corrected = entropy * clustering_correction
        
        # Weight by precision quality
        entropy_final = entropy_corrected * precision_quality
        
        # Add velocity-based entropy if data quality allows
        velocity = np.diff(ts_clean)
        if len(velocity) > 1 and precision_quality > 0.5:
            autocorr = np.correlate(velocity, velocity, mode='full')[len(velocity)-1:]
            autocorr = autocorr / (autocorr[0] + 1e-12)
            try:
                decay_rate = -np.log(np.abs(autocorr[1]) + 1e-12)
                entropy_final += self.damping * decay_rate * precision_quality
            except:
                pass
        
        return entropy_final
    
    def compute_unconstrained_phase_volume(self, ts: np.ndarray, truncation_metrics: dict) -> float:
        """Compute phase space volume corrected for artificial boundaries"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        if N < 2:
            return 1.0
        
        boundary_factor = truncation_metrics.get('boundary_constraint_factor', 0.0)
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        
        x = ts_clean[:-1]
        v = np.diff(ts_clean)
        
        if N > 1000:
            # Bounding box approximation with boundary correction
            x_range = np.ptp(x)
            v_range = np.ptp(v)
            
            # Extrapolate for artificial boundaries
            boundary_expansion = 1 + boundary_factor * 0.5
            corrected_volume = x_range * v_range * np.pi * boundary_expansion
        else:
            try:
                from scipy.spatial import ConvexHull
                points = np.column_stack([x, v])
                hull = ConvexHull(points)
                
                # Correct for boundary constraints
                boundary_expansion = 1 + boundary_factor * 0.3
                corrected_volume = hull.volume * boundary_expansion
            except:
                # Fallback with boundary correction
                boundary_expansion = 1 + boundary_factor * 0.5
                corrected_volume = np.std(x) * np.std(v) * np.pi * boundary_expansion
        
        # Weight by overall confidence
        return corrected_volume * confidence
    
    def compute_robust_dissipation_rate(self, ts: np.ndarray, truncation_metrics: dict, dt: float = 1.0) -> float:
        """Compute dissipation rate robust to truncation artifacts"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        if N < 3:
            return 0.0
        
        flat_ratio = truncation_metrics.get('flat_segment_ratio', 0.0)
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        
        velocity = np.diff(ts_clean) / dt
        if len(velocity) < 2:
            return np.var(velocity) * confidence
        
        # Filter out flat segments for correlation analysis
        if flat_ratio < 0.5:
            significant_velocity = velocity[np.abs(velocity) > 1e-10]
            if len(significant_velocity) > 1:
                autocorr = np.correlate(significant_velocity, significant_velocity, mode='full')
                autocorr = autocorr[len(significant_velocity)-1:]
                autocorr = autocorr / (autocorr[0] + 1e-12)
                dissipation = -(autocorr[1] - autocorr[0]) / dt if len(autocorr) > 1 else self.damping * np.var(significant_velocity)
                return max(0, dissipation) * confidence
        
        # Fallback for heavily truncated data
        return self.damping * np.var(velocity) * confidence
    
    def compute_validated_ergodic_measure(self, ts: np.ndarray, truncation_metrics: dict, n_windows: int = 10) -> float:
        """Compute ergodicity measure validated against truncation artifacts"""
        ts_clean = ts[~np.isnan(ts)]
        N = len(ts_clean)
        
        if N < n_windows:
            return 0.0
        
        confidence = truncation_metrics.get('truncation_confidence', 1.0)
        clustering_score = truncation_metrics.get('clustering_artifact_score', 0.0)
        
        # Adjust window analysis for data quality
        effective_windows = max(5, int(n_windows * confidence))
        window_size = N // effective_windows
        
        time_avg = np.mean(ts_clean)
        ensemble_avgs = []
        
        for i in range(0, N-window_size+1, window_size):
            window_data = ts_clean[i:i+window_size]
            if len(window_data) > 0:
                ensemble_avgs.append(np.mean(window_data))
        
        if len(ensemble_avgs) < 2:
            return 0.0
        
        ensemble_avg = np.mean(ensemble_avgs)
        ensemble_std = np.std(ensemble_avgs)
        
        if ensemble_std == 0:
            return 0.0
        
        ergodic_measure = np.abs(time_avg - ensemble_avg) / ensemble_std
        
        # Correct for artificial clustering effects
        clustering_correction = max(0.5, 1 - clustering_score * 0.3)
        
        return ergodic_measure * clustering_correction * confidence
    
    def analyze_time_series(self, ts: np.ndarray, tstar: int = None, dt: float = 1.0) -> dict:
        """Comprehensive truncation-aware thermodynamic analysis"""
        ts = np.array(ts)
        N = len(ts)
        
        # First, detect truncation patterns
        truncation_metrics = self.truncation_detector.compute_truncation_confidence(ts)
        
        # Calibrate parameters with truncation awareness
        self.calibrate_parameters(ts, truncation_metrics)
        
        # Compute robust thermodynamic metrics
        robust_potential = self.compute_robust_potential_energy(ts, truncation_metrics)
        robust_kinetic = self.compute_robust_kinetic_energy(ts, truncation_metrics)
        validated_spikes = self.compute_validated_spike_metrics(ts, truncation_metrics)
        
        # Compute quality-weighted composite metrics
        robust_total_energy = robust_potential['harmonic'] + robust_kinetic['variance_based']
        quality_weighted_total_energy = robust_total_energy * truncation_metrics['truncation_confidence']
        
        artifact_corrected_efficiency = (robust_kinetic['variance_based'] / 
                                        (robust_total_energy + 1e-12) * 
                                        truncation_metrics['truncation_confidence'])
        
        clustering_corrected_entropy = self.compute_clustering_corrected_entropy(ts, truncation_metrics)
        
        boundary_corrected_free_energy = ((robust_potential['harmonic'] + robust_kinetic['variance_based'] - 
                                          self.temperature * clustering_corrected_entropy) * 
                                         (1 - truncation_metrics['boundary_constraint_factor'] * 0.2))
        
        unconstrained_phase_volume = self.compute_unconstrained_phase_volume(ts, truncation_metrics)
        robust_dissipation_rate = self.compute_robust_dissipation_rate(ts, truncation_metrics, dt)
        validated_ergodic_measure = self.compute_validated_ergodic_measure(ts, truncation_metrics)
        
        # Compile all metrics
        results = {
            # Truncation quality metrics
            'truncation_confidence': truncation_metrics['truncation_confidence'],
            'precision_quality_score': truncation_metrics['precision_quality_score'],
            'boundary_constraint_factor': truncation_metrics['boundary_constraint_factor'],
            'clustering_artifact_score': truncation_metrics['clustering_artifact_score'],
            'flat_segment_ratio': truncation_metrics['flat_segment_ratio'],
            'boundary_spike_ratio': truncation_metrics['boundary_spike_ratio'],
            
            # Robust thermodynamic metrics
            'robust_potential_energy_length_weighted': robust_potential['length_weighted'],
            'robust_potential_energy_curvature': robust_potential['curvature'],
            'robust_potential_energy_gradient': robust_potential['gradient'],
            'robust_potential_energy_harmonic': robust_potential['harmonic'],
            'robust_kinetic_energy_variance_based': robust_kinetic['variance_based'],
            'robust_kinetic_energy_velocity': robust_kinetic['velocity'],
            'robust_kinetic_energy_amplitude': robust_kinetic['amplitude'],
            'robust_kinetic_energy_spectral': robust_kinetic['spectral'],
            
            # Validated spike metrics
            **validated_spikes,
            
            # Quality-weighted composite metrics
            'quality_weighted_total_energy': quality_weighted_total_energy,
            'artifact_corrected_energy_efficiency': artifact_corrected_efficiency,
            'clustering_corrected_entropy': clustering_corrected_entropy,
            'boundary_corrected_free_energy': boundary_corrected_free_energy,
            'unconstrained_phase_volume': unconstrained_phase_volume,
            'robust_dissipation_rate': robust_dissipation_rate,
            'validated_ergodic_measure': validated_ergodic_measure,
            
            # Position information
            'tstar_position': tstar / N if tstar is not None and N > 0 else np.nan
        }
        
        # Add pre/post analysis if tstar is provided
        if tstar is not None and 0 < tstar < N:
            pre_results = self.analyze_segment(ts[:tstar], truncation_metrics, dt, 'pre_')
            post_results = self.analyze_segment(ts[tstar:], truncation_metrics, dt, 'post_')
            results.update(pre_results)
            results.update(post_results)
            
            # Compute simple ratios (not the complex adaptive ones)
            ratio_results = self.compute_simple_ratios(pre_results, post_results)
            results.update(ratio_results)
        
        return results
    
    def analyze_segment(self, ts_segment: np.ndarray, global_truncation_metrics: dict, dt: float, prefix: str) -> dict:
        """Analyze a time series segment with truncation awareness"""
        if len(ts_segment) < 2:
            return {f'{prefix}truncation_confidence': 0.0}
        
        # Detect truncation in this segment
        segment_truncation = self.truncation_detector.compute_truncation_confidence(ts_segment)
        
        # Use global context for better truncation assessment
        combined_confidence = (segment_truncation['truncation_confidence'] + 
                             global_truncation_metrics['truncation_confidence']) / 2
        
        combined_metrics = {
            'truncation_confidence': combined_confidence,
            'precision_quality_score': segment_truncation['precision_quality_score'],
            'boundary_constraint_factor': segment_truncation['boundary_constraint_factor'],
            'clustering_artifact_score': segment_truncation['clustering_artifact_score'],
            'flat_segment_ratio': segment_truncation['flat_segment_ratio'],
            'boundary_spike_ratio': segment_truncation.get('boundary_spike_ratio', 0.0)
        }
        
        # Compute robust metrics for segment
        robust_potential = self.compute_robust_potential_energy(ts_segment, combined_metrics)
        robust_kinetic = self.compute_robust_kinetic_energy(ts_segment, combined_metrics)
        validated_spikes = self.compute_validated_spike_metrics(ts_segment, combined_metrics)
        
        # Composite metrics
        robust_total_energy = robust_potential['harmonic'] + robust_kinetic['variance_based']
        quality_weighted_total_energy = robust_total_energy * combined_confidence
        
        return {
            f'{prefix}truncation_confidence': combined_confidence,
            f'{prefix}robust_potential_energy_harmonic': robust_potential['harmonic'],
            f'{prefix}robust_kinetic_energy_variance_based': robust_kinetic['variance_based'],
            f'{prefix}quality_weighted_total_energy': quality_weighted_total_energy,
            f'{prefix}validated_spike_count_2sigma_pos': validated_spikes['validated_spike_count_2sigma_pos'],
            f'{prefix}validated_spike_count_2sigma_neg': validated_spikes['validated_spike_count_2sigma_neg'],
            f'{prefix}corrected_spike_energy_2sigma': validated_spikes['corrected_spike_energy_2sigma'],
            f'{prefix}clustering_corrected_entropy': self.compute_clustering_corrected_entropy(ts_segment, combined_metrics),
            f'{prefix}unconstrained_phase_volume': self.compute_unconstrained_phase_volume(ts_segment, combined_metrics)
        }
    
    def compute_simple_ratios(self, pre_results: dict, post_results: dict) -> dict:
        """Compute simple ratios between pre and post segments"""
        ratios = {}
        
        # Key metrics for ratio analysis
        key_metrics = [
            'truncation_confidence',
            'robust_potential_energy_harmonic',
            'robust_kinetic_energy_variance_based',
            'quality_weighted_total_energy',
            'corrected_spike_energy_2sigma',
            'clustering_corrected_entropy',
            'unconstrained_phase_volume'
        ]
        
        for metric in key_metrics:
            pre_key = f'pre_{metric}'
            post_key = f'post_{metric}'
            
            if pre_key in pre_results and post_key in post_results:
                pre_val = pre_results[pre_key]
                post_val = post_results[post_key]
                
                if pre_val != 0 and not np.isnan(pre_val) and not np.isnan(post_val):
                    ratios[f'ratio_{metric}'] = post_val / (pre_val + 1e-12)
                else:
                    ratios[f'ratio_{metric}'] = np.nan
        
        # Special ratio: truncation asymmetry
        pre_conf = pre_results.get('pre_truncation_confidence', 1.0)
        post_conf = post_results.get('post_truncation_confidence', 1.0)
        ratios['truncation_asymmetry'] = abs(pre_conf - post_conf)
        
        return ratios
    
    def batch_analyze(self, series_list: list, tstar_list: list = None, dt: float = 1.0, n_jobs: int = -1) -> list:
        """Analyze multiple time series with truncation awareness in parallel"""
        if tstar_list is None:
            tstar_list = [None] * len(series_list)
        
        if len(tstar_list) != len(series_list):
            raise ValueError("tstar_list must have same length as series_list")
        
        def process_series(ts, tstar):
            return self.analyze_time_series(ts, tstar, dt)
        
        return Parallel(n_jobs=n_jobs)(
            delayed(process_series)(ts, tstar) 
            for ts, tstar in tqdm(zip(series_list, tstar_list), 
                                total=len(series_list), 
                                desc="Analyzing time series (truncation-aware)")
        )

# Demonstration and testing
if __name__ == "__main__":
    print("=== Truncation-Aware Thermodynamic Analysis Framework ===\n")
    
    # Initialize truncation-aware analyzer
    analyzer = TruncationAwareThermodynamicAnalyzer(temperature=0.9, damping=0.1)
    
    # Generate test data with different truncation patterns
    np.random.seed(42)
    
    # Test 1: Clean data
    clean_series = np.random.normal(0, 1, 200)
    
    # Test 2: Boundary truncated data
    truncated_series = np.random.normal(0, 1, 200)
    truncated_series = np.clip(truncated_series, -0.07, 0.07)
    
    # Test 3: Precision truncated data
    precision_series = np.random.normal(0, 1, 200)
    precision_series = np.round(precision_series, 2)
    
    test_series = [clean_series, truncated_series, precision_series]
    test_names = ["Clean", "Boundary Truncated", "Precision Truncated"]
    
    print("Analyzing test series...")
    for i, (series, name) in enumerate(zip(test_series, test_names)):
        print(f"\n{name} Series:")
        results = analyzer.analyze_time_series(series, tstar=100)
        print(f"  Truncation Confidence: {results['truncation_confidence']:.3f}")
        print(f"  Precision Quality: {results['precision_quality_score']:.3f}")
        print(f"  Boundary Constraint: {results['boundary_constraint_factor']:.3f}")
        print(f"  Clustering Artifacts: {results['clustering_artifact_score']:.3f}")
        print(f"  Robust Total Energy: {results['quality_weighted_total_energy']:.3f}")
        print(f"  Validated Spikes (2σ+): {results['validated_spike_count_2sigma_pos']:.1f}")
    
    print("\n✅ Truncation-aware thermodynamic analysis completed!")