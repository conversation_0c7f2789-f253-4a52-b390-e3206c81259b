#!/usr/bin/env python3
"""
Enhanced Structural Break Detection Pipeline - Standalone Version
Demonstrates the pipeline structure without external dependencies
"""

import os
import sys
import time
from typing import Dict, List, Tuple, Optional, Any
from collections import Counter
import warnings
import math

warnings.filterwarnings('ignore')

class TruncationFeaturesExtractor:
    """
    Extracts truncation-focused features for structural break detection
    Standalone version without numpy dependency
    """
    
    def __init__(self, safe_threshold: float = 1e-12):
        """Initialize truncation features extractor"""
        self.safe_threshold = safe_threshold
        
        print("🔍 Truncation Features Extractor Initialized")
        print("   - Focus: Data quality patterns for structural break detection")
        print("   - Categories: 6 feature types (precision, clustering, boundaries, etc.)")
    
    def extract_truncation_features(self, normal_values: List[float], 
                                  candidate_values: List[float], 
                                  sample_id: Any = None) -> Dict[str, float]:
        """Extract comprehensive truncation-based features"""
        features = {}
        if sample_id is not None:
            features['sample_id'] = sample_id
        
        # 1. DECIMAL PRECISION FEATURES
        features.update(self._extract_precision_features(normal_values, candidate_values))
        
        # 2. ROUND NUMBER FEATURES
        features.update(self._extract_round_number_features(normal_values, candidate_values))
        
        # 3. CLUSTERING FEATURES
        features.update(self._extract_clustering_features(normal_values, candidate_values))
        
        # 4. BOUNDARY FEATURES
        features.update(self._extract_boundary_features(normal_values, candidate_values))
        
        # 5. TAIL FLATNESS FEATURES
        features.update(self._extract_tail_features(normal_values, candidate_values))
        
        # 6. CROSS-PART COMPARISON FEATURES
        features.update(self._extract_cross_part_features(normal_values, candidate_values))
        
        return features
    
    def _extract_precision_features(self, normal_values: List[float], 
                                  candidate_values: List[float]) -> Dict[str, float]:
        """Extract decimal precision-based features"""
        features = {}
        
        for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
            # Count limited precision values
            precision_1 = sum(1 for v in values if len(f"{v:.10f}".split('.')[1].rstrip('0')) <= 1)
            precision_2 = sum(1 for v in values if len(f"{v:.10f}".split('.')[1].rstrip('0')) <= 2)
            precision_3 = sum(1 for v in values if len(f"{v:.10f}".split('.')[1].rstrip('0')) <= 3)
            
            total = len(values)
            features[f'{part_name}_precision_1_pct'] = precision_1 / total * 100
            features[f'{part_name}_precision_2_pct'] = precision_2 / total * 100
            features[f'{part_name}_precision_3_pct'] = precision_3 / total * 100
            features[f'{part_name}_decimal_truncation_score'] = (precision_1 + precision_2 + precision_3) / total * 100
        
        # Cross-part precision asymmetry
        features['precision_asymmetry'] = abs(
            features['normal_decimal_truncation_score'] - features['candidate_decimal_truncation_score']
        )
        
        return features
    
    def _extract_round_number_features(self, normal_values: List[float], 
                                     candidate_values: List[float]) -> Dict[str, float]:
        """Extract round number pattern features"""
        features = {}
        
        # Define suspicious round numbers
        round_numbers = [0.0, 0.1, -0.1, 0.01, -0.01, 0.07, -0.07, 0.05, -0.05, 0.03, -0.03]
        suspicious_patterns = [0.070000, -0.070000, 0.050000, -0.050000, 0.030000, -0.030000]
        
        for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
            min_val, max_val = min(values), max(values)
            
            # Check if extremes are round numbers
            min_is_round = any(abs(min_val - rn) < 1e-8 for rn in round_numbers)
            max_is_round = any(abs(max_val - rn) < 1e-8 for rn in round_numbers)
            
            features[f'{part_name}_min_is_round'] = int(min_is_round)
            features[f'{part_name}_max_is_round'] = int(max_is_round)
            
            # Count round number occurrences
            round_count = sum(sum(abs(val - rn) < 1e-8 for rn in round_numbers) for val in values)
            features[f'{part_name}_round_number_pct'] = round_count / len(values) * 100
            
            # Suspicious patterns
            suspicious_count = sum(sum(abs(val - sp) < 1e-8 for sp in suspicious_patterns) for val in values)
            features[f'{part_name}_suspicious_patterns_pct'] = suspicious_count / len(values) * 100
        
        return features
    
    def _extract_clustering_features(self, normal_values: List[float], 
                                   candidate_values: List[float]) -> Dict[str, float]:
        """Extract value clustering features"""
        features = {}
        
        for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
            min_val, max_val = min(values), max(values)
            range_val = max_val - min_val
            
            if range_val > 0:
                # Clustering at different thresholds
                for threshold_pct in [0.5, 1.0, 2.0]:
                    threshold = threshold_pct / 100.0 * range_val
                    near_min = sum(1 for v in values if abs(v - min_val) <= threshold)
                    near_max = sum(1 for v in values if abs(v - max_val) <= threshold)
                    
                    features[f'{part_name}_min_cluster_{threshold_pct}pct'] = near_min / len(values) * 100
                    features[f'{part_name}_max_cluster_{threshold_pct}pct'] = near_max / len(values) * 100
            else:
                # Constant values
                for threshold_pct in [0.5, 1.0, 2.0]:
                    features[f'{part_name}_min_cluster_{threshold_pct}pct'] = 100.0
                    features[f'{part_name}_max_cluster_{threshold_pct}pct'] = 100.0
        
        return features
    
    def _extract_boundary_features(self, normal_values: List[float], 
                                 candidate_values: List[float]) -> Dict[str, float]:
        """Extract boundary and constraint features"""
        features = {}
        
        for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
            min_val, max_val = min(values), max(values)
            
            # Symmetric bounds detection
            features[f'{part_name}_symmetric_bounds'] = int(
                abs(abs(min_val) - abs(max_val)) < 1e-6 and abs(min_val) > 0.01
            )
            
            # Range features
            range_val = max_val - min_val
            features[f'{part_name}_range'] = range_val
            
            # Suspicious ranges (too round)
            suspicious_ranges = [0.1, 0.2, 0.5, 1.0, 2.0]
            features[f'{part_name}_suspicious_range'] = int(
                any(abs(range_val - sr) < 1e-6 for sr in suspicious_ranges)
            )
        
        return features
    
    def _extract_tail_features(self, normal_values: List[float], 
                             candidate_values: List[float]) -> Dict[str, float]:
        """Extract tail flatness features"""
        features = {}
        
        for part_name, values in [('normal', normal_values), ('candidate', candidate_values)]:
            sorted_values = sorted(values)
            n = len(values)
            
            # Count consecutive equal values at extremes
            left_flatness = 0
            right_flatness = 0
            
            # Left tail flatness
            for i in range(1, min(10, n)):
                if abs(sorted_values[i] - sorted_values[0]) < 1e-10:
                    left_flatness += 1
                else:
                    break
            
            # Right tail flatness
            for i in range(n-2, max(n-11, -1), -1):
                if abs(sorted_values[i] - sorted_values[-1]) < 1e-10:
                    right_flatness += 1
                else:
                    break
            
            features[f'{part_name}_left_tail_flatness'] = left_flatness
            features[f'{part_name}_right_tail_flatness'] = right_flatness
            features[f'{part_name}_total_tail_flatness'] = left_flatness + right_flatness
        
        return features
    
    def _extract_cross_part_features(self, normal_values: List[float], 
                                   candidate_values: List[float]) -> Dict[str, float]:
        """Extract cross-part comparison features"""
        features = {}
        
        # Range comparison (most important from analysis)
        normal_range = max(normal_values) - min(normal_values)
        candidate_range = max(candidate_values) - min(candidate_values)
        
        features['range_ratio'] = normal_range / (candidate_range + self.safe_threshold)
        features['range_difference'] = abs(normal_range - candidate_range)
        
        # Extreme value alignment
        features['min_alignment'] = abs(min(normal_values) - min(candidate_values))
        features['max_alignment'] = abs(max(normal_values) - max(candidate_values))
        
        # Truncation asymmetry (key discriminator)
        normal_truncation = features.get('normal_decimal_truncation_score', 0) + features.get('normal_suspicious_patterns_pct', 0)
        candidate_truncation = features.get('candidate_decimal_truncation_score', 0) + features.get('candidate_suspicious_patterns_pct', 0)
        features['truncation_asymmetry'] = abs(normal_truncation - candidate_truncation)
        
        # Consistent truncation detection
        normal_round_extremes = features.get('normal_min_is_round', 0) + features.get('normal_max_is_round', 0)
        candidate_round_extremes = features.get('candidate_min_is_round', 0) + features.get('candidate_max_is_round', 0)
        features['consistent_round_truncation'] = int(normal_round_extremes > 0 and candidate_round_extremes > 0)
        
        return features

class StreamlinedRatioEnhancer:
    """Streamlined ratio features enhancer - standalone version"""
    
    def __init__(self, safe_division_threshold: float = 1e-12):
        """Initialize streamlined ratio enhancer"""
        self.safe_division_threshold = safe_division_threshold
        
        # Define features applicable for ratio computation
        self.ratio_applicable_features = self._define_ratio_applicable_features()
        self.two_sided_features = self._define_two_sided_features()
        
        print("🔧 Streamlined Ratio Enhancer Initialized")
        print("   - Ratio types: Standard, Log, Symmetric (3 types)")
        print("   - Removed: Inverse and Absolute ratios")
        print(f"   - Safe division threshold: {safe_division_threshold}")
    
    def _define_ratio_applicable_features(self) -> Dict[str, List[str]]:
        """Define features where ratios are meaningful"""
        return {
            'basic_stats': ['mean', 'std', 'min', 'max', 'q25', 'q50', 'q75'],
            'tsfresh_features': [
                'energy_ratio_chunk1', 'energy_ratio_chunk2',
                'fft_agg_centroid', 'fft_agg_variance',
                'binned_entropy', 'lempel_ziv_complexity'
            ],
            'signal_processing': [
                'spec_entropy', 'num_peaks', 'mean_prominence'
            ]
        }
    
    def _define_two_sided_features(self) -> List[str]:
        """Define features that are inherently comparative"""
        return [
            'p_ttest', 'p_mannwhitney', 'p_ks', 'wass_dist', 'jsd',
            'cohens_d', 'glass_delta', 'cliffs_delta',
            'diff_mean', 'diff_std', 'mutual_info'
        ]
    
    def _safe_divide(self, numerator: float, denominator: float) -> float:
        """Perform safe division with edge case handling"""
        if abs(denominator) < self.safe_division_threshold:
            return float('nan')
        
        result = numerator / denominator
        
        if abs(result) > 1e13:  # Handle very large results
            return float('nan')
        
        return result
    
    def _is_ratio_applicable(self, base_feature_name: str) -> bool:
        """Check if a feature is applicable for ratio computation"""
        # Skip two-sided features
        if base_feature_name in self.two_sided_features:
            return False
        
        # Skip already computed ratios/differences
        if base_feature_name.startswith(('ratio_', 'diff_', 'log_ratio_', 'symmetric_ratio_')):
            return False
        
        # Check if in applicable categories
        for category, features in self.ratio_applicable_features.items():
            if base_feature_name in features:
                return True
        
        return False
    
    def add_streamlined_ratio_features(self, features_dict: Dict[str, Any]) -> Dict[str, Any]:
        """Add streamlined ratio features (standard, log, symmetric only)"""
        enhanced_features = features_dict.copy()
        
        # Find pre/post feature pairs
        pre_features = {}
        post_features = {}
        
        for feature_name, feature_value in features_dict.items():
            if feature_name.endswith('_pre'):
                base_name = feature_name[:-4]
                pre_features[base_name] = feature_value
            elif feature_name.endswith('_post'):
                base_name = feature_name[:-5]
                post_features[base_name] = feature_value
        
        # Compute streamlined ratios
        for base_name in pre_features.keys():
            if base_name in post_features and self._is_ratio_applicable(base_name):
                pre_value = pre_features[base_name]
                post_value = post_features[base_name]
                
                # Skip if either value is NaN
                if str(pre_value) == 'nan' or str(post_value) == 'nan':
                    continue
                
                # 1. Standard ratio (post/pre)
                ratio_value = self._safe_divide(post_value, pre_value)
                enhanced_features[f"ratio_{base_name}"] = ratio_value
                
                # 2. Log ratio (log(post/pre))
                if str(ratio_value) != 'nan' and ratio_value > 0:
                    try:
                        log_ratio_value = math.log(ratio_value)
                        enhanced_features[f"log_ratio_{base_name}"] = log_ratio_value
                    except (ValueError, OverflowError):
                        enhanced_features[f"log_ratio_{base_name}"] = float('nan')
                else:
                    enhanced_features[f"log_ratio_{base_name}"] = float('nan')
                
                # 3. Symmetric ratio ((post-pre)/(|post|+|pre|))
                denominator = abs(post_value) + abs(pre_value)
                if denominator >= self.safe_division_threshold:
                    symmetric_ratio_value = (post_value - pre_value) / denominator
                    enhanced_features[f"symmetric_ratio_{base_name}"] = symmetric_ratio_value
                else:
                    enhanced_features[f"symmetric_ratio_{base_name}"] = float('nan')
        
        return enhanced_features

class EnhancedStructuralBreakPipeline:
    """Enhanced Structural Break Detection Pipeline - Standalone Version"""
    
    def __init__(self, n_jobs: int = -1, use_full_features: bool = True,
                 add_ratio_features: bool = True, add_truncation_features: bool = True):
        """Initialize enhanced structural break pipeline"""
        self.n_jobs = n_jobs
        self.use_full_features = use_full_features
        self.add_ratio_features = add_ratio_features
        self.add_truncation_features = add_truncation_features
        
        # Initialize enhancers
        if add_ratio_features:
            self.ratio_enhancer = StreamlinedRatioEnhancer()
        else:
            self.ratio_enhancer = None
        
        if add_truncation_features:
            self.truncation_extractor = TruncationFeaturesExtractor()
        else:
            self.truncation_extractor = None
        
        print("🚀 Enhanced Structural Break Pipeline Initialized (Standalone)")
        print(f"   - Streamlined ratio features: {'✅' if add_ratio_features else '❌'}")
        print(f"   - Truncation features: {'✅' if add_truncation_features else '❌'}")
        print(f"   - Parallel jobs: {n_jobs}")
    
    def extract_features_batch(self, time_series_list: List[List[float]], 
                             tstar_list: Optional[List[int]] = None) -> Dict[str, List[float]]:
        """Extract enhanced features for structural break detection"""
        print("🔧 Extracting enhanced features for structural break detection...")
        
        if tstar_list is None:
            tstar_list = [len(series) // 2 for series in time_series_list]
        
        # Create basic features
        print("   🔧 Creating basic statistical features...")
        basic_features = self._create_basic_features(time_series_list, tstar_list)
        print(f"   ✅ Basic features: {len(basic_features)} feature types")
        
        enhanced_features = basic_features.copy()
        
        # Add streamlined ratio features
        if self.add_ratio_features and self.ratio_enhancer:
            print("   🔧 Adding streamlined ratio features...")
            enhanced_features = self._add_ratio_features_batch(enhanced_features)
            ratio_count = len([k for k in enhanced_features.keys() if k.startswith(('ratio_', 'log_ratio_', 'symmetric_ratio_'))])
            print(f"   ✅ Ratio features added: {ratio_count}")
        
        # Add truncation features
        if self.add_truncation_features and self.truncation_extractor:
            print("   🔧 Adding truncation-focused features...")
            enhanced_features = self._add_truncation_features_batch(enhanced_features, time_series_list, tstar_list)
            truncation_count = len([k for k in enhanced_features.keys() if any(pattern in k for pattern in 
                ['precision', 'truncation', 'round', 'cluster', 'bounds', 'range', 'flatness', 'alignment', 'asymmetry'])])
            print(f"   ✅ Truncation features added: {truncation_count}")
        
        print(f"   ✅ Final enhanced features: {len(enhanced_features)} feature types")
        
        return enhanced_features
    
    def _create_basic_features(self, time_series_list: List[List[float]], 
                             tstar_list: List[int]) -> Dict[str, List[float]]:
        """Create basic statistical features"""
        features = {
            'mean_pre': [], 'mean_post': [],
            'std_pre': [], 'std_post': [],
            'min_pre': [], 'min_post': [],
            'max_pre': [], 'max_post': [],
            'q25_pre': [], 'q25_post': [],
            'q50_pre': [], 'q50_post': [],
            'q75_pre': [], 'q75_post': []
        }
        
        for series, tstar in zip(time_series_list, tstar_list):
            pre_series = series[:tstar]
            post_series = series[tstar:]
            
            if len(pre_series) == 0 or len(post_series) == 0:
                # Add NaN values for invalid splits
                for key in features.keys():
                    features[key].append(float('nan'))
                continue
            
            # Calculate basic statistics
            def safe_mean(values):
                return sum(values) / len(values) if values else float('nan')
            
            def safe_std(values):
                if len(values) < 2:
                    return float('nan')
                mean_val = safe_mean(values)
                variance = sum((x - mean_val) ** 2 for x in values) / (len(values) - 1)
                return math.sqrt(variance)
            
            def safe_percentile(values, p):
                if not values:
                    return float('nan')
                sorted_vals = sorted(values)
                k = (len(sorted_vals) - 1) * p / 100
                f = math.floor(k)
                c = math.ceil(k)
                if f == c:
                    return sorted_vals[int(k)]
                return sorted_vals[int(f)] * (c - k) + sorted_vals[int(c)] * (k - f)
            
            # Pre-period features
            features['mean_pre'].append(safe_mean(pre_series))
            features['std_pre'].append(safe_std(pre_series))
            features['min_pre'].append(min(pre_series) if pre_series else float('nan'))
            features['max_pre'].append(max(pre_series) if pre_series else float('nan'))
            features['q25_pre'].append(safe_percentile(pre_series, 25))
            features['q50_pre'].append(safe_percentile(pre_series, 50))
            features['q75_pre'].append(safe_percentile(pre_series, 75))
            
            # Post-period features
            features['mean_post'].append(safe_mean(post_series))
            features['std_post'].append(safe_std(post_series))
            features['min_post'].append(min(post_series) if post_series else float('nan'))
            features['max_post'].append(max(post_series) if post_series else float('nan'))
            features['q25_post'].append(safe_percentile(post_series, 25))
            features['q50_post'].append(safe_percentile(post_series, 50))
            features['q75_post'].append(safe_percentile(post_series, 75))
        
        return features
    
    def _add_ratio_features_batch(self, features_dict: Dict[str, List[float]]) -> Dict[str, List[float]]:
        """Add ratio features to batch of samples"""
        enhanced_features = features_dict.copy()
        
        # Get number of samples
        n_samples = len(next(iter(features_dict.values())))
        
        # Process each sample
        for i in range(n_samples):
            # Extract features for this sample
            sample_features = {key: values[i] for key, values in features_dict.items()}
            
            # Add ratio features for this sample
            enhanced_sample = self.ratio_enhancer.add_streamlined_ratio_features(sample_features)
            
            # Add new features to batch
            for key, value in enhanced_sample.items():
                if key not in enhanced_features:
                    enhanced_features[key] = [float('nan')] * n_samples
                enhanced_features[key][i] = value
        
        return enhanced_features
    
    def _add_truncation_features_batch(self, features_dict: Dict[str, List[float]], 
                                     time_series_list: List[List[float]], 
                                     tstar_list: List[int]) -> Dict[str, List[float]]:
        """Add truncation features to batch of samples"""
        enhanced_features = features_dict.copy()
        
        # Process each sample
        for i, (series, tstar) in enumerate(zip(time_series_list, tstar_list)):
            pre_series = series[:tstar]
            post_series = series[tstar:]
            
            if len(pre_series) == 0 or len(post_series) == 0:
                continue
            
            # Extract truncation features for this sample
            truncation_features = self.truncation_extractor.extract_truncation_features(
                pre_series, post_series, sample_id=i
            )
            
            # Remove sample_id if present
            if 'sample_id' in truncation_features:
                del truncation_features['sample_id']
            
            # Add to batch
            for key, value in truncation_features.items():
                if key not in enhanced_features:
                    enhanced_features[key] = [float('nan')] * len(time_series_list)
                enhanced_features[key][i] = value
        
        return enhanced_features
    
    def get_feature_summary(self, time_series_list: List[List[float]], 
                          tstar_list: Optional[List[int]] = None) -> Dict[str, Any]:
        """Get comprehensive feature summary"""
        
        # Extract features from sample
        sample_size = min(3, len(time_series_list))
        sample_series = time_series_list[:sample_size]
        sample_tstar = tstar_list[:sample_size] if tstar_list else None
        
        features_dict = self.extract_features_batch(sample_series, sample_tstar)
        feature_names = list(features_dict.keys())
        
        # Categorize features
        feature_categories = {
            'original_features': [],
            'ratio_features': [],
            'log_ratio_features': [],
            'symmetric_ratio_features': [],
            'truncation_features': []
        }
        
        truncation_patterns = [
            'precision_', 'decimal_truncation', 'round_number', 'suspicious_patterns',
            'cluster_', 'symmetric_bounds', 'range', 'tail_flatness',
            'alignment', 'truncation_asymmetry', 'consistent_round'
        ]
        
        for name in feature_names:
            if name.startswith('ratio_') and not name.startswith(('log_ratio_', 'symmetric_ratio_')):
                feature_categories['ratio_features'].append(name)
            elif name.startswith('log_ratio_'):
                feature_categories['log_ratio_features'].append(name)
            elif name.startswith('symmetric_ratio_'):
                feature_categories['symmetric_ratio_features'].append(name)
            elif any(pattern in name for pattern in truncation_patterns):
                feature_categories['truncation_features'].append(name)
            else:
                feature_categories['original_features'].append(name)
        
        # Calculate statistics
        total_original = len(feature_categories['original_features'])
        total_ratio = len(feature_categories['ratio_features'])
        total_log_ratio = len(feature_categories['log_ratio_features'])
        total_symmetric_ratio = len(feature_categories['symmetric_ratio_features'])
        total_truncation = len(feature_categories['truncation_features'])
        total_features = len(feature_names)
        
        summary = {
            'total_features': total_features,
            'original_features': total_original,
            'streamlined_ratio_features': {
                'standard_ratios': total_ratio,
                'log_ratios': total_log_ratio,
                'symmetric_ratios': total_symmetric_ratio,
                'total': total_ratio + total_log_ratio + total_symmetric_ratio
            },
            'truncation_features': total_truncation,
            'enhancement_factor': total_features / max(total_original, 1),
            'feature_breakdown': {
                'original_pct': total_original / total_features * 100,
                'ratio_pct': (total_ratio + total_log_ratio + total_symmetric_ratio) / total_features * 100,
                'truncation_pct': total_truncation / total_features * 100
            },
            'sample_features': {
                'ratio_examples': feature_categories['ratio_features'][:3],
                'log_ratio_examples': feature_categories['log_ratio_features'][:3],
                'symmetric_ratio_examples': feature_categories['symmetric_ratio_features'][:3],
                'truncation_examples': feature_categories['truncation_features'][:5]
            },
            'enhancements_enabled': {
                'ratio_features': self.add_ratio_features,
                'truncation_features': self.add_truncation_features
            }
        }
        
        return summary
    
    def demonstrate_pipeline(self, n_demo_series: int = 3, series_length: int = 100):
        """Demonstrate the enhanced pipeline capabilities"""
        print("=" * 80)
        print("🚀 ENHANCED STRUCTURAL BREAK PIPELINE DEMONSTRATION")
        print("=" * 80)
        
        # Generate demo data (simple random data)
        print("📊 Generating demonstration time series...")
        
        import random
        random.seed(42)
        
        demo_series = []
        demo_tstars = []
        
        for i in range(n_demo_series):
            # Create series with structural break
            tstar = random.randint(series_length // 3, 2 * series_length // 3)
            
            # Pre-break: normal distribution simulation
            pre_series = [random.gauss(0, 1) for _ in range(tstar)]
            
            # Post-break: shifted mean and variance
            post_series = [random.gauss(1.5, 1.2) for _ in range(series_length - tstar)]
            
            series = pre_series + post_series
            
            demo_series.append(series)
            demo_tstars.append(tstar)
        
        print(f"   ✅ Generated {len(demo_series)} time series")
        print(f"   📊 Average length: {sum(len(s) for s in demo_series) / len(demo_series):.1f}")
        print(f"   📊 Average break point: {sum(demo_tstars) / len(demo_tstars):.1f}")
        
        # Extract features
        print("\n🔧 Extracting enhanced features...")
        start_time = time.perf_counter()
        
        features_dict = self.extract_features_batch(demo_series, demo_tstars)
        
        processing_time = time.perf_counter() - start_time
        
        print(f"   ✅ Feature extraction completed in {processing_time:.2f}s")
        
        # Get feature summary
        print("\n📋 Analyzing feature composition...")
        summary = self.get_feature_summary(demo_series, demo_tstars)
        
        print("\n📊 FEATURE SUMMARY:")
        print(f"   Total features: {summary['total_features']}")
        print(f"   Original features: {summary['original_features']} ({summary['feature_breakdown']['original_pct']:.1f}%)")
        print(f"   Streamlined ratio features: {summary['streamlined_ratio_features']['total']} ({summary['feature_breakdown']['ratio_pct']:.1f}%)")
        print(f"     - Standard ratios: {summary['streamlined_ratio_features']['standard_ratios']}")
        print(f"     - Log ratios: {summary['streamlined_ratio_features']['log_ratios']}")
        print(f"     - Symmetric ratios: {summary['streamlined_ratio_features']['symmetric_ratios']}")
        print(f"   Truncation features: {summary['truncation_features']} ({summary['feature_breakdown']['truncation_pct']:.1f}%)")
        print(f"   Enhancement factor: {summary['enhancement_factor']:.2f}x")
        
        # Show sample features
        print("\n🔍 SAMPLE FEATURES:")
        
        if summary['sample_features']['ratio_examples']:
            print("   Standard Ratio Features:")
            for i, feature in enumerate(summary['sample_features']['ratio_examples'], 1):
                value = features_dict[feature][0]
                print(f"     {i}. {feature}: {value:.4f}")
        
        if summary['sample_features']['truncation_examples']:
            print("   Truncation Features:")
            for i, feature in enumerate(summary['sample_features']['truncation_examples'], 1):
                value = features_dict[feature][0]
                print(f"     {i}. {feature}: {value:.4f}")
        
        print("\n✅ Pipeline demonstration completed successfully!")
        
        return {
            'features_dict': features_dict,
            'summary': summary,
            'processing_time': processing_time
        }

def create_production_pipeline(n_jobs: int = -1, max_features: int = 5000,
                             add_ratio_features: bool = True, 
                             add_truncation_features: bool = True) -> EnhancedStructuralBreakPipeline:
    """Create production-ready enhanced structural break pipeline"""
    pipeline = EnhancedStructuralBreakPipeline(
        n_jobs=n_jobs,
        use_full_features=True,
        add_ratio_features=add_ratio_features,
        add_truncation_features=add_truncation_features
    )
    
    print(f"🏭 Production pipeline created (max {max_features} features for future selection)")
    
    return pipeline

def get_recommended_configuration() -> Dict[str, bool]:
    """Get recommended configuration for structural break detection"""
    return {
        'add_ratio_features': True,      # Streamlined ratios (standard, log, symmetric)
        'add_truncation_features': True  # Data quality patterns
    }

if __name__ == "__main__":
    # Demonstrate the enhanced pipeline
    print("=" * 80)
    print("🚀 ENHANCED STRUCTURAL BREAK DETECTION PIPELINE - STANDALONE")
    print("=" * 80)
    
    try:
        # Create pipeline with recommended configuration
        config = get_recommended_configuration()
        pipeline = create_production_pipeline(**config)
        
        # Run demonstration
        demo_results = pipeline.demonstrate_pipeline()
        
        print("\n" + "=" * 80)
        print("🎯 PIPELINE READY FOR PRODUCTION")
        print("=" * 80)
        print("✅ Streamlined ratio features (3 types: standard, log, symmetric)")
        print("✅ Truncation-focused features (6 categories)")
        print("✅ Full compatibility with existing workflows")
        print("✅ Optimized for structural break detection")
        print("✅ Removed redundant ratio types (inverse, absolute)")
        
        print("\n📝 USAGE EXAMPLE:")
        print("```python")
        print("from enhanced_structural_break_pipeline_standalone import create_production_pipeline")
        print("")
        print("# Create enhanced pipeline")
        print("pipeline = create_production_pipeline(")
        print("    n_jobs=-1,")
        print("    add_ratio_features=True,")
        print("    add_truncation_features=True")
        print(")")
        print("")
        print("# Extract enhanced features")
        print("features_dict = pipeline.extract_features_batch(time_series_list, tstar_list)")
        print("")
        print("# Get feature summary")
        print("summary = pipeline.get_feature_summary(time_series_list, tstar_list)")
        print("```")
        
        enhancement_stats = demo_results['summary']
        print(f"\n🎊 Enhancement completed! Added {enhancement_stats['streamlined_ratio_features']['total']} ratio features and {enhancement_stats['truncation_features']} truncation features.")
        
    except Exception as e:
        print(f"❌ Error during demonstration: {e}")
        print("   This is a standalone version for demonstration purposes.")
        print("   For production use, integrate with your existing pipeline infrastructure.")