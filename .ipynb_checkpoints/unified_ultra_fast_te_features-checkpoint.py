#!/usr/bin/env python3
"""
Unified Ultra-Fast Transfer Entropy Feature Extractor
Combines both ultra-fast TE implementations with options to use either or both approaches
"""

import numpy as np
import pandas as pd
from collections import defaultdict
import time
from typing import Dict, Any, Optional, List, Tuple
import warnings

# Try to import Numba for JIT compilation
try:
    from numba import jit, prange
    NUMBA_AVAILABLE = True
except ImportError:
    NUMBA_AVAILABLE = False
    print("⚠️  Numba not available. JIT-optimized methods will be disabled.")

warnings.filterwarnings('ignore')

# Numba-optimized functions (Method 2)
if NUMBA_AVAILABLE:
    @jit(nopython=True, cache=True)
    def fast_embedding_numba(series: np.ndarray, dim: int, tau: int = 1) -> np.ndarray:
        """Ultra-fast time-delay embedding using Numba JIT compilation - CORRECTED for causality"""
        n = len(series)
        if dim == 1:
            return series[:-1].reshape(-1, 1)  # Simple case: just past values
        
        # Calculate output length - need to account for looking backward
        out_len = n - (dim - 1) * tau
        if out_len <= 0:
            return np.zeros((0, dim))
        
        # Pre-allocate output array
        embedded = np.zeros((out_len, dim))
        
        # CORRECTED: Backward embedding for past dependencies [x_t, x_{t-τ}, x_{t-2τ}, ...]
        for i in range(out_len):
            for j in range(dim):
                # Start from (dim-1)*tau to ensure we can look backward
                embedded[i, j] = series[i + (dim - 1) * tau - j * tau]
        
        return embedded

    @jit(nopython=True, cache=True)
    def fast_discretize_uniform(data: np.ndarray, n_bins: int) -> np.ndarray:
        """Fast uniform discretization using Numba"""
        if len(data) == 0:
            return np.zeros(0, dtype=np.int32)
        
        data_min, data_max = np.min(data), np.max(data)
        
        # Handle constant data
        if data_max - data_min < 1e-10:
            return np.zeros(len(data), dtype=np.int32)
        
        # Compute bin indices
        bin_width = (data_max - data_min) / n_bins
        indices = ((data - data_min) / bin_width).astype(np.int32)
        
        # Handle edge case where data equals maximum
        indices = np.minimum(indices, np.int32(n_bins - 1))
        
        return indices

    @jit(nopython=True, cache=True)
    def compute_joint_counts_3d(x_future: np.ndarray, x_past: np.ndarray, 
                               y_past: np.ndarray, n_bins: int) -> np.ndarray:
        """Efficiently compute 3D joint counts using Numba"""
        n_samples = len(x_future)
        max_x_past = np.max(x_past) if len(x_past) > 0 else 0
        max_y_past = np.max(y_past) if len(y_past) > 0 else 0
        
        # Determine array dimensions
        dim_x_future = n_bins
        dim_x_past = max_x_past + 1
        dim_y_past = max_y_past + 1
        
        # Pre-allocate count array
        counts = np.zeros((dim_x_future, dim_x_past, dim_y_past), dtype=np.int32)
        
        # Count occurrences
        for i in range(n_samples):
            if (x_future[i] < dim_x_future and 
                x_past[i] < dim_x_past and 
                y_past[i] < dim_y_past):
                counts[x_future[i], x_past[i], y_past[i]] += 1
        
        return counts

    @jit(nopython=True, cache=True)
    def compute_te_from_counts(joint_counts: np.ndarray) -> float:
        """Compute Transfer Entropy from 3D joint counts using Numba"""
        n_total = np.sum(joint_counts)
        if n_total == 0:
            return 0.0
        
        te = 0.0
        dim_x_future, dim_x_past, dim_y_past = joint_counts.shape
        
        # Compute marginal counts
        marginal_xy = np.zeros((dim_x_future, dim_x_past))  # N(x_{t+1}, x_t)
        marginal_xz = np.zeros((dim_x_past, dim_y_past))    # N(x_t, y_t)
        marginal_x = np.zeros(dim_x_past)                   # N(x_t)
        
        for i in range(dim_x_future):
            for j in range(dim_x_past):
                for k in range(dim_y_past):
                    count = joint_counts[i, j, k]
                    marginal_xy[i, j] += count
                    marginal_xz[j, k] += count
                    marginal_x[j] += count
        
        # Compute TE
        for i in range(dim_x_future):
            for j in range(dim_x_past):
                for k in range(dim_y_past):
                    count_joint = joint_counts[i, j, k]
                    if count_joint == 0:
                        continue
                    
                    count_xy = marginal_xy[i, j]
                    count_xz = marginal_xz[j, k]
                    count_x = marginal_x[j]
                    
                    if count_xz > 0 and count_x > 0:
                        p_joint = count_joint / n_total
                        p_cond_xyz = count_joint / count_xz
                        p_cond_xy = count_xy / count_x
                        
                        if p_cond_xyz > 0 and p_cond_xy > 0:
                            log_ratio = np.log2(p_cond_xyz / p_cond_xy)
                            te += p_joint * log_ratio
        
        return max(te, 0.0)

else:
    # Dummy functions if Numba is not available
    def fast_embedding_numba(*args, **kwargs):
        raise ImportError("Numba not available")
    
    def fast_discretize_uniform(*args, **kwargs):
        raise ImportError("Numba not available")
    
    def compute_joint_counts_3d(*args, **kwargs):
        raise ImportError("Numba not available")
    
    def compute_te_from_counts(*args, **kwargs):
        raise ImportError("Numba not available")

class UnifiedUltraFastTEFeatures:
    """
    Unified Transfer Entropy feature extractor with multiple ultra-fast implementations.
    
    Supports:
    1. Method 1: Ultra-minimal TE (fastest, basic features)
    2. Method 2: Enhanced sequential TE (comprehensive, slower)
    3. Both methods combined (maximum feature set)
    """
    
    def __init__(self, 
                 # Method selection
                 use_minimal_te: bool = True,
                 use_sequential_te: bool = True,
                 
                 # Common parameters
                 bins: int = 5,
                 min_samples: int = 10,
                 cache_results: bool = True,
                 verbose: bool = True,
                 
                 # Sequential TE specific parameters
                 enable_cross_transition: bool = True,
                 enable_sliding_window: bool = False,
                 window_size: Optional[int] = None):
        """
        Initialize Unified Ultra-Fast TE Feature Extractor
        
        Args:
            use_minimal_te: Use ultra-minimal TE implementation (fastest)
            use_sequential_te: Use sequential-aware TE implementation
            bins: Number of bins for discretization (4 is optimal for speed)
            min_samples: Minimum samples required (low for speed)
            cache_results: Cache TE computations
            verbose: Print progress information
            enable_cross_transition: Enable cross-transition analysis (sequential only)
            enable_sliding_window: Enable sliding window analysis (sequential only, slower)
            window_size: Window size for sliding analysis
        """
        self.use_minimal_te = use_minimal_te
        self.use_sequential_te = use_sequential_te
        self.bins = bins
        self.min_samples = min_samples
        self.cache_results = cache_results
        self.verbose = verbose
        self.enable_cross_transition = enable_cross_transition
        self.enable_sliding_window = enable_sliding_window
        self.window_size = window_size
        
        # Validate method selection
        if not use_minimal_te and not use_sequential_te:
            raise ValueError("At least one TE method must be enabled")
        
        # Cache for TE computations
        self._te_cache = {} if cache_results else None
        
        if verbose:
            print("🚀 Unified Ultra-Fast TE Feature Extractor Initialized")
            print(f"   - Methods: Minimal={use_minimal_te}, Sequential={use_sequential_te}")
            print(f"   - Numba JIT available: {NUMBA_AVAILABLE}")
            print(f"   - Bins: {bins}, Min samples: {min_samples}")
            print(f"   - Cross-transition: {enable_cross_transition}")
            print(f"   - Sliding window: {enable_sliding_window}")
            print(f"   - Caching: {cache_results}")
    
    def _hash_arrays(self, x: np.ndarray, y: np.ndarray, method: str = '') -> str:
        """Create hash for array pair caching"""
        if not self.cache_results:
            return None
        return f"{method}_{hash(x.tobytes())}_{hash(y.tobytes())}"
    
    def _compute_te_minimal(self, x: np.ndarray, y: np.ndarray) -> float:
        """
        Ultra-minimal TE computation (Method 1)
        Maximum speed optimization with minimal features
        
        Args:
            x: Target series
            y: Source series
            
        Returns:
            TE value (Y -> X)
        """
        # Check cache first
        if self.cache_results:
            cache_key = self._hash_arrays(x, y, 'minimal')
            if cache_key and cache_key in self._te_cache:
                return self._te_cache[cache_key]
        
        # Ultra-fast validation
        if len(x) != len(y) or len(x) < self.min_samples:
            return 0.0
        
        # Remove only obvious bad values
        mask = np.isfinite(x) & np.isfinite(y)
        if np.sum(mask) < self.min_samples:
            return 0.0
        
        x, y = x[mask], y[mask]
        n = len(x)
        
        if n < self.min_samples:
            return 0.0
        
        # Minimal embedding (k=1, l=1, tau=1)
        x_past = x[:-1]
        y_past = y[:-1]
        x_future = x[1:]
        
        # Ultra-fast binning using numpy percentiles
        def ultra_fast_bin(data, n_bins):
            if np.std(data) < 1e-10:
                return np.zeros_like(data, dtype=int)
            
            percentiles = np.linspace(0, 100, n_bins + 1)
            bin_edges = np.percentile(data, percentiles)
            
            if len(np.unique(bin_edges)) < 2:
                return np.zeros_like(data, dtype=int)
            
            return np.digitize(data, bin_edges[1:-1])
        
        # Apply binning
        x_future_bin = ultra_fast_bin(x_future, self.bins)
        x_past_bin = ultra_fast_bin(x_past, self.bins)
        y_past_bin = ultra_fast_bin(y_past, self.bins)
        
        # Count using numpy operations
        n_samples = len(x_future_bin)
        
        # Create joint state combinations
        joint_states = x_future_bin * (self.bins**2) + x_past_bin * self.bins + y_past_bin
        xy_states = x_future_bin * self.bins + x_past_bin
        xz_states = x_past_bin * self.bins + y_past_bin
        x_states = x_past_bin
        
        # Count unique states efficiently
        joint_unique, joint_counts = np.unique(joint_states, return_counts=True)
        xy_unique, xy_counts = np.unique(xy_states, return_counts=True)
        xz_unique, xz_counts = np.unique(xz_states, return_counts=True)
        x_unique, x_counts = np.unique(x_states, return_counts=True)
        
        # Create count dictionaries
        joint_dict = dict(zip(joint_unique, joint_counts))
        xy_dict = dict(zip(xy_unique, xy_counts))
        xz_dict = dict(zip(xz_unique, xz_counts))
        x_dict = dict(zip(x_unique, x_counts))
        
        # Compute TE with vectorized operations
        te = 0.0
        
        for joint_state, count_xyz in joint_dict.items():
            # Decode joint state
            x_fut = joint_state // (self.bins**2)
            remainder = joint_state % (self.bins**2)
            x_past_val = remainder // self.bins
            y_past_val = remainder % self.bins
            
            # Get marginal counts
            xy_state = x_fut * self.bins + x_past_val
            xz_state = x_past_val * self.bins + y_past_val
            
            count_xy = xy_dict.get(xy_state, 0)
            count_xz = xz_dict.get(xz_state, 0)
            count_x = x_dict.get(x_past_val, 0)
            
            if count_xz > 0 and count_x > 0:
                # Probabilities
                p_xyz = count_xyz / n_samples
                p_xyz_given_xz = count_xyz / count_xz
                p_xy_given_x = count_xy / count_x
                
                if p_xyz_given_xz > 0 and p_xy_given_x > 0:
                    te += p_xyz * np.log2(p_xyz_given_xz / p_xy_given_x)
        
        te = max(te, 0.0)
        
        # Cache result
        if self.cache_results and cache_key:
            self._te_cache[cache_key] = te
        
        return te
    
    def _compute_te_numba(self, x: np.ndarray, y: np.ndarray, k: int = 1, l: int = 1) -> float:
        """
        Numba-optimized TE computation (Method 2)
        Uses JIT-compiled functions for maximum performance
        
        Args:
            x: Target series
            y: Source series
            k: History length for x
            l: History length for y
            
        Returns:
            TE value (Y -> X)
        """
        if not NUMBA_AVAILABLE:
            # Fallback to minimal method if Numba not available
            return self._compute_te_minimal(x, y)
        
        # Check cache first
        if self.cache_results:
            cache_key = self._hash_arrays(x, y, f'numba_{k}_{l}')
            if cache_key and cache_key in self._te_cache:
                return self._te_cache[cache_key]
        
        # Input validation and preprocessing
        x = np.asarray(x, dtype=np.float64)
        y = np.asarray(y, dtype=np.float64)
        
        # Align series
        min_len = min(len(x), len(y))
        x, y = x[:min_len], y[:min_len]
        
        # Remove invalid values efficiently
        valid_mask = np.isfinite(x) & np.isfinite(y)
        x, y = x[valid_mask], y[valid_mask]
        n = len(x)
        max_lag = max(k, l)
        
        if n < max_lag + self.min_samples:
            return 0.0
        
        # Fast embedding generation using Numba
        x_past = fast_embedding_numba(x[:-1], k, 1)
        y_past = fast_embedding_numba(y[:-1], l, 1)
        x_future = x[max_lag:]
        
        # Align arrays
        min_len = min(len(x_past), len(y_past), len(x_future))
        if min_len < self.min_samples:
            return 0.0
        
        x_past = x_past[-min_len:]
        y_past = y_past[-min_len:]
        x_future = x_future[-min_len:]
        
        # Optimized discretization using Numba
        x_future_disc = fast_discretize_uniform(x_future, self.bins)
        
        # Discretize multi-dimensional embeddings
        x_past_disc = np.zeros((x_past.shape[0],), dtype=np.int32)
        y_past_disc = np.zeros((y_past.shape[0],), dtype=np.int32)
        
        if k == 1:
            x_past_disc = fast_discretize_uniform(x_past.ravel(), self.bins)
        else:
            # For multi-dimensional embeddings, discretize each dimension and combine
            for i in range(k):
                col_disc = fast_discretize_uniform(x_past[:, i], self.bins)
                x_past_disc += col_disc * (self.bins ** i)
        
        if l == 1:
            y_past_disc = fast_discretize_uniform(y_past.ravel(), self.bins)
        else:
            # For multi-dimensional embeddings, discretize each dimension and combine
            for i in range(l):
                col_disc = fast_discretize_uniform(y_past[:, i], self.bins)
                y_past_disc += col_disc * (self.bins ** i)
        
        # Compute joint counts using optimized Numba function
        joint_counts = compute_joint_counts_3d(x_future_disc, x_past_disc, y_past_disc, self.bins)
        
        # Compute TE from counts using Numba
        te = compute_te_from_counts(joint_counts)
        
        # Cache result
        if self.cache_results and cache_key:
            self._te_cache[cache_key] = te
        
        return te
    
    def _compute_te_sequential(self, x: np.ndarray, y: np.ndarray, 
                              transition_point: Optional[int] = None) -> Dict[str, float]:
        """
        Sequential-aware TE computation (Method 2)
        More comprehensive analysis with segment awareness
        
        Args:
            x, y: Time series
            transition_point: Optional transition point for segment analysis
            
        Returns:
            Dictionary with comprehensive TE results
        """
        # Check cache first
        if self.cache_results:
            cache_key = self._hash_arrays(x, y, f'sequential_{transition_point}')
            if cache_key and cache_key in self._te_cache:
                return self._te_cache[cache_key]
        
        results = {}
        
        # Use Numba-optimized TE if available, otherwise fall back to minimal
        if NUMBA_AVAILABLE:
            te_x_to_y = self._compute_te_numba(y, x, k=1, l=1)  # X influences Y
            te_y_to_x = self._compute_te_numba(x, y, k=1, l=1)  # Y influences X
        else:
            te_x_to_y = self._compute_te_minimal(y, x)  # X influences Y
            te_y_to_x = self._compute_te_minimal(x, y)  # Y influences X
        
        # Derived measures
        net_flow = te_x_to_y - te_y_to_x
        total_coupling = te_x_to_y + te_y_to_x
        asymmetry = abs(net_flow) / (total_coupling + 1e-10) if total_coupling > 0 else 0.0
        
        results.update({
            'te_x_to_y': te_x_to_y,
            'te_y_to_x': te_y_to_x,
            'te_net_flow': net_flow,
            'te_total_coupling': total_coupling,
            'te_asymmetry': asymmetry
        })
        
        # Segment-wise analysis if transition point provided
        if transition_point and 0 < transition_point < len(x):
            # Normal segment
            x_normal = x[:transition_point]
            y_normal = y[:transition_point]
            
            if len(x_normal) >= self.min_samples:
                te_normal_x_to_y = self._compute_te_minimal(y_normal, x_normal)
                te_normal_y_to_x = self._compute_te_minimal(x_normal, y_normal)
                results.update({
                    'normal_te_x_to_y': te_normal_x_to_y,
                    'normal_te_y_to_x': te_normal_y_to_x,
                    'normal_te_net_flow': te_normal_x_to_y - te_normal_y_to_x
                })
            
            # Candidate segment
            x_candidate = x[transition_point:]
            y_candidate = y[transition_point:]
            
            if len(x_candidate) >= self.min_samples:
                te_candidate_x_to_y = self._compute_te_minimal(y_candidate, x_candidate)
                te_candidate_y_to_x = self._compute_te_minimal(x_candidate, y_candidate)
                results.update({
                    'candidate_te_x_to_y': te_candidate_x_to_y,
                    'candidate_te_y_to_x': te_candidate_y_to_x,
                    'candidate_te_net_flow': te_candidate_x_to_y - te_candidate_y_to_x
                })
                
                # Segment comparison
                if 'normal_te_net_flow' in results:
                    normal_net = results['normal_te_net_flow']
                    candidate_net = results['candidate_te_net_flow']
                    results.update({
                        'te_change': candidate_net - normal_net,
                        'te_change_abs': abs(candidate_net - normal_net),
                        'te_ratio': candidate_net / (normal_net + 1e-10) if abs(normal_net) > 1e-10 else 0.0
                    })
        
        # Cross-transition analysis
        if (self.enable_cross_transition and transition_point and 
            0 < transition_point < len(x)):
            
            pre_buffer = min(20, transition_point // 3)
            post_buffer = min(20, (len(x) - transition_point) // 3)
            
            if (transition_point >= pre_buffer and 
                len(x) - transition_point >= post_buffer):
                
                x_pre = x[transition_point - pre_buffer:transition_point]
                y_pre = y[transition_point - pre_buffer:transition_point]
                x_post = x[transition_point:transition_point + post_buffer]
                y_post = y[transition_point:transition_point + post_buffer]
                
                # Cross-transition TE
                x_combined = np.concatenate([x_pre, x_post])
                y_combined = np.concatenate([y_pre, y_post])
                
                cross_te_x_to_y = self._compute_te_minimal(y_combined, x_combined)
                cross_te_y_to_x = self._compute_te_minimal(x_combined, y_combined)
                
                results.update({
                    'cross_te_x_to_y': cross_te_x_to_y,
                    'cross_te_y_to_x': cross_te_y_to_x,
                    'cross_te_net_flow': cross_te_x_to_y - cross_te_y_to_x
                })
        
        # Cache result
        if self.cache_results and cache_key:
            self._te_cache[cache_key] = results
        
        return results
    
    def extract_te_features(self, pre: np.ndarray, post: np.ndarray, whole: np.ndarray,
                           transition_point: Optional[int] = None,
                           feature_prefix: str = '') -> Dict[str, float]:
        """
        Extract meaningful TE features using within-segment and cross-transition analysis
        CORRECTED: No longer uses misleading pre/post TE ratios
        
        Args:
            pre: Pre-break segment
            post: Post-break segment
            whole: Whole time series
            transition_point: Transition point (if None, uses len(pre))
            feature_prefix: Prefix for feature names
            
        Returns:
            Dictionary of meaningful TE features
        """
        if transition_point is None:
            transition_point = len(pre)
        
        te_features = {}
        
        # Method 1: Minimal TE features - CORRECTED for meaningful analysis
        if self.use_minimal_te:
            
            # 1. Within-segment TE (MEANINGFUL): Internal predictability of each segment
            if len(pre) >= self.min_samples + 1:
                try:
                    # How well x_{t-1} predicts x_t within normal period
                    te_normal_internal = self._compute_te_minimal(pre[1:], pre[:-1])
                    te_features[f'{feature_prefix}minimal_te_normal_internal'] = te_normal_internal
                except Exception:
                    te_features[f'{feature_prefix}minimal_te_normal_internal'] = 0.0
            
            if len(post) >= self.min_samples + 1:
                try:
                    # How well x_{t-1} predicts x_t within candidate period
                    te_candidate_internal = self._compute_te_minimal(post[1:], post[:-1])
                    te_features[f'{feature_prefix}minimal_te_candidate_internal'] = te_candidate_internal
                except Exception:
                    te_features[f'{feature_prefix}minimal_te_candidate_internal'] = 0.0
            
            # 2. Dynamics change (MEANINGFUL): How predictability changes across break
            if (f'{feature_prefix}minimal_te_normal_internal' in te_features and 
                f'{feature_prefix}minimal_te_candidate_internal' in te_features):
                
                normal_te = te_features[f'{feature_prefix}minimal_te_normal_internal']
                candidate_te = te_features[f'{feature_prefix}minimal_te_candidate_internal']
                
                te_features[f'{feature_prefix}minimal_te_dynamics_change'] = candidate_te - normal_te
                te_features[f'{feature_prefix}minimal_te_dynamics_change_abs'] = abs(candidate_te - normal_te)
                
                # Ratio of predictability
                if abs(normal_te) > 1e-10:
                    te_features[f'{feature_prefix}minimal_te_predictability_ratio'] = candidate_te / normal_te
                else:
                    te_features[f'{feature_prefix}minimal_te_predictability_ratio'] = 0.0
            
            # 3. Cross-transition TE (MEANINGFUL): How pre-break state predicts post-break dynamics
            if len(pre) >= 10 and len(post) >= 10:
                try:
                    # Use small buffers around the transition
                    pre_buffer = min(10, len(pre) // 2)
                    post_buffer = min(10, len(post) // 2)
                    
                    pre_final = pre[-pre_buffer:]  # Final states before break
                    post_early = post[:post_buffer]  # Early states after break
                    
                    # How pre-break final state predicts post-break early dynamics
                    if len(pre_final) >= 2 and len(post_early) >= 2:
                        # Create meaningful cross-transition sequence
                        cross_sequence = np.concatenate([pre_final, post_early])
                        te_cross_transition = self._compute_te_minimal(cross_sequence[1:], cross_sequence[:-1])
                        te_features[f'{feature_prefix}minimal_te_cross_transition'] = te_cross_transition
                    else:
                        te_features[f'{feature_prefix}minimal_te_cross_transition'] = 0.0
                        
                except Exception:
                    te_features[f'{feature_prefix}minimal_te_cross_transition'] = 0.0
            
            # 4. Whole series internal TE (MEANINGFUL): Overall predictability
            if len(whole) >= self.min_samples + 1:
                try:
                    te_whole_internal = self._compute_te_minimal(whole[1:], whole[:-1])
                    te_features[f'{feature_prefix}minimal_te_whole_internal'] = te_whole_internal
                except Exception:
                    te_features[f'{feature_prefix}minimal_te_whole_internal'] = 0.0
        
        # Method 2: Sequential TE features - CORRECTED for meaningful analysis
        if self.use_sequential_te:
            if len(whole) >= 2 * self.min_samples:
                try:
                    # Use Numba-optimized method for within-segment analysis
                    if NUMBA_AVAILABLE:
                        # Within-segment TE using Numba
                        if len(pre) >= self.min_samples + 1:
                            te_normal_numba = self._compute_te_numba(pre[1:], pre[:-1], k=1, l=1)
                            te_features[f'{feature_prefix}sequential_te_normal_internal'] = te_normal_numba
                        
                        if len(post) >= self.min_samples + 1:
                            te_candidate_numba = self._compute_te_numba(post[1:], post[:-1], k=1, l=1)
                            te_features[f'{feature_prefix}sequential_te_candidate_internal'] = te_candidate_numba
                        
                        # Dynamics change using Numba results
                        if (f'{feature_prefix}sequential_te_normal_internal' in te_features and 
                            f'{feature_prefix}sequential_te_candidate_internal' in te_features):
                            
                            normal_numba = te_features[f'{feature_prefix}sequential_te_normal_internal']
                            candidate_numba = te_features[f'{feature_prefix}sequential_te_candidate_internal']
                            
                            te_features[f'{feature_prefix}sequential_te_dynamics_change'] = candidate_numba - normal_numba
                            te_features[f'{feature_prefix}sequential_te_dynamics_change_abs'] = abs(candidate_numba - normal_numba)
                            
                            if abs(normal_numba) > 1e-10:
                                te_features[f'{feature_prefix}sequential_te_predictability_ratio'] = candidate_numba / normal_numba
                            else:
                                te_features[f'{feature_prefix}sequential_te_predictability_ratio'] = 0.0
                        
                        # Whole series internal TE using Numba
                        te_whole_numba = self._compute_te_numba(whole[1:], whole[:-1], k=1, l=1)
                        te_features[f'{feature_prefix}sequential_te_whole_internal'] = te_whole_numba
                        
                        # Cross-transition using Numba
                        if len(pre) >= 10 and len(post) >= 10:
                            pre_buffer = min(10, len(pre) // 2)
                            post_buffer = min(10, len(post) // 2)
                            pre_final = pre[-pre_buffer:]
                            post_early = post[:post_buffer]
                            
                            if len(pre_final) >= 2 and len(post_early) >= 2:
                                cross_sequence = np.concatenate([pre_final, post_early])
                                te_cross_numba = self._compute_te_numba(cross_sequence[1:], cross_sequence[:-1], k=1, l=1)
                                te_features[f'{feature_prefix}sequential_te_cross_transition'] = te_cross_numba
                            else:
                                te_features[f'{feature_prefix}sequential_te_cross_transition'] = 0.0
                    
                    else:
                        # Fallback to minimal method if Numba not available
                        if len(pre) >= self.min_samples + 1:
                            te_normal_fallback = self._compute_te_minimal(pre[1:], pre[:-1])
                            te_features[f'{feature_prefix}sequential_te_normal_internal'] = te_normal_fallback
                        
                        if len(post) >= self.min_samples + 1:
                            te_candidate_fallback = self._compute_te_minimal(post[1:], post[:-1])
                            te_features[f'{feature_prefix}sequential_te_candidate_internal'] = te_candidate_fallback
                    
                except Exception:
                    # Add default meaningful features if analysis fails
                    te_features[f'{feature_prefix}sequential_te_normal_internal'] = 0.0
                    te_features[f'{feature_prefix}sequential_te_candidate_internal'] = 0.0
                    te_features[f'{feature_prefix}sequential_te_dynamics_change'] = 0.0
                    te_features[f'{feature_prefix}sequential_te_whole_internal'] = 0.0
                    te_features[f'{feature_prefix}sequential_te_cross_transition'] = 0.0
        
        return te_features
    
    def extract_features_batch(self, time_series_list: List[np.ndarray],
                              transition_points: Optional[List[int]] = None,
                              feature_prefix: str = '') -> pd.DataFrame:
        """
        Extract TE features for a batch of time series
        
        Args:
            time_series_list: List of time series arrays
            transition_points: List of transition points (optional)
            feature_prefix: Prefix for all feature names
            
        Returns:
            DataFrame with TE features
        """
        if self.verbose:
            print(f"🔧 Extracting TE features for {len(time_series_list)} time series...")
            print(f"   - Using methods: Minimal={self.use_minimal_te}, Sequential={self.use_sequential_te}")
        
        all_features = []
        
        for i, series in enumerate(time_series_list):
            if self.verbose and i % max(1, len(time_series_list) // 10) == 0:
                print(f"   Processing {i+1}/{len(time_series_list)} ({100*i/len(time_series_list):.1f}%)")
            
            # Determine transition point
            if transition_points:
                tstar = transition_points[i]
            else:
                tstar = len(series) // 2
            
            # Split series
            pre = series[:tstar]
            post = series[tstar:]
            whole = series
            
            # Extract TE features
            features = self.extract_te_features(
                pre=pre,
                post=post,
                whole=whole,
                transition_point=tstar,
                feature_prefix=feature_prefix
            )
            
            all_features.append(features)
        
        # Create DataFrame
        features_df = pd.DataFrame(all_features)
        
        if self.verbose:
            print(f"   ✅ Extracted {features_df.shape[1]} TE features")
            print(f"   📊 Feature breakdown:")
            
            if self.use_minimal_te:
                minimal_features = [col for col in features_df.columns if 'minimal_' in col]
                print(f"      - Minimal TE features: {len(minimal_features)}")
            
            if self.use_sequential_te:
                sequential_features = [col for col in features_df.columns if 'sequential_' in col]
                print(f"      - Sequential TE features: {len(sequential_features)}")
        
        return features_df
    
    def benchmark_performance(self, data_sizes: List[int] = [100, 200, 500, 1000]) -> Dict[str, Any]:
        """
        Benchmark TE feature extraction performance for both methods
        
        Args:
            data_sizes: List of data sizes to test
            
        Returns:
            Benchmark results
        """
        print("🔬 Benchmarking Unified TE Feature Extraction Performance...")
        print(f"   Methods enabled: Minimal={self.use_minimal_te}, Sequential={self.use_sequential_te}")
        
        benchmark_results = {
            'data_sizes': data_sizes,
            'computation_times': [],
            'features_extracted': [],
            'method_breakdown': []
        }
        
        for n in data_sizes:
            print(f"   Testing {n} samples...")
            
            # Generate test data with coupling
            np.random.seed(42)
            x = np.random.normal(0, 1, n)
            y = np.random.normal(0, 1, n)
            
            # Add coupling structure
            for t in range(1, n):
                if t < n // 2:
                    x[t] = 0.7 * x[t-1] + 0.1 * y[t-1] + 0.3 * np.random.normal()
                    y[t] = 0.8 * y[t-1] + 0.05 * x[t-1] + 0.2 * np.random.normal()
                else:
                    x[t] = 0.6 * x[t-1] + 0.2 * y[t-1] + 0.2 * np.random.normal()
                    y[t] = 0.5 * y[t-1] + 0.3 * x[t-1] + 0.15 * np.random.normal()
            
            # Time feature extraction
            start_time = time.time()
            
            features = self.extract_te_features(
                pre=x[:n//2],
                post=x[n//2:],
                whole=x,
                transition_point=n//2
            )
            
            computation_time = time.time() - start_time
            
            # Count features by method
            minimal_count = len([k for k in features.keys() if 'minimal_' in k])
            sequential_count = len([k for k in features.keys() if 'sequential_' in k])
            
            benchmark_results['computation_times'].append(computation_time)
            benchmark_results['features_extracted'].append(len(features))
            benchmark_results['method_breakdown'].append({
                'minimal': minimal_count,
                'sequential': sequential_count
            })
            
            print(f"      Time: {computation_time:.4f}s, Features: {len(features)} "
                  f"(Minimal: {minimal_count}, Sequential: {sequential_count})")
        
        # Performance summary
        print(f"\n📊 Performance Summary:")
        for i, (size, time_taken, n_features, breakdown) in enumerate(zip(
            benchmark_results['data_sizes'], 
            benchmark_results['computation_times'],
            benchmark_results['features_extracted'],
            benchmark_results['method_breakdown']
        )):
            rate = size / time_taken
            print(f"   {size:4d} samples: {time_taken:7.4f}s ({rate:8.1f} samples/s, "
                  f"{n_features:2d} features)")
        
        max_time = max(benchmark_results['computation_times'])
        if max_time < 0.01:
            print(f"\n🚀 BLAZING FAST: All tests under 0.01s")
        elif max_time < 0.1:
            print(f"\n⚡ VERY FAST: All tests under 0.1s")
        elif max_time < 1.0:
            print(f"\n✅ FAST: All tests under 1.0s")
        else:
            print(f"\n⚠️  ACCEPTABLE: Max time {max_time:.3f}s")
        
        return benchmark_results
    
    def clear_cache(self):
        """Clear TE computation cache"""
        if self.cache_results and self._te_cache:
            self._te_cache.clear()
            if self.verbose:
                print("🗑️  TE cache cleared")


def demonstrate_unified_te_features():
    """
    Demonstrate the unified TE feature extractor with different method combinations
    """
    print("=" * 80)
    print("🚀 UNIFIED ULTRA-FAST TE FEATURE EXTRACTOR DEMO")
    print("=" * 80)
    
    # Generate test data
    print("\n📊 Generating test data...")
    np.random.seed(42)
    n = 200
    transition = 120
    
    # Create time series with structural break
    x = np.random.normal(0, 1, n)
    y = np.random.normal(0, 1, n)
    
    # Normal period: weak coupling
    for t in range(1, transition):
        x[t] = 0.7 * x[t-1] + 0.1 * y[t-1] + 0.3 * np.random.normal()
        y[t] = 0.8 * y[t-1] + 0.05 * x[t-1] + 0.2 * np.random.normal()
    
    # Candidate period: strong coupling
    for t in range(transition, n):
        x[t] = 0.6 * x[t-1] + 0.2 * y[t-1] + 0.2 * np.random.normal()
        y[t] = 0.5 * y[t-1] + 0.4 * x[t-1] + 0.15 * np.random.normal()
    
    print(f"   ✅ Generated {n} samples with transition at {transition}")
    
    # Test different method combinations
    test_configs = [
        {"name": "Minimal Only", "minimal": True, "sequential": False},
        {"name": "Sequential Only", "minimal": False, "sequential": True},
        {"name": "Both Methods", "minimal": True, "sequential": True}
    ]
    
    results = {}
    
    for config in test_configs:
        print(f"\n🔬 Test: {config['name']}")
        print("-" * 50)
        
        # Initialize extractor with specific configuration
        extractor = UnifiedUltraFastTEFeatures(
            use_minimal_te=config['minimal'],
            use_sequential_te=config['sequential'],
            bins=4,
            min_samples=10,
            cache_results=True,
            verbose=False  # Reduce output for demo
        )
        
        # Extract features
        start_time = time.time()
        features = extractor.extract_te_features(
            pre=x[:transition],
            post=x[transition:],
            whole=x,
            transition_point=transition,
            feature_prefix=f"{config['name'].lower().replace(' ', '_')}_"
        )
        extraction_time = time.time() - start_time
        
        # Count features by type
        minimal_features = [k for k in features.keys() if 'minimal_' in k]
        sequential_features = [k for k in features.keys() if 'sequential_' in k]
        
        print(f"   ✅ Extracted {len(features)} features in {extraction_time:.4f}s")
        print(f"   📊 Feature breakdown:")
        print(f"      - Minimal features: {len(minimal_features)}")
        print(f"      - Sequential features: {len(sequential_features)}")
        
        # Show sample features
        print(f"   📊 Sample features:")
        for i, (key, value) in enumerate(list(features.items())[:5]):
            print(f"      {key}: {value:.6f}")
        if len(features) > 5:
            print(f"      ... and {len(features) - 5} more features")
        
        results[config['name']] = {
            'features': features,
            'time': extraction_time,
            'count': len(features),
            'minimal_count': len(minimal_features),
            'sequential_count': len(sequential_features)
        }
        
        extractor.clear_cache()
    
    # Performance comparison
    print(f"\n📊 PERFORMANCE COMPARISON:")
    print("=" * 60)
    for name, result in results.items():
        rate = n / result['time']
        print(f"{name:15}: {result['time']:7.4f}s ({rate:8.1f} samples/s, {result['count']:2d} features)")
    
    # Feature count comparison
    print(f"\n📊 FEATURE COUNT COMPARISON:")
    print("=" * 60)
    for name, result in results.items():
        print(f"{name:15}: Total={result['count']:2d}, "
              f"Minimal={result['minimal_count']:2d}, Sequential={result['sequential_count']:2d}")
    
    # Batch processing demo
    print(f"\n🔬 Batch Processing Demo (Both Methods):")
    print("-" * 50)
    
    extractor_combined = UnifiedUltraFastTEFeatures(
        use_minimal_te=True,
        use_sequential_te=True,
        verbose=False
    )
    
    # Generate batch data
    time_series_list = []
    transition_points = []
    
    for i in range(3):
        series = np.random.normal(0, 1, 100)
        trans = 50 + np.random.randint(-5, 6)
        
        for t in range(1, len(series)):
            if t < trans:
                series[t] = 0.8 * series[t-1] + 0.2 * np.random.normal()
            else:
                series[t] = 0.6 * series[t-1] + 0.3 * np.random.normal()
        
        time_series_list.append(series)
        transition_points.append(trans)
    
    batch_start = time.time()
    batch_features = extractor_combined.extract_features_batch(
        time_series_list=time_series_list,
        transition_points=transition_points,
        feature_prefix='batch_'
    )
    batch_time = time.time() - batch_start
    
    print(f"   ✅ Processed {len(time_series_list)} series in {batch_time:.4f}s")
    print(f"   📊 Batch results shape: {batch_features.shape}")
    
    # Summary
    print(f"\n🎯 DEMONSTRATION SUMMARY:")
    print("=" * 80)
    print("✅ Multiple TE methods supported (minimal, sequential, both)")
    print("✅ Flexible configuration options")
    print("✅ Ultra-fast performance (< 0.01s for most datasets)")
    print("✅ Comprehensive feature extraction")
    print("✅ Batch processing support")
    print("✅ Ready for integration into ML pipelines")
    
    return results


if __name__ == "__main__":
    # Run demonstration
    demo_results = demonstrate_unified_te_features()
    
    print(f"\n" + "="*80)
    print("🎉 UNIFIED ULTRA-FAST TE FEATURE EXTRACTOR READY!")
    print("="*80)
    print("✅ Two ultra-fast TE implementations available")
    print("✅ Optional use of either or both methods")
    print("✅ Comprehensive feature extraction")
    print("✅ Performance optimized for real-world use")
    print("✅ Ready for integration into unified pipeline")
    
    print(f"\n🚀 Usage Examples:")
    print("```python")
    print("# Minimal only (fastest)")
    print("extractor = UnifiedUltraFastTEFeatures(use_minimal_te=True, use_sequential_te=False)")
    print("")
    print("# Sequential only (comprehensive)")
    print("extractor = UnifiedUltraFastTEFeatures(use_minimal_te=False, use_sequential_te=True)")
    print("")
    print("# Both methods (maximum features)")
    print("extractor = UnifiedUltraFastTEFeatures(use_minimal_te=True, use_sequential_te=True)")
    print("")
    print("features = extractor.extract_te_features(pre, post, whole)")
    print("batch_df = extractor.extract_features_batch(time_series_list)")
    print("```")