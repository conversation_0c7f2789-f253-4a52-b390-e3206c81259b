"""
Optimal Fractal Windows Analysis
Find most stable rolling windows and create ensemble for better model calibration
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from collections import Counter
import json
from scipy import stats
from sklearn.metrics import mean_squared_error

def load_data():
    """Load dataset with memory-efficient approach"""
    try:
        print("📊 Loading training dataset...")
        y_data = pd.read_parquet('y_train.parquet')
        
        if 'structural_breakpoint' in y_data.columns:
            labels = y_data['structural_breakpoint'].astype(int).values
        else:
            labels = y_data.iloc[:, 0].astype(int).values
        
        print(f"   Loaded {len(labels):,} labels")
        print(f"   Label distribution: {Counter(labels)}")
        
        # Use only a manageable subset for analysis (last 50k samples for recency)
        if len(labels) > 50000:
            print(f"   Using last 50,000 samples for analysis (most recent data)")
            labels = labels[-50000:]
        
        return labels
    except Exception as e:
        print(f"Error loading data: {e}")
        return None

def analyze_window_stability():
    """Analyze stability of different rolling windows"""
    
    print("🔍 OPTIMAL FRACTAL WINDOWS ANALYSIS")
    print("=" * 60)
    
    labels = load_data()
    if labels is None:
        return None
    
    overall_ratio = np.mean(labels)
    print(f"📊 Dataset: {len(labels):,} samples, ratio: {overall_ratio:.4f}")
    
    # Test comprehensive range of window sizes
    window_sizes = [25, 50, 75, 100, 150, 200, 300, 500, 750, 1000, 1500, 2000, 3000, 5000]
    step_ratios = [0.1, 0.2, 0.25, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5, 0.5]  # Step as fraction of window
    
    window_analysis = {}
    
    print(f"\n📈 TESTING {len(window_sizes)} WINDOW SIZES:")
    
    for i, window_size in enumerate(window_sizes):
        if window_size > len(labels):
            continue
            
        step_size = max(1, int(window_size * step_ratios[i]))
        
        print(f"\n🔍 Window: {window_size}, Step: {step_size}")
        
        # Calculate rolling ratios
        positions = []
        ratios = []
        
        for start in range(0, len(labels) - window_size + 1, step_size):
            window = labels[start:start + window_size]
            ratio = np.mean(window)
            
            positions.append(start + window_size // 2)
            ratios.append(ratio)
        
        ratios = np.array(ratios)
        positions = np.array(positions)
        
        # Comprehensive stability metrics
        stability_metrics = calculate_stability_metrics(ratios, overall_ratio, positions)
        
        window_analysis[window_size] = {
            'window_size': window_size,
            'step_size': step_size,
            'n_windows': len(ratios),
            'ratios': ratios,
            'positions': positions,
            'metrics': stability_metrics
        }
        
        print(f"   📊 Stability Score: {stability_metrics['stability_score']:.4f}")
        print(f"   📊 Variance: {stability_metrics['variance']:.6f}")
        print(f"   📊 Predictability: {stability_metrics['predictability']:.4f}")
    
    return window_analysis, overall_ratio

def calculate_stability_metrics(ratios, target_ratio, positions):
    """Calculate comprehensive stability metrics"""
    
    metrics = {}
    
    # Basic statistics
    metrics['mean_ratio'] = np.mean(ratios)
    metrics['std_ratio'] = np.std(ratios)
    metrics['variance'] = np.var(ratios)
    metrics['min_ratio'] = np.min(ratios)
    metrics['max_ratio'] = np.max(ratios)
    metrics['range'] = metrics['max_ratio'] - metrics['min_ratio']
    
    # Stability measures
    deviations = np.abs(ratios - target_ratio)
    metrics['mean_deviation'] = np.mean(deviations)
    metrics['max_deviation'] = np.max(deviations)
    
    # Stability score (percentage within 5% of target)
    stable_windows = np.sum(deviations < 0.05)
    metrics['stability_score'] = stable_windows / len(ratios)
    
    # Coefficient of variation
    metrics['cv'] = metrics['std_ratio'] / metrics['mean_ratio'] if metrics['mean_ratio'] > 0 else 0
    
    # Trend analysis
    if len(ratios) > 2:
        trend_correlation = np.corrcoef(positions, ratios)[0, 1]
        metrics['trend_correlation'] = trend_correlation if not np.isnan(trend_correlation) else 0
        
        # Linear trend strength
        slope, intercept, r_value, p_value, std_err = stats.linregress(positions, ratios)
        metrics['trend_slope'] = slope
        metrics['trend_r_squared'] = r_value ** 2
        metrics['trend_p_value'] = p_value
    else:
        metrics['trend_correlation'] = 0
        metrics['trend_slope'] = 0
        metrics['trend_r_squared'] = 0
        metrics['trend_p_value'] = 1
    
    # Predictability (inverse of variance, normalized)
    metrics['predictability'] = 1 / (1 + metrics['variance'] * 100)
    
    # Consistency (how often consecutive windows are similar)
    if len(ratios) > 1:
        consecutive_diffs = np.abs(np.diff(ratios))
        metrics['consistency'] = 1 / (1 + np.mean(consecutive_diffs) * 10)
    else:
        metrics['consistency'] = 1.0
    
    # Stationarity test (Augmented Dickey-Fuller)
    try:
        from statsmodels.tsa.stattools import adfuller
        adf_result = adfuller(ratios)
        metrics['adf_statistic'] = adf_result[0]
        metrics['adf_p_value'] = adf_result[1]
        metrics['is_stationary'] = adf_result[1] < 0.05
    except:
        metrics['adf_statistic'] = 0
        metrics['adf_p_value'] = 1
        metrics['is_stationary'] = False
    
    return metrics

def find_optimal_windows(window_analysis):
    """Find optimal windows based on multiple criteria"""
    
    print(f"\n🎯 FINDING OPTIMAL WINDOWS")
    print("=" * 40)
    
    # Extract metrics for all windows
    windows_data = []
    for window_size, data in window_analysis.items():
        metrics = data['metrics']
        windows_data.append({
            'window_size': window_size,
            'stability_score': metrics['stability_score'],
            'variance': metrics['variance'],
            'predictability': metrics['predictability'],
            'consistency': metrics['consistency'],
            'cv': metrics['cv'],
            'mean_deviation': metrics['mean_deviation'],
            'trend_strength': abs(metrics['trend_correlation']),
            'n_windows': data['n_windows']
        })
    
    windows_df = pd.DataFrame(windows_data)
    
    # Ranking criteria (lower is better for some, higher for others)
    criteria = {
        'stability_score': 'higher',      # Higher stability is better
        'variance': 'lower',              # Lower variance is better
        'predictability': 'higher',       # Higher predictability is better
        'consistency': 'higher',          # Higher consistency is better
        'cv': 'lower',                   # Lower CV is better
        'mean_deviation': 'lower',        # Lower deviation is better
        'trend_strength': 'lower'         # Lower trend is better (more stationary)
    }
    
    # Calculate composite scores
    composite_scores = []
    
    for _, row in windows_df.iterrows():
        score = 0
        weight_sum = 0
        
        # Weighted scoring
        weights = {
            'stability_score': 0.25,
            'variance': 0.20,
            'predictability': 0.15,
            'consistency': 0.15,
            'cv': 0.10,
            'mean_deviation': 0.10,
            'trend_strength': 0.05
        }
        
        for criterion, direction in criteria.items():
            if criterion in row:
                value = row[criterion]
                weight = weights[criterion]
                
                if direction == 'higher':
                    # Normalize to 0-1 and use as is
                    normalized = (value - windows_df[criterion].min()) / (windows_df[criterion].max() - windows_df[criterion].min() + 1e-8)
                else:
                    # Normalize to 0-1 and invert
                    normalized = 1 - (value - windows_df[criterion].min()) / (windows_df[criterion].max() - windows_df[criterion].min() + 1e-8)
                
                score += weight * normalized
                weight_sum += weight
        
        composite_scores.append(score / weight_sum if weight_sum > 0 else 0)
    
    windows_df['composite_score'] = composite_scores
    
    # Sort by composite score
    windows_df = windows_df.sort_values('composite_score', ascending=False)
    
    print(f"📊 TOP 10 OPTIMAL WINDOWS:")
    print("   Rank | Window | Stability | Variance  | Predict | Composite")
    print("   -----|--------|-----------|-----------|---------|----------")
    
    for i, (_, row) in enumerate(windows_df.head(10).iterrows()):
        print(f"     {i+1:2d}  |  {row['window_size']:4.0f}  |   {row['stability_score']:.4f}  | {row['variance']:.6f} |  {row['predictability']:.4f}  |  {row['composite_score']:.4f}")
    
    return windows_df

def create_ensemble_calibration(window_analysis, windows_df, top_n=5):
    """Create ensemble of top windows for calibration"""
    
    print(f"\n🎯 CREATING ENSEMBLE CALIBRATION")
    print("=" * 40)
    
    # Get top N windows
    top_windows = windows_df.head(top_n)
    
    print(f"📊 ENSEMBLE COMPOSITION (Top {top_n}):")
    ensemble_weights = []
    
    for i, (_, row) in enumerate(top_windows.iterrows()):
        window_size = int(row['window_size'])
        weight = row['composite_score']
        ensemble_weights.append(weight)
        
        print(f"   {i+1}. Window {window_size}: weight={weight:.4f}, stability={row['stability_score']:.4f}")
    
    # Normalize weights
    ensemble_weights = np.array(ensemble_weights)
    ensemble_weights = ensemble_weights / np.sum(ensemble_weights)
    
    print(f"\n📊 NORMALIZED WEIGHTS: {[f'{w:.3f}' for w in ensemble_weights]}")
    
    # Create ensemble predictions
    ensemble_ratios = create_ensemble_predictions(window_analysis, top_windows, ensemble_weights)
    
    return {
        'top_windows': top_windows,
        'ensemble_weights': ensemble_weights,
        'ensemble_ratios': ensemble_ratios
    }

def create_ensemble_predictions(window_analysis, top_windows, weights):
    """Create ensemble predictions from top windows"""
    
    # Find common positions across all windows
    all_positions = []
    for _, row in top_windows.iterrows():
        window_size = int(row['window_size'])
        positions = window_analysis[window_size]['positions']
        all_positions.extend(positions)
    
    # Get position range that all windows cover
    min_pos = max([min(window_analysis[int(row['window_size'])]['positions']) for _, row in top_windows.iterrows()])
    max_pos = min([max(window_analysis[int(row['window_size'])]['positions']) for _, row in top_windows.iterrows()])
    
    # Create ensemble predictions at regular intervals
    ensemble_positions = np.arange(min_pos, max_pos + 1, 100)  # Every 100 positions
    ensemble_ratios = []
    
    for pos in ensemble_positions:
        weighted_ratio = 0
        total_weight = 0
        
        for i, (_, row) in enumerate(top_windows.iterrows()):
            window_size = int(row['window_size'])
            positions = window_analysis[window_size]['positions']
            ratios = window_analysis[window_size]['ratios']
            
            # Find closest position
            closest_idx = np.argmin(np.abs(positions - pos))
            if abs(positions[closest_idx] - pos) <= 200:  # Within reasonable distance
                weighted_ratio += weights[i] * ratios[closest_idx]
                total_weight += weights[i]
        
        if total_weight > 0:
            ensemble_ratios.append(weighted_ratio / total_weight)
        else:
            ensemble_ratios.append(np.nan)
    
    return {
        'positions': ensemble_positions,
        'ratios': np.array(ensemble_ratios)
    }

def evaluate_ensemble_performance(ensemble_data, window_analysis, overall_ratio):
    """Evaluate ensemble performance vs individual windows"""
    
    print(f"\n📊 ENSEMBLE PERFORMANCE EVALUATION")
    print("=" * 40)
    
    ensemble_ratios = ensemble_data['ensemble_ratios']['ratios']
    ensemble_ratios = ensemble_ratios[~np.isnan(ensemble_ratios)]  # Remove NaN values
    
    if len(ensemble_ratios) == 0:
        print("❌ No valid ensemble predictions")
        return None
    
    # Ensemble metrics
    ensemble_std = np.std(ensemble_ratios)
    ensemble_mean = np.mean(ensemble_ratios)
    ensemble_deviation = np.mean(np.abs(ensemble_ratios - overall_ratio))
    ensemble_stability = np.sum(np.abs(ensemble_ratios - overall_ratio) < 0.05) / len(ensemble_ratios)
    
    print(f"🎯 ENSEMBLE PERFORMANCE:")
    print(f"   Mean ratio: {ensemble_mean:.4f} (target: {overall_ratio:.4f})")
    print(f"   Std deviation: {ensemble_std:.6f}")
    print(f"   Mean deviation: {ensemble_deviation:.6f}")
    print(f"   Stability score: {ensemble_stability:.4f}")
    
    # Compare with individual windows
    print(f"\n📊 COMPARISON WITH INDIVIDUAL WINDOWS:")
    print("   Window | Std Dev  | Mean Dev | Stability | vs Ensemble")
    print("   -------|----------|----------|-----------|-------------")
    
    top_windows = ensemble_data['top_windows']
    
    for _, row in top_windows.head(5).iterrows():
        window_size = int(row['window_size'])
        metrics = window_analysis[window_size]['metrics']
        
        std_improvement = (metrics['std_ratio'] - ensemble_std) / metrics['std_ratio'] * 100
        dev_improvement = (metrics['mean_deviation'] - ensemble_deviation) / metrics['mean_deviation'] * 100
        stab_improvement = (ensemble_stability - metrics['stability_score']) / metrics['stability_score'] * 100
        
        print(f"   {window_size:4d}   | {metrics['std_ratio']:.6f} | {metrics['mean_deviation']:.6f} |   {metrics['stability_score']:.4f}   | {std_improvement:+.1f}%/{dev_improvement:+.1f}%/{stab_improvement:+.1f}%")
    
    return {
        'ensemble_std': ensemble_std,
        'ensemble_mean': ensemble_mean,
        'ensemble_deviation': ensemble_deviation,
        'ensemble_stability': ensemble_stability
    }

def create_calibration_function(ensemble_data, overall_ratio):
    """Create calibration function based on ensemble"""
    
    print(f"\n🎯 CREATING CALIBRATION FUNCTION")
    print("=" * 40)
    
    ensemble_ratios = ensemble_data['ensemble_ratios']['ratios']
    ensemble_positions = ensemble_data['ensemble_ratios']['positions']
    
    # Remove NaN values
    valid_mask = ~np.isnan(ensemble_ratios)
    ensemble_ratios = ensemble_ratios[valid_mask]
    ensemble_positions = ensemble_positions[valid_mask]
    
    if len(ensemble_ratios) == 0:
        print("❌ No valid ensemble data for calibration")
        return None
    
    # Calculate calibration factors
    calibration_factors = ensemble_ratios - overall_ratio
    
    print(f"📊 CALIBRATION STATISTICS:")
    print(f"   Mean calibration factor: {np.mean(calibration_factors):.6f}")
    print(f"   Std calibration factor: {np.std(calibration_factors):.6f}")
    print(f"   Range: [{np.min(calibration_factors):.6f}, {np.max(calibration_factors):.6f}]")
    
    # Create calibration lookup
    calibration_lookup = {
        'positions': ensemble_positions.tolist(),
        'factors': calibration_factors.tolist(),
        'target_ratio': overall_ratio,
        'mean_factor': float(np.mean(calibration_factors)),
        'std_factor': float(np.std(calibration_factors))
    }
    
    return calibration_lookup

def visualize_optimal_windows(window_analysis, windows_df, ensemble_data):
    """Create comprehensive visualization"""
    
    print(f"\n📊 Creating Optimal Windows Visualization...")
    
    fig, axes = plt.subplots(3, 2, figsize=(16, 12))
    fig.suptitle('Optimal Fractal Windows Analysis', fontsize=16)
    
    # 1. Window stability scores
    window_sizes = [w['window_size'] for w in windows_df.to_dict('records')]
    stability_scores = [w['stability_score'] for w in windows_df.to_dict('records')]
    
    axes[0, 0].semilogx(window_sizes, stability_scores, 'o-', linewidth=2, markersize=6)
    axes[0, 0].set_xlabel('Window Size')
    axes[0, 0].set_ylabel('Stability Score')
    axes[0, 0].set_title('Stability vs Window Size')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].axhline(y=0.8, color='green', linestyle='--', alpha=0.5, label='High Stability')
    axes[0, 0].legend()
    
    # 2. Composite scores
    composite_scores = [w['composite_score'] for w in windows_df.to_dict('records')]
    
    # Plot all points first
    axes[0, 1].semilogx(window_sizes, composite_scores, 'o', markersize=6, color='blue', alpha=0.7)
    # Highlight top 5
    top_5_sizes = window_sizes[:5]
    top_5_scores = composite_scores[:5]
    axes[0, 1].semilogx(top_5_sizes, top_5_scores, 'o', markersize=8, color='red', alpha=0.9)
    axes[0, 1].set_xlabel('Window Size')
    axes[0, 1].set_ylabel('Composite Score')
    axes[0, 1].set_title('Composite Score vs Window Size (Red=Top 5)')
    axes[0, 1].grid(True, alpha=0.3)
    
    # 3. Variance vs window size
    variances = [w['variance'] for w in windows_df.to_dict('records')]
    
    axes[1, 0].loglog(window_sizes, variances, 'o-', linewidth=2, markersize=6, color='purple')
    axes[1, 0].set_xlabel('Window Size (log)')
    axes[1, 0].set_ylabel('Variance (log)')
    axes[1, 0].set_title('Variance vs Window Size')
    axes[1, 0].grid(True, alpha=0.3)
    
    # 4. Top windows comparison
    top_5 = windows_df.head(5)
    metrics = ['stability_score', 'predictability', 'consistency']
    
    x = np.arange(len(top_5))
    width = 0.25
    
    for i, metric in enumerate(metrics):
        values = [row[metric] for _, row in top_5.iterrows()]
        axes[1, 1].bar(x + i*width, values, width, label=metric.replace('_', ' ').title(), alpha=0.7)
    
    axes[1, 1].set_xlabel('Top Windows (by rank)')
    axes[1, 1].set_ylabel('Score')
    axes[1, 1].set_title('Top 5 Windows Comparison')
    axes[1, 1].set_xticks(x + width)
    axes[1, 1].set_xticklabels([f"W{int(row['window_size'])}" for _, row in top_5.iterrows()])
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # 5. Ensemble vs individual predictions
    if ensemble_data and 'ensemble_ratios' in ensemble_data:
        ensemble_pos = ensemble_data['ensemble_ratios']['positions']
        ensemble_ratios = ensemble_data['ensemble_ratios']['ratios']
        
        # Plot ensemble
        valid_mask = ~np.isnan(ensemble_ratios)
        axes[2, 0].plot(ensemble_pos[valid_mask], ensemble_ratios[valid_mask], 
                       'r-', linewidth=3, label='Ensemble', alpha=0.8)
        
        # Plot top 3 individual windows
        top_3 = windows_df.head(3)
        colors = ['blue', 'green', 'orange']
        
        for i, (_, row) in enumerate(top_3.iterrows()):
            window_size = int(row['window_size'])
            positions = window_analysis[window_size]['positions']
            ratios = window_analysis[window_size]['ratios']
            
            # Subsample for clarity
            if len(positions) > 200:
                indices = np.linspace(0, len(positions)-1, 200, dtype=int)
                positions = positions[indices]
                ratios = ratios[indices]
            
            axes[2, 0].plot(positions, ratios, color=colors[i], alpha=0.6, 
                           label=f'Window {window_size}', linewidth=1)
        
        axes[2, 0].set_xlabel('Position in Dataset')
        axes[2, 0].set_ylabel('Ratio')
        axes[2, 0].set_title('Ensemble vs Individual Windows')
        axes[2, 0].legend()
        axes[2, 0].grid(True, alpha=0.3)
    
    # 6. Calibration factors distribution
    if ensemble_data and 'ensemble_ratios' in ensemble_data:
        ensemble_ratios = ensemble_data['ensemble_ratios']['ratios']
        valid_ratios = ensemble_ratios[~np.isnan(ensemble_ratios)]
        
        if len(valid_ratios) > 0:
            overall_ratio = 0.2909  # From previous analysis
            calibration_factors = valid_ratios - overall_ratio
            
            axes[2, 1].hist(calibration_factors, bins=20, alpha=0.7, color='skyblue', density=True)
            axes[2, 1].axvline(x=0, color='red', linestyle='--', alpha=0.8, label='No Calibration')
            axes[2, 1].axvline(x=np.mean(calibration_factors), color='green', linestyle='-', 
                              label=f'Mean: {np.mean(calibration_factors):.4f}')
            axes[2, 1].set_xlabel('Calibration Factor')
            axes[2, 1].set_ylabel('Density')
            axes[2, 1].set_title('Calibration Factors Distribution')
            axes[2, 1].legend()
            axes[2, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('optimal_fractal_windows_analysis.png', dpi=300, bbox_inches='tight')
    print("   ✅ Visualization saved to 'optimal_fractal_windows_analysis.png'")

def main():
    """Main analysis function"""
    
    print("🔬 Optimal Fractal Windows Analysis")
    print("=" * 70)
    print("🎯 Finding most stable windows and creating ensemble calibration")
    print("=" * 70)
    
    # Analyze window stability
    window_analysis, overall_ratio = analyze_window_stability()
    
    if window_analysis is None:
        print("❌ Analysis failed")
        return None
    
    # Find optimal windows
    windows_df = find_optimal_windows(window_analysis)
    
    # Create ensemble calibration
    ensemble_data = create_ensemble_calibration(window_analysis, windows_df, top_n=5)
    
    # Evaluate ensemble performance
    ensemble_performance = evaluate_ensemble_performance(ensemble_data, window_analysis, overall_ratio)
    
    # Create calibration function
    calibration_function = create_calibration_function(ensemble_data, overall_ratio)
    
    # Create visualizations
    visualize_optimal_windows(window_analysis, windows_df, ensemble_data)
    
    # Prepare results
    results = {
        'optimal_windows': windows_df.head(10).to_dict('records'),
        'ensemble_composition': {
            'top_windows': ensemble_data['top_windows'].to_dict('records'),
            'weights': ensemble_data['ensemble_weights'].tolist()
        },
        'ensemble_performance': ensemble_performance,
        'calibration_function': calibration_function,
        'recommendations': {
            'best_single_window': int(windows_df.iloc[0]['window_size']),
            'ensemble_windows': [int(row['window_size']) for _, row in ensemble_data['top_windows'].iterrows()],
            'ensemble_weights': ensemble_data['ensemble_weights'].tolist()
        }
    }
    
    # Save results
    with open('optimal_fractal_windows_results.json', 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    print(f"\n🎉 Optimal Windows Analysis Complete!")
    print(f"   Best single window: {results['recommendations']['best_single_window']}")
    print(f"   Ensemble windows: {results['recommendations']['ensemble_windows']}")
    print(f"   💾 Results saved to 'optimal_fractal_windows_results.json'")
    
    return results

if __name__ == "__main__":
    results = main()