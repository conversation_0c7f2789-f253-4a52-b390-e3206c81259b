#!/usr/bin/env python3
"""
distribution_metrics_probing_v2.py AUCROC 0.66

This script extends v1 by adding features that quantify how well the pre-break
and post-break distributions fit various theoretical distributions. It then
creates ratios of these "goodness-of-fit" scores to detect changes in the
fundamental shape of the time series distribution.

It includes:
1.  Direct pre-vs-post distribution comparison metrics (from v1).
2.  Goodness-of-fit (GoF) statistics for pre/post segments against several
    theoretical distributions (Normal, T, Exponential, Log-Normal, etc.).
3.  Ratio features comparing the GoF statistics (post_fit / pre_fit).
4.  A complete feature importance evaluation pipeline using XGBoost.
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import (entropy, wasserstein_distance, cramervonmises_2samp, 
                         kstest, norm, t, expon, lognorm, gamma, beta, genextreme)
import xgboost as xgb
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score
from multiprocessing import Pool
import warnings
import logging

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Goodness-of-Fit Feature Function ---

def get_goodness_of_fit_stat(segment, dist_name):
    """
    Calculates the K-S test statistic for a data segment against a theoretical distribution.
    Returns the statistic, a smaller value indicates a better fit.
    """
    if len(segment) < 10:
        return np.nan
    
    try:
        # For Beta distribution, data must be in [0, 1]
        if dist_name == 'beta':
            segment_scaled = (segment - np.min(segment)) / (np.max(segment) - np.min(segment) + 1e-9)
            params = getattr(stats, dist_name).fit(segment_scaled)
            ks_stat, _ = kstest(segment_scaled, dist_name, args=params)
        else:
            # Fit the distribution to the data to get its parameters
            params = getattr(stats, dist_name).fit(segment)
            # Perform the K-S test against the fitted distribution
            ks_stat, _ = kstest(segment, dist_name, args=params)
        
        return ks_stat
    except Exception:
        # Catches errors from fitting (e.g., non-positive data for lognorm)
        return np.nan

# --- Main Feature Computation Function ---

def compute_all_distributional_features(args):
    """
    Computes a full suite of distributional features, including GoF tests.
    """
    series_id, series, tstar = args
    feats = {'series_id': series_id}

    series = np.array(series, dtype=float)
    series = series[~np.isnan(series)]

    if len(series) < 20 or tstar <= 10 or tstar >= len(series) - 10:
        return feats

    pre_seg, post_seg = series[:tstar], series[tstar:]

    # --- 1. Direct Pre vs. Post Comparison (from v1) ---
    feats['js_divergence'] = jensen_shannon_divergence(pre_seg, post_seg)
    feats['wasserstein_dist'] = wasserstein_distance(pre_seg, post_seg)
    try:
        feats['ks_stat_pre_post'], _ = stats.ks_2samp(pre_seg, post_seg)
    except Exception:
        feats['ks_stat_pre_post'] = np.nan

    # --- 2. NEW: Goodness-of-Fit (GoF) Features ---
    distributions_to_test = {
        'norm': norm, 't': t, 'expon': expon, 
        'lognorm': lognorm, 'gamma': gamma, 'beta': beta, 'genextreme': genextreme
    }
    
    gof_stats = {}
    for name, dist_obj in distributions_to_test.items():
        # Calculate GoF for pre and post segments
        gof_stats[f'gof_{name}_pre'] = get_goodness_of_fit_stat(pre_seg, name)
        gof_stats[f'gof_{name}_post'] = get_goodness_of_fit_stat(post_seg, name)
    
    feats.update(gof_stats)

    # --- 3. NEW: Ratio of GoF Features ---
    for name in distributions_to_test.keys():
        pre_fit = gof_stats.get(f'gof_{name}_pre')
        post_fit = gof_stats.get(f'gof_{name}_post')
        
        if pre_fit is not None and post_fit is not None and not np.isnan(pre_fit) and not np.isnan(post_fit) and pre_fit > 1e-9:
            feats[f'ratio_gof_{name}'] = post_fit / pre_fit
        else:
            feats[f'ratio_gof_{name}'] = np.nan
            
    return feats

def jensen_shannon_divergence(pre, post):
    """Helper function from v1."""
    if len(pre) < 5 or len(post) < 5: return np.nan
    try:
        bins = np.histogram(np.concatenate([pre, post]), bins='auto')[1]
        hist_pre, _ = np.histogram(pre, bins=bins, density=True)
        hist_post, _ = np.histogram(post, bins=bins, density=True)
        hist_pre /= hist_pre.sum() if hist_pre.sum() > 0 else 1
        hist_post /= hist_post.sum() if hist_post.sum() > 0 else 1
        m = 0.5 * (hist_pre + hist_post)
        return 0.5 * (entropy(hist_pre, m) + entropy(hist_post, m))
    except Exception:
        return np.nan

def extract_features_parallel(series_list, tstars_list, n_processes=None):
    """Parallel feature extraction wrapper."""
    if n_processes is None:
        n_processes = max(1, Pool()._processes - 1)
    args = [(i, series_list[i], tstars_list[i]) for i in range(len(series_list))]
    logging.info(f"Initializing {n_processes} workers for {len(series_list)} series...")
    with Pool(processes=n_processes) as pool:
        results = pool.map(compute_all_distributional_features, args)
    feature_df = pd.DataFrame(results).set_index('series_id')
    feature_df.replace([np.inf, -np.inf], np.nan, inplace=True)
    logging.info(f"Feature extraction complete. Matrix shape: {feature_df.shape}")
    return feature_df

def evaluate_feature_importance(X, y):
    """Trains XGBoost using 5-fold CV and evaluates feature importance."""
    logging.info("Starting feature importance evaluation with XGBoost...")
    y = y.loc[X.index]
    X.fillna(X.median(), inplace=True)
    skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    model = xgb.XGBClassifier(objective='binary:logistic', eval_metric='auc', use_label_encoder=False,
                              n_estimators=200, learning_rate=0.05, max_depth=4,
                              subsample=0.8, colsample_bytree=0.8, random_state=42)
    oof_preds = np.zeros(len(X))
    feature_importances = pd.DataFrame(index=X.columns)
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logging.info(f"--- Fold {fold+1}/5 ---")
        X_train, y_train, X_val, y_val = X.iloc[train_idx], y.iloc[train_idx], X.iloc[val_idx], y.iloc[val_idx]
        model.fit(X_train, y_train, early_stopping_rounds=30, eval_set=[(X_val, y_val)], verbose=False)
        oof_preds[val_idx] = model.predict_proba(X_val)[:, 1]
        feature_importances[f'fold_{fold+1}'] = model.feature_importances_
    logging.info(f"\nOverall 5-Fold CV AUC: {roc_auc_score(y, oof_preds):.5f}")
    feature_importances['mean'] = feature_importances.mean(axis=1)
    return feature_importances[['mean']].sort_values('mean', ascending=False)

# --- Main Execution Block ---
if __name__ == "__main__":
    logging.info("Starting Distributional Metrics Probing Pipeline (v2)")
    logging.info("Loading data...")
    try:
        np.random.seed(42)
        n_series = 200
        all_series = [np.random.randn(np.random.randint(200, 500)) for _ in range(n_series)]
        all_tstars = [len(s) // 2 for s in all_series]
        all_labels = pd.Series(np.random.randint(0, 2, n_series))
        for i in range(n_series):
            if all_labels[i] == 1 and i % 2 == 0:
                # Make post-break log-normally distributed (skewed and heavy-tailed)
                all_series[i][all_tstars[i]:] = np.random.lognormal(0.5, 0.8, len(all_series[i][all_tstars[i]:]))
        logging.info("Synthetic data created for demonstration.")
    except Exception as e:
        logging.error(f"Could not load or create data. Error: {e}"); exit()

    features_df = extract_features_parallel(all_series, all_tstars)
    
    if not features_df.empty:
        feature_importance_df = evaluate_feature_importance(features_df, all_labels)
        logging.info("\n--- Top 20 Most Important Distributional Features ---")
        print(feature_importance_df.head(20))
        features_df.to_csv("distributional_features_v2_output.csv")
        feature_importance_df.to_csv("distributional_feature_importance_v2.csv")
        logging.info("\nSaved features and importances to CSV files.")
    else:
        logging.warning("Feature DataFrame is empty. Skipping evaluation.")
    logging.info("Pipeline finished.")
 