#!/usr/bin/env python3
"""
VAE Autoencoder V2 with Adaptive Noise and Adversarial Perturbations
PyTorch CUDA implementation with local variance-based noise and confusion maximization
^^^^^^^^^^^^^^^
  File "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/autoencoder_test_v2.py", line 442, in train_enhanced_vae_model
    val_loss += loss.item()
                ^^^^^^^^^^^
RuntimeError: CUDA error: device-side assert triggered
CUDA kernel errors might be asynchronously reported at some other API call, so the stacktrace below might be incorrect.
For debugging consider passing CUDA_LAUNCH_BLOCKING=1
Compile with `TORCH_USE_CUDA_DSA` to enable device-side assertions.
"""

import numpy as np
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import optuna
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
from sklearn.preprocessing import StandardScaler
import logging
from typing import Dict, Tuple
import warnings
from tqdm import tqdm

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Set device
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

class AdversarialNoiseGenerator(nn.Module):
    """Network to generate minimal changes that maximize classifier confusion"""
    
    def __init__(self, input_dim, hidden_dim=64):
        super(AdversarialNoiseGenerator, self).__init__()
        
        self.noise_net = nn.Sequential(
            nn.Linear(input_dim, hidden_dim),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(hidden_dim // 2, input_dim),
            nn.Tanh()  # Bounded noise [-1, 1]
        )
        
        # Learnable noise strength parameter
        self.noise_strength = nn.Parameter(torch.tensor(0.1))
        
    def forward(self, x, target_confusion=True):
        """Generate adversarial noise to maximize confusion"""
        base_noise = self.noise_net(x)
        
        # Scale noise by learnable strength
        noise = base_noise * torch.abs(self.noise_strength)
        
        return noise

class LocalVarianceNoiseInjector:
    """Inject noise proportional to local variance in candidate segments"""
    
    def __init__(self, noise_scale=0.1, window_size=10):
        self.noise_scale = noise_scale
        self.window_size = window_size
    
    def calculate_local_variance(self, time_series, break_point):
        """Calculate local variance around break point"""
        # Focus on candidate part (post-break)
        candidate_part = time_series[break_point:]
        
        if len(candidate_part) < self.window_size:
            return np.var(candidate_part) if len(candidate_part) > 1 else 0.1
        
        # Calculate rolling variance
        local_variances = []
        for i in range(len(candidate_part) - self.window_size + 1):
            window = candidate_part[i:i + self.window_size]
            local_variances.append(np.var(window))
        
        # Return mean local variance
        return np.mean(local_variances) if local_variances else 0.1
    
    def inject_variance_proportional_noise(self, features, time_series_list, labels):
        """Inject noise proportional to local variance in candidate parts"""
        noisy_features = features.copy()
        
        for i, (series, break_point) in enumerate(time_series_list):
            if i >= len(features):
                break
                
            # Calculate local variance in candidate part
            local_var = self.calculate_local_variance(series, break_point)
            
            # Generate noise proportional to local variance
            noise_strength = self.noise_scale * np.sqrt(local_var)
            
            # Different noise patterns for different classes
            if labels[i] == 1:  # Candidate (structural break)
                # Add more structured noise to candidate samples
                noise = np.random.normal(0, noise_strength, features[i].shape)
                # Add some periodic components to simulate structural changes
                freq_noise = noise_strength * 0.3 * np.sin(np.linspace(0, 4*np.pi, len(features[i])))
                noisy_features[i] += noise + freq_noise
            else:  # Normal
                # Add simpler noise to normal samples
                noise = np.random.normal(0, noise_strength * 0.5, features[i].shape)
                noisy_features[i] += noise
        
        return noisy_features

class TimeSeriesDistributionDataset(Dataset):
    """Enhanced dataset with noise injection capabilities"""
    
    def __init__(self, X_features, y_labels, time_series_list=None, use_noise=False, noise_injector=None):
        self.X_original = torch.FloatTensor(X_features)
        self.y = torch.LongTensor(y_labels)
        self.time_series_list = time_series_list
        self.use_noise = use_noise
        self.noise_injector = noise_injector
        
        # Apply noise if requested
        if use_noise and noise_injector and time_series_list:
            noisy_features = noise_injector.inject_variance_proportional_noise(
                X_features, time_series_list, y_labels
            )
            self.X = torch.FloatTensor(noisy_features)
        else:
            self.X = self.X_original
    
    def __len__(self):
        return len(self.X)
    
    def __getitem__(self, idx):
        return self.X[idx], self.y[idx], self.X_original[idx]

class EnhancedDistributionVAE(nn.Module):
    """Enhanced VAE with adversarial noise generation and confusion maximization"""
    
    def __init__(self, input_dim, latent_dim=32, hidden_dims=[128, 64], use_adversarial=True):
        super(EnhancedDistributionVAE, self).__init__()
        
        self.input_dim = input_dim
        self.latent_dim = latent_dim
        self.use_adversarial = use_adversarial
        
        # Encoder
        encoder_layers = []
        prev_dim = input_dim
        for hidden_dim in hidden_dims:
            encoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        self.encoder = nn.Sequential(*encoder_layers)
        
        # Latent space
        self.fc_mu = nn.Linear(hidden_dims[-1], latent_dim)
        self.fc_logvar = nn.Linear(hidden_dims[-1], latent_dim)
        
        # Decoder
        decoder_layers = []
        prev_dim = latent_dim
        for hidden_dim in reversed(hidden_dims):
            decoder_layers.extend([
                nn.Linear(prev_dim, hidden_dim),
                nn.ReLU(),
                nn.Dropout(0.2)
            ])
            prev_dim = hidden_dim
        
        decoder_layers.append(nn.Linear(hidden_dims[0], input_dim))
        self.decoder = nn.Sequential(*decoder_layers)
        
        # Classifier head - output raw logits (no sigmoid)
        self.classifier = nn.Sequential(
            nn.Linear(latent_dim, 32),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(16, 1)
            # No sigmoid - BCEWithLogitsLoss will handle it
        )
        
        # Adversarial noise generator
        if use_adversarial:
            self.noise_generator = AdversarialNoiseGenerator(input_dim, hidden_dim=64)
        
        # Confusion detector (helps identify when to apply noise)
        self.confusion_detector = nn.Sequential(
            nn.Linear(latent_dim, 16),
            nn.ReLU(),
            nn.Linear(16, 1),
            nn.Sigmoid()
        )
    
    def encode(self, x):
        h = self.encoder(x)
        mu = self.fc_mu(h)
        logvar = self.fc_logvar(h)
        return mu, logvar
    
    def reparameterize(self, mu, logvar):
        std = torch.exp(0.5 * logvar)
        eps = torch.randn_like(std)
        return mu + eps * std
    
    def decode(self, z):
        return self.decoder(z)
    
    def generate_adversarial_noise(self, x):
        """Generate adversarial noise to maximize classifier confusion"""
        if not self.use_adversarial:
            return torch.zeros_like(x)
        
        return self.noise_generator(x)
    
    def forward(self, x, apply_adversarial=False):
        # Apply adversarial noise if requested
        if apply_adversarial and self.use_adversarial:
            adv_noise = self.generate_adversarial_noise(x)
            x_noisy = x + adv_noise
        else:
            x_noisy = x
            adv_noise = torch.zeros_like(x)
        
        # VAE forward pass
        mu, logvar = self.encode(x_noisy)
        z = self.reparameterize(mu, logvar)
        recon_x = self.decode(z)
        class_pred = self.classifier(z)
        
        # Confusion score (how uncertain the classifier is)
        confusion_score = self.confusion_detector(z)
        
        return recon_x, class_pred, mu, logvar, adv_noise, confusion_score

def load_and_prepare_data():
    """Load raw time series data and extract distribution features"""
    logger.info("📊 Loading raw time series data...")
    
    try:
        # Load raw data
        X_data = pd.read_parquet('X_train.parquet')
        y_data = pd.read_parquet('y_train.parquet')
        
        logger.info(f"   ✅ Raw data loaded: {X_data.shape}")
        
        # Extract time series and labels
        time_series_list = []
        labels = []
        
        if isinstance(X_data.index, pd.MultiIndex):
            grouped = X_data.groupby(level='id')
            for id_, group in grouped:
                series_data = group.sort_index(level='time')
                values = series_data['value'].values
                periods = series_data['period'].values
                
                # Find break point
                period_changes = np.where(np.diff(periods) != 0)[0]
                break_point = period_changes[0] + 1 if len(period_changes) > 0 else len(values) // 2
                
                time_series_list.append((values, break_point))
                
                label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
                labels.append(int(label))
        
        logger.info(f"   ✅ Extracted {len(time_series_list)} time series")
        logger.info(f"   📊 Label distribution: {pd.Series(labels).value_counts().to_dict()}")
        
        return time_series_list, labels
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def extract_distribution_features(time_series_list, n_bins=50):
    """Extract distribution features from time series segments"""
    logger.info(f"🔧 Extracting distribution features (n_bins={n_bins})...")
    
    features_list = []
    
    for i, (series, break_point) in enumerate(tqdm(time_series_list, desc="Processing series")):
        if i % 1000 == 0:
            logger.info(f"   Processing {i+1}/{len(time_series_list)}...")
        
        series_clean = series[~np.isnan(series)]
        if len(series_clean) < 20:
            continue
        
        # Split into pre and post segments
        pre_segment = series_clean[:break_point]
        post_segment = series_clean[break_point:]
        
        if len(pre_segment) < 5 or len(post_segment) < 5:
            continue
        
        features = []
        
        # Pre-segment distribution
        pre_hist, _ = np.histogram(pre_segment, bins=n_bins, density=True)
        features.extend(pre_hist)
        
        # Post-segment distribution
        post_hist, _ = np.histogram(post_segment, bins=n_bins, density=True)
        features.extend(post_hist)
        
        # Enhanced comparative statistics with local variance info
        pre_local_var = np.var(pre_segment)
        post_local_var = np.var(post_segment)
        
        features.extend([
            np.mean(pre_segment), np.std(pre_segment), np.median(pre_segment),
            np.mean(post_segment), np.std(post_segment), np.median(post_segment),
            np.mean(post_segment) - np.mean(pre_segment),  # Mean difference
            np.std(post_segment) / (np.std(pre_segment) + 1e-8),  # Std ratio
            np.percentile(pre_segment, 25), np.percentile(pre_segment, 75),
            np.percentile(post_segment, 25), np.percentile(post_segment, 75),
            pre_local_var, post_local_var,  # Local variances
            post_local_var / (pre_local_var + 1e-8),  # Variance ratio
        ])
        
        # KL divergence approximation
        pre_hist_smooth = pre_hist + 1e-8
        post_hist_smooth = post_hist + 1e-8
        kl_div = np.sum(pre_hist_smooth * np.log(pre_hist_smooth / post_hist_smooth))
        features.append(kl_div)
        
        # Wasserstein distance approximation
        wasserstein_approx = np.sum(np.abs(np.cumsum(pre_hist) - np.cumsum(post_hist)))
        features.append(wasserstein_approx)
        
        features_list.append(features)
    
    features_array = np.array(features_list)
    logger.info(f"   ✅ Extracted features shape: {features_array.shape}")
    
    return features_array

def enhanced_vae_loss_function(recon_x, x, mu, logvar, class_pred, y_true, adv_noise, confusion_score, 
                              beta=1.0, alpha=1.0, gamma=0.5, delta=0.1):
    """Enhanced VAE loss with adversarial and confusion components"""
    # Reconstruction loss
    recon_loss = nn.MSELoss()(recon_x, x)
    
    # KL divergence loss
    kl_loss = -0.5 * torch.sum(1 + logvar - mu.pow(2) - logvar.exp())
    kl_loss /= x.size(0) * x.size(1)
    
    # Classification loss - use BCEWithLogitsLoss for numerical stability
    class_pred_flat = class_pred.view(-1)
    y_true_flat = y_true.float().view(-1)
    class_loss = nn.BCEWithLogitsLoss()(class_pred_flat, y_true_flat)
    
    # Adversarial noise regularization (encourage minimal noise)
    noise_reg = torch.mean(torch.norm(adv_noise, dim=1))
    
    # Confusion maximization loss (encourage uncertainty for hard samples)
    confusion_target = torch.ones_like(confusion_score.view(-1)) * 0.5  # Target maximum confusion
    confusion_loss = nn.MSELoss()(confusion_score.view(-1), confusion_target)
    
    # Combined loss
    total_loss = (recon_loss + 
                  beta * kl_loss + 
                  alpha * class_loss + 
                  gamma * confusion_loss + 
                  delta * noise_reg)
    
    return total_loss, recon_loss, kl_loss, class_loss, confusion_loss, noise_reg

def train_enhanced_vae_model(model, train_loader, val_loader, params, n_epochs=100):
    """Train enhanced VAE model with adversarial components"""
    optimizer = optim.Adam(model.parameters(), lr=params['lr'], weight_decay=params['weight_decay'])
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=10, factor=0.5)
    
    best_val_auc = 0
    patience_counter = 0
    patience = 15
    
    for epoch in range(n_epochs):
        # Training
        model.train()
        train_loss = 0
        train_preds = []
        train_targets = []
        
        for batch_x, batch_y, batch_x_orig in train_loader:
            batch_x, batch_y = batch_x.to(device), batch_y.to(device)
            batch_x_orig = batch_x_orig.to(device)
            
            optimizer.zero_grad()
            
            # Alternate between normal and adversarial training
            apply_adversarial = (epoch % 3 == 0)  # Apply adversarial every 3rd epoch
            
            recon_x, class_pred, mu, logvar, adv_noise, confusion_score = model(
                batch_x, apply_adversarial=apply_adversarial
            )
            
            loss, recon_loss, kl_loss, class_loss, confusion_loss, noise_reg = enhanced_vae_loss_function(
                recon_x, batch_x_orig, mu, logvar, class_pred, batch_y, adv_noise, confusion_score,
                beta=params['beta'], alpha=params['alpha'], 
                gamma=params.get('gamma', 0.5), delta=params.get('delta', 0.1)
            )
            
            loss.backward()
            torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)
            optimizer.step()
            
            train_loss += loss.item()
            train_preds.extend(class_pred.cpu().detach().numpy())
            train_targets.extend(batch_y.cpu().numpy())
        
        # Validation
        model.eval()
        val_loss = 0
        val_preds = []
        val_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y, batch_x_orig in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                batch_x_orig = batch_x_orig.to(device)
                
                recon_x, class_pred, mu, logvar, adv_noise, confusion_score = model(
                    batch_x, apply_adversarial=False
                )
                
                loss, _, _, _, _, _ = enhanced_vae_loss_function(
                    recon_x, batch_x_orig, mu, logvar, class_pred, batch_y, adv_noise, confusion_score,
                    beta=params['beta'], alpha=params['alpha'],
                    gamma=params.get('gamma', 0.5), delta=params.get('delta', 0.1)
                )
                
                val_loss += loss.item()
                val_preds.extend(class_pred.cpu().numpy())
                val_targets.extend(batch_y.cpu().numpy())
        
        # Calculate metrics - convert logits to probabilities
        try:
            val_preds_proba = torch.sigmoid(torch.tensor(val_preds)).numpy()
            val_auc = roc_auc_score(val_targets, val_preds_proba)
        except:
            val_auc = 0.5
            
        scheduler.step(val_loss)
        
        # Early stopping
        if val_auc > best_val_auc:
            best_val_auc = val_auc
            patience_counter = 0
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            break
        
        if epoch % 10 == 0:
            logger.info(f"     Epoch {epoch}: Val AUC = {val_auc:.4f}")
    
    return best_val_auc

def optimize_enhanced_vae_hyperparameters(X_features, y_labels, time_series_list, n_trials=30, n_folds=3):
    """Optimize enhanced VAE hyperparameters"""
    logger.info("🔧 Optimizing Enhanced VAE hyperparameters...")
    
    def objective(trial):
        params = {
            'latent_dim': trial.suggest_int('latent_dim', 16, 64),
            'hidden_dim1': trial.suggest_int('hidden_dim1', 64, 256),
            'hidden_dim2': trial.suggest_int('hidden_dim2', 32, 128),
            'lr': trial.suggest_float('lr', 1e-4, 1e-2, log=True),
            'batch_size': trial.suggest_categorical('batch_size', [512, 1024, 2048]),
            'weight_decay': trial.suggest_float('weight_decay', 1e-6, 1e-3, log=True),
            'beta': trial.suggest_float('beta', 0.1, 2.0),
            'alpha': trial.suggest_float('alpha', 0.5, 3.0),
            'gamma': trial.suggest_float('gamma', 0.1, 1.0),  # Confusion loss weight
            'delta': trial.suggest_float('delta', 0.01, 0.5),  # Noise regularization weight
            'noise_scale': trial.suggest_float('noise_scale', 0.05, 0.3)  # Variance-based noise scale
        }
        
        # Cross-validation
        skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
        cv_scores = []
        
        # Create noise injector
        noise_injector = LocalVarianceNoiseInjector(
            noise_scale=params['noise_scale'], 
            window_size=10
        )
        
        for train_idx, val_idx in skf.split(X_features, y_labels):
            X_train, X_val = X_features[train_idx], X_features[val_idx]
            y_train, y_val = y_labels[train_idx], y_labels[val_idx]
            train_series = [time_series_list[i] for i in train_idx if i < len(time_series_list)]
            val_series = [time_series_list[i] for i in val_idx if i < len(time_series_list)]
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_val_scaled = scaler.transform(X_val)
            
            # Create datasets with noise injection
            train_dataset = TimeSeriesDistributionDataset(
                X_train_scaled, y_train, train_series, 
                use_noise=True, noise_injector=noise_injector
            )
            val_dataset = TimeSeriesDistributionDataset(
                X_val_scaled, y_val, val_series, 
                use_noise=False, noise_injector=None
            )
            
            train_loader = DataLoader(train_dataset, batch_size=params['batch_size'], shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=params['batch_size'], shuffle=False)
            
            # Create enhanced model
            model = EnhancedDistributionVAE(
                input_dim=X_features.shape[1],
                latent_dim=params['latent_dim'],
                hidden_dims=[params['hidden_dim1'], params['hidden_dim2']],
                use_adversarial=True
            ).to(device)
            
            # Train model
            val_auc = train_enhanced_vae_model(model, train_loader, val_loader, params, n_epochs=30)
            cv_scores.append(val_auc)
            
            # Clean up
            del model
            torch.cuda.empty_cache()
        
        return np.mean(cv_scores)
    
    # Create and run study
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best AUC: {study.best_value:.4f}")
    logger.info(f"   📊 Best params: {study.best_params}")
    
    return study.best_params, study.best_value

def evaluate_enhanced_vae_comprehensive(X_features, y_labels, time_series_list, best_params, n_folds=3):
    """Comprehensive evaluation of enhanced VAE model"""
    logger.info("📊 Comprehensive Enhanced VAE evaluation...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    # Create noise injector
    noise_injector = LocalVarianceNoiseInjector(
        noise_scale=best_params['noise_scale'], 
        window_size=10
    )
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X_features, y_labels)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X_features[train_idx], X_features[val_idx]
        y_train, y_val = y_labels[train_idx], y_labels[val_idx]
        train_series = [time_series_list[i] for i in train_idx if i < len(time_series_list)]
        val_series = [time_series_list[i] for i in val_idx if i < len(time_series_list)]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # Create datasets
        train_dataset = TimeSeriesDistributionDataset(
            X_train_scaled, y_train, train_series,
            use_noise=True, noise_injector=noise_injector
        )
        val_dataset = TimeSeriesDistributionDataset(
            X_val_scaled, y_val, val_series,
            use_noise=False, noise_injector=None
        )
        
        train_loader = DataLoader(train_dataset, batch_size=best_params['batch_size'], shuffle=True)
        val_loader = DataLoader(val_dataset, batch_size=best_params['batch_size'], shuffle=False)
        
        # Create and train model
        model = EnhancedDistributionVAE(
            input_dim=X_features.shape[1],
            latent_dim=best_params['latent_dim'],
            hidden_dims=[best_params['hidden_dim1'], best_params['hidden_dim2']],
            use_adversarial=True
        ).to(device)
        
        # Train model
        train_enhanced_vae_model(model, train_loader, val_loader, best_params, n_epochs=100)
        
        # Evaluate
        model.eval()
        fold_preds = []
        fold_targets = []
        
        with torch.no_grad():
            for batch_x, batch_y, batch_x_orig in val_loader:
                batch_x, batch_y = batch_x.to(device), batch_y.to(device)
                
                _, class_pred, _, _, _, _ = model(batch_x, apply_adversarial=False)
                
                fold_preds.extend(class_pred.cpu().numpy())
                fold_targets.extend(batch_y.cpu().numpy())
        
        # Calculate metrics - convert logits to probabilities
        fold_preds_proba = torch.sigmoid(torch.tensor(fold_preds)).numpy()
        y_pred_binary = (fold_preds_proba >= 0.5).astype(int)
        
        try:
            auc = roc_auc_score(fold_targets, fold_preds_proba)
            f1 = f1_score(fold_targets, y_pred_binary)
            precision = precision_score(fold_targets, y_pred_binary, zero_division=0)
            recall = recall_score(fold_targets, y_pred_binary, zero_division=0)
            accuracy = accuracy_score(fold_targets, y_pred_binary)
            
            # Confusion matrix
            tn, fp, fn, tp = confusion_matrix(fold_targets, y_pred_binary).ravel()
            
            # Store metrics
            all_metrics['auc'].append(auc)
            all_metrics['f1'].append(f1)
            all_metrics['precision'].append(precision)
            all_metrics['recall'].append(recall)
            all_metrics['accuracy'].append(accuracy)
            all_metrics['tp'].append(tp)
            all_metrics['tn'].append(tn)
            all_metrics['fp'].append(fp)
            all_metrics['fn'].append(fn)
            
            # Store for overall analysis
            all_y_true.extend(fold_targets)
            all_y_pred_proba.extend(fold_preds_proba)
            
            logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
            
        except Exception as e:
            logger.warning(f"     Metrics calculation failed: {e}")
            # Add default values
            for metric in ['auc', 'f1', 'precision', 'recall', 'accuracy']:
                all_metrics[metric].append(0.5 if metric == 'auc' else 0.0)
            for metric in ['tp', 'tn', 'fp', 'fn']:
                all_metrics[metric].append(0)
        
        # Clean up
        del model
        torch.cuda.empty_cache()
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    if len(all_y_pred_proba) > 0:
        all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
        overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    else:
        overall_cm = np.array([[0, 0], [0, 0]])
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba)
    }

def main():
    """Main execution function"""
    logger.info("🚀 Starting Enhanced VAE with Adaptive Noise and Adversarial Training...")
    
    # Load and prepare data
    time_series_list, labels = load_and_prepare_data()
    
    # Extract distribution features
    X_features = extract_distribution_features(time_series_list, n_bins=50)
    y_labels = np.array(labels[:len(X_features)])
    
    logger.info(f"📊 Final dataset: {X_features.shape[0]} samples, {X_features.shape[1]} features")
    
    # Optimize hyperparameters
    best_params, best_auc = optimize_enhanced_vae_hyperparameters(
        X_features, y_labels, time_series_list, n_trials=20, n_folds=3
    )
    
    # Comprehensive evaluation
    results = evaluate_enhanced_vae_comprehensive(
        X_features, y_labels, time_series_list, best_params, n_folds=3
    )
    
    # Print final results
    logger.info("\n🎉 ENHANCED VAE ANALYSIS COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Save results
    import joblib
    final_results = {
        'best_params': best_params,
        'evaluation_results': results,
        'feature_shape': X_features.shape
    }
    joblib.dump(final_results, 'enhanced_vae_autoencoder_results.joblib')
    logger.info("💾 Results saved to 'enhanced_vae_autoencoder_results.joblib'")
    
    return final_results

if __name__ == "__main__":
    results = main()