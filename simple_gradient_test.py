"""
Simple test to validate gradient-based learning
"""

import sys
import os

# Add current directory to path
sys.path.append(os.getcwd())

try:
    import torch
    import torch.nn as nn
    import numpy as np
    from gradient_based_branch_classifier import GradientBranchClassifier
    
    def test_gradient_learning():
        """Test basic gradient learning functionality"""
        
        print("🧠 Testing Gradient-Based Learning")
        print("=" * 40)
        
        # Initialize classifier
        classifier = GradientBranchClassifier(
            data_dim=100,
            learning_rate=0.01,
            batch_size=16
        )
        
        print("✅ Classifier initialized")
        print(f"   Device: {classifier.device}")
        print(f"   Training mode: {classifier.training_mode}")
        
        # Test 1: Basic processing without learning
        print("\n📊 Test 1: Basic Processing")
        test_data = np.random.randn(100)
        result = classifier.process_new_part(test_data)
        
        print(f"   Decision: {result['decision']}")
        print(f"   Branch ID: {result['branch_id']}")
        print(f"   Confidence: {result['confidence']:.3f}")
        print(f"   Step: {result['step']}")
        
        # Test 2: Learning with ground truth
        print("\n🎯 Test 2: Learning with Ground Truth")
        
        # Create simple learning scenario
        learning_results = []
        
        for i in range(20):
            # Generate data with pattern
            if i < 5:
                # Pattern A: sine wave
                data = np.sin(np.linspace(0, 2*np.pi, 100)) + np.random.randn(100) * 0.1
                ground_truth = {'action': 0 if i == 0 else 1}  # NEW then CONTINUE
            elif i < 10:
                # Pattern B: cosine wave (structural break)
                data = np.cos(np.linspace(0, 2*np.pi, 100)) + np.random.randn(100) * 0.1
                ground_truth = {'action': 2 if i == 5 else 1}  # BREAK then CONTINUE
            else:
                # Pattern C: random (novel)
                data = np.random.randn(100)
                ground_truth = {'action': 0}  # NEW_BRANCH
            
            # Process with learning
            result = classifier.process_new_part(data, ground_truth)
            
            # Track learning
            predicted_action = {'NEW_BRANCH': 0, 'CONTINUE_BRANCH': 1, 'STRUCTURAL_BREAK': 2}[result['decision']]
            correct = predicted_action == ground_truth['action']
            
            learning_results.append({
                'step': i,
                'predicted': predicted_action,
                'actual': ground_truth['action'],
                'correct': correct,
                'confidence': result['confidence']
            })
            
            if i % 5 == 4:
                recent_accuracy = np.mean([r['correct'] for r in learning_results[-5:]])
                recent_confidence = np.mean([r['confidence'] for r in learning_results[-5:]])
                print(f"   Steps {i-4:2d}-{i:2d}: Accuracy={recent_accuracy:.3f}, "
                      f"Confidence={recent_confidence:.3f}")
        
        # Test 3: Validation (inference mode)
        print("\n🔍 Test 3: Validation Mode")
        classifier.set_training_mode(False)
        
        validation_results = []
        for i in range(10):
            # Test similar patterns to training
            if i < 3:
                data = np.sin(np.linspace(0, 2*np.pi, 100)) + np.random.randn(100) * 0.1
                expected = 1  # Should continue sine pattern
            elif i < 6:
                data = np.cos(np.linspace(0, 2*np.pi, 100)) + np.random.randn(100) * 0.1
                expected = 2  # Should detect as break
            else:
                data = np.random.randn(100)
                expected = 0  # Should be new branch
            
            result = classifier.process_new_part(data)
            predicted = {'NEW_BRANCH': 0, 'CONTINUE_BRANCH': 1, 'STRUCTURAL_BREAK': 2}[result['decision']]
            
            validation_results.append({
                'predicted': predicted,
                'expected': expected,
                'correct': predicted == expected,
                'confidence': result['confidence']
            })
        
        val_accuracy = np.mean([r['correct'] for r in validation_results])
        val_confidence = np.mean([r['confidence'] for r in validation_results])
        
        print(f"   Validation Accuracy: {val_accuracy:.3f}")
        print(f"   Validation Confidence: {val_confidence:.3f}")
        
        # Test 4: Check gradient flow
        print("\n⚡ Test 4: Gradient Flow Check")
        
        # Check if parameters have gradients
        classifier.set_training_mode(True)
        
        # Process one sample with ground truth
        test_data = np.random.randn(100)
        ground_truth = {'action': 1}
        result = classifier.process_new_part(test_data, ground_truth)
        
        # Check if any parameters have gradients
        has_gradients = False
        total_params = 0
        params_with_grad = 0
        
        for name, param in classifier.state_encoder.named_parameters():
            total_params += 1
            if param.grad is not None:
                has_gradients = True
                params_with_grad += 1
        
        print(f"   Total parameters: {total_params}")
        print(f"   Parameters with gradients: {params_with_grad}")
        print(f"   Gradient flow: {'✅ YES' if has_gradients else '❌ NO'}")
        
        # Summary
        print("\n🏆 LEARNING TEST SUMMARY")
        print("=" * 40)
        
        final_training_accuracy = np.mean([r['correct'] for r in learning_results])
        print(f"Training Accuracy: {final_training_accuracy:.3f}")
        print(f"Validation Accuracy: {val_accuracy:.3f}")
        print(f"Gradient Flow: {'✅ Working' if has_gradients else '❌ Not working'}")
        print(f"Experience Buffer: {len(classifier.experience_buffer)} samples")
        
        # Determine success
        if final_training_accuracy > 0.6 and has_gradients:
            print("🎉 SUCCESS: Model is learning with gradient flow!")
        elif final_training_accuracy > 0.4:
            print("⚠️  PARTIAL: Some learning detected, needs improvement")
        else:
            print("❌ FAILED: No significant learning detected")
        
        return {
            'training_accuracy': final_training_accuracy,
            'validation_accuracy': val_accuracy,
            'gradient_flow': has_gradients,
            'buffer_size': len(classifier.experience_buffer)
        }
    
    if __name__ == "__main__":
        results = test_gradient_learning()
        
except ImportError as e:
    print(f"❌ Import Error: {e}")
    print("Required packages: torch, numpy")
    print("Please install with: pip install torch numpy")
    
except Exception as e:
    print(f"❌ Error during testing: {e}")
    import traceback
    traceback.print_exc()