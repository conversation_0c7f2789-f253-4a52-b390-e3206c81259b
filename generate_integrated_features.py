#!/usr/bin/env python3
"""
Generate features using IntegratedTSFreshThermoPipeline and save them
"""

import pandas as pd
import numpy as np
import joblib
import time
from resources.integrated_pipeline import IntegratedTSFreshThermoPipeline
import sys
sys.path.append('/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources')
def load_data():
    """Load training data"""
    print("Loading data...")
    X_train = pd.read_parquet('X_train.parquet')
    y_train = pd.read_parquet('y_train.parquet')
    
    print(f"✓ Loaded {len(X_train)} time series")
    return X_train, y_train

def extract_and_save_features():
    """Extract features using integrated pipeline and save"""
    
    # Load data
    X_train, y_train = load_data()
    
    # Convert to list format for pipeline
    time_series_list = []
    tstar_list = []
    
    for idx in X_train.index:
        series = X_train.loc[idx].values
        tstar = y_train.loc[idx, 'tstar'] if 'tstar' in y_train.columns else None
        
        time_series_list.append(series)
        tstar_list.append(tstar)
    
    print(f"Prepared {len(time_series_list)} series for feature extraction")
    
    # Initialize pipeline
    print("Initializing IntegratedTSFreshThermoPipeline...")
    pipeline = IntegratedTSFreshThermoPipeline(n_jobs=-1, use_full_features=True)
    
    # Extract features
    print("Extracting integrated features...")
    start_time = time.time()
    
    features_df = pipeline.extract_features_batch(time_series_list, tstar_list)
    
    extraction_time = time.time() - start_time
    
    print(f"✓ Feature extraction completed in {extraction_time:.2f}s")
    print(f"✓ Features shape: {features_df.shape}")
    
    # Analyze feature types
    tsfresh_features = [col for col in features_df.columns if col.startswith('tsfresh_')]
    thermo_features = [col for col in features_df.columns if col.startswith('thermo_')]
    other_features = [col for col in features_df.columns if not col.startswith(('tsfresh_', 'thermo_'))]
    
    print(f"\n📊 Feature breakdown:")
    print(f"  - TSFresh: {len(tsfresh_features)}")
    print(f"  - Thermodynamics: {len(thermo_features)}")
    print(f"  - Other: {len(other_features)}")
    
    # Save features
    output_file = 'integrated_features.parquet'
    print(f"Saving features to {output_file}...")
    features_df.to_parquet(output_file)
    
    # Save metadata
    metadata = {
        'n_samples': len(features_df),
        'n_features': features_df.shape[1],
        'tsfresh_features': len(tsfresh_features),
        'thermo_features': len(thermo_features),
        'extraction_time': extraction_time,
        'feature_names': {
            'tsfresh': tsfresh_features,
            'thermo': thermo_features,
            'other': other_features
        }
    }
    
    joblib.dump(metadata, 'integrated_features_metadata.joblib')
    
    print(f"✓ Features saved to {output_file}")
    print(f"✓ Metadata saved to integrated_features_metadata.joblib")
    
    return features_df

if __name__ == "__main__":
    features_df = extract_and_save_features()
    print("\n🎉 Feature generation completed!")