#!/usr/bin/env python3
"""
TabICL Test V2 - Using Real TabICL Library WRONG PARAMETERS!
In-Context Learning with pre-trained TabICL model
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score, confusion_matrix
import logging
from typing import Dict, List, Tuple
import warnings
from tqdm import tqdm
import random

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
# Import TabICL model
try:
    import torch
    from tabicl import TabICL
    TABICL_AVAILABLE = True
    logger.info("✅ TabICL model imported successfully")
except ImportError as e:
    TABICL_AVAILABLE = False
    logger.error(f"❌ TabICL library not available: {e}")
    logger.error("Install with: pip install tabicl")

warnings.filterwarnings('ignore')

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TabICLEvaluator:
    """TabICL evaluator using the real TabICL model"""
    
    def __init__(self, context_size=32, device='cpu'):
        if not TABICL_AVAILABLE:
            raise ImportError("TabICL library not available. Install with: pip install tabicl")
        
        self.context_size = context_size
        self.device = device
        
        # Initialize TabICL model with proper parameters
        logger.info(f"🔧 Initializing TabICL model...")
        self.model = TabICL(
            max_classes=2,          # Binary classification
            embed_dim=128,          # Model dimension
            col_num_blocks=3,       # Column embedding blocks
            col_nhead=4,            # Column attention heads
            col_num_inds=128,       # Column inducing points
            row_num_blocks=3,       # Row interaction blocks
            row_nhead=8,            # Row attention heads
            row_num_cls=4,          # CLS tokens per row
            icl_num_blocks=12,      # In-context learning blocks
            icl_nhead=4,            # ICL attention heads
            dropout=0.1,            # Dropout rate
            activation="gelu"       # Activation function
        ).to(device)
        
    def select_balanced_context(self, X_train, y_train, context_size):
        """Select balanced context examples"""
        pos_indices = np.where(y_train == 1)[0]
        neg_indices = np.where(y_train == 0)[0]
        
        # Balance the context
        n_pos = min(context_size // 2, len(pos_indices))
        n_neg = min(context_size - n_pos, len(neg_indices))
        
        if len(pos_indices) > 0:
            selected_pos = np.random.choice(pos_indices, n_pos, replace=False)
        else:
            selected_pos = []
            
        if len(neg_indices) > 0:
            selected_neg = np.random.choice(neg_indices, n_neg, replace=False)
        else:
            selected_neg = []
        
        context_indices = np.concatenate([selected_pos, selected_neg])
        np.random.shuffle(context_indices)
        
        return context_indices
    
    def prepare_tabicl_data(self, X_context, y_context, X_queries):
        """Prepare data in TabICL format"""
        # Convert to DataFrame format expected by TabICL
        context_df = pd.DataFrame(X_context)
        context_df['label'] = y_context
        
        queries_df = pd.DataFrame(X_queries)
        
        return context_df, queries_df
    
    def predict_batch(self, X_train, y_train, X_test, batch_size=32):
        """Make predictions using TabICL with batching"""
        predictions = []
        
        # Process in batches to manage memory
        for i in tqdm(range(0, len(X_test), batch_size), desc="TabICL prediction"):
            batch_end = min(i + batch_size, len(X_test))
            X_batch = X_test[i:batch_end]
            
            # Select context for this batch
            context_indices = self.select_balanced_context(X_train, y_train, self.context_size)
            X_context = X_train[context_indices]
            y_context = y_train[context_indices]
            
            # Prepare data for TabICL
            context_df, queries_df = self.prepare_tabicl_data(X_context, y_context, X_batch)
            
            try:
                # Use TabICL for prediction
                batch_predictions = self.classifier.predict(
                    context_examples=context_df.drop('label', axis=1),
                    context_labels=context_df['label'],
                    query_examples=queries_df,
                    return_proba=True
                )
                
                # Extract probabilities for positive class
                if isinstance(batch_predictions, tuple):
                    batch_proba = batch_predictions[1][:, 1]  # Probability of class 1
                else:
                    batch_proba = batch_predictions
                
                predictions.extend(batch_proba)
                
            except Exception as e:
                logger.warning(f"TabICL prediction failed for batch {i}: {e}")
                # Fallback to random predictions
                batch_proba = np.random.random(len(X_batch)) * 0.1 + 0.45  # Around 0.5
                predictions.extend(batch_proba)
        
        return np.array(predictions)

def load_data():
    """Load prepared features and labels"""
    logger.info("📊 Loading prepared features and labels...")
    
    try:
        # Load features
        features_path = "/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/resources/processed_data/prepared_features.parquet"
        X = pd.read_parquet(features_path)
        logger.info(f"   ✅ Features loaded: {X.shape}")
        
        # Load labels
        y_data = pd.read_parquet('y_train.parquet')
        y = y_data.iloc[:, 0].values if len(y_data.columns) > 0 else y_data.values
        y = y.astype(int)
        logger.info(f"   ✅ Labels loaded: {len(y)}")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X.values, y
        
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def evaluate_tabicl_cv(X, y, context_size=32, n_folds=5):
    """5-fold cross-validation evaluation of real TabICL"""
    logger.info(f"📊 Real TabICL 5-fold CV evaluation (context_size={context_size})...")
    
    if not TABICL_AVAILABLE:
        logger.error("❌ TabICL library not available")
        return None
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    all_metrics = {
        'auc': [], 'f1': [], 'precision': [], 'recall': [], 'accuracy': [],
        'tp': [], 'tn': [], 'fp': [], 'fn': []
    }
    
    all_y_true = []
    all_y_pred_proba = []
    
    # Initialize TabICL evaluator
    evaluator = TabICLEvaluator(context_size=context_size, model_name='gpt2', device='cpu')
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X[train_idx], X[val_idx]
        y_train, y_val = y[train_idx], y[val_idx]
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_val_scaled = scaler.transform(X_val)
        
        # TabICL prediction (no training needed!)
        logger.info(f"     Making TabICL predictions...")
        y_pred_proba = evaluator.predict_batch(X_train_scaled, y_train, X_val_scaled, batch_size=16)
        
        # Handle any NaN predictions
        if np.isnan(y_pred_proba).any():
            logger.warning(f"     Found NaN predictions, replacing with 0.5")
            y_pred_proba = np.nan_to_num(y_pred_proba, nan=0.5)
        
        # Ensure probabilities are in valid range
        y_pred_proba = np.clip(y_pred_proba, 1e-7, 1-1e-7)
        y_pred_binary = (y_pred_proba >= 0.5).astype(int)
        
        # Calculate metrics
        try:
            auc = roc_auc_score(y_val, y_pred_proba)
            f1 = f1_score(y_val, y_pred_binary)
            precision = precision_score(y_val, y_pred_binary, zero_division=0)
            recall = recall_score(y_val, y_pred_binary)
            accuracy = accuracy_score(y_val, y_pred_binary)
            
            # Confusion matrix
            tn, fp, fn, tp = confusion_matrix(y_val, y_pred_binary).ravel()
            
            # Store metrics
            all_metrics['auc'].append(auc)
            all_metrics['f1'].append(f1)
            all_metrics['precision'].append(precision)
            all_metrics['recall'].append(recall)
            all_metrics['accuracy'].append(accuracy)
            all_metrics['tp'].append(tp)
            all_metrics['tn'].append(tn)
            all_metrics['fp'].append(fp)
            all_metrics['fn'].append(fn)
            
            # Store for overall analysis
            all_y_true.extend(y_val)
            all_y_pred_proba.extend(y_pred_proba)
            
            logger.info(f"     AUC: {auc:.4f}, F1: {f1:.4f}, Precision: {precision:.4f}, Recall: {recall:.4f}")
            
        except Exception as e:
            logger.error(f"     Error calculating metrics for fold {fold+1}: {e}")
            # Add default values
            for metric in all_metrics.keys():
                all_metrics[metric].append(0.5 if metric == 'auc' else 0)
    
    # Calculate mean metrics
    mean_metrics = {metric: np.mean(values) for metric, values in all_metrics.items()}
    std_metrics = {metric: np.std(values) for metric, values in all_metrics.items()}
    
    # Overall confusion matrix
    if len(all_y_true) > 0 and len(all_y_pred_proba) > 0:
        all_y_pred_binary = (np.array(all_y_pred_proba) >= 0.5).astype(int)
        overall_cm = confusion_matrix(all_y_true, all_y_pred_binary)
    else:
        overall_cm = np.array([[0, 0], [0, 0]])
    
    return {
        'mean_metrics': mean_metrics,
        'std_metrics': std_metrics,
        'confusion_matrix': overall_cm,
        'all_predictions': (all_y_true, all_y_pred_proba),
        'fold_metrics': all_metrics
    }

def test_tabicl_installation():
    """Test if TabICL is properly installed and working"""
    logger.info("🧪 Testing TabICL installation...")
    
    if not TABICL_AVAILABLE:
        logger.error("❌ TabICL not available")
        logger.info("💡 Install with: pip install tabicl")
        return False
    
    try:
        # Create small test data
        X_context = np.random.randn(10, 5)
        y_context = np.random.randint(0, 2, 10)
        X_query = np.random.randn(3, 5)
        
        # Test TabICL
        classifier = TabICLClassifier(model_name='gpt2', device='cpu')
        
        context_df = pd.DataFrame(X_context)
        query_df = pd.DataFrame(X_query)
        
        predictions = classifier.predict(
            context_examples=context_df,
            context_labels=y_context,
            query_examples=query_df,
            return_proba=True
        )
        
        logger.info("✅ TabICL installation test passed!")
        logger.info(f"   Test predictions shape: {np.array(predictions).shape}")
        return True
        
    except Exception as e:
        logger.error(f"❌ TabICL test failed: {e}")
        return False

def main():
    """Main execution function"""
    logger.info("🚀 Starting Real TabICL Structural Break Detection...")
    
    # Test TabICL installation first
    if not test_tabicl_installation():
        logger.error("❌ TabICL installation test failed. Cannot proceed.")
        return None
    
    # Load data
    X, y = load_data()
    
    # # Reduce dataset size for faster testing (optional)
    # if len(X) > 2000:
    #     logger.info(f"🔄 Reducing dataset size for faster testing...")
    #     indices = np.random.choice(len(X), 2000, replace=False)
    #     X, y = X[indices], y[indices]
    #     logger.info(f"   Using {len(X)} samples for evaluation")
    
    # Run 5-fold CV evaluation
    results = evaluate_tabicl_cv(X, y, context_size=32, n_folds=5)
    
    if results is None:
        logger.error("❌ Evaluation failed")
        return None
    
    # Print final results
    logger.info("\n🎉 REAL TABICL EVALUATION COMPLETED!")
    logger.info("=" * 60)
    logger.info(f"📊 FINAL RESULTS:")
    logger.info(f"   ROC AUC:    {results['mean_metrics']['auc']:.4f} ± {results['std_metrics']['auc']:.4f}")
    logger.info(f"   F1 Score:   {results['mean_metrics']['f1']:.4f} ± {results['std_metrics']['f1']:.4f}")
    logger.info(f"   Precision:  {results['mean_metrics']['precision']:.4f} ± {results['std_metrics']['precision']:.4f}")
    logger.info(f"   Recall:     {results['mean_metrics']['recall']:.4f} ± {results['std_metrics']['recall']:.4f}")
    logger.info(f"   Accuracy:   {results['mean_metrics']['accuracy']:.4f} ± {results['std_metrics']['accuracy']:.4f}")
    
    logger.info(f"\n🎯 CONFUSION MATRIX:")
    cm = results['confusion_matrix']
    logger.info(f"   True Negatives:  {cm[0,0]:5d} | False Positives: {cm[0,1]:5d}")
    logger.info(f"   False Negatives: {cm[1,0]:5d} | True Positives:  {cm[1,1]:5d}")
    
    # Calculate rates
    total_positives = cm[1,0] + cm[1,1]
    total_negatives = cm[0,0] + cm[0,1]
    
    if total_positives > 0 and total_negatives > 0:
        logger.info(f"\n📈 DETAILED METRICS:")
        logger.info(f"   True Positive Rate (Recall):  {cm[1,1]/total_positives:.4f}")
        logger.info(f"   True Negative Rate:           {cm[0,0]/total_negatives:.4f}")
        logger.info(f"   False Positive Rate:          {cm[0,1]/total_negatives:.4f}")
        logger.info(f"   False Negative Rate:          {cm[1,0]/total_positives:.4f}")
    
    # Save results
    import joblib
    joblib.dump(results, 'tabicl_results.joblib')
    logger.info("💾 Results saved to 'tabicl_results.joblib'")
    
    return results

if __name__ == "__main__":
    results = main()