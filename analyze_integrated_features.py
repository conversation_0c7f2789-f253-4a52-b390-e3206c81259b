#!/usr/bin/env python3
"""
Analyze feature importance from integrated pipeline for AUCROC performance
Focus on TSFresh 1280 + Thermodynamics features
"""

import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
from sklearn.feature_selection import mutual_info_classif
from sklearn.model_selection import cross_val_score
from sklearn.metrics import roc_auc_score
import matplotlib.pyplot as plt
import seaborn as sns
import warnings
warnings.filterwarnings('ignore')

def load_and_analyze_features():
    """Load processed features and analyze importance for AUCROC"""
    
    print("=" * 60)
    print("INTEGRATED PIPELINE FEATURE IMPORTANCE ANALYSIS")
    print("=" * 60)
    
    # Load processed features
    print("Loading processed features...")
    try:
        features_df = pd.read_parquet('resources/processed_data/prepared_features.parquet')
        print(f"✓ Loaded features: {features_df.shape}")
    except Exception as e:
        print(f"Error loading features: {e}")
        return None
    
    # Load labels
    try:
        import joblib
        labels = joblib.load('resources/processed_data/labels.joblib')
        print(f"✓ Loaded labels: {len(labels)} samples")
    except Exception as e:
        print(f"Error loading labels: {e}")
        return None
    
    # Ensure alignment
    if len(features_df) != len(labels):
        min_len = min(len(features_df), len(labels))
        features_df = features_df.iloc[:min_len]
        labels = labels[:min_len]
        print(f"⚠️  Aligned to {min_len} samples")
    
    # Categorize features by source
    feature_categories = {
        'tsfresh': [col for col in features_df.columns if col.startswith('tsfresh_')],
        'thermo': [col for col in features_df.columns if col.startswith('thermo_')],
        'other': [col for col in features_df.columns if not col.startswith(('tsfresh_', 'thermo_'))]
    }
    
    print(f"\n📊 FEATURE BREAKDOWN:")
    for category, features in feature_categories.items():
        print(f"  - {category.upper()}: {len(features)} features")
    
    return features_df, labels, feature_categories

def analyze_feature_importance(features_df, labels, feature_categories):
    """Analyze feature importance using multiple methods"""
    
    print(f"\n🔍 FEATURE IMPORTANCE ANALYSIS:")
    
    # Remove any infinite or NaN values
    features_clean = features_df.fillna(0)
    features_clean = features_clean.replace([np.inf, -np.inf], 0)
    
    results = {}
    
    # 1. Random Forest Feature Importance
    print("  Computing Random Forest importance...")
    rf = RandomForestClassifier(n_estimators=100, random_state=42, n_jobs=-1)
    rf.fit(features_clean, labels)
    rf_importance = pd.Series(rf.feature_importances_, index=features_clean.columns)
    results['rf_importance'] = rf_importance
    
    # 2. Mutual Information
    print("  Computing Mutual Information...")
    mi_scores = mutual_info_classif(features_clean, labels, random_state=42)
    mi_importance = pd.Series(mi_scores, index=features_clean.columns)
    results['mi_importance'] = mi_importance
    
    # 3. Category-wise analysis
    print("  Analyzing by feature category...")
    category_performance = {}
    
    for category, feature_list in feature_categories.items():
        if len(feature_list) == 0:
            continue
            
        # Select features for this category
        category_features = features_clean[feature_list]
        
        # Cross-validation AUCROC
        rf_cat = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
        cv_scores = cross_val_score(rf_cat, category_features, labels, 
                                  cv=5, scoring='roc_auc', n_jobs=-1)
        
        category_performance[category] = {
            'mean_aucroc': cv_scores.mean(),
            'std_aucroc': cv_scores.std(),
            'n_features': len(feature_list)
        }
    
    results['category_performance'] = category_performance
    
    return results

def analyze_top_features(results, feature_categories, top_n=20):
    """Analyze top performing features"""
    
    print(f"\n🏆 TOP {top_n} FEATURES ANALYSIS:")
    
    # Get top features by Random Forest importance
    top_rf = results['rf_importance'].nlargest(top_n)
    print(f"\n📈 TOP {top_n} BY RANDOM FOREST IMPORTANCE:")
    
    # Categorize top features
    top_by_category = {'tsfresh': 0, 'thermo': 0, 'other': 0}
    
    for i, (feature, importance) in enumerate(top_rf.items(), 1):
        category = 'other'
        if feature.startswith('tsfresh_'):
            category = 'tsfresh'
        elif feature.startswith('thermo_'):
            category = 'thermo'
        
        top_by_category[category] += 1
        
        # Clean feature name for display
        clean_name = feature.replace('tsfresh_', '').replace('thermo_', '')
        print(f"  {i:2d}. [{category.upper():7s}] {clean_name[:50]:<50} ({importance:.4f})")
    
    print(f"\n📊 TOP {top_n} FEATURE DISTRIBUTION:")
    for category, count in top_by_category.items():
        percentage = (count / top_n) * 100
        print(f"  - {category.upper()}: {count}/{top_n} ({percentage:.1f}%)")
    
    # Get top features by Mutual Information
    top_mi = results['mi_importance'].nlargest(top_n)
    print(f"\n📈 TOP {top_n} BY MUTUAL INFORMATION:")
    
    top_mi_by_category = {'tsfresh': 0, 'thermo': 0, 'other': 0}
    
    for i, (feature, mi_score) in enumerate(top_mi.items(), 1):
        category = 'other'
        if feature.startswith('tsfresh_'):
            category = 'tsfresh'
        elif feature.startswith('thermo_'):
            category = 'thermo'
        
        top_mi_by_category[category] += 1
        
        clean_name = feature.replace('tsfresh_', '').replace('thermo_', '')
        print(f"  {i:2d}. [{category.upper():7s}] {clean_name[:50]:<50} ({mi_score:.4f})")
    
    print(f"\n📊 TOP {top_n} MI FEATURE DISTRIBUTION:")
    for category, count in top_mi_by_category.items():
        percentage = (count / top_n) * 100
        print(f"  - {category.upper()}: {count}/{top_n} ({percentage:.1f}%)")
    
    return top_rf, top_mi, top_by_category, top_mi_by_category

def analyze_category_performance(results):
    """Analyze performance by feature category"""
    
    print(f"\n🎯 CATEGORY PERFORMANCE ANALYSIS:")
    
    category_perf = results['category_performance']
    
    # Sort by mean AUCROC
    sorted_categories = sorted(category_perf.items(), 
                             key=lambda x: x[1]['mean_aucroc'], 
                             reverse=True)
    
    print(f"\n📊 AUCROC PERFORMANCE BY CATEGORY:")
    print(f"{'Category':<12} {'Mean AUCROC':<12} {'Std':<8} {'Features':<10} {'AUCROC/Feature':<15}")
    print("-" * 65)
    
    for category, perf in sorted_categories:
        aucroc_per_feature = perf['mean_aucroc'] / perf['n_features'] if perf['n_features'] > 0 else 0
        print(f"{category.upper():<12} {perf['mean_aucroc']:<12.4f} {perf['std_aucroc']:<8.4f} "
              f"{perf['n_features']:<10} {aucroc_per_feature:<15.6f}")
    
    return sorted_categories

def generate_summary_report(results, feature_categories, top_rf, top_mi):
    """Generate concise summary report"""
    
    print(f"\n" + "=" * 60)
    print("INTEGRATED PIPELINE FEATURE IMPORTANCE SUMMARY")
    print("=" * 60)
    
    # Overall statistics
    total_features = sum(len(features) for features in feature_categories.values())
    category_perf = results['category_performance']
    
    print(f"\n📈 OVERALL STATISTICS:")
    print(f"  - Total features analyzed: {total_features}")
    print(f"  - TSFresh features: {len(feature_categories['tsfresh'])}")
    print(f"  - Thermodynamics features: {len(feature_categories['thermo'])}")
    print(f"  - Other features: {len(feature_categories['other'])}")
    
    # Best performing category
    best_category = max(category_perf.items(), key=lambda x: x[1]['mean_aucroc'])
    print(f"\n🏆 BEST PERFORMING CATEGORY:")
    print(f"  - {best_category[0].upper()}: {best_category[1]['mean_aucroc']:.4f} AUCROC")
    print(f"  - Features: {best_category[1]['n_features']}")
    print(f"  - AUCROC per feature: {best_category[1]['mean_aucroc']/best_category[1]['n_features']:.6f}")
    
    # Feature efficiency analysis
    print(f"\n⚡ FEATURE EFFICIENCY RANKING:")
    efficiency_ranking = []
    for category, perf in category_perf.items():
        if perf['n_features'] > 0:
            efficiency = perf['mean_aucroc'] / perf['n_features']
            efficiency_ranking.append((category, efficiency, perf['mean_aucroc'], perf['n_features']))
    
    efficiency_ranking.sort(key=lambda x: x[1], reverse=True)
    
    for i, (category, efficiency, aucroc, n_features) in enumerate(efficiency_ranking, 1):
        print(f"  {i}. {category.upper()}: {efficiency:.6f} AUCROC/feature "
              f"({aucroc:.4f} AUCROC, {n_features} features)")
    
    # Top feature insights
    print(f"\n🔍 KEY INSIGHTS:")
    
    # Count top features by category
    tsfresh_in_top = sum(1 for feat in top_rf.index[:20] if feat.startswith('tsfresh_'))
    thermo_in_top = sum(1 for feat in top_rf.index[:20] if feat.startswith('thermo_'))
    
    print(f"  - TSFresh dominance in top 20: {tsfresh_in_top}/20 ({tsfresh_in_top/20*100:.1f}%)")
    print(f"  - Thermodynamics in top 20: {thermo_in_top}/20 ({thermo_in_top/20*100:.1f}%)")
    
    # Feature type analysis
    tsfresh_types = {}
    for feat in top_rf.index[:20]:
        if feat.startswith('tsfresh_'):
            # Extract feature type
            clean_feat = feat.replace('tsfresh_', '')
            if '_whole' in clean_feat:
                feat_type = 'whole_series'
            elif '_pre' in clean_feat:
                feat_type = 'pre_break'
            elif '_post' in clean_feat:
                feat_type = 'post_break'
            elif '_bin' in clean_feat:
                feat_type = 'binned'
            else:
                feat_type = 'other'
            
            tsfresh_types[feat_type] = tsfresh_types.get(feat_type, 0) + 1
    
    if tsfresh_types:
        print(f"  - TSFresh feature types in top 20:")
        for feat_type, count in sorted(tsfresh_types.items(), key=lambda x: x[1], reverse=True):
            print(f"    • {feat_type}: {count}")
    
    # Thermodynamics insights
    thermo_types = {}
    for feat in top_rf.index[:20]:
        if feat.startswith('thermo_'):
            clean_feat = feat.replace('thermo_', '')
            if 'energy' in clean_feat:
                feat_type = 'energy'
            elif 'spike' in clean_feat:
                feat_type = 'spike'
            elif 'entropy' in clean_feat:
                feat_type = 'entropy'
            elif 'ratio' in clean_feat:
                feat_type = 'ratio'
            else:
                feat_type = 'other'
            
            thermo_types[feat_type] = thermo_types.get(feat_type, 0) + 1
    
    if thermo_types:
        print(f"  - Thermodynamics feature types in top 20:")
        for feat_type, count in sorted(thermo_types.items(), key=lambda x: x[1], reverse=True):
            print(f"    • {feat_type}: {count}")
    
    print(f"\n💡 RECOMMENDATIONS:")
    
    if best_category[0] == 'tsfresh':
        print(f"  - TSFresh features are most valuable for AUCROC performance")
        print(f"  - Focus on TSFresh feature engineering and selection")
    elif best_category[0] == 'thermo':
        print(f"  - Thermodynamics features show superior performance")
        print(f"  - Prioritize thermodynamics-based feature development")
    
    # Efficiency recommendations
    most_efficient = efficiency_ranking[0]
    print(f"  - {most_efficient[0].upper()} features are most efficient")
    print(f"  - Consider feature selection to optimize model complexity")
    
    if tsfresh_in_top > 15:
        print(f"  - Strong TSFresh dominance suggests comprehensive time series analysis")
    elif thermo_in_top > 10:
        print(f"  - Significant thermodynamics contribution indicates physical insights matter")

def main():
    """Main analysis function"""
    
    # Load data
    data = load_and_analyze_features()
    if data is None:
        return
    
    features_df, labels, feature_categories = data
    
    # Analyze feature importance
    results = analyze_feature_importance(features_df, labels, feature_categories)
    
    # Analyze top features
    top_rf, top_mi, top_by_category, top_mi_by_category = analyze_top_features(
        results, feature_categories, top_n=20
    )
    
    # Analyze category performance
    sorted_categories = analyze_category_performance(results)
    
    # Generate summary report
    generate_summary_report(results, feature_categories, top_rf, top_mi)
    
    print(f"\n" + "=" * 60)
    print("ANALYSIS COMPLETED!")
    print("=" * 60)

if __name__ == "__main__":
    main()