#!/usr/bin/env python3
"""
Debug script to check what features are being produced by Thermodynamics_truncated.py
"""

import numpy as np
from Thermodynamics_truncated import (
    AdaptiveTruncationDetector, 
    TruncationAwareThermodynamicAnalyzer,
    analyze_truncation_in_series_list
)

def test_basic_detector():
    """Test the basic truncation detector"""
    print("🔍 Testing Basic AdaptiveTruncationDetector")
    print("-" * 50)
    
    np.random.seed(42)
    
    # Create test data
    clean_ts = np.random.normal(0, 1, 200)
    truncated_ts = np.random.normal(0, 1, 200)
    truncated_ts = np.clip(truncated_ts, -0.5, 0.5)
    
    detector = AdaptiveTruncationDetector()
    
    # Test without advanced features
    print("Without advanced features:")
    basic_results = detector.compute_truncation_confidence(clean_ts, include_advanced_features=False)
    print(f"  Keys returned: {len(basic_results)}")
    print(f"  Keys: {list(basic_results.keys())}")
    
    # Test with advanced features
    print("\nWith advanced features:")
    advanced_results = detector.compute_truncation_confidence(clean_ts, include_advanced_features=True)
    print(f"  Keys returned: {len(advanced_results)}")
    print(f"  Sample advanced keys: {[k for k in advanced_results.keys() if k not in basic_results.keys()][:10]}")
    
    return basic_results, advanced_results

def test_thermodynamic_analyzer():
    """Test the thermodynamic analyzer"""
    print("\n🌡️  Testing TruncationAwareThermodynamicAnalyzer")
    print("-" * 50)
    
    np.random.seed(42)
    
    # Create test data with structural break
    pre_break = np.random.normal(0, 1, 100)
    post_break = np.random.normal(0.5, 1.5, 100)
    full_series = np.concatenate([pre_break, post_break])
    
    analyzer = TruncationAwareThermodynamicAnalyzer()
    
    # Test without advanced features
    print("Without advanced features:")
    basic_results = analyzer.analyze_time_series(full_series, tstar=100, include_advanced_features=False)
    print(f"  Keys returned: {len(basic_results)}")
    print(f"  Has pre/post features: {'pre_truncation_confidence' in basic_results}")
    
    # Test with advanced features
    print("\nWith advanced features:")
    advanced_results = analyzer.analyze_time_series(full_series, tstar=100, include_advanced_features=True)
    print(f"  Keys returned: {len(advanced_results)}")
    print(f"  Advanced keys present: {any('boundary_concentration_ratio' in k for k in advanced_results.keys())}")
    print(f"  Sample advanced keys: {[k for k in advanced_results.keys() if 'boundary_concentration' in k or 'composite_truncation' in k]}")
    
    return basic_results, advanced_results

def test_series_list_function():
    """Test the analyze_truncation_in_series_list function"""
    print("\n📊 Testing analyze_truncation_in_series_list Function")
    print("-" * 50)
    
    np.random.seed(42)
    
    # Create test data
    series_list = []
    tstar_list = []
    
    for i in range(3):
        pre_segment = np.random.normal(0, 1, 50)
        if i == 0:
            post_segment = np.random.normal(0.5, 1.2, 50)  # Clean
        elif i == 1:
            post_raw = np.random.normal(0.5, 1.2, 50)
            post_segment = np.clip(post_raw, -0.3, 0.8)  # Truncated
        else:
            post_raw = np.random.normal(0.5, 1.2, 50)
            post_segment = np.round(post_raw, 1)  # Precision limited
        
        full_series = np.concatenate([pre_segment, post_segment])
        series_list.append(full_series)
        tstar_list.append(50)
    
    # Test 1: Basic analysis
    print("Test 1: Basic analysis (no advanced features, no thermodynamic)")
    results1 = analyze_truncation_in_series_list(
        series_list=series_list,
        tstar_list=tstar_list,
        detailed_output=True,
        include_advanced_features=False,
        include_thermodynamic_features=False,
        n_jobs=1
    )
    
    if results1['detailed_results'] and 0 in results1['detailed_results']:
        sample_keys = list(results1['detailed_results'][0].keys())
        print(f"  Keys returned: {len(sample_keys)}")
        print(f"  Has pre/post: {any('pre_' in k for k in sample_keys)}")
        print(f"  Sample keys: {sample_keys[:10]}")
    
    # Test 2: With advanced features
    print("\nTest 2: With advanced features (no thermodynamic)")
    results2 = analyze_truncation_in_series_list(
        series_list=series_list,
        tstar_list=tstar_list,
        detailed_output=True,
        include_advanced_features=True,
        include_thermodynamic_features=False,
        n_jobs=1
    )
    
    if results2['detailed_results'] and 0 in results2['detailed_results']:
        sample_keys = list(results2['detailed_results'][0].keys())
        advanced_keys = [k for k in sample_keys if 'boundary_concentration' in k or 'composite_truncation' in k or 'precision_entropy' in k]
        print(f"  Keys returned: {len(sample_keys)}")
        print(f"  Advanced keys found: {len(advanced_keys)}")
        print(f"  Advanced keys: {advanced_keys}")
    
    # Test 3: With thermodynamic features
    print("\nTest 3: With thermodynamic features")
    results3 = analyze_truncation_in_series_list(
        series_list=series_list,
        tstar_list=tstar_list,
        detailed_output=True,
        include_advanced_features=True,
        include_thermodynamic_features=True,
        n_jobs=1
    )
    
    if results3['detailed_results'] and 0 in results3['detailed_results']:
        sample_keys = list(results3['detailed_results'][0].keys())
        thermo_keys = [k for k in sample_keys if 'energy' in k or 'entropy' in k or 'phase_volume' in k]
        print(f"  Keys returned: {len(sample_keys)}")
        print(f"  Thermodynamic keys found: {len(thermo_keys)}")
        print(f"  Sample thermo keys: {thermo_keys[:5]}")
    
    return results1, results2, results3

def main():
    """Run all debug tests"""
    print("🐛 Debugging Thermodynamics_truncated.py Feature Generation")
    print("=" * 70)
    
    try:
        # Test individual components
        basic_detector, advanced_detector = test_basic_detector()
        basic_thermo, advanced_thermo = test_thermodynamic_analyzer()
        results1, results2, results3 = test_series_list_function()
        
        print("\n" + "=" * 70)
        print("📋 SUMMARY")
        print("=" * 70)
        
        print(f"✅ Basic detector works: {len(basic_detector)} keys")
        print(f"✅ Advanced detector works: {len(advanced_detector)} keys")
        print(f"✅ Basic thermodynamic works: {len(basic_thermo)} keys")
        print(f"✅ Advanced thermodynamic works: {len(advanced_thermo)} keys")
        
        print(f"\n📊 Series list function:")
        print(f"  Basic analysis: {len(results1['detailed_results'][0]) if results1['detailed_results'] and 0 in results1['detailed_results'] else 'N/A'} keys")
        print(f"  Advanced analysis: {len(results2['detailed_results'][0]) if results2['detailed_results'] and 0 in results2['detailed_results'] else 'N/A'} keys")
        print(f"  Thermodynamic analysis: {len(results3['detailed_results'][0]) if results3['detailed_results'] and 0 in results3['detailed_results'] else 'N/A'} keys")
        
        # Check if advanced features are actually being generated
        if results2['detailed_results'] and 0 in results2['detailed_results']:
            advanced_present = any('boundary_concentration_ratio' in k for k in results2['detailed_results'][0].keys())
            print(f"\n🔍 Advanced features present in series analysis: {advanced_present}")
            
            if not advanced_present:
                print("❌ ISSUE: Advanced features not being generated in series list analysis")
                print("   This suggests the include_advanced_features flag is not being passed correctly")
            else:
                print("✅ Advanced features are being generated correctly")
        
    except Exception as e:
        print(f"❌ Error during debugging: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()