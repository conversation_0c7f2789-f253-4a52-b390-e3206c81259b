#!/usr/bin/env python3
"""
Test the Ratios_updated.py functionality
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
import sklearn.metrics

# Test with small dataset
np.random.seed(42)

# Generate test data
n_series = 100
series_length = 200

test_series = []
test_tstars = []
test_labels = []

for i in range(n_series):
    if i % 2 == 0:
        # Series with structural break
        tstar = np.random.randint(50, 150)
        pre_series = np.random.normal(0, 1, tstar)
        post_series = np.random.normal(2, 1.5, series_length - tstar)
        series = np.concatenate([pre_series, post_series])
        label = 1
    else:
        # Normal series without break
        series = np.random.normal(0, 1, series_length)
        tstar = series_length // 2
        label = 0
    
    test_series.append(series)
    test_tstars.append(tstar)
    test_labels.append(label)

print(f"Generated {len(test_series)} test series")

# Import the functions from Ratios_updated.py
import sys
sys.path.append('.')

# Import the extract_features function
from Ratios_updated import extract_features

# Extract features
print("Extracting features...")
features_df = extract_features(
    series_list=test_series,
    split_points=test_tstars,
    compute_pre_post=True,
    normalize=False,
    tstar=None,
    weight_type='proportion',
    sigma=0.1,
    compute_jsd=False,
    n_processes=1  # Use sequential processing
)

print(f"Features extracted: {features_df.shape}")
print(f"Feature columns: {list(features_df.columns)[:10]}...")

# Handle missing values
features_df.fillna(features_df.median(), inplace=True)

# Train model
model = RandomForestClassifier(n_estimators=50, random_state=42, n_jobs=-1)
model.fit(features_df, test_labels)

# Predict
pred_proba = model.predict_proba(features_df)[:, 1]

# Evaluate
auroc = sklearn.metrics.roc_auc_score(test_labels, pred_proba)
print(f"Test AUROC: {auroc:.4f}")

print("✅ Test completed successfully!")