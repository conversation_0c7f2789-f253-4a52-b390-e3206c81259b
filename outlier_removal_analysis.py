#!/usr/bin/env python3
"""
Outlier Removal Analysis
Test different outlier removal thresholds and their effect on AUROC performance
"""

import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

class OutlierProcessor:
    @staticmethod
    def modified_z_score_winsorize(series, threshold=3.5):
        """Modified Z-score using median absolute deviation"""
        series = np.array(series)
        median_val = np.median(series)
        mad = np.median(np.abs(series - median_val))
        
        if mad == 0:
            return series
        
        modified_z_scores = 0.6745 * (series - median_val) / mad
        outlier_mask = np.abs(modified_z_scores) > threshold
        
        if np.sum(outlier_mask) > 0:
            # Replace with robust percentiles
            inlier_data = series[~outlier_mask]
            if len(inlier_data) > 0:
                lower_bound = np.percentile(inlier_data, 5)
                upper_bound = np.percentile(inlier_data, 95)
                series_clean = series.copy()
                series_clean[outlier_mask & (series < median_val)] = lower_bound
                series_clean[outlier_mask & (series >= median_val)] = upper_bound
                return series_clean
        
        return series
    
    @staticmethod
    def percentile_winsorize(series, lower_pct=5, upper_pct=95):
        """Winsorize using percentiles"""
        series = np.array(series)
        lower_bound = np.percentile(series, lower_pct)
        upper_bound = np.percentile(series, upper_pct)
        return np.clip(series, lower_bound, upper_bound)
    
    @staticmethod
    def iqr_winsorize(series, iqr_factor=1.5):
        """Winsorize using IQR method"""
        series = np.array(series)
        q25, q75 = np.percentile(series, [25, 75])
        iqr = q75 - q25
        lower_bound = q25 - iqr_factor * iqr
        upper_bound = q75 + iqr_factor * iqr
        return np.clip(series, lower_bound, upper_bound)

def load_data():
    """Load time series data, labels, and volatility patterns"""
    print("📊 Loading data...")
    X_data = pd.read_parquet('X_train.parquet')
    y_data = pd.read_parquet('y_train.parquet')
    
    time_series_list = []
    labels = []
    
    if isinstance(X_data.index, pd.MultiIndex):
        grouped = X_data.groupby(level='id')
        for id_, group in grouped:
            series_data = group.sort_index(level='time').values.flatten()
            time_series_list.append(series_data)
            label = y_data.loc[id_].iloc[0] if hasattr(y_data.loc[id_], 'iloc') else y_data.loc[id_]
            labels.append(label)
    else:
        for idx, row in X_data.iterrows():
            time_series_list.append(row.values)
            labels.append(y_data.iloc[idx] if idx < len(y_data) else 0)
    
    # Load volatility patterns
    try:
        vol_df = pd.read_csv('volatility_pattern_analysis.csv')
        print(f"   ✅ Loaded {len(vol_df)} volatility patterns")
    except:
        print("   ⚠️ volatility_pattern_analysis.csv not found, creating basic patterns...")
        vol_df = create_basic_patterns(time_series_list, labels)
    
    print(f"   ✅ Loaded {len(time_series_list)} series")
    return time_series_list, np.array(labels), vol_df

def create_basic_patterns(time_series_list, labels):
    """Create basic volatility patterns if file doesn't exist"""
    patterns = []
    
    for i, (series, label) in enumerate(zip(time_series_list, labels)):
        series_clean = np.array(series)[~np.isnan(series)]
        if len(series_clean) < 10:
            continue
        
        # Basic volatility metrics
        std_val = np.std(series_clean, ddof=1)
        mean_val = np.mean(series_clean)
        cv = std_val / abs(mean_val) if mean_val != 0 else np.inf
        
        # Spike detection
        z_scores = np.abs((series_clean - mean_val) / std_val) if std_val > 0 else np.zeros_like(series_clean)
        spike_rate = np.sum(z_scores > 3) / len(series_clean)
        
        # Boundary repeat and uniqueness
        unique_vals = len(np.unique(series_clean))
        uniqueness_ratio = unique_vals / len(series_clean)
        sorted_vals = np.sort(series_clean)
        min_repeats = np.sum(series_clean == sorted_vals[0])
        max_repeats = np.sum(series_clean == sorted_vals[-1])
        boundary_repeat_rate = (min_repeats + max_repeats) / len(series_clean)
        
        # Calmness score (simplified)
        calmness_score = 1 / (1 + cv + spike_rate)
        
        # Pattern classification
        pattern_type = 'moderate'
        
        # Thresholds (simplified)
        calm_threshold = 0.7
        volatile_threshold = 0.3
        
        if calmness_score >= calm_threshold:
            pattern_type = 'calm'
        elif calmness_score <= volatile_threshold:
            pattern_type = 'volatile'
        
        # Specific patterns
        if boundary_repeat_rate > 0.1 and uniqueness_ratio < 0.5:
            pattern_type = 'clipped'
        elif spike_rate > 0.05:
            pattern_type = 'spiky'
        
        patterns.append({
            'series_id': i,
            'label': label,
            'pattern_type': pattern_type,
            'calmness_score': calmness_score,
            'spike_rate': spike_rate,
            'boundary_repeat_rate': boundary_repeat_rate,
            'uniqueness_ratio': uniqueness_ratio
        })
    
    return pd.DataFrame(patterns)

def compute_basic_features(series, pre_processed=None):
    """Compute basic statistical features"""
    series_clean = np.array(series)[~np.isnan(series)]
    if len(series_clean) < 5:
        return {}
    
    # Split into pre/post segments
    split_point = len(series_clean) // 2
    pre = pre_processed if pre_processed is not None else series_clean[:split_point]
    post = series_clean[split_point:]
    
    features = {
        'mean_diff': np.mean(post) - np.mean(pre),
        'std_diff': np.std(post, ddof=1) - np.std(pre, ddof=1),
        'var_ratio': np.var(post, ddof=1) / np.var(pre, ddof=1) if np.var(pre, ddof=1) > 0 else 1,
        'range_diff': (np.max(post) - np.min(post)) - (np.max(pre) - np.min(pre)),
        'cv_pre': np.std(pre, ddof=1) / abs(np.mean(pre)) if np.mean(pre) != 0 else np.inf,
        'cv_post': np.std(post, ddof=1) / abs(np.mean(post)) if np.mean(post) != 0 else np.inf,
        'skew_diff': pd.Series(post).skew() - pd.Series(pre).skew(),
        'kurt_diff': pd.Series(post).kurtosis() - pd.Series(pre).kurtosis()
    }
    
    # Statistical tests
    try:
        from scipy.stats import ks_2samp, mannwhitneyu
        _, features['ks_pvalue'] = ks_2samp(pre, post)
        _, features['mw_pvalue'] = mannwhitneyu(pre, post, alternative='two-sided')
    except:
        features['ks_pvalue'] = np.nan
        features['mw_pvalue'] = np.nan
    
    return features

def test_outlier_methods_by_cluster(time_series_list, labels, vol_df, target_patterns=['spiky', 'volatile', 'calm'], max_samples=2000):
    """Test outlier removal methods on specific volatility clusters"""
    print(f"🔧 Testing outlier removal on clusters: {target_patterns}")
    
    # Filter for target patterns
    target_indices = vol_df[vol_df['pattern_type'].isin(target_patterns)]['series_id'].values
    
    # Filter time series and labels
    filtered_series = [time_series_list[i] for i in target_indices if i < len(time_series_list)]
    filtered_labels = [labels[i] for i in target_indices if i < len(labels)]
    filtered_patterns = [vol_df[vol_df['series_id'] == i]['pattern_type'].iloc[0] for i in target_indices if i < len(time_series_list)]
    
    print(f"   📊 Filtered to {len(filtered_series)} series from target patterns")
    
    # Limit samples for faster testing
    if len(filtered_series) > max_samples:
        indices = np.random.choice(len(filtered_series), max_samples, replace=False)
        filtered_series = [filtered_series[i] for i in indices]
        filtered_labels = [filtered_labels[i] for i in indices]
        filtered_patterns = [filtered_patterns[i] for i in indices]
    
    # Define test configurations
    test_configs = [
        ('original', None, {}),
        ('modified_z_2.0', 'modified_z_score_winsorize', {'threshold': 2.0}),
        ('modified_z_2.5', 'modified_z_score_winsorize', {'threshold': 2.5}),
        ('modified_z_3.0', 'modified_z_score_winsorize', {'threshold': 3.0}),
        ('modified_z_3.5', 'modified_z_score_winsorize', {'threshold': 3.5}),
        ('modified_z_4.0', 'modified_z_score_winsorize', {'threshold': 4.0}),
        ('percentile_1_99', 'percentile_winsorize', {'lower_pct': 1, 'upper_pct': 99}),
        ('percentile_2_98', 'percentile_winsorize', {'lower_pct': 2, 'upper_pct': 98}),
        ('percentile_5_95', 'percentile_winsorize', {'lower_pct': 5, 'upper_pct': 95}),
        ('percentile_10_90', 'percentile_winsorize', {'lower_pct': 10, 'upper_pct': 90}),
        ('iqr_1.0', 'iqr_winsorize', {'iqr_factor': 1.0}),
        ('iqr_1.5', 'iqr_winsorize', {'iqr_factor': 1.5}),
        ('iqr_2.0', 'iqr_winsorize', {'iqr_factor': 2.0}),
        ('iqr_2.5', 'iqr_winsorize', {'iqr_factor': 2.5})
    ]
    
    results = []
    
    for config_name, method_name, params in test_configs:
        print(f"   Testing {config_name}...")
        
        # Process all series with current configuration
        processed_features = []
        
        for i, (series, label, pattern) in enumerate(zip(filtered_series, filtered_labels, filtered_patterns)):
            series_clean = np.array(series)[~np.isnan(series)]
            
            if len(series_clean) < 10:
                continue
            
            # Apply outlier removal method ONLY to pre-segment (normal part)
            split_point = len(series_clean) // 2
            pre_segment = series_clean[:split_point]
            
            if method_name is None:
                processed_pre = pre_segment
            else:
                method = getattr(OutlierProcessor, method_name)
                processed_pre = method(pre_segment, **params)
            
            # Compute features with processed pre-segment, original post-segment
            features = compute_basic_features(series_clean, pre_processed=processed_pre)
            features['label'] = label
            features['pattern_type'] = pattern
            processed_features.append(features)
        
        if len(processed_features) < 20:
            continue
        
        # Convert to DataFrame and evaluate
        features_df = pd.DataFrame(processed_features)
        features_df = features_df.replace([np.inf, -np.inf], np.nan)
        
        # Prepare for classification
        feature_cols = [col for col in features_df.columns if col not in ['label', 'pattern_type']]
        X = features_df[feature_cols].fillna(features_df[feature_cols].median())
        y = features_df['label'].values
        
        if len(np.unique(y)) < 2:
            continue
        
        # Cross-validation
        cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        model = RandomForestClassifier(n_estimators=100, random_state=42, class_weight='balanced')
        
        try:
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)
            cv_scores = cross_val_score(model, X_scaled, y, cv=cv, scoring='roc_auc')
            
            # Calculate outlier statistics
            outlier_stats = calculate_outlier_impact(time_series_list, method_name, params)
            
            results.append({
                'method': config_name,
                'mean_auroc': cv_scores.mean(),
                'std_auroc': cv_scores.std(),
                'n_samples': len(features_df),
                'break_rate': y.mean(),
                **outlier_stats
            })
            
            print(f"      AUROC: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
            
        except Exception as e:
            print(f"      Error: {e}")
    
    return pd.DataFrame(results)

def calculate_outlier_impact(time_series_list, method_name, params):
    """Calculate statistics about outlier removal impact"""
    if method_name is None:
        return {
            'avg_outliers_removed': 0,
            'avg_pct_outliers': 0,
            'avg_range_reduction': 0,
            'avg_std_reduction': 0
        }
    
    outlier_counts = []
    pct_outliers = []
    range_reductions = []
    std_reductions = []
    
    method = getattr(OutlierProcessor, method_name)
    
    for series in time_series_list[:100]:  # Sample for efficiency
        series_clean = np.array(series)[~np.isnan(series)]
        if len(series_clean) < 10:
            continue
        
        original_range = np.max(series_clean) - np.min(series_clean)
        original_std = np.std(series_clean)
        
        processed_series = method(series_clean, **params)
        
        # Count changes
        outliers_removed = np.sum(series_clean != processed_series)
        outlier_counts.append(outliers_removed)
        pct_outliers.append(outliers_removed / len(series_clean) * 100)
        
        # Range and std reduction
        new_range = np.max(processed_series) - np.min(processed_series)
        new_std = np.std(processed_series)
        
        range_reductions.append((original_range - new_range) / original_range * 100 if original_range > 0 else 0)
        std_reductions.append((original_std - new_std) / original_std * 100 if original_std > 0 else 0)
    
    return {
        'avg_outliers_removed': np.mean(outlier_counts) if outlier_counts else 0,
        'avg_pct_outliers': np.mean(pct_outliers) if pct_outliers else 0,
        'avg_range_reduction': np.mean(range_reductions) if range_reductions else 0,
        'avg_std_reduction': np.mean(std_reductions) if std_reductions else 0
    }

def analyze_results(results_df):
    """Analyze outlier removal results"""
    print("\n📈 OUTLIER REMOVAL ANALYSIS RESULTS")
    print("=" * 60)
    
    # Sort by AUROC performance
    results_sorted = results_df.sort_values('mean_auroc', ascending=False)
    
    print("🏆 Top 5 performing methods:")
    for i, (_, row) in enumerate(results_sorted.head(5).iterrows()):
        print(f"   {i+1}. {row['method']:20}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
        print(f"      Outliers removed: {row['avg_pct_outliers']:.1f}%, Range reduction: {row['avg_range_reduction']:.1f}%")
    
    print(f"\n📉 Bottom 3 performing methods:")
    for i, (_, row) in enumerate(results_sorted.tail(3).iterrows()):
        print(f"   {row['method']:20}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
    
    # Find baseline (original)
    baseline_auroc = results_df[results_df['method'] == 'original']['mean_auroc'].iloc[0]
    
    print(f"\n📊 Improvement over baseline (AUROC = {baseline_auroc:.4f}):")
    for _, row in results_sorted.iterrows():
        if row['method'] != 'original':
            improvement = row['mean_auroc'] - baseline_auroc
            pct_improvement = (improvement / baseline_auroc) * 100
            status = "📈" if improvement > 0 else "📉"
            print(f"   {status} {row['method']:20}: {improvement:+.4f} ({pct_improvement:+.2f}%)")
    
    # Best method analysis
    best_method = results_sorted.iloc[0]
    print(f"\n🎯 Best method: {best_method['method']}")
    print(f"   AUROC improvement: +{best_method['mean_auroc'] - baseline_auroc:.4f}")
    print(f"   Outliers removed: {best_method['avg_pct_outliers']:.1f}%")
    print(f"   Range reduction: {best_method['avg_range_reduction']:.1f}%")
    print(f"   Std reduction: {best_method['avg_std_reduction']:.1f}%")

def analyze_results_by_pattern(results_df, pattern_results):
    """Analyze results by volatility pattern"""
    print("\n� TRESULTS BY VOLATILITY PATTERN")
    print("=" * 60)
    
    for pattern, pattern_df in pattern_results.items():
        if len(pattern_df) == 0:
            continue
            
        print(f"\n🏷️ {pattern.upper()} PATTERN:")
        results_sorted = pattern_df.sort_values('mean_auroc', ascending=False)
        
        # Top 3 methods for this pattern
        print("   Top 3 methods:")
        for i, (_, row) in enumerate(results_sorted.head(3).iterrows()):
            print(f"   {i+1}. {row['method']:15}: AUROC = {row['mean_auroc']:.4f} ± {row['std_auroc']:.4f}")
        
        # Best improvement
        baseline = pattern_df[pattern_df['method'] == 'original']['mean_auroc'].iloc[0]
        best_method = results_sorted.iloc[0]
        improvement = best_method['mean_auroc'] - baseline
        print(f"   Best improvement: +{improvement:.4f} ({improvement/baseline*100:+.2f}%)")

def main():
    """Main pipeline execution"""
    print("🚀 OUTLIER REMOVAL ANALYSIS ON VOLATILITY CLUSTERS")
    print("=" * 60)
    
    # Load data with volatility patterns
    time_series_list, labels, vol_df = load_data()
    
    print(f"\n📊 Pattern distribution:")
    pattern_counts = vol_df['pattern_type'].value_counts()
    for pattern, count in pattern_counts.items():
        print(f"   {pattern}: {count} ({count/len(vol_df)*100:.1f}%)")
    
    # Test outlier removal methods on target clusters
    results_df = test_outlier_methods_by_cluster(time_series_list, labels, vol_df)
    
    # Analyze overall results
    analyze_results(results_df)
    
    # Analyze by individual patterns
    pattern_results = {}
    for pattern in ['spiky', 'volatile', 'calm']:
        pattern_mask = results_df['method'].str.contains('original') | (results_df['n_samples'] > 0)  # Placeholder for pattern-specific results
        pattern_results[pattern] = results_df  # Simplified - in full implementation would filter by pattern
    
    # Save results
    results_df.to_csv('outlier_removal_comparison.csv', index=False)
    
    print(f"\n💾 Results saved to 'outlier_removal_comparison.csv'")
    
    return results_df

if __name__ == "__main__":
    results = main()