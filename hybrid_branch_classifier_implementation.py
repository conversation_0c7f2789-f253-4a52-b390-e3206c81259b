"""
Hybrid Implementation Strategy for Sequential Branch Classification
Combines statistical methods, online learning, and rule-based logic
"""

import numpy as np
from collections import deque, defaultdict
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional
import time

@dataclass
class OnlineStatistics:
    """<PERSON><PERSON><PERSON>'s online algorithm for mean/variance"""
    count: int = 0
    mean: float = 0.0
    m2: float = 0.0
    
    def update(self, value: float):
        self.count += 1
        delta = value - self.mean
        self.mean += delta / self.count
        delta2 = value - self.mean
        self.m2 += delta * delta2
    
    @property
    def variance(self) -> float:
        return self.m2 / self.count if self.count > 1 else 0.0
    
    @property
    def std(self) -> float:
        return np.sqrt(self.variance)

class CUSUMDetector:
    """Online change detection using CUSUM"""
    def __init__(self, threshold: float = 5.0, drift: float = 1.0):
        self.threshold = threshold
        self.drift = drift
        self.cusum_pos = 0.0
        self.cusum_neg = 0.0
        self.baseline_mean = 0.0
        self.baseline_count = 0
        
    def update(self, value: float) -> bool:
        """Returns True if change detected"""
        if self.baseline_count < 30:  # Build baseline
            self.baseline_count += 1
            delta = value - self.baseline_mean
            self.baseline_mean += delta / self.baseline_count
            return False
        
        # CUSUM calculation
        deviation = value - self.baseline_mean
        self.cusum_pos = max(0, self.cusum_pos + deviation - self.drift)
        self.cusum_neg = max(0, self.cusum_neg - deviation - self.drift)
        
        change_detected = (self.cusum_pos > self.threshold or 
                          self.cusum_neg > self.threshold)
        
        if change_detected:
            # Reset after detection
            self.cusum_pos = 0.0
            self.cusum_neg = 0.0
            self.baseline_mean = value
            self.baseline_count = 1
            
        return change_detected

class SlidingWindowSimilarity:
    """Efficient sliding window similarity computation"""
    def __init__(self, window_size: int = 100):
        self.window_size = window_size
        self.windows = {}  # branch_id -> deque
        
    def add_to_branch(self, branch_id: str, data: np.ndarray):
        if branch_id not in self.windows:
            self.windows[branch_id] = deque(maxlen=self.window_size)
        
        # Add data points to sliding window
        for point in data:
            self.windows[branch_id].append(point)
    
    def compute_similarity(self, branch_id: str, new_data: np.ndarray) -> float:
        if branch_id not in self.windows or len(self.windows[branch_id]) < 10:
            return 0.0
        
        window_data = np.array(list(self.windows[branch_id]))
        
        # Use last N points for comparison
        comparison_length = min(len(new_data), len(window_data), 50)
        
        if comparison_length < 5:
            return 0.0
        
        window_tail = window_data[-comparison_length:]
        new_head = new_data[:comparison_length]
        
        # Fast correlation computation
        correlation = np.corrcoef(window_tail, new_head)[0, 1]
        return max(0, correlation) if not np.isnan(correlation) else 0.0

class AdaptiveThresholds:
    """Self-adjusting thresholds based on performance"""
    def __init__(self):
        self.similarity_threshold = 0.7
        self.break_threshold = 0.3
        self.novelty_threshold = 0.8
        
        # Performance tracking
        self.decision_history = deque(maxlen=1000)
        self.performance_window = deque(maxlen=100)
        
    def update_thresholds(self, decision_quality: float):
        """Adjust thresholds based on decision quality feedback"""
        self.performance_window.append(decision_quality)
        
        if len(self.performance_window) < 50:
            return
        
        recent_performance = np.mean(list(self.performance_window)[-20:])
        
        # If performance is poor, make thresholds more conservative
        if recent_performance < 0.6:
            self.similarity_threshold *= 1.02
            self.break_threshold *= 0.98
            self.novelty_threshold *= 1.01
        # If performance is good, make thresholds more aggressive
        elif recent_performance > 0.8:
            self.similarity_threshold *= 0.99
            self.break_threshold *= 1.01
            self.novelty_threshold *= 0.99
        
        # Keep thresholds in reasonable bounds
        self.similarity_threshold = np.clip(self.similarity_threshold, 0.5, 0.9)
        self.break_threshold = np.clip(self.break_threshold, 0.1, 0.5)
        self.novelty_threshold = np.clip(self.novelty_threshold, 0.6, 0.95)

class HybridBranchClassifier:
    """
    Hybrid implementation combining:
    1. Statistical methods (CUSUM, online stats)
    2. Sliding window similarity
    3. Adaptive thresholds
    4. Rule-based fallbacks
    5. Separate handling of normal vs candidate parts
    """
    
    def __init__(self, 
                 max_active_branches: int = 10,
                 window_size: int = 100,
                 cusum_threshold: float = 5.0,
                 separate_concatenation: bool = True):
        
        # Core components
        self.similarity_computer = SlidingWindowSimilarity(window_size)
        self.adaptive_thresholds = AdaptiveThresholds()
        
        # Branch management
        self.active_branches = {}  # branch_id -> metadata
        self.branch_statistics = {}  # branch_id -> OnlineStatistics
        self.change_detectors = {}  # branch_id -> CUSUMDetector
        
        # Separate concatenation tracking
        self.separate_concatenation = separate_concatenation
        if separate_concatenation:
            # Separate storage for normal vs candidate parts
            self.normal_parts = {}  # branch_id -> list of normal parts
            self.candidate_parts = {}  # branch_id -> list of candidate parts
            self.normal_similarity_computer = SlidingWindowSimilarity(window_size)
            self.candidate_similarity_computer = SlidingWindowSimilarity(window_size)
        
        # Configuration
        self.max_active_branches = max_active_branches
        self.cusum_threshold = cusum_threshold
        
        # Performance tracking
        self.decision_count = 0
        self.processing_times = deque(maxlen=100)
        
    def process_new_part(self, new_data: np.ndarray, timestamp: int = None) -> Dict:
        """
        Main processing function - optimized for speed and accuracy
        """
        start_time = time.time()
        
        if timestamp is None:
            timestamp = self.decision_count
        
        # Step 1: Quick feature extraction
        features = self._extract_fast_features(new_data)
        
        # Step 2: Compute similarities (O(k) where k = active branches)
        similarities = self._compute_similarities_fast(new_data)
        
        # Step 3: Detect structural breaks (O(k))
        break_scores = self._detect_breaks_fast(new_data)
        
        # Step 4: Assess novelty (O(1))
        novelty_score = self._assess_novelty_fast(features)
        
        # Step 5: Make decision using hybrid logic
        decision = self._make_hybrid_decision(similarities, break_scores, novelty_score)
        
        # Step 6: Execute decision and update state
        self._execute_and_update(decision, new_data, timestamp)
        
        # Step 7: Maintain system health
        self._maintain_system()
        
        # Performance tracking
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        self.decision_count += 1
        
        return {
            'decision': decision['action'],
            'branch_id': decision['branch_id'],
            'confidence': decision['confidence'],
            'similarities': similarities,
            'break_scores': break_scores,
            'novelty_score': novelty_score,
            'processing_time': processing_time,
            'active_branches': len(self.active_branches)
        }
    
    def process_two_parts(self, normal_part: np.ndarray, candidate_part: np.ndarray, 
                         timestamp: int = None) -> Dict:
        """
        Process two parts separately: normal part and candidate part
        Uses separate concatenation logic for each part type
        
        Args:
            normal_part: The normal/baseline time series segment
            candidate_part: The candidate/test time series segment
            timestamp: Optional timestamp
            
        Returns:
            Decision dictionary with separate analysis for both parts
        """
        start_time = time.time()
        
        if timestamp is None:
            timestamp = self.decision_count
        
        if not self.separate_concatenation:
            # Fallback to combined processing if separate concatenation disabled
            combined_data = np.concatenate([normal_part, candidate_part])
            return self.process_new_part(combined_data, timestamp)
        
        # Step 1: Extract features for both parts separately
        normal_features = self._extract_fast_features(normal_part)
        candidate_features = self._extract_fast_features(candidate_part)
        
        # Step 2: Compute similarities separately for normal and candidate parts
        normal_similarities = self._compute_similarities_separate(normal_part, 'normal')
        candidate_similarities = self._compute_similarities_separate(candidate_part, 'candidate')
        
        # Step 3: Detect breaks using both parts
        break_scores = self._detect_breaks_two_parts(normal_part, candidate_part)
        
        # Step 4: Assess novelty for both parts
        normal_novelty = self._assess_novelty_fast(normal_features)
        candidate_novelty = self._assess_novelty_fast(candidate_features)
        
        # Step 5: Make decision using two-part logic
        decision = self._make_two_part_decision(
            normal_similarities, candidate_similarities,
            break_scores, normal_novelty, candidate_novelty,
            normal_features, candidate_features
        )
        
        # Step 6: Execute decision and update state for both parts
        self._execute_and_update_two_parts(decision, normal_part, candidate_part, timestamp)
        
        # Step 7: Maintain system health
        self._maintain_system()
        
        # Performance tracking
        processing_time = time.time() - start_time
        self.processing_times.append(processing_time)
        self.decision_count += 1
        
        return {
            'decision': decision['action'],
            'branch_id': decision['branch_id'],
            'confidence': decision['confidence'],
            'normal_similarities': normal_similarities,
            'candidate_similarities': candidate_similarities,
            'break_scores': break_scores,
            'normal_novelty': normal_novelty,
            'candidate_novelty': candidate_novelty,
            'processing_time': processing_time,
            'active_branches': len(self.active_branches),
            'part_analysis': {
                'normal_features': normal_features,
                'candidate_features': candidate_features,
                'similarity_difference': self._compute_similarity_difference(normal_similarities, candidate_similarities)
            }
        }
    
    def _extract_fast_features(self, data: np.ndarray) -> Dict:
        """Extract features optimized for speed"""
        return {
            'mean': np.mean(data),
            'std': np.std(data),
            'length': len(data),
            'trend': (data[-1] - data[0]) / len(data) if len(data) > 1 else 0,
            'energy': np.sum(data ** 2),
            'zero_crossings': np.sum(np.diff(np.sign(data)) != 0)
        }
    
    def _compute_similarities_fast(self, new_data: np.ndarray) -> Dict[str, float]:
        """Fast similarity computation using sliding windows"""
        similarities = {}
        
        for branch_id in self.active_branches:
            sim = self.similarity_computer.compute_similarity(branch_id, new_data)
            similarities[branch_id] = sim
            
        return similarities
    
    def _detect_breaks_fast(self, new_data: np.ndarray) -> Dict[str, float]:
        """Fast break detection using CUSUM"""
        break_scores = {}
        
        # Use mean of new data as test statistic
        test_statistic = np.mean(new_data)
        
        for branch_id in self.active_branches:
            if branch_id not in self.change_detectors:
                self.change_detectors[branch_id] = CUSUMDetector(self.cusum_threshold)
            
            change_detected = self.change_detectors[branch_id].update(test_statistic)
            break_scores[branch_id] = 1.0 if change_detected else 0.1
            
        return break_scores
    
    def _assess_novelty_fast(self, features: Dict) -> float:
        """Fast novelty assessment using feature statistics"""
        if not self.branch_statistics:
            return 1.0  # First data is always novel
        
        # Compare with global statistics
        novelty_scores = []
        
        for stat_name in ['mean', 'std', 'energy']:
            if stat_name in features:
                # Compare with all branch statistics
                branch_values = []
                for branch_stats in self.branch_statistics.values():
                    if hasattr(branch_stats, stat_name):
                        branch_values.append(getattr(branch_stats, stat_name))
                
                if branch_values:
                    feature_value = features[stat_name]
                    distances = [abs(feature_value - bv) for bv in branch_values]
                    min_distance = min(distances)
                    # Normalize distance to [0, 1]
                    novelty_scores.append(min(1.0, min_distance / (np.std(branch_values) + 1e-8)))
        
        return np.mean(novelty_scores) if novelty_scores else 1.0
    
    def _make_hybrid_decision(self, similarities: Dict, break_scores: Dict, novelty_score: float) -> Dict:
        """Hybrid decision making with multiple strategies"""
        
        # Get current thresholds
        sim_thresh = self.adaptive_thresholds.similarity_threshold
        break_thresh = self.adaptive_thresholds.break_threshold
        novelty_thresh = self.adaptive_thresholds.novelty_threshold
        
        # Strategy 1: Statistical rules
        if not similarities or max(similarities.values()) < sim_thresh:
            return {
                'action': 'NEW_BRANCH',
                'branch_id': f'branch_{len(self.active_branches)}',
                'confidence': 0.9,
                'reason': 'low_similarity'
            }
        
        best_branch = max(similarities.keys(), key=lambda k: similarities[k])
        best_sim = similarities[best_branch]
        best_break = break_scores.get(best_branch, 0.5)
        
        # Strategy 2: Break detection priority
        if best_sim > sim_thresh and best_break > break_thresh:
            return {
                'action': 'STRUCTURAL_BREAK',
                'branch_id': f'branch_{len(self.active_branches)}',
                'confidence': 0.8,
                'reason': 'structural_break_detected',
                'split_from': best_branch
            }
        
        # Strategy 3: Continuation
        if best_sim > sim_thresh and best_break <= break_thresh:
            return {
                'action': 'CONTINUE_BRANCH',
                'branch_id': best_branch,
                'confidence': best_sim,
                'reason': 'good_continuation'
            }
        
        # Strategy 4: Novelty-based
        if novelty_score > novelty_thresh:
            return {
                'action': 'NEW_BRANCH',
                'branch_id': f'branch_{len(self.active_branches)}',
                'confidence': novelty_score,
                'reason': 'high_novelty'
            }
        
        # Default: Conservative new branch
        return {
            'action': 'NEW_BRANCH',
            'branch_id': f'branch_{len(self.active_branches)}',
            'confidence': 0.5,
            'reason': 'uncertain_default'
        }
    
    def _execute_and_update(self, decision: Dict, new_data: np.ndarray, timestamp: int):
        """Execute decision and update all internal state"""
        
        branch_id = decision['branch_id']
        action = decision['action']
        
        if action == 'NEW_BRANCH':
            # Create new branch
            self.active_branches[branch_id] = {
                'created': timestamp,
                'last_updated': timestamp,
                'part_count': 1,
                'total_length': len(new_data)
            }
            self.branch_statistics[branch_id] = OnlineStatistics()
            
        elif action == 'CONTINUE_BRANCH':
            # Update existing branch
            self.active_branches[branch_id]['last_updated'] = timestamp
            self.active_branches[branch_id]['part_count'] += 1
            self.active_branches[branch_id]['total_length'] += len(new_data)
        
        elif action == 'STRUCTURAL_BREAK':
            # Handle structural break (create new branch)
            self.active_branches[branch_id] = {
                'created': timestamp,
                'last_updated': timestamp,
                'part_count': 1,
                'total_length': len(new_data),
                'split_from': decision.get('split_from')
            }
            self.branch_statistics[branch_id] = OnlineStatistics()
        
        # Update similarity computer
        self.similarity_computer.add_to_branch(branch_id, new_data)
        
        # Update statistics
        if branch_id in self.branch_statistics:
            for value in new_data:
                self.branch_statistics[branch_id].update(value)
    
    def _maintain_system(self):
        """Maintain system health and performance"""
        
        # Remove old branches if too many active
        if len(self.active_branches) > self.max_active_branches:
            # Remove oldest branch
            oldest_branch = min(self.active_branches.keys(), 
                              key=lambda k: self.active_branches[k]['last_updated'])
            
            del self.active_branches[oldest_branch]
            if oldest_branch in self.branch_statistics:
                del self.branch_statistics[oldest_branch]
            if oldest_branch in self.change_detectors:
                del self.change_detectors[oldest_branch]
        
        # Update adaptive thresholds based on recent performance
        if self.decision_count % 100 == 0:  # Every 100 decisions
            # Simple performance metric: branch utilization
            avg_parts_per_branch = np.mean([b['part_count'] for b in self.active_branches.values()])
            performance_score = min(1.0, avg_parts_per_branch / 10)  # Normalize
            self.adaptive_thresholds.update_thresholds(performance_score)
    
    def _compute_similarities_separate(self, data: np.ndarray, part_type: str) -> Dict[str, float]:
        """Compute similarities using separate similarity computers for normal/candidate parts"""
        similarities = {}
        
        # Choose the appropriate similarity computer
        if part_type == 'normal':
            similarity_computer = self.normal_similarity_computer
        elif part_type == 'candidate':
            similarity_computer = self.candidate_similarity_computer
        else:
            # Fallback to main similarity computer
            similarity_computer = self.similarity_computer
        
        for branch_id in self.active_branches:
            sim = similarity_computer.compute_similarity(branch_id, data)
            similarities[branch_id] = sim
            
        return similarities
    
    def _detect_breaks_two_parts(self, normal_part: np.ndarray, candidate_part: np.ndarray) -> Dict[str, float]:
        """Detect breaks using both normal and candidate parts"""
        break_scores = {}
        
        # Use difference between normal and candidate means as test statistic
        normal_mean = np.mean(normal_part)
        candidate_mean = np.mean(candidate_part)
        
        # Test statistic: difference between parts
        test_statistic = abs(candidate_mean - normal_mean)
        
        # Also consider variance difference
        normal_std = np.std(normal_part)
        candidate_std = np.std(candidate_part)
        variance_ratio = max(candidate_std, normal_std) / (min(candidate_std, normal_std) + 1e-8)
        
        for branch_id in self.active_branches:
            if branch_id not in self.change_detectors:
                self.change_detectors[branch_id] = CUSUMDetector(self.cusum_threshold)
            
            # Use combined statistic for break detection
            combined_statistic = test_statistic + 0.5 * (variance_ratio - 1)
            change_detected = self.change_detectors[branch_id].update(combined_statistic)
            
            # Higher break score if significant difference between parts
            if change_detected or test_statistic > 2.0 or variance_ratio > 3.0:
                break_scores[branch_id] = 1.0
            else:
                break_scores[branch_id] = min(0.8, test_statistic / 2.0 + variance_ratio / 10.0)
            
        return break_scores
    
    def _make_two_part_decision(self, normal_similarities: Dict, candidate_similarities: Dict,
                               break_scores: Dict, normal_novelty: float, candidate_novelty: float,
                               normal_features: Dict, candidate_features: Dict) -> Dict:
        """Make decision using two-part analysis logic"""
        
        # Get current thresholds
        sim_thresh = self.adaptive_thresholds.similarity_threshold
        break_thresh = self.adaptive_thresholds.break_threshold
        novelty_thresh = self.adaptive_thresholds.novelty_threshold
        
        # Compute similarity differences between normal and candidate parts
        similarity_differences = self._compute_similarity_difference(normal_similarities, candidate_similarities)
        
        # Strategy 1: Large similarity difference suggests structural break
        max_sim_diff = max(similarity_differences.values()) if similarity_differences else 0.0
        if max_sim_diff > 0.3:  # Significant difference threshold
            best_diff_branch = max(similarity_differences.keys(), key=lambda k: similarity_differences[k])
            return {
                'action': 'STRUCTURAL_BREAK',
                'branch_id': f'branch_{len(self.active_branches)}',
                'confidence': 0.9,
                'reason': f'large_similarity_difference_{max_sim_diff:.3f}',
                'split_from': best_diff_branch
            }
        
        # Strategy 2: Both parts have low similarity - new branch
        normal_max_sim = max(normal_similarities.values()) if normal_similarities else 0.0
        candidate_max_sim = max(candidate_similarities.values()) if candidate_similarities else 0.0
        
        if normal_max_sim < sim_thresh and candidate_max_sim < sim_thresh:
            return {
                'action': 'NEW_BRANCH',
                'branch_id': f'branch_{len(self.active_branches)}',
                'confidence': 0.9,
                'reason': 'both_parts_low_similarity'
            }
        
        # Strategy 3: Break detection priority
        if break_scores:
            best_break_branch = max(break_scores.keys(), key=lambda k: break_scores[k])
            best_break_score = break_scores[best_break_branch]
            
            if best_break_score > break_thresh:
                return {
                    'action': 'STRUCTURAL_BREAK',
                    'branch_id': f'branch_{len(self.active_branches)}',
                    'confidence': 0.8,
                    'reason': f'break_detected_{best_break_score:.3f}',
                    'split_from': best_break_branch
                }
        
        # Strategy 4: Normal part similarity dominates - continue branch
        if normal_max_sim > candidate_max_sim and normal_max_sim > sim_thresh:
            best_normal_branch = max(normal_similarities.keys(), key=lambda k: normal_similarities[k])
            return {
                'action': 'CONTINUE_BRANCH',
                'branch_id': best_normal_branch,
                'confidence': normal_max_sim,
                'reason': 'normal_part_similarity_dominates'
            }
        
        # Strategy 5: Candidate part similarity dominates - continue branch
        if candidate_max_sim > normal_max_sim and candidate_max_sim > sim_thresh:
            best_candidate_branch = max(candidate_similarities.keys(), key=lambda k: candidate_similarities[k])
            return {
                'action': 'CONTINUE_BRANCH',
                'branch_id': best_candidate_branch,
                'confidence': candidate_max_sim,
                'reason': 'candidate_part_similarity_dominates'
            }
        
        # Strategy 6: High novelty in either part
        if normal_novelty > novelty_thresh or candidate_novelty > novelty_thresh:
            return {
                'action': 'NEW_BRANCH',
                'branch_id': f'branch_{len(self.active_branches)}',
                'confidence': max(normal_novelty, candidate_novelty),
                'reason': f'high_novelty_normal_{normal_novelty:.3f}_candidate_{candidate_novelty:.3f}'
            }
        
        # Default: Conservative new branch
        return {
            'action': 'NEW_BRANCH',
            'branch_id': f'branch_{len(self.active_branches)}',
            'confidence': 0.5,
            'reason': 'uncertain_two_part_default'
        }
    
    def _compute_similarity_difference(self, normal_similarities: Dict, candidate_similarities: Dict) -> Dict[str, float]:
        """Compute absolute difference in similarities between normal and candidate parts"""
        differences = {}
        
        # Get common branch IDs
        common_branches = set(normal_similarities.keys()) & set(candidate_similarities.keys())
        
        for branch_id in common_branches:
            normal_sim = normal_similarities[branch_id]
            candidate_sim = candidate_similarities[branch_id]
            differences[branch_id] = abs(normal_sim - candidate_sim)
        
        return differences
    
    def _execute_and_update_two_parts(self, decision: Dict, normal_part: np.ndarray, 
                                     candidate_part: np.ndarray, timestamp: int):
        """Execute decision and update state for both normal and candidate parts"""
        
        branch_id = decision['branch_id']
        action = decision['action']
        
        if action == 'NEW_BRANCH':
            # Create new branch
            self.active_branches[branch_id] = {
                'created': timestamp,
                'last_updated': timestamp,
                'part_count': 1,
                'total_length': len(normal_part) + len(candidate_part),
                'normal_parts': 1,
                'candidate_parts': 1
            }
            self.branch_statistics[branch_id] = OnlineStatistics()
            
            # Initialize separate part storage
            if self.separate_concatenation:
                self.normal_parts[branch_id] = [normal_part]
                self.candidate_parts[branch_id] = [candidate_part]
            
        elif action == 'CONTINUE_BRANCH':
            # Update existing branch
            self.active_branches[branch_id]['last_updated'] = timestamp
            self.active_branches[branch_id]['part_count'] += 1
            self.active_branches[branch_id]['total_length'] += len(normal_part) + len(candidate_part)
            self.active_branches[branch_id]['normal_parts'] = self.active_branches[branch_id].get('normal_parts', 0) + 1
            self.active_branches[branch_id]['candidate_parts'] = self.active_branches[branch_id].get('candidate_parts', 0) + 1
            
            # Add to separate part storage
            if self.separate_concatenation:
                if branch_id not in self.normal_parts:
                    self.normal_parts[branch_id] = []
                if branch_id not in self.candidate_parts:
                    self.candidate_parts[branch_id] = []
                
                self.normal_parts[branch_id].append(normal_part)
                self.candidate_parts[branch_id].append(candidate_part)
        
        elif action == 'STRUCTURAL_BREAK':
            # Handle structural break (create new branch)
            self.active_branches[branch_id] = {
                'created': timestamp,
                'last_updated': timestamp,
                'part_count': 1,
                'total_length': len(normal_part) + len(candidate_part),
                'normal_parts': 1,
                'candidate_parts': 1,
                'split_from': decision.get('split_from')
            }
            self.branch_statistics[branch_id] = OnlineStatistics()
            
            # Initialize separate part storage
            if self.separate_concatenation:
                self.normal_parts[branch_id] = [normal_part]
                self.candidate_parts[branch_id] = [candidate_part]
        
        # Update separate similarity computers
        if self.separate_concatenation:
            self.normal_similarity_computer.add_to_branch(branch_id, normal_part)
            self.candidate_similarity_computer.add_to_branch(branch_id, candidate_part)
        
        # Update main similarity computer with combined data
        combined_data = np.concatenate([normal_part, candidate_part])
        self.similarity_computer.add_to_branch(branch_id, combined_data)
        
        # Update statistics with both parts
        if branch_id in self.branch_statistics:
            for value in normal_part:
                self.branch_statistics[branch_id].update(value)
            for value in candidate_part:
                self.branch_statistics[branch_id].update(value)
    
    def get_separate_concatenation_stats(self) -> Dict:
        """Get statistics about separate concatenation if enabled"""
        if not self.separate_concatenation:
            return {'separate_concatenation': False}
        
        stats = {
            'separate_concatenation': True,
            'normal_parts_stored': {bid: len(parts) for bid, parts in self.normal_parts.items()},
            'candidate_parts_stored': {bid: len(parts) for bid, parts in self.candidate_parts.items()},
            'total_normal_parts': sum(len(parts) for parts in self.normal_parts.values()),
            'total_candidate_parts': sum(len(parts) for parts in self.candidate_parts.values())
        }
        
        return stats
    
    def get_performance_stats(self) -> Dict:
        """Get system performance statistics"""
        return {
            'decisions_processed': self.decision_count,
            'active_branches': len(self.active_branches),
            'avg_processing_time': np.mean(list(self.processing_times)) if self.processing_times else 0,
            'current_thresholds': {
                'similarity': self.adaptive_thresholds.similarity_threshold,
                'break': self.adaptive_thresholds.break_threshold,
                'novelty': self.adaptive_thresholds.novelty_threshold
            },
            'branch_stats': {
                bid: {
                    'parts': self.active_branches[bid]['part_count'],
                    'length': self.active_branches[bid]['total_length']
                } for bid in self.active_branches
            }
        }

# Example usage and testing
if __name__ == "__main__":
    classifier = HybridBranchClassifier()
    
    # Simulate real-time processing
    print("🚀 Testing Hybrid Branch Classifier")
    print("=" * 50)
    
    for i in range(20):
        # Generate different types of data
        if i < 5:
            # Normal continuation
            data = np.random.randn(100) + i * 0.1
        elif i < 10:
            # Structural break
            data = np.random.randn(100) + 5 + i * 0.1
        elif i < 15:
            # Return to normal
            data = np.random.randn(100) + i * 0.1
        else:
            # Novel pattern
            data = np.sin(np.linspace(0, 4*np.pi, 100)) + np.random.randn(100) * 0.1
        
        result = classifier.process_new_part(data, timestamp=i)
        
        print(f"Part {i:2d}: {result['decision']:15s} -> {result['branch_id']:10s} "
              f"(conf: {result['confidence']:.3f}, time: {result['processing_time']*1000:.1f}ms)")
        
        if i % 5 == 4:  # Every 5 parts
            stats = classifier.get_performance_stats()
            print(f"  📊 Active branches: {stats['active_branches']}, "
                  f"Avg time: {stats['avg_processing_time']*1000:.1f}ms")
            print()
    
    print("\n🏆 Final Performance Stats:")
    final_stats = classifier.get_performance_stats()
    for key, value in final_stats.items():
        if key != 'branch_stats':
            print(f"  {key}: {value}")