#!/usr/bin/env python3
"""
Compare Thermodynamics_final.py vs analyze_metrics_ranges.py features
XGBoost GPU AUROC 5-fold CV comparison
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold, cross_val_score
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import roc_auc_score
import xgboost as xgb
import time
import warnings

warnings.filterwarnings('ignore')

def load_thermodynamics_features():
    """Load features from Thermodynamics_final.py approach"""
    try:
        # Try to load existing results or run the script
        print("🔥 Loading Thermodynamics features...")
        
        # First try to load existing CSV files
        possible_files = [
            'thermodynamics_features.csv',
            'features_df.csv', 
            'processed_features.csv',
            'final_features.csv'
        ]
        
        for filename in possible_files:
            try:
                features_df = pd.read_csv(filename)
                if 'label' in features_df.columns:
                    print(f"   ✅ Loaded from {filename}: {features_df.shape}")
                    return features_df
            except:
                continue
        
        # If no CSV found, try to run the script directly
        import subprocess
        import sys
        
        script_path = '/media/kl/819ebd59-6dcf-446a-89aa-e9da52745bc8/dockerdata/CrunchDAO/structuralbreak/Update_Kiro_pruned/Thermodynamics_final.py'
        
        print("   🔄 Running Thermodynamics_final.py...")
        result = subprocess.run([sys.executable, script_path], 
                              capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            # Try to load generated files again
            for filename in possible_files:
                try:
                    features_df = pd.read_csv(filename)
                    if 'label' in features_df.columns:
                        print(f"   ✅ Generated and loaded: {features_df.shape}")
                        return features_df
                except:
                    continue
        
        print(f"   ❌ Could not load Thermodynamics features")
        return None
        
    except Exception as e:
        print(f"   ❌ Error loading Thermodynamics features: {e}")
        return None

def load_metrics_ranges_features():
    """Load features from analyze_metrics_ranges.py approach"""
    try:
        # Run analyze_metrics_ranges.py and extract features
        from analyze_metrics_ranges import main as metrics_main
        print("📊 Running Metrics Ranges analysis...")
        metrics_results = metrics_main()
        
        # Extract features DataFrame
        if isinstance(metrics_results, tuple):
            features_df = metrics_results[0]  # Assuming first element is features
        else:
            features_df = metrics_results
            
        print(f"   ✅ Metrics Ranges features: {features_df.shape}")
        return features_df
        
    except Exception as e:
        print(f"   ❌ Error loading Metrics Ranges features: {e}")
        return None

def evaluate_xgboost_performance(features_df, method_name):
    """Evaluate XGBoost GPU performance with 5-fold CV"""
    print(f"\n🚀 Evaluating {method_name} with XGBoost GPU...")
    
    # Prepare data
    y = features_df['label'].values
    feature_cols = [col for col in features_df.columns if col not in ['series_id', 'label', 'id']]
    X = features_df[feature_cols].copy()
    
    # Handle missing values and infinities
    X = X.replace([np.inf, -np.inf], np.nan)
    X = X.fillna(X.median())
    
    print(f"   📊 Dataset: {len(y)} samples, {X.shape[1]} features")
    print(f"   📊 Positive class: {np.sum(y)} ({np.sum(y)/len(y)*100:.1f}%)")
    
    # Standardize features
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)
    
    # XGBoost GPU model
    xgb_model = xgb.XGBClassifier(
        tree_method='gpu_hist',
        gpu_id=0,
        n_estimators=200,
        max_depth=6,
        learning_rate=0.1,
        subsample=0.8,
        colsample_bytree=0.8,
        random_state=42,
        eval_metric='auc',
        use_label_encoder=False
    )
    
    # 5-fold stratified cross-validation
    cv = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    
    start_time = time.time()
    
    try:
        cv_scores = cross_val_score(xgb_model, X_scaled, y, cv=cv, scoring='roc_auc', n_jobs=1)
        
        training_time = time.time() - start_time
        
        results = {
            'method': method_name,
            'mean_auroc': cv_scores.mean(),
            'std_auroc': cv_scores.std(),
            'cv_scores': cv_scores,
            'n_features': X.shape[1],
            'training_time': training_time
        }
        
        print(f"   🎯 AUROC: {cv_scores.mean():.4f} ± {cv_scores.std():.4f}")
        print(f"   ⏱️  Training time: {training_time:.1f}s")
        print(f"   📈 Individual folds: {[f'{score:.4f}' for score in cv_scores]}")
        
        return results
        
    except Exception as e:
        print(f"   ❌ XGBoost evaluation failed: {e}")
        return None

def compare_feature_importance(thermo_df, metrics_df):
    """Compare feature importance between methods"""
    print("\n🔍 Comparing feature importance...")
    
    # Train XGBoost on both datasets to get feature importance
    results = {}
    
    for name, df in [('Thermodynamics', thermo_df), ('Metrics_Ranges', metrics_df)]:
        if df is None:
            continue
            
        y = df['label'].values
        feature_cols = [col for col in df.columns if col not in ['series_id', 'label', 'id']]
        X = df[feature_cols].copy()
        
        X = X.replace([np.inf, -np.inf], np.nan).fillna(X.median())
        
        # Train XGBoost
        xgb_model = xgb.XGBClassifier(
            tree_method='gpu_hist',
            gpu_id=0,
            n_estimators=100,
            random_state=42,
            eval_metric='auc',
            use_label_encoder=False
        )
        
        try:
            xgb_model.fit(X, y)
            
            # Get feature importance
            importance = xgb_model.feature_importances_
            feature_importance = pd.DataFrame({
                'feature': feature_cols,
                'importance': importance
            }).sort_values('importance', ascending=False)
            
            results[name] = feature_importance
            
            print(f"\n🔝 Top 10 features - {name}:")
            for i, (_, row) in enumerate(feature_importance.head(10).iterrows()):
                print(f"   {i+1:2d}. {row['feature']:30} {row['importance']:.4f}")
                
        except Exception as e:
            print(f"   ⚠️  Feature importance failed for {name}: {e}")
    
    return results

def create_comparison_summary(thermo_results, metrics_results):
    """Create comparison summary"""
    print("\n📋 PERFORMANCE COMPARISON SUMMARY")
    print("=" * 50)
    
    if thermo_results and metrics_results:
        print(f"🔥 Thermodynamics Final:")
        print(f"   AUROC: {thermo_results['mean_auroc']:.4f} ± {thermo_results['std_auroc']:.4f}")
        print(f"   Features: {thermo_results['n_features']}")
        print(f"   Time: {thermo_results['training_time']:.1f}s")
        
        print(f"\n📊 Metrics Ranges:")
        print(f"   AUROC: {metrics_results['mean_auroc']:.4f} ± {metrics_results['std_auroc']:.4f}")
        print(f"   Features: {metrics_results['n_features']}")
        print(f"   Time: {metrics_results['training_time']:.1f}s")
        
        # Performance difference
        auroc_diff = metrics_results['mean_auroc'] - thermo_results['mean_auroc']
        print(f"\n🏆 WINNER: {'Metrics Ranges' if auroc_diff > 0 else 'Thermodynamics Final'}")
        print(f"   AUROC Difference: {auroc_diff:+.4f}")
        print(f"   Improvement: {abs(auroc_diff)/thermo_results['mean_auroc']*100:+.2f}%")
        
        # Feature efficiency
        thermo_efficiency = thermo_results['mean_auroc'] / thermo_results['n_features']
        metrics_efficiency = metrics_results['mean_auroc'] / metrics_results['n_features']
        
        print(f"\n⚡ FEATURE EFFICIENCY (AUROC/Feature):")
        print(f"   Thermodynamics: {thermo_efficiency:.6f}")
        print(f"   Metrics Ranges: {metrics_efficiency:.6f}")
        print(f"   Efficiency Winner: {'Metrics Ranges' if metrics_efficiency > thermo_efficiency else 'Thermodynamics Final'}")
        
    elif thermo_results:
        print(f"🔥 Thermodynamics Final (only):")
        print(f"   AUROC: {thermo_results['mean_auroc']:.4f} ± {thermo_results['std_auroc']:.4f}")
        print(f"   Features: {thermo_results['n_features']}")
        
    elif metrics_results:
        print(f"📊 Metrics Ranges (only):")
        print(f"   AUROC: {metrics_results['mean_auroc']:.4f} ± {metrics_results['std_auroc']:.4f}")
        print(f"   Features: {metrics_results['n_features']}")
    
    else:
        print("❌ No results available for comparison")

def main():
    """Main comparison function"""
    print("🏁 FEATURE SET PERFORMANCE COMPARISON")
    print("XGBoost GPU | 5-Fold CV | AUROC")
    print("=" * 60)
    
    # Load features from both methods
    thermo_df = load_thermodynamics_features()
    metrics_df = load_metrics_ranges_features()
    
    # Evaluate performance
    thermo_results = None
    metrics_results = None
    
    if thermo_df is not None:
        thermo_results = evaluate_xgboost_performance(thermo_df, "Thermodynamics_Final")
    
    if metrics_df is not None:
        metrics_results = evaluate_xgboost_performance(metrics_df, "Metrics_Ranges")
    
    # Compare feature importance
    if thermo_df is not None and metrics_df is not None:
        importance_comparison = compare_feature_importance(thermo_df, metrics_df)
    
    # Create summary
    create_comparison_summary(thermo_results, metrics_results)
    
    # Save results
    comparison_data = []
    
    if thermo_results:
        comparison_data.append({
            'method': 'Thermodynamics_Final',
            'mean_auroc': thermo_results['mean_auroc'],
            'std_auroc': thermo_results['std_auroc'],
            'n_features': thermo_results['n_features'],
            'training_time': thermo_results['training_time']
        })
    
    if metrics_results:
        comparison_data.append({
            'method': 'Metrics_Ranges',
            'mean_auroc': metrics_results['mean_auroc'],
            'std_auroc': metrics_results['std_auroc'],
            'n_features': metrics_results['n_features'],
            'training_time': metrics_results['training_time']
        })
    
    if comparison_data:
        comparison_df = pd.DataFrame(comparison_data)
        comparison_df.to_csv('feature_method_comparison.csv', index=False)
        print(f"\n💾 Comparison saved to 'feature_method_comparison.csv'")
    
    return thermo_results, metrics_results

if __name__ == "__main__":
    results = main()