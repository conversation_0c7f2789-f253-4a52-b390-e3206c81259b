#!/usr/bin/env python3
"""
Ensemble V4 - AdaBoost + CatBoost GPU with 5-Fold CV
"""

import numpy as np
import pandas as pd
from sklearn.model_selection import StratifiedKFold
from sklearn.metrics import roc_auc_score, f1_score, precision_score, recall_score, accuracy_score
from sklearn.ensemble import AdaBoostClassifier
import catboost as cb
import optuna
import logging
import warnings

warnings.filterwarnings('ignore')
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_prepared_features():
    """Load prepared features and labels separately"""
    logger.info("📊 Loading prepared_features.parquet and y_train.parquet...")
    
    try:
        X = pd.read_parquet('resources/processed_data/prepared_features.parquet')
        y = pd.read_parquet('y_train.parquet')
        
        if isinstance(y.index, pd.MultiIndex):
            y = y.reset_index(drop=True).iloc[:, 0] if y.shape[1] == 1 else y.reset_index(drop=True)
        else:
            y = y.iloc[:, 0] if y.shape[1] == 1 else y.squeeze()
        
        min_len = min(len(X), len(y))
        X = X.iloc[:min_len]
        y = y.iloc[:min_len] if hasattr(y, 'iloc') else y[:min_len]
        
        logger.info(f"   ✅ Features: {X.shape[0]} samples, {X.shape[1]} features")
        logger.info(f"   📊 Label distribution: {pd.Series(y).value_counts().to_dict()}")
        
        return X, y
    except Exception as e:
        logger.error(f"❌ Error loading data: {e}")
        raise

def optimize_adaboost(X, y, n_trials=20):
    """Optimize AdaBoost hyperparameters"""
    logger.info("🔧 Optimizing AdaBoost hyperparameters...")
    
    def objective(trial):
        params = {
            'n_estimators': trial.suggest_int('n_estimators', 50, 51),
            'learning_rate': trial.suggest_float('learning_rate', 0.01, 2.0),
            'algorithm': trial.suggest_categorical('algorithm', ['SAMME', 'SAMME.R']),
            'random_state': 42
        }
        
        # Base estimator parameters
        base_max_depth = trial.suggest_int('base_max_depth', 1, 10)
        base_min_samples_split = trial.suggest_int('base_min_samples_split', 2, 20)
        base_min_samples_leaf = trial.suggest_int('base_min_samples_leaf', 1, 10)
        
        estimator = cb.CatBoostClassifier(
            depth=base_max_depth,
            iterations=10,task_type='GPU',
            learning_rate=0.1,
            verbose=False,
            random_state=42
        )
        
        params['estimator'] = estimator  # Changed from base_estimator to estimator
        
        # 5-fold CV
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        cv_scores = []
        
        for train_idx, val_idx in skf.split(X, y):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            model = AdaBoostClassifier(**params)
            model.fit(X_train, y_train)
            
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            auc = roc_auc_score(y_val, y_pred_proba)
            cv_scores.append(auc)
        
        return np.mean(cv_scores)
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best AdaBoost AUC: {study.best_value:.4f}")
    
    return study.best_params

def optimize_catboost_gpu(X, y, n_trials=20):
    """Optimize CatBoost GPU hyperparameters"""
    logger.info("🔧 Optimizing CatBoost GPU hyperparameters...")
    
    def objective(trial):
        early_stopping_count = []
        
        params = {
            'depth': trial.suggest_int('depth', 4, 10),
            'learning_rate': trial.suggest_float('learning_rate', 0.001, 0.07),
            'iterations': trial.suggest_int('iterations', 500, 2000),
            'l2_leaf_reg': trial.suggest_float('l2_leaf_reg', 0.01, 1.0),
            'bootstrap_type': trial.suggest_categorical('bootstrap_type', ['Bayesian', 'Bernoulli']),
            'early_stopping_rounds': 50,
            'task_type': 'GPU',
            'loss_function': 'Logloss',
            'eval_metric': 'Logloss',  # Use Logloss instead of AUC for GPU
            'random_state': 42,
            'verbose': False
        }
        
        # Bootstrap-specific parameters
        if params['bootstrap_type'] == 'Bayesian':
            params['bagging_temperature'] = trial.suggest_float('bagging_temperature', 0.0, 10.0)
            params['langevin'] = trial.suggest_categorical('langevin', [True, False])
        else:  # Bernoulli
            params['subsample'] = trial.suggest_float('subsample', 0.6, 1.0)
        
        # 5-fold CV
        skf = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
        cv_scores = []
        
        for train_idx, val_idx in skf.split(X, y):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            model = cb.CatBoostClassifier(**params)
            model.fit(
                X_train, y_train,
                eval_set=(X_val, y_val),
                use_best_model=True,
                verbose=False
            )
            
            # Track early stopping iterations
            best_iteration = 1 if model.best_iteration_ == 0 else model.best_iteration_
            early_stopping_count.append(best_iteration)
            
            y_pred_proba = model.predict_proba(X_val)[:, 1]
            auc = roc_auc_score(y_val, y_pred_proba)
            cv_scores.append(auc)
        
        # Set user attribute for optimal iterations
        if early_stopping_count:
            trial.set_user_attr('c', np.max(early_stopping_count))
        
        return np.mean(cv_scores)
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    logger.info(f"   ✅ Best CatBoost AUC: {study.best_value:.4f}")
    
    # Get best params and add optimal iterations
    best_params = study.best_params.copy()
    if 'c' in study.best_trial.user_attrs:
        best_params['c'] = study.best_trial.user_attrs['c']
        logger.info(f"   📊 Optimal iterations: {best_params['c']}")
    
    return best_params

def create_models(ada_params, cat_params):
    """Create optimized models"""
    
    # Create AdaBoost model with CatBoost base estimator
    estimator = cb.CatBoostClassifier(
        task_type='GPU',
        depth=ada_params['base_max_depth'],
        iterations=50,
        learning_rate=0.1,
        verbose=False,
        random_state=42
    )
    
    ada_model_params = {k: v for k, v in ada_params.items() 
                       if k not in ['base_max_depth', 'base_min_samples_split', 'base_min_samples_leaf']}
    ada_model_params['estimator'] = estimator  # Changed from base_estimator to estimator
    
    ada_model = AdaBoostClassifier(**ada_model_params)
    
    # Create CatBoost model
    cat_model_params = cat_params.copy()
    
    # Use exact iterations if available
    if 'c' in cat_params:
        cat_model_params['iterations'] = cat_params['c']
        cat_model_params.pop('early_stopping_rounds', None)
        cat_model_params.pop('c', None)
    
    # Add fixed parameters
    cat_model_params.update({
        'task_type': 'GPU',
        'loss_function': 'Logloss',
        'eval_metric': 'Logloss',
        'random_state': 42,
        'verbose': False
    })
    
    cat_model = cb.CatBoostClassifier(**cat_model_params)
    
    return ada_model, cat_model

def train_ensemble_cv(ada_model, cat_model, X, y, n_folds=5):
    """Train ensemble with 5-fold CV"""
    logger.info("🎯 Training AdaBoost + CatBoost ensemble with 5-fold CV...")
    
    skf = StratifiedKFold(n_splits=n_folds, shuffle=True, random_state=42)
    
    oof_predictions = np.zeros((len(X), 2))  # 2 models
    individual_aucs = {'AdaBoost': [], 'CatBoost': []}
    
    for fold, (train_idx, val_idx) in enumerate(skf.split(X, y)):
        logger.info(f"   Fold {fold + 1}/{n_folds}...")
        
        X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
        y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
        
        # Train AdaBoost
        ada_model.fit(X_train, y_train)
        ada_pred = ada_model.predict_proba(X_val)[:, 1]
        oof_predictions[val_idx, 0] = ada_pred
        
        ada_auc = roc_auc_score(y_val, ada_pred)
        individual_aucs['AdaBoost'].append(ada_auc)
        
        # Train CatBoost
        cat_model.fit(X_train, y_train, verbose=False)
        cat_pred = cat_model.predict_proba(X_val)[:, 1]
        oof_predictions[val_idx, 1] = cat_pred
        
        cat_auc = roc_auc_score(y_val, cat_pred)
        individual_aucs['CatBoost'].append(cat_auc)
        
        logger.info(f"     AdaBoost: {ada_auc:.4f}, CatBoost: {cat_auc:.4f}")
    
    # Calculate ensemble predictions
    ensemble_predictions = np.mean(oof_predictions, axis=1)
    ensemble_auc = roc_auc_score(y, ensemble_predictions)
    
    logger.info("\n📊 Individual Model Performance:")
    for name, aucs in individual_aucs.items():
        mean_auc = np.mean(aucs)
        std_auc = np.std(aucs)
        logger.info(f"   {name:>8}: {mean_auc:.4f} ± {std_auc:.4f}")
    
    logger.info(f"\n🎉 Ensemble AUC: {ensemble_auc:.4f}")
    
    return {
        'ensemble_auc': ensemble_auc,
        'individual_aucs': individual_aucs,
        'oof_predictions': oof_predictions,
        'ensemble_predictions': ensemble_predictions
    }

def optimize_ensemble_weights(oof_predictions, y, n_trials=50):
    """Optimize ensemble weights"""
    logger.info("🔧 Optimizing ensemble weights...")
    
    def objective(trial):
        ada_weight = trial.suggest_float('ada_weight', 0.0, 1.0)
        cat_weight = trial.suggest_float('cat_weight', 0.0, 1.0)
        
        # Normalize weights
        total_weight = ada_weight + cat_weight
        if total_weight == 0:
            return 0.5
        
        ada_weight /= total_weight
        cat_weight /= total_weight
        
        weighted_predictions = (ada_weight * oof_predictions[:, 0] + 
                              cat_weight * oof_predictions[:, 1])
        
        auc = roc_auc_score(y, weighted_predictions)
        return auc
    
    study = optuna.create_study(direction='maximize')
    study.optimize(objective, n_trials=n_trials, show_progress_bar=True)
    
    # Get normalized weights
    ada_weight = study.best_params['ada_weight']
    cat_weight = study.best_params['cat_weight']
    total_weight = ada_weight + cat_weight
    
    if total_weight > 0:
        ada_weight /= total_weight
        cat_weight /= total_weight
    else:
        ada_weight = cat_weight = 0.5
    
    final_predictions = (ada_weight * oof_predictions[:, 0] + 
                        cat_weight * oof_predictions[:, 1])
    final_auc = roc_auc_score(y, final_predictions)
    
    logger.info(f"   ✅ Optimized Ensemble AUC: {final_auc:.4f}")
    logger.info(f"   📊 Weights - AdaBoost: {ada_weight:.3f}, CatBoost: {cat_weight:.3f}")
    
    return [ada_weight, cat_weight], final_auc, final_predictions

def calculate_comprehensive_metrics(y_true, y_pred_proba, threshold=0.5):
    """Calculate comprehensive classification metrics"""
    y_pred_binary = (y_pred_proba >= threshold).astype(int)
    
    return {
        'auc': roc_auc_score(y_true, y_pred_proba),
        'f1': f1_score(y_true, y_pred_binary),
        'precision': precision_score(y_true, y_pred_binary, zero_division=0),
        'recall': recall_score(y_true, y_pred_binary),
        'accuracy': accuracy_score(y_true, y_pred_binary)
    }

logger.info("🚀 Starting AdaBoost + CatBoost GPU Ensemble V4...")

# Load data
X, y = load_prepared_features()

# Optimize AdaBoost
ada_params = optimize_adaboost(X, y, n_trials=4)

# Optimize CatBoost GPU
cat_params = optimize_catboost_gpu(X, y, n_trials=3)

# Create optimized models
ada_model, cat_model = create_models(ada_params, cat_params)

# Train ensemble with CV
ensemble_results = train_ensemble_cv(ada_model, cat_model, X, y, n_folds=5)

# Optimize ensemble weights
best_weights, optimized_auc, optimized_predictions = optimize_ensemble_weights(
    ensemble_results['oof_predictions'], y, n_trials=50
)

# Calculate metrics
simple_metrics = calculate_comprehensive_metrics(y, ensemble_results['ensemble_predictions'])
optimized_metrics = calculate_comprehensive_metrics(y, optimized_predictions)

# Print results
logger.info("\n🎉 ADABOOST + CATBOOST ENSEMBLE V4 COMPLETED!")
logger.info("=" * 60)
logger.info("📊 RESULTS:")
logger.info(f"   Simple Ensemble:      {simple_metrics['auc']:.4f} AUC")
logger.info(f"   Optimized Ensemble:   {optimized_metrics['auc']:.4f} AUC")

logger.info(f"\n📈 OPTIMIZED ENSEMBLE METRICS:")
logger.info(f"   AUC:        {optimized_metrics['auc']:.4f}")
logger.info(f"   F1 Score:   {optimized_metrics['f1']:.4f}")
logger.info(f"   Precision:  {optimized_metrics['precision']:.4f}")
logger.info(f"   Recall:     {optimized_metrics['recall']:.4f}")
logger.info(f"   Accuracy:   {optimized_metrics['accuracy']:.4f}")

# Save results
import joblib
final_results = {
    'ada_params': ada_params,
    'cat_params': cat_params,
    'ensemble_results': ensemble_results,
    'best_weights': best_weights,
    'simple_metrics': simple_metrics,
    'optimized_metrics': optimized_metrics
}
joblib.dump(final_results, 'ensemble_v4_results.joblib')
logger.info("💾 Results saved to 'ensemble_v4_results.joblib'")
    